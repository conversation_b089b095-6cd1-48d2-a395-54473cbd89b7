<template>
	<!-- 
		stepList: [
			{
				info: '开卡', // 左侧内容 -- 可选
				index: '1', // 中间 Index -- 可选
				isFinished: true, // 是否已完成（完成 index 为 √）-- 可选
				isActive: false, // 是否为当前节点 Active（当前节点 即使完成 index 也不会显示 √）-- 可选
				isShowSlot: false // 右侧是否有 Slot（显示在 右侧内容下方）-- 可选
			},
			{
				date: '08-30',
				time: '15:33',
				info: '激活',
				index: '2',
				isFinished: false,
				isActive: true,
				isShowSlot: true
			}
		]

		slot 示例：
		<y-steps :stepList="stepList">
			<mx-button type="danger" value="激活网点查看" />
		</y-steps>

		可按自己需求修改 css 中颜色变量
	 -->
	<div class="steps">
		<div
			v-for="(step, index) in stepList"
			:key="'stepsItem' + index"
      		:class="['steps-item', step.isActive ? 'steps-item-active' : '', { 'not-icon': isStatus2 }]">
			<div class="steps-item-left">
        <div>{{ step.title }}</div>
			</div>
			<div class="steps-item-index">
				<div :class="['line', index != 0 ? '' : 'line-transparent']"></div>
        <div v-if="!step.isActive && step.isFinished" class="index index-success"></div>
      <div v-else class="index"><div v-if="!step.isActive" class="point-index"></div><div v-else class="point-success-index"></div></div>
				<div :class="['line', index != stepList.length - 1 ? '' : 'line-transparent']"></div>
			</div>
			<div class="steps-item-right">
				<div class="right-info-item">
					<div v-if="step.showSlot" v-html='step.showSlot'>

					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		name: 'YSteps',
		props: {
			stepList: {
				type: Array,
				default: () => []
			},
      isStatus2: false
		}
	};
</script>

<style lang="scss" scoped>
	$normolColor: #333333;
	$activeColor: #0081FF;
	$finishedColor: #0081FF;
	$normolBgColor: #DBDBDB;
	$activeBgColor: #BADDFF;
	$finishedBgColor: #0081FF;
  $pointBgColor: #939393;

  .steps-item-left {
    position: relative;
    font-weight: bold;

    &:after {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      width: 3.1rem;
      height: 2.96rem;
      background: url('../images/done.png') no-repeat center / 100% 100%;
      transform: translateY(-41%) scale(.8);
    }
  }

  .steps-item-active {
    .steps-item-left::after {
      background-image: url('../images/clz.png');
    }
  }

  .not-icon .steps-item-left::after {
    display: none !important;
  }

  .not-icon .steps-item-left {
    width: 55px !important;
  }

	.steps {
		display: flex;
		flex-direction: column;
    font-family: PingFangSC-Medium,sans-serif;
    padding: 0 12px;
    font-size: 13px;
		.steps-item {
			display: flex;
			.steps-item-left {
				display: flex;
				align-items: center;
				color: $normolColor;
        width: 85px;
			}
			.steps-item-index {
				padding: 0 20px;
				display: flex;
				flex-direction: column;
				align-items: center;
				.line {
					flex: 1;
					width: 2px;
					background-color: $normolBgColor;
				}
				.line-transparent {
					background-color: transparent;
				}
				.index {
					width: 15px;
					height: 15px;
          display: flex;
          justify-content: center;
          align-items: center;
					line-height: 50px;
					border-radius: 50px;
					color: $normolColor;
					background-color: $normolBgColor;
          .point-index{
            width: 8px;
            height: 8px;
            border-radius: 50px;
            background-color: $pointBgColor;
          }
          .point-success-index{
            width: 8px;
            height: 8px;
            border-radius: 50px;
            background-color: $activeColor;
          }
				}
				/deep/ .index-success {
					display: flex;
					justify-content: center;
					align-items: center;
					.uni-icon-success_no_circle {
						color: $normolColor;
					}
				}
			}
			.steps-item-right {
        width: 210px;
				display: flex;
				flex-direction: column;
        flex: 1;
				padding: 20px 0;
				color: $normolColor;
				.right-info-item {
					display: flex;
					flex-direction: column;
					justify-content: center;
					padding: 0px;
          line-height: 1.5;
					span {
						font-size: 14px;
						font-weight: 600;
						line-height: 30px;
					}
				}
			}
		}
		.steps-item-finished {
			.steps-item-left {
				color: $finishedColor;
			}
			.steps-item-index {
				.index {
					color: $finishedColor;
					background-color: $finishedBgColor;
				}
			}
			.steps-item-right {
				color: $finishedColor;
			}
		}
		.steps-item-active {
			.steps-item-left {
				color: $activeColor;
			}
			.steps-item-index {
				.index {
					color: $activeColor;
					background-color: $activeBgColor;
				}
			}
			.steps-item-right {
				color: $activeColor;
			}
		}
	}
</style>
