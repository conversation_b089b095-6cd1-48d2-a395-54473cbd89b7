import browserTool from './browser-toolkit';
import ExpandMobile from "./expandMobile";
import BizMobile from "./bizMobile";
import <PERSON> from './jcl';
import {safeTrans} from "./util";

//终端类型,a为android,i为ios
var deviceType = (function(){
    /*
    IpuMobile/i1/android/00/2.0/Hybrid
    userAgent格式
    i1版本规范:
    标识符/规范版本号/终端类型(ios,android,wp)/终端型号(平板，或尺寸,00表示默认)/框架版本号/结尾标识符
    */
    var sUserAgent = window.navigator.userAgent;
    //          标识符     规范1  类型2 型号3  框架4 结尾标识符
    var re = /IpuMobile\/(.*)\/(.*)\/(.*)\/(.*)\/Hybrid/ig;
    var arrMessages = re.exec(sUserAgent);
    if(arrMessages && arrMessages[1] =="i1" ){
        if(arrMessages[2] == "android"){
            return "a";
        }else if(arrMessages[2] == "ios"){
            return "i"
        }else if(arrMessages[2] == "wp"){
            return "w";
        }else{
            return null;
        }
    }else{
        return null;
    }
})();
if(!window["TerminalType"]){
    window["TerminalType"] = deviceType;
}
var terminalType = window["TerminalType"];
var WadeMobile = (function(){
    return{
        isIpuApp:function() { // 判断是否IpuApp
            let userAgent = window.navigator.userAgent;
            return userAgent.includes("IpuMobile")
        },
        isAndroid:function(){
            return terminalType=='a';
        },isIOS:function(){
            return terminalType=='i';
        },isWP:function(){
            return terminalType=='w';
        },isApp:function(){//判断是否是APP应用
            return !!terminalType;
        },getSysInfo:function(key){//TELNUMBER|IMEI|IMSI|SDKVERSION|OSVERSION|PLATFORM|SIMNUMBER
            return new Promise((resolve, reject) => {
                WadeMobile.callback.storageCallback("getSysInfo",(data) => {
                    resolve(safeTrans(data));
                });
                execute("getSysInfo", [key],(err) => {
                    reject(safeTrans(err));
                });
            })
        },close:function(confirm,err){
            if(typeof(confirm)!="boolean"){
                confirm = true;
            }
            execute("close", [confirm],err);
        },httpRequest:function(requestUrl,encode,conTimeout,readTimeout){
            if(terminalType=="i"){
                requestUrl = encodeURIComponent(requestUrl);
            }
            return new Promise((resolve, reject) => {
                WadeMobile.callback.storageCallback("httpRequest",(data) => {
                    resolve(safeTrans(data));
                });
                execute("httpRequest", [requestUrl,encode,conTimeout,readTimeout],(err) => {
                    reject(safeTrans(err));
                });
            })
        },dataRequest:function(dataAction,param,encode,conTimeout,readTimeout){
            return new Promise((resolve, reject) => {
                WadeMobile.callback.storageCallback("dataRequest",(data) => {
                    resolve(safeTrans(data));
                });
                execute("dataRequest", [dataAction,param,encode,conTimeout,readTimeout],(err) => {
                    reject(safeTrans(err));
                });
            })
        },dataRequestWithHost:function(url,dataAction,param,encode,conTimeout,readTimeout){
            return new Promise((resolve, reject) => {
                WadeMobile.callback.storageCallback("dataRequestWithHost",(data) => {
                    resolve(safeTrans(data));
                });
                execute("dataRequestWithHost", [url,dataAction,param,encode,conTimeout,readTimeout],(err) => {
                    reject(safeTrans(err));
                });
            })
        },loadUrl:function(url,err){
            execute("loadUrl", [encodeURIComponent(url)],err);
        },openUrl: function (url, callback, title, buttons, styles, err) {
            WadeMobile.callback.storageCallback("openUrl", callback);
            execute("openUrl", [encodeURIComponent(url), title, buttons, styles], err);
        },openCordova: function(url,callback,err){
            WadeMobile.callback.storageCallback("openCordova",callback);
            execute("openCordova",[encodeURIComponent(url)],err);
        },openPage:function(action,data,err){
            execute("openPage", [action,data],err);
        },openTemplate:function(action,context,err){
            execute("openTemplate", [action,context],err);
        },loadPage:function(action,data,err){
            execute("loadPage", [action,data],err);
        },loadTemplate:function(action,context,err){
            execute("loadTemplate", [action,context],err);
        },back:function(tag,err){
            execute("back",[tag],err);
        },backWithCallback:function(data,tag,err){
            execute("backWithCallback",[data,tag],err);
        },getPage:function(action,param){
            return new Promise((resolve, reject) => {
                WadeMobile.callback.storageCallback("getPage",(data) => {
                    resolve(safeTrans(data));
                });
                execute("getPage", [action,param],(err) => {
                    reject(safeTrans(err));
                });
            })
        },getTemplate:function(action,context){
            return new Promise((resolve, reject) => {
                WadeMobile.callback.storageCallback("getTemplate",(data) => {
                    resolve(safeTrans(data));
                });
                execute("getTemplate", [action,context],(err) => {
                    reject(safeTrans(err));
                });
            })
        },storageDataByThread:function(dataAction,param,waitoutTime,err){
            execute("storageDataByThread", [dataAction,param,waitoutTime],err);
        },openDialog:function(pageAction,param,width,height){
            return new Promise((resolve, reject) => {
                WadeMobile.callback.storageCallback("openDialog",(data) => {
                    resolve(safeTrans(data));
                });
                execute("openDialog", [pageAction,param,width,height],(err) => {
                    reject(safeTrans(err));
                });
            })
        },closeDialog:function(result,state,err){
            execute("closeDialog", [result,state],err);
        },openWindow:function(pageAction,param){
            return new Promise((resolve, reject) => {
                WadeMobile.callback.storageCallback("openWindow",(data) => {
                    resolve(safeTrans(data));
                });
                execute("openWindow", [pageAction,param],(err) => {
                    reject(safeTrans(err));
                });
            })
        },closeWindow:function(result,state,err){
            execute("closeWindow", [result,state],err);
        },openSlidingMenu:function(action,param,width,height,leftMargin,topMargin){
            return new Promise((resolve, reject) => {
                WadeMobile.callback.storageCallback("openSlidingMenu",(data) => {
                    resolve(safeTrans(data));
                });
                execute("openSlidingMenu", [action,param,width,height,leftMargin,topMargin],(err) => {
                    reject(safeTrans(err));
                });
            })
        },closeSlidingMenu:function(result,state,err){
            execute("closeSlidingMenu", [result,state],err);
        },showLoadingHUD: function () {//ios显示加载
            execute("showLoadingHUD", []);
        }, hideLoadingHUD: function () {//ios隐藏加载
            execute("hideLoadingHUD", []);
        }, setH5TopBar: function (options) {
            function genTitle(options) {
                if (options) {
                    // 增加是否IpuApp判断，若不是IpuApp，只传title
                    if(WadeMobile.isIpuApp()) {
                        return Object.entries(options).map(value => {
                            if (value[0] === 'copy') {
                                return `cp=${value[1]}`
                            }else {
                                return `${value[0]}=${value[1]}`;
                            }
                        }).join('&');
                    } else {
                        return options.title;
                    }
                }else {
                    return '';
                }
            }
            document.title = options ? genTitle(options) : '';
            if (WadeMobile.isIOS()) {
                let iframe = document.createElement('iframe');
                iframe.style.display = 'none';
                const _img = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';
                iframe.setAttribute('src', _img);
                let iframeCallback = function () {
                    setTimeout(function () {
                        iframe.removeEventListener('load', iframeCallback);
                        document.body.removeChild(iframe)
                    }, 0)
                };
                iframe.addEventListener('load', iframeCallback);
                document.body.appendChild(iframe)
            }
        }
    };
})();
//自定义的按钮点击事件
WadeMobile.customEvents =(function()
{
    return {
        cancelEvent:function(){
            var param = Wade.DataMap();
            param.put("title","取消成功！");
            param.put("content","已经取消了！");
            param.put("alertType",1);
            param.put("cancelable",true);
            param.put("imageID",0);
            WadeMobile.sweetAlert(param);
        },confirmEvent:function(){
            var param = Wade.DataMap();
            param.put("title","确认成功！");
            param.put("content","成功完成了任务！");
            param.put("alertType",2);
            param.put("cancelable",true);
            param.put("imageID",0);
            WadeMobile.sweetAlert(param);
        },nextEvent:function(){
            var param = Wade.DataMap();
            param.put("title","加载成功！");
            param.put("content","");
            param.put("alertType",2);
            param.put("cancelable",true);
            param.put("imageID",0);
            WadeMobile.sweetAlert(param);
        }
    };
})();
//全局变量
var callbackId = 0;
var callbacks = {};//用来存放成功和失败的js回调函数
var callbackDefine = {};//用来存放自定义的js回调函数
var globalErrorKey = null;//全局错误关键字,定位错误

/*绝大多数情况下,success回调函数是用不上的,有需要回调函数的时候异步方式传入取值*/
var isAlert = true;//防止反复弹出alert
var execute = function(action, args, error, success){
    args = stringify(args);
    if(terminalType=="a"){
        androidExecute(action, args, error, success);
    }else if(terminalType=="i"){
        iosExecute(action, args, error, success);
    }else if(terminalType=="w"){
        winphoneExecute(action, args, error, success);
    }else{
        if(isAlert){
            isAlert = false
            alert(action+"无终端类型");
            error && error(action+"无终端类型");
        }else{
            console.log(action+"无终端类型");
            error && error(action+"无终端类型");
        }
    }
};

WadeMobile.execute = execute;

if(typeof PluginManager === "undefined"){
    window.PluginManager = {
        exec: function(arg0, arg1, arg2) {
            prompt('_$$_SafelyJsInterface:_' + JSON.stringify({
                inf: 'PluginManager',
                func: 'exec',
                args: [arg0, arg1, arg2]
            }));
        },
    };
}

var androidExecute = function(action, args, error, success){
    //执行android方法时，带入到android底层的key值为，回调方法实际的key值 + 用于在top上索引本iframe的WadeMobile的唯一标识。
    //在android底层，如果发现回调函数的key值包含这个特殊的串。那么将解析这个key。并且取出加回调函数key的后半部分，作为在top上索引本iframe相对应的WadeMobile对象的唯一依据。
    var tmpKey = action+callbackId++;
    if(window._WadeMobileSet_Key_ != undefined){
        tmpKey += window._WadeMobileSet_Key_;
    }
    var callbackKey = globalErrorKey = tmpKey;
    if (success || error) {
        callbacks[callbackKey] = {success:success, error:error};
    }
    if(WadeMobile.debug){
        //alert("准备调用"+action+" 参数:"+args);
        console.log("action:"+action+" param:"+args);
    }
    PluginManager.exec(action, callbackKey, args);
    globalErrorKey = null;
};

var iosExecute = function(action, args, error, success){
    var callbackKey = globalErrorKey = action+callbackId++;
    if (success || error) {
        callbacks[callbackKey] = {success:success, error:error};
    }
    if(WadeMobile.debug){
        //alert("准备调用"+action+" 参数:"+args);
        console.log("action:"+action+" param:"+args);
    }

    var WADE_SCHEME = "wade://";
    var url = WADE_SCHEME+action+"?param="+encodeURIComponent(args)+"&callback="+callbackKey;
    //一个动作请求客户端的最大数量，超过会造成请求覆盖
    var limitAction = 10;
    var ifrmName = "WADE_FRAME_"+(callbackId%limitAction);
    var ifrm = document.getElementById(ifrmName);
    if(!ifrm){
        var ifrm = document.createElement("iframe");
        ifrm.setAttribute("id",ifrmName);
        ifrm.setAttribute("width","0");
        ifrm.setAttribute("height","0");
        ifrm.setAttribute("border","0");
        ifrm.setAttribute("frameBorder","0");
        ifrm.setAttribute("name",ifrmName);
        document.body.appendChild(ifrm);
    }
    document.getElementById(ifrmName).contentWindow.location = encodeURIComponent(url);
    //document.getElementById(ifrmName).src = encodeURI(url);//无法处理&符号
    globalErrorKey = null;
};

var winphoneExecute = function(action, args, error, success){
    var callbackKey = globalErrorKey = action+callbackId++;
    if (success || error) {
        callbacks[callbackKey] = {success:success, error:error};
    }
    if(WadeMobile.debug){
        //alert("准备调用"+action+" 参数:"+args);
        console.log("action:"+action+" param:"+args);
    }
    window.external.Notify(stringify([action, callbackKey, args])); //[action, callbackKey, args]
    globalErrorKey = null;
};

WadeMobile.callback = (function(){
    return{
        success:function(callbackKey, message) {
            if(typeof message == "undefined"){
                return;
            }
            if (callbacks[callbackKey]) {
                if (callbacks[callbackKey].success) {
                    if(typeof callbacks[callbackKey].success==="function"){
                        var func = callbacks[callbackKey].success;
                        func(message);
                    }else{
                        _eval(callbacks[callbackKey].success+"('"+message+"','"+callbackKey+"')");
                    }
                }
                if (callbacks[callbackKey]) {
                    delete callbacks[callbackKey];
                }
            }
        },error:function(callbackKey, message, isEncode) {
            if(typeof message == "undefined"){
                return;
            }
            if(isEncode){
                message = decodeURIComponent(message);
            }
            if (callbacks[callbackKey]) {
                if (callbacks[callbackKey].error) {
                    if(typeof callbacks[callbackKey].error==="function"){
                        var func = callbacks[callbackKey].error;
                        func(message);
                    }else{
                        _eval(callbacks[callbackKey].error+"('"+message+"','"+callbackKey+"')");
                    }
                }
                if (callbacks[callbackKey]) {
                    delete callbacks[callbackKey];
                }
            }else{
                alert(message);
            }
        },storageCallback:function(action,callback){
            var callbackKey = action+callbackId;
            if (callback) {
                callbackDefine[callbackKey] = {callback:callback};
            }
        },execCallback:function(callbackKey, data, isEncode){
            globalErrorKey = callbackKey;
            var callbackItem = callbackDefine[callbackKey];
            if (callbackItem) {
                data = data=="null"?null:data;
                if(data){
                    if(isEncode){
                        data = decodeURIComponent(data);
                    }
                }
                if (callbackItem.callback) {
                    if(typeof callbackItem.callback==="function"){
                        var func = callbackItem.callback;
                        func(data);
                    }else{
                        _eval(callbackItem.callback+"('"+data+"','"+callbackKey+"')");
                    }
                }
                if (callbackItem) {
                    delete callbackDefine[callbackKey];
                }
            }
            globalErrorKey = null;
        }
    };
})();

/**物理按键监听start**/
WadeMobile.setKeyListener = function(key, callback,isOverload){
    if(key=="back"||key=="menu"||key=="home"){
        document.addEventListener(key, callback, false);
        if(isOverload != null){
            execute("setKeyDownFlag", [key,isOverload]);
        }
    }else if(key == "slipToRight"){
        document.addEventListener(key, callback, false);
        if(isOverload != null){
            execute("coverIOSCurrentTemplateSlipToRight", [isOverload]);
        }
    }
}

WadeMobile.cleanKeyDownFlag = function(key){
    if(key != null){
        execute("cleanKeyDownFlag", [key]);
    }
}

WadeMobile.event = (function(){
    if(WadeMobile.isApp()){
        // Create the event.
        var backEvent = document.createEvent('Event');
        var menuEvent = document.createEvent('Event');
        var homeEvent = document.createEvent('Event');
        var slipToRightEvent = document.createEvent('Event');
        return {
            back:function(){
                backEvent.initEvent("back", true, true);
                document.dispatchEvent(backEvent);
            },menu:function(){
                menuEvent.initEvent("menu", true, true);
                document.dispatchEvent(menuEvent);
            },home:function(){
                homeEvent.initEvent("home", true, true);
                document.dispatchEvent(homeEvent);
            },slipToRight:function(){
                slipToRightEvent.initEvent("slipToRight", true, true);
                document.dispatchEvent(slipToRightEvent);
            }
        };
    }
})();
/**物理按键监听end**/

/**back回调事件监听开始**/
WadeMobile.backevent = (function(){
    if(WadeMobile.isApp()){
        return {
            backCall:function(data){
                var backCallEvent = document.createEvent('Event');
                backCallEvent.initEvent("backCall",true,true);
                backCallEvent.data = data;
                document.dispatchEvent(backCallEvent);
            }
        };
    }
})();

WadeMobile.setBackCallListener = function(callback){
    document.addEventListener("backCall", callback);
}
/**back回调事件监听结束**/

/************公共方法**************/
/**
 * @param {String}  errorMessage   错误信息
 * @param {String}  scriptURI      错误文件
 * @param {Long}    lineNumber     错误行号
 */
window.onerror = function(errorMessage, scriptURI, lineNumber) {
    var msgArray = [];
    if (errorMessage)
        msgArray.push("错误信息:" + errorMessage);
    if (lineNumber)
        msgArray.push("错误行号:" + lineNumber);
    if (globalErrorKey)
        msgArray.push("错误关键字:" + globalErrorKey);
    if (scriptURI)
        msgArray.push("错误文件:" + scriptURI);
    var msg = msgArray.join("\t\n");
    console.log(msg);
    if (window.$beePlugin_errTip) {
        alert("应用君走丢了，请重试")
    }
};
/**
 * 重写alert方法，解决在iOS7以上不可点击问题
 * @param name
 */
/**/
// window.alert = function(name){
//
//     var iframe = document.createElement("IFRAME");
//     iframe.style.display="none";
//     iframe.setAttribute("src", 'data:text/plain,');
//     document.documentElement.appendChild(iframe);
//     window.frames[window.frames.length-1].window.alert(name);
//     iframe.parentNode.removeChild(iframe);
// }


//动态执行js方法
function _eval(code,action){
    if(WadeMobile.debug){
        alert(code);
    }
    var func = eval(code);
    if(typeof func==="function"){
        func();
    }
}
//格式转换方法
function stringify(args) {
    if (typeof JSON == "undefined") {
        var s = "[";
        for (var i=0; i<args.length; i++) {
            if (i > 0) {
                s = s + ",";
            }
            var type = typeof args[i];
            if ((type == "number") || (type == "boolean")) {
                s = s + args[i];
            }
            else if (args[i] instanceof Array) {
                s = s + "[" + args[i] + "]";
            }
            else if (args[i] instanceof Object) {
                var start = true;
                s = s + '{';
                for (var name in args[i]) {
                    if (args[i][name] != null) {
                        if (!start) {
                            s = s + ',';
                        }
                        s = s + '"' + name + '":';
                        var nameType = typeof args[i][name];
                        if ((nameType == "number") || (nameType == "boolean")) {
                            s = s + args[i][name];
                        }
                        else if ((typeof args[i][name]) == 'function') {
                            // don't copy the functions
                            s = s + '""';
                        }
                        else if (args[i][name] instanceof Object) {
                            s = s + stringify(args[i][name]);
                        }
                        else {
                            s = s + '"' + args[i][name] + '"';
                        }
                        start=false;
                    }
                }
                s = s + '}';
            }else {
                var a = args[i].replace(/\\/g, '\\\\');
                a = a.replace(/"/g, '\\"');
                s = s + '"' + a + '"';
            }
        }
        s = s + "]";
        return s;
    }else {
        return JSON.stringify(args);
    }
};

Object.assign(WadeMobile, ExpandMobile, BizMobile);

if (!WadeMobile.isApp()) {
    WadeMobile.initAppConfig = function (param) {
        param = JSON.stringify(param);
        browserTool.browser.setMemoryCache("appConfig", param)
    };
    window.addEventListener("message", function (ev) {
        var data = ev.data;
        if (data && data.type == "copySession") {
            copySession(data.value);
            top.window.postMessage({type: "redirect", value: ""}, "*");
        }
    }, false);
    function copySession(storage) {
        for (var key in storage) {
            window.sessionStorage[key] = storage[key];
        }
    }
    WadeMobile.openIpuApp = function (param, callback, err) {
        param = param ? Wade.DataMap(param) : Wade.DataMap();
        param.put("MENU_TYPE", "I");
        var appId = param.get("APP_ID");
        var pageAction = param.get("MENU_PAGE_ACTION");
        if (window.APP_CONFIG && window.APP_CONFIG[appId]) {
            return redirectTo(window.APP_CONFIG[appId] + "/mobile?action=" + pageAction, param.get("EXT_PARAM"), browserTool);
        } else {
            // 找app配置的链接信息
            browserTool.browser.getMemoryCache(function (appList) {
                var appInfo = null;
                if (appList && appList.length > 0) {
                    appList = JSON.parse(appList);
                    for (var i = 0; i < appList.length; i++) {
                        if (appList[i].APP_ID == appId) {
                            appInfo = appList[i];
                            break;
                        }
                    }
                }
                if (null == appInfo) {
                    alert("未找到该appId对应的应用信息：" + appId);
                    return;
                }
                var url = appInfo.APP_REQUEST_HOST + appInfo.APP_REQUEST_PATH + "/mobile?action=" + pageAction;
                redirectTo(url, param.get("EXT_PARAM"), browserTool);
            }, "appConfig")
        }
    };
    var callback = null;
    function redirectTo(url, param, browserTool) {
        var data = {isContext: true};
        param && (data.data = param);
        var host = url.match("http.*?://(.*?)/")[1];
        if (window.location.host == host) {
            browserTool.redirect.postPage(url, data);
        } else {
            // cross region
            var iframe = document.createElement("iframe");
            iframe.name = '_subApp';
            iframe.id = '_subApp';
            iframe.style.display ='none';
            iframe.onload=function(){
                iframe.contentWindow.postMessage({type: "copySession", value: copyFromSession()}, url);
                callback && window.removeEventListener("message", callback);
                callback = function (ev) {
                    var dir = ev.data;
                    if (dir && dir.type == "redirect") {
                        browserTool.redirect.postPage(url, data);
                    }
                };
                window.addEventListener("message", callback, false);
                setTimeout(function () {
                    console.log("长时间无响应，直接跳转");
                    browserTool.redirect.postPage(url, data);
                },8000);
            }
            data.target="_subApp";
            document.body.appendChild(iframe);
            browserTool.redirect.postPage(url, data);
        }
    }
    function copyFromSession() {
        var copied = {};
        var storage = window.sessionStorage || {};
        for (var key in storage) {
            if (storage.hasOwnProperty(key) && typeof key != 'function') {
                copied[key] = storage[key];
            }
        }
        return copied;
    }

    WadeMobile.openH5 = function (url) {
        var data = {target: "_subApp"};
        var iframe = document.createElement("iframe");
        iframe.name='_subApp';
        iframe.id='_subApp';
        iframe.style.display='none';
        iframe.onload = function(){
            iframe.contentWindow.postMessage({type: "copySession", value: copyFromSession()}, url);
            callback && window.removeEventListener("message", callback);
            callback = function (ev) {
                var dir = ev.data;
                if (dir && dir.type == "redirect") {
                    // browserTool.redirect.getPage(url, data);
                    location.href = url;
                }
            };
            window.addEventListener("message", callback, false);
            setTimeout(function () {
                console.log("长时间无响应，直接跳转");
                browserTool.redirect.getPage(url, data);
            },8000);
        };
        document.body.appendChild(iframe);
        browserTool.redirect.getPage(url, data);
    }
}
//让top对象上，保持有一个当前iframe里面的WadeMobile对象的引用。
//注意：在iframe中，_WadeMobileSet_Key_+时间戳表示一个key，此key作为了在top对象上索引iframe中的WadeMobile的依据。
//将保持引用的key值存入到当前ifame的window对象上。
(function(){
    //屏蔽所有浏览器
    if( window.navigator.userAgent.indexOf("IpuMobile\/") == -1 ) {
        console.log("<WadeMobileSet> \"IpuMobile\/\" string does not exist in the userAgent. return.");
        return;
    }

    if(top != window){
        if(top.WadeMobileSet == undefined){
            top.WadeMobileSet = {};
        }
        for(var key in top.WadeMobileSet){
            try{
                if( key.indexOf("_WadeMobileSet_Key_") != -1 && ( !top.WadeMobileSet[key] || ( top.WadeMobileSet[key].canRemoved && top.WadeMobileSet[key].canRemoved() ) ) ){
                    console.log("(top set)delete:" + key);
                    delete top.WadeMobileSet[key];
                    console.log("(top set)delete success :" + key);
                }
            }catch(e){
                console.log("a error(WadeMobile) : " + e);
                console.log("(top set)delete:" + key);
                delete top.WadeMobileSet[key];
                console.log("(top set)delete success :" + key);
            }
        }
        var key = "_WadeMobileSet_Key_" + new Date().getTime();
        window._WadeMobileSet_Key_ = key;
        console.log("in an iframe, window.WadeMobile object is referenced top.WadeMobileSet." + key);
        top.WadeMobileSet[key] = window.WadeMobile;
        window.WadeMobile.canRemoved = function(){
            return !window;
        };
    }
})();
window.WadeMobile = WadeMobile;
export default WadeMobile;
