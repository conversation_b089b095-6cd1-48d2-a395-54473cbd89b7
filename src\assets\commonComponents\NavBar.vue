<!--
功能：左侧是返回图标，中间是标题的导航栏
props
    title: 中间显示的标题，string
    showBack: 是否显示左侧的返回图标，boolean
slots
    right： 自定义右侧区域内容
events
    click-left: 点击左侧返回按钮时触发
    click-right: 点击右侧按钮时触发-->
<template>
    <van-nav-bar
        class="navBar ig-navBar"
        :border="false"
        @click-left="back"
        @click-right="$emit('click-right')"
    >
        <template #left>
            <van-icon v-if="showBack" name="rux-fanhui" class="backIcon" />
        </template>
        <template #title>
            <span class="title">{{ title }}</span>
            <slot name="title" />
        </template>
        <template #right>
            <slot name="right" />
        </template>
    </van-nav-bar>
</template>

<script>
export default {
    name: 'NavBar',
    props: {
        title: {
            type: String,
            default: ''
        },
        showBack: {
            type: <PERSON>olean,
            default: true
        }
    },
    methods: {
        back() {
            if (!this.showBack) {
                return;
            }
            if (this.$listeners['click-left']) {
                this.$emit('click-left');
            } else {
                this.$router.back();
            }
        }
    }
};
</script>

<style lang="scss" scoped>
    .navBar {
        box-sizing: border-box;
        height: 44px;
    }

    .ig-navBar {
        border-bottom: 1px solid #e5e5e5;
    }

    .backIcon {
        font-size: 16px;
        color: #141c23;
    }

    .title, /deep/.van-nav-bar__title {
        /* font-family: PingFang-SC-Bold; */
        font-weight: bold;
        font-size: 17px;
        color: #141c23;
    }

    .no-border {
        border-bottom: 0;
    }
</style>