<p align="center">
    <img alt="logo" src="https://120.52.49.80/portal/static/unicom_logo.9fdb7470.png" width="120" height="120" style="margin-bottom: 10px;">
</p>

<h3 align="center" style="margin: 30px 0 35px;">@zgltryy/super-app-h5 built on Vant and Vue</h3>
<p align="center" style="margin: 16px 0 4px;">基于Vant、Vue的多页应用H5模板工程</p>

<p align="center">
  🔥 <a href="https://youzan.github.io/vant">Vant</a>
  &nbsp;
  &nbsp;
  💡 <a href="https://vant-contrib.gitee.io/vant">国内镜像文档</a>
  &nbsp;
  &nbsp;
  📚  <a href="https://www.npmjs.com/package/rux-ui">NPM</a>
  &nbsp;
</p>

---

## Installation

```npm i @zgltryy/super-app-h5```

## Quickstart

```npm run dev```

访问菜单页面
    每个菜单对应的url为`主机地址:端口号/部署路径/${menuName}.html`
    例如：本地开发时部署路径为“/”，则pluginDashboard菜单对应的url为“http://localhost:8081/pluginDashboard.html”


