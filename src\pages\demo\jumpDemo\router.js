import Vue from 'vue';
import Router from 'vue-router';
import Main from './routerPages/Main';
import { fixRouter } from '@/assets/js/utils';

Vue.use(Router);
let router = new Router({
    routes: [
        {
            path: '/',
            name: 'Main',
            component: Main,
            meta: {
                topBar: {
                    title: '缓存测试页面',
                    share: true,
                    cp: true,
                    topBarBg: '#ffec38',
                    textColor: '#ff2328',
                    titleAlign: 'left',
                    back: true,
                    exit: true,
                    refresh: false
                }
            }
        }
    ]
});
fixRouter(router);
export default router;
