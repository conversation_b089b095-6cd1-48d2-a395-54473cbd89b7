import Vue from 'vue';
import Vuex from 'vuex';
Vue.use(Vuex);

export default new Vuex.Store({
    state: {
        btName: '',
        address: '',
        readerIP: {
            'SYD': '************:18000',
            'SR': '**************:6000',
            'ST': 'senter-online.cn:10002',
            'KT': 'www.kaercloud.top:443',
            'DC': 'www.kaercloud.top:443',
            'SR_SE': '1',
            'SYDP': 'http://hodpicdec.com:18080/Inter-Real/photoDecode',
            'SYD_SE': '1',
            'KAER_ACCOUNT': 'WoXingXiao',
            'KAER_PASSWORD': 'WoXingXiao'
        }
    },
    mutations: {
        initBlueTooth(state, { btName, address }) {
            state.btName = btName;
            state.address = address;
        }
    }
});