import WadeMobile from "./wadeMobile";
import <PERSON> from './jcl';
import {safeTrans} from "./util";

var BizMobile = (function(){
    return{
        openIpuApp: function (param, callback, err) { //打开ipu应用
            param = param ? Wade.DataMap(param) : Wade.DataMap();
            param.put("MENU_TYPE", "I");
            storageCallback("openIpuApp", callback);
            execute("openIpuApp", [param.toString()], err);
        }, closeIpuApp:function(result){
            execute("closeIpuApp",[result]);
        },openNativeApp: function (param, err) { //打开原生应用
            param = param ? Wade.DataMap(param) : "";
            execute("openNativeApp", [param.toString()], err);
        }, openNative:function(data,err){
            execute("openNative", [data], err);
        }, openNativeTab:function(param,callback){ // 原生tab
            execute("openNativeTab", [param], callback);
        }, openTopNativeTab:function(param,callback){ // 原生顶部tab
            execute("openTopNativeTab", param, callback);
        },openRemoteURL: function (url, err) {
            execute("openRemoteURL", [url], err);
        }, openPushInUrl: function (url, param, err, success, callback) {
            storageCallback("openPushInUrl", callback);
            param = param ? param : [];
            execute("openPushInUrl", [url, ...param], err, success);
        }, closePushInUrl: function(param) {
            execute("closePushInUrl", [param]);
        }, openH5: function(url, cookies, callback, err) {
            storageCallback("openH5", callback);
            execute("openH5", [url, cookies], err);
        }, closeH5: function(result) {
            execute("closeH5",[result]);
        }, closeAllSubApp: function() {
            execute("closeAllSubApp",[]);
        }, initAppConfig: function (param, err) { //初始化应用
            param = param ? Wade.DatasetList(param) : "";
            execute("initAppConfig", [param.toString()], err);
        }, readIDCard:function(params){//读取身份证
            return new Promise((resolve, reject) => {
                storageCallback("readIDCard",(data) => {
                    resolve(safeTrans(data));
                });
                execute("readIDCard", params , (err) => {
                    reject(safeTrans(err));
                });
            })
        },getAppVersion: function () {//应用版本
            return new Promise((resolve, reject) => {
                storageCallback("getAppVersion", (data) => {
                    resolve(safeTrans(data));
                });
                execute("getAppVersion", [], (err) => {
                    reject(safeTrans(err));
                })
            })
        },writeCard:function(params){//读取身份证
            return new Promise((resolve, reject) => {
                storageCallback("writeCard",(data) => {
                    resolve(safeTrans(data));
                });
                execute("writeCard", params , (err) => {
                    reject(safeTrans(err));
                });
            })
        },readCard:function(params){//读取身份证
            return new Promise((resolve, reject) => {
                storageCallback("readCard",(data) => {
                    resolve(safeTrans(data));
                });
                execute("readCard", params , (err) => {
                    reject(safeTrans(err));
                });
            })
        },deviceList:function(params){//获取设备列表
            params = params ? params : [];
            return new Promise((resolve, reject) => {
                storageCallback("deviceList",(data) => {
                    resolve(safeTrans(data));
                });
                execute("deviceList", params , (err) => {
                    reject(safeTrans(err));
                });
            })
        },btDtList:function(params){// 码上购获取蓝牙信息
            return new Promise((resolve, reject) => {
                storageCallback("btDtList",(data) => {
                    resolve(safeTrans(data));
                });
                execute("btDtList", params , (err) => {
                    reject(safeTrans(err));
                });
            })
        },updateGrayInfo: function (params) {
            params = params ? params : [];
            //开启灰度发布环境
            return new Promise((resolve, reject) => {
                storageCallback("updateGrayInfo", (data) => {
                    resolve(safeTrans(data));
                });
                execute("updateGrayInfo", params, err => {
                    reject(safeTrans(err));
                });
            })
        },faceAuthentication:function(params){//人脸识别
            params = params ? params : [];
            return new Promise((resolve, reject) => {
                storageCallback("faceAuthentication",(data) => {
                    resolve(safeTrans(data));
                });
                execute("faceAuthentication", params , (err) => {
                    reject(safeTrans(err));
                });
            })
        },wxPay:function(params){//微信支付
            return new Promise((resolve, reject) => {
                storageCallback("wxPay",(data) => {
                    resolve(safeTrans(data));
                });
                execute("wxPay", params , (err) => {
                    reject(safeTrans(err));
                });
            })
        },selectMap:function(params){//地图选址
            return new Promise((resolve, reject) => {
                storageCallback("selectMap",(data) => {
                    resolve(safeTrans(data));
                });
                execute("selectMap", params , (err) => {
                    reject(safeTrans(err));
                });
            })
        },zfbPay:function(params){//支付宝
            return new Promise((resolve, reject) => {
                storageCallback("zfbPay",(data) => {
                    resolve(safeTrans(data));
                });
                execute("zfbPay", params , (err) => {
                    reject(safeTrans(err));
                });
            })
        },jShare:function(params){//分享功能
            return new Promise((resolve, reject) => {
                storageCallback("jShare",(data) => {
                    resolve(safeTrans(data));
                });
                execute("jShare", params , (err) => {
                    reject(safeTrans(err));
                });
            })
        },jPush:function(params){// 推送功能
            return new Promise((resolve, reject) => {
                storageCallback("jPush",(data) => {
                    resolve(safeTrans(data));
                });
                execute("jPush", params , (err) => {
                    reject(safeTrans(err));
                });
            })
        },closeCountLog: function(closeCountLogFLag, err) {
            // APM数据采集
            execute("closeCountLog",[closeCountLogFLag], err);
        }, forceUpdateAPP: function (params) {
            // 更新app
            return new Promise((resolve, reject) => {
                storageCallback("forceUpdateAPP", (data) => {
                    resolve(safeTrans(data));
                });
                execute("forceUpdateAPP", params, (err) => {
                    reject(safeTrans(err));
                });
            })
        }, setMobileConfig: function (params) {
            params = params ? params : [];
            // 切换环境插件
            return new Promise((resolve, reject) => {
                storageCallback("setMobileConfig", (data) => {
                    resolve(safeTrans(data));
                });
                execute("setMobileConfig", params, err => {
                    reject(safeTrans(err));
                });
            })
        }, resetMobileConfig: function () {
            // 还原环境插件
            return new Promise((resolve, reject) => {
                storageCallback("resetMobileConfig", (data) => {
                    resolve(safeTrans(data));
                });
                execute("resetMobileConfig", [], err => {
                    reject(safeTrans(err));
                });
            })
        }, setMobileConfigNoRestart: function (params) {
            params = params ? params : [];
            // 切换环境插件（NoRestart）
            return new Promise((resolve, reject) => {
                storageCallback("setMobileConfigNoRestart", (data) => {
                    resolve(safeTrans(data));
                });
                execute("setMobileConfigNoRestart", params, err => {
                    reject(safeTrans(err));
                });
            })
        }, resetMobileConfigNoRestart: function () {
            // 还原环境插件（NoRestart）
            return new Promise((resolve, reject) => {
                storageCallback("resetMobileConfigNoRestart", (data) => {
                    resolve(safeTrans(data));
                });
                execute("resetMobileConfigNoRestart", [], err => {
                    reject(safeTrans(err));
                });
            })
        }, sysLocation: function () {
            // 系统定位（IOS）
            return new Promise((resolve, reject) => {
                storageCallback("sysLocation", (data) => {
                    resolve(safeTrans(data));
                });
                execute("sysLocation", [], err => {
                    reject(safeTrans(err));
                });
            })
        }, clearData: function () {
            // 清理缓存
            return new Promise((resolve, reject) => {
                storageCallback("clearData", (data) => {
                    resolve(safeTrans(data));
                });
                execute("clearData", [], err => {
                    reject(safeTrans(err));
                });
            })
        },
        resetSimCard:function(params){//重置废卡
            return new Promise((resolve, reject) => {
                storageCallback("resetSimCard",(data) => {
                    resolve(safeTrans(data));
                });
                execute("resetSimCard", params , (err) => {
                    reject(safeTrans(err));
                });
            })
        },
        getAuthorization: function (param) { // 获取系统权限
            return new Promise((resolve, reject) => {
                storageCallback("getAuthorization", (data) => {
                    resolve(safeTrans(data));
                });
                execute("getAuthorization", [param], (err) => {
                    reject(safeTrans(err));
                });
            })
        }
    };
})();

// var WadeMobile;
function execute(action, args, error, success) {
    /*循环依赖,懒加载*/
    // if(!WadeMobile){
    //     WadeMobile = require("wadeMobile")
    // }
    return WadeMobile.execute(action, args, error, success)
}
function storageCallback(action,callback,isEscape,isBase64) {
    /*循环依赖,懒加载*/
    // if(!WadeMobile){
    //     WadeMobile = require("wadeMobile")
    // }
    WadeMobile.callback.storageCallback(action,callback,isEscape,isBase64)
}

export default BizMobile;
