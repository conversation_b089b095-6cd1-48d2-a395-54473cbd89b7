import store from "@/pages/easyAcceptance/easyAcceptance/store";

//*姓名脱敏
function name(val) {
    if (!val) {
        return val;
    }
    val = val.replace(/^\s+|\s+$/g, "");
    if (val && val.length == 2) {
        return val.replace(/^(.{1}).*(.{1})$/, "$1*");
    }
    else if (val && val.length > 2) {
        return val.replace(/^(.{1}).*(.{1})$/, "$1**");
        // return val.replace(/^(.{1}).*(.{1})$/, "$1*$2");
    }
    else {
        return val;
    }
}

function nameWithCode(v, dataCode) {
    let staffDataCodes = store.state.staffDataCodes
    if (staffDataCodes.indexOf(dataCode) > -1) {
        return v;
    }
    else {
        return name(v);
    }
}

//*电话脱敏
function phone(val) {
    if (!val) {
        return val;
    }
    return val.replace(/^(.{3}).*(.{4})$/, "$1****$2");
}


function phoneWithCode(v, dataCode) {
    let staffDataCodes = store.state.staffDataCodes
    if (staffDataCodes.indexOf(dataCode) > -1) {
        return v;
    }
    else {
        return phone(v);
    }
}

//*身份证号脱敏
function idCard(val) {
    if (!val) {
        return val;
    }
    return val.replace(/^(.{3}).*(.{4})$/, "$1****$2");
}

function idCardWithCode(v, dataCode) {
    let staffDataCodes = store.state.staffDataCodes
    if (staffDataCodes.indexOf(dataCode) > -1) {
        return v;
    }
    else {
        return idCard(v);
    }
}

//*邮箱脱敏
function email(val) {
    if (!val) {
        return val;
    }
    return val.replace(/^(.{3}).*(.{5})$/, "$1****$2");
}


function emailWithCode(v, dataCode) {
    let staffDataCodes = store.state.staffDataCodes
    if (staffDataCodes.indexOf(dataCode) > -1) {
        return v;
    }
    else {
        return email(v);
    }
}

//*地址脱敏
function address(val) {
    if (!val) {
        return val;
    }
    return val.replace(/^(.{6}).*(.{5})$/, "$1****");
}


function addressWithCode(v, dataCode) {
    let staffDataCodes = store.state.staffDataCodes
    if (staffDataCodes.indexOf(dataCode) > -1) {
        return v;
    }
    else {
        return address(v);
    }
}

export default {
    name,
    nameWithCode,
    phone,
    phoneWithCode,
    idCard,
    idCardWithCode,
    email,
    emailWithCode,
    address,
    addressWithCode
}
