#user  root;
worker_processes  1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
error_log  /usr/local/logs/error.log  info;

#pid        logs/nginx.pid;
events {
    worker_connections  65535;
}
http {
    include       mime.types;
    default_type  application/octet-stream;
    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;

    sendfile        on;
    #tcp_nopush     on;
    #keepalive_timeout  0;
    keepalive_timeout  65;
    client_max_body_size  100m;
    gzip on;
    gzip_buffers 32 4K;
    gzip_comp_level 6;
    gzip_min_length 100;
    gzip_types application/javascript text/css text/xml;
    gzip_disable "MSIE [1-6]\."; #配置禁用gzip条件，支持正则。此处表示ie6及以下不启用gzip（因为ie低版本不支持）
    gzip_vary on;
    #upstream sslfpm {
	#	server **********:8200 weight=10 max_fails=3 fail_timeout=20s;
    #}
    upstream woapp {
        server  **************:8011;
    }
    server {
        listen 8081;
        client_max_body_size  100m;
        server_name localhost;
        location /jlzwtaih5 {
            #autoindex on;
            root /usr/local/view/;
            index woapp.html;
        }
        location /preorderAppIntf {
            access_log           /usr/local/logs/boAdmin_bsds_pro.log;
            proxy_pass           http://woapp;
            proxy_redirect       off;
            proxy_set_header  X-Forwarded-Proto $scheme;
            proxy_set_header  X-Forwarded-Port $server_port;
            proxy_set_header  X-Forwarded-For  $proxy_add_x_forwarded_for;
            proxy_set_header  X-Real-IP  $remote_addr;
            proxy_set_header  Host $http_host;
        }
    }
}
