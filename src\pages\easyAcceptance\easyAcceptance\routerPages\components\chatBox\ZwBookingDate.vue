<template>
    <div class="reservationMsg">
<!--        <div style="margin-left: 4px">预约信息</div>-->
<!--        <van-cell-group style="margin: 2px 5px">-->
<!--            <van-field v-model="contactPhone" label="联系人电话"  @blur="saveContactPhone"  placeholder="请输入联系人电话" />-->
<!--            <van-field v-model="contactName" label="联系人"   @blur="saveContactName"  placeholder="请输入联系人名称" />-->
<!--        </van-cell-group>-->
<!--        <van-cell center title="是否预约" style="margin-left: 5px">-->
<!--            <template #right-icon>-->
<!--                <van-switch v-model="checked" size="24" @change="isBookTime()"  />-->
<!--            </template>-->
<!--        </van-cell>-->
        <van-cell v-show = "checked"  style="margin-left: 5px" title="预约日期" :value="date" @click="show = true" :icon="require('@/assets/images/reservationDate.png')" />
        <van-calendar  v-show = "checked"  v-model="show" @confirm="onConfirm"/>
        <div  class="time-list"  v-show = "checked" >
          <div class="init" :class="item.flag === 1 ? 'bidden' : (item.flag === 0 && radio === (index + 1) ? 'active' : '')" v-for="(item,index) in timeList"

               :key="index" @click="changTime(item,index)">
            {{item.value}}
          </div>
        </div>
      <van-cell-group style="margin: 2px 5px">
        <van-field v-model="remark" label="备注" placeholder="请输入备注" />
      </van-cell-group>
      <div class="submit-container">
        <van-button class="sub" @click="submit()" block>确定</van-button>
      </div>
    </div>
</template>

<script>
import {mapMutations, mapState} from "vuex";

export default {
    name: 'ZwBookingDate',
    props: {
        params: {
            audio: '',
            text: ''
        }
    },
    data() {
        return {
          resp:{
           
          },
          contactPhone:'',
          contactName:'',
          timeData:{
            contactPhone:'',
            stdBookDay:'',
            contactName:'',
            stdBookTime:''
          },
          radio:'',
          date: this.formatDate(new Date()),
          show: false,
          checked: true,
          remark:'',
          timeList: [
            {value:'上午(9:00-12:00)',flag:0},
            {value:'下午(12:00-18:00)',flag:0},
            {value:'晚上(18:00-21:00)',flag:0}
          ]
        }
    },
  
  computed: {
    ...mapState([
      'mqBookFlagData',
      'zwOrderData',
    ])
  },
    mounted() {
      this.isBookTime();
      // console.log(this.zwOrderData.zwGoodData.commodityChooseYWUlList)
      // console.log(this.zwOrderData.zwGoodData.zwList)
     this.date=this.formatDate(new Date());
      this.resp=this.zwOrderData.checkNumberData;
      this.timeData=this.zwOrderData.timeData;
      this.contactName=this.timeData.contactName;
      this.contactPhone=this.timeData.contactPhone;
      // console.log(this.timeData)
     if(!this.isEmpty(this.timeData.stdBookDay)){
       this.date=this.timeData.stdBookDay
     }
     this.queryBook();
     this.onConfirm(this.date);

    },
    methods: {
      ...mapMutations(['setZwOrderData','setMqBookFlagData']),
      isEmpty(value) {
        let flag = false
        if (value == '' || value == 'undefined' || value == undefined || value == null || value == 'null') {
          flag = true
        }
        return flag
      },
      // saveContactPhone(){
      //   console.log(this.contactPhone)
      //   this.zwOrderData.timeData.contactPhone=this.contactPhone;
      //   this.setZwOrderData(this.zwOrderData);
      // },
      // saveContactName(){
      //   console.log(this.timeData.contactName)
      //   this.zwOrderData.timeData.contactName=this.contactName;
      //   this.setZwOrderData(this.zwOrderData);
      // },
      changTime(item,index){
        // console.log(index)
        this.radio=(index+1)
        if(item.flag==1){
          // console.log("item.flag")
          // console.log(item.flag)
          this.$toast('当前时间不可预约，请换一个')
        }
        else{
          this.zwOrderData.timeData.stdBookTime=item.value;
          this.setZwOrderData(this.zwOrderData);
        }
      },
        play() {
            this.$refs.audio.play()
        },
      submit(){
        // if(this.isEmpty(this.contactPhone)){
        //   this.$toast('请填写联系电话')
        //   return;
        // }
        // if(this.isEmpty(this.contactName)){
        //   this.$toast('请填写联系人')
        //   return;
        // }
        
          this.zwOrderData.remark=this.remark;
          this.mqBookFlagData.bookAddrTimeFlag="0";
          this.setZwOrderData(this.zwOrderData);
          this.setMqBookFlagData(this.mqBookFlagData);

          let data = {
          inputType: "1",
          type: '1',
          textInput: "bookTimeSubmit",
          notifyFlag: '',
          taskName:'智能组网甩单'
        }
        this.$emit('newChatApi', data);
      },
        isBookTime(){
            if(this.checked){
                this.zwOrderData.choiceValue="1";
            }
            else {
                this.zwOrderData.choiceValue="0";
            }
            this.mqBookFlagData.bookAddrTimeFlag="1";
            this.setZwOrderData(this.zwOrderData);
            this.setMqBookFlagData(this.mqBookFlagData);

        },
        formatDate(val) {
          var date = new Date(val);
          var year = date.getFullYear();
          var month = Number(date.getMonth()) + 1;
          month = month >= 10 ? month : "0" + month;
          var day = date.getDate();
          day = day >= 10 ? day : "0" + day;
          return year + "-" + month + "-" + day;
        },
      queryBook(){
        let req = {
          siteCd:this.zwOrderData.checkNumberData.showExchCode,
          bookTime:this.formatDate(this.date),
          stdAddrId:this.zwOrderData.checkNumberData.showAddrCode
        }
        
        this.$http.post('/iptvReceive/iptvBookingDate',
           req 
        ).then(res=>{
          this.timeList=[];
          if(res.respCode=='0000'){
            if(res.respData.length>0){
              for(let i=0;i<res.respData.length;i++){
                let c={};
                if(i==0){
                    if(res.respData[i].flag==0){
                        c= {value:'上午(9:00-12:00)',flag:res.respData[i].flag}

                    }else {
                        c= {value:'上午(已约满)',flag:res.respData[i].flag}
                    }
                }
                if(i==1){
                    if(res.respData[i].flag==0){
                        c= {value:'下午(12:00-18:00)',flag:res.respData[i].flag}

                    }else {
                        c= {value:'下午(已约满)',flag:res.respData[i].flag}
                    }
                }
                if(i==2){
                    if(res.respData[i].flag==0){
                        c= {value:'晚上(18:00-21:00)',flag:res.respData[i].flag}
                    }else {
                        c= {value:'晚上(已约满)',flag:res.respData[i].flag}
                    }
                }
                  
                this.timeList.push(c)
              }
              if(!this.isEmpty(this.timeData.choseIndex)){

                let indCache=Number(this.timeData.choseIndex)-1

                this.changTime(this.timeList[indCache],indCache);

              }
            }else{
              this.timeList= [
                {value:'上午(9:00-12:00)',flag:0},
                {value:'下午(12:00-18:00)',flag:0},
                {value:'晚上(18:00-21:00)',flag:0}
              ]
            }
            
            // this.timeList=res.respData.data
          }
          else {
            this.timeList= [
              {value:'上午(9:00-12:00)',flag:0},
              {value:'下午(12:00-18:00)',flag:0},
              {value:'晚上(18:00-21:00)',flag:0}
            ]
          }
        })
      },
      
      
        onConfirm(date) {
            this.show = false;
            this.date = this.formatDate(date);
             this.zwOrderData.timeData.stdBookDay=this.date;
             this.setZwOrderData(this.zwOrderData);
             this.queryBook();
        },
      
    }
};
</script>

<style lang="scss" scoped>
.van-icon-play-circle-o {
    position: absolute;
    display: block;
    font-size: 26px;
    color: #4997ff;
    bottom: 0;
    right: 35px;
}
.time-list{
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  .init{
    background-color: white;
    border: 1.5px solid rgba(203, 203, 203, 1);
    border-radius: 8px;
    padding: 6px 0;
    height:fit-content;
    line-height: 30px;
    font-size: 14px;
    margin-right: 10px;
    margin-top: 10px;
    width: 45%;
    text-align: center;
  }
  .active{
    background: #EDF7FF;
    border: 1px solid rgba(80,148,245,1);
  }
  .bidden{
    background: rgba(245,248,250,1) !important;
    color:rgba(76,80,82,0.3);
  }
  .gren{
    background: #EDF7FF;
    border: 1px solid green;
  }
}
</style>
<style lang="scss">
.reservationMsg {
  .van-cell,.van-field__label,.van-field__control{
    color: #263A5F;
    .van-cell__value{
      color: #263A5F;
    }
  }
}
</style>