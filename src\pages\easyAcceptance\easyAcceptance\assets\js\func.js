/**
 * 公共方法集合
 */
import http from '@/assets/js/axios.js';
import { Dialog } from 'vant';
import { CUST_TYPE } from './constants';

/**
 * 获取数据类型
 * @method getTypeName
 * @param {Object} [param] 值
 * @return 类型
 */
export function getTypeName(param) {
    return Object.prototype.toString.call(param);
}

/**
 * 复制文本
 * @param text 文本
 */
export function copyText(text) {
    // 创建input标签
    let input = document.createElement('input');
    // 将input的值设置为需要复制的内容
    input.value = text;
    // 添加input标签
    document.body.appendChild(input);
    // 选中input标签
    input.select();
    // 执行复制
    document.execCommand('copy');
    // 移除input标签
    document.body.removeChild(input);
}

/**
 * 从订单行列表中找到第一个做了客户认证的客户cardNo
 * @param orderLine {Array} 订单行列表
 * @param key {string} 键值，mPhoneInfo/fixedInfo
 * @param ignoreIndex {Number} 忽略不判断的订单行的下标（用于过滤当前正在判断的订单行）
 * @param searchInstallMode {string|undefined} 查询标识，undefined都查 0新装/携转 1纳入（默认undefined）
 * @returns {string}
 */
export function findCardNo(orderLine, key, ignoreIndex = -1, searchInstallMode = undefined) {
    let result = '';
    for (let index in orderLine) {
        // 忽略下标
        if (ignoreIndex.toString() === index.toString()) continue;
        let item = orderLine[index];
        // 根据查询标识过滤
        if (searchInstallMode !== undefined && searchInstallMode.toString() !== item[key]?.installMode?.toString()) continue;

        let cardNo = item[key]?.custPicInfo?.custPicInfo?.cardNo || '';
        if (cardNo) {
            result = cardNo;
        }
    }
    return result;
}

/**
 * 【客户】获取跳转读证页面时的统一入参
 */
export function getCustAuthDefaultQuery() {
    return {
        custType: CUST_TYPE.CUST, // 客户认证类型
        sceneType: '200001',
        netTypeCode: '50',
        checkTag: '2', //0,1)是否需要进行一证五户校验,0进行
        onePeriOnePersonFlag: '0',
        policyName: '', //判断是否王卡，安徽用
        isAgent: '0', //只有代办人客户认证必传,其他不需要传(1: 代办人, 0:非代办人) ",
        limitFlag: '1', //限制部分场景使用经办人流程(1: 进行代办人流程，0:不进行代办人流)
        showflag: false, // 输证校验在新融合菜单中默认为false不展示
        grayCertFlag: '1' //客户认证灰名单标识
    };
}

/**
 * 【代办人】获取跳转读证页面时的统一入参
 */
export function getAgentAuthDefaultQuery() {
    return {
        custType: CUST_TYPE.AGENT, // 客户认证类型
        sceneType: '200001',
        netTypeCode: '',
        checkTag: '0', //（0,1)是否需要进行一证五户校验, 0进行
        onePeriOnePersonFlag: '0',
        policyName: '', //判断是否王卡，安徽用
        isAgent: '1', //只有代办人客户认证必传,其他不需要传(1: 代办人, 0:非代办人) ",
        agentFlag: '1', //若agentFlag为1时，增加判断代办人是否满18周岁，增加机主证件号与代办人客户证件号比较
        agentUserCardNo: '' //44528-关于针对16岁以下入网增加经办人年龄判断需求 (打开页面时再进行设置)
    };
}

/**
 * 修改背景颜色
 * @param color 颜色值
 */
export function setBgColor(color) {
    document.getElementsByTagName('html')[0].style.backgroundColor = color;
    document.body.style.backgroundColor = color;
    document.body.style.borderBottomColor = color;
}

/**
 * 文字转语音
 * @param txtInfo
 */
export function txtToVoiceInfo(params) {
    return new Promise(resolve => {
        http.post('/ai/txtToVoiceInfo', params).then(res => {
            if(res.respCode === '0000') {
                let audio = 'data:audio/wav;base64,' + res.respData
                resolve(audio)
            } else {
                Dialog.alert({ title: '出错了~', message: res.respMsg});
                resolve('')
            }
        }).catch(() => {
            resolve()
            this.$toast('糟糕！接口出错了，请您重试！');
        });
    })
}

/**
 * 获取(逐字展示)机器人纯文字回复展示完成所需时间(秒数)
 * @method getSentenceTime
 * @param {String} [param] 值
 * @return Int
 */
export function getSentenceTime(sentence) {
    return sentence.length * 120;
}