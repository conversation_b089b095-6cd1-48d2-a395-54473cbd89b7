const { smart } = require('webpack-merge');
const base = require('./webpack.base');
const webpack = require('webpack');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const path = require('path');

const vantVarPath = path.resolve(__dirname, 'src/assets/css/var.less');

//启用sourceMap、监听源码自动编译、禁用代码压缩
module.exports = smart(base, {
    mode: 'development',
    devServer: {
        host: '0.0.0.0',
        contentBase: path.join(__dirname, 'dist'),
        compress: true,
        port: 8081,
        // hot: true,
        proxy: {
            // '/woappjlbksvc/preorderAppIntf': {
            //     target: 'https://wappay.easybss.com',
            //     // pathRewrite: { '^/nx-jjsl-web/touch_sub_back/nx_easy': '' }
            // }
            // '/nx-jjsl-web/touch_sub_back': {
            //     target: 'http://xx.xx.xx.xx:8080/touch_sub_back',
            //     pathRewrite: { '^/nx-jjsl-web/touch_sub_back': '' }
            // }
            '/shbm/api': {
                // target: 'http://xx.xx.xx.xx:8080/shzwt/api',  // 测试环境url
                // target: 'http://xx.xx.xx.xx:8080/shzwt/api',  // 测试环境url
                target: 'http://xx.xx.xx.xx:8080/shbm',  // 灰度后端
                pathRewrite: {"^/shbm/api": ""},
            }
        }
    },
    devtool: 'inline-source-map',
    module: {
        rules: [
            {
                test: /\.css$/,
                use: [
                    {
                        loader: MiniCssExtractPlugin.loader,
                        options: {
                            sourceMap: true
                        }
                    }, {
                        loader: 'css-loader',
                        options: {
                            sourceMap: true
                        }
                    }, {
                        loader: 'postcss-loader',
                        options: {
                            sourceMap: 'inline'
                        }
                    }
                ]
            }, {
                test: /\.s[ac]ss$/i,
                use: [
                    {
                        loader: MiniCssExtractPlugin.loader,
                        options: {
                            sourceMap: true
                        }
                    }, {
                        loader: 'css-loader',
                        options: {
                            sourceMap: true
                        }
                    }, {
                        loader: 'postcss-loader',
                        options: {
                            sourceMap: 'inline'
                        }
                    }, {
                        loader: 'sass-loader',
                        options: {
                            sourceMap: true
                        }
                    }
                ]
            },
            {
                test: /\.less$/,
                use: [
                    {
                        loader: MiniCssExtractPlugin.loader,
                        options: {
                            sourceMap: true
                        }
                    }, {
                        loader: 'css-loader',
                        options: {
                            sourceMap: true
                        }
                    }, {
                        loader: 'postcss-loader',
                        options: {
                            sourceMap: 'inline'
                        }
                    }, {
                        loader: 'less-loader',
                        options: {
                            sourceMap: true,
                            lessOptions: {
                                modifyVars: {
                                    hack: `true; @import "${vantVarPath}";`
                                }
                            }
                        }
                    }
                ]
            }
        ]
    },
    plugins: [
        new webpack.DefinePlugin({
            ENV: JSON.stringify('test')
        })
    ]
});
