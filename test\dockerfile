#FROM harbor.dcos.xixian.unicom.local/jlbaseimage/centosbasenginx:202105120006
#qzkj扫描
#FROM harbor.dcos.xixian.unicom.local/jlbaseimage/centosbasenginx:202312180002forquanzhi
FROM harbor.dcos.xixian.unicom.local/jlbaseimage/centosbasenginx:202405270002forquanzhi
ENV TZ Asia/Shanghai
ENV SCA_PROFILE k8s
ENV PASSJLLOCAL_SCA_PROFILE dev
ENV REDIS_URL needtoconfig
ENV REDIS_PORT 6379
ENV REDIS_PWD needtoconfig
RUN rm -rf /etc/nginx/nginx.conf
RUN mkdir -p /usr/local/logs
RUN chmod 777 /usr/local/logs
ADD test/nginx.conf /etc/nginx/nginx.conf
ADD dist /usr/local/dist
RUN mkdir -p /usr/local/view
RUN mv /usr/local/dist /usr/local/view/jlzwtaih5
ADD test/run.sh /usr/local/run.sh
RUN chmod a+x /usr/local/run.sh
#qzkj扫描1
#ENV QZKJ_INTERFACE eth0
#ENV QZKJ_FTPSERVER 10.170.38.29:211
#ENV QZKJ_FTPUSER ftp
#ENV QZKJ_FTPPASSWORD LfQKae1d
#ENTRYPOINT ["/usr/local/run.sh"]
#ENTRYPOINT ["top", "-b"]
#CMD ["-c"]

