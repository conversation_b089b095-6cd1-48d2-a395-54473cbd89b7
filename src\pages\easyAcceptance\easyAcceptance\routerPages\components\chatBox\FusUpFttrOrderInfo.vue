<template>
  <div class="card-verification" style="background-color: white">
    <div class="subpreview-box" v-for="(item,index1) in showList" :key="index1" @click="item.showAll=!item.showAll">
      <div :style="{background: item.showAll ? '#E5F0FF':'#EDF7FF'}" class="total-title">
        <span class="custom-label-name">{{ item.name }}</span>
        <van-icon :name="item.showAll ? 'arrow-up':'arrow'" />
      </div>
      <div v-for="(item1,index) in item.itemList" :key="index" style="padding: 0 10px;">
        <div v-show="item.showAll" style="display:flex">
          <span class="custom-title flex-shink">{{ item1.text }}</span>
          <span class="custom-title" >{{item1.value}}</span>
        </div>
      </div>
    </div>
    <div class="submit-container">
      <van-button class="sub" @click="preSubmit()" block>提交</van-button>
    </div>
  </div>
</template>

<script>
import WadeMobile from "rk-native-plugin";
import {decrypted} from '@/assets/js/AESUtil.js';

import errorTips from '@/assets/bizComponents/errorTips/errorTips.vue';
import {copyText} from "../../../assets/js/func";
import {mapActions, mapMutations, mapState} from "vuex";
export default {
  name: "FusUpFttrOrderInfo",
  data() {
    return {
      imageList: [
        require('../../../images/arrow.png'),
        require('../../../images/add.png')
      ],
      showList: [
      
      ],
   
    }
  },
  components: {
    errorTips
  },
  computed: {
    ...mapState([
      'chatList',
      'sessionId',
      'staffId',
      'flowStep',
      'num',
      'shbmMsgInfo',
      'jzfkOrderData',
      'activeModuleIndex',
      'loginPhoneNumber',
      'instanceId',
        'fusionUpFttrOrderData'
    ])
  },
  mounted(){
    this.setRespTipArrQry([]);
    this.setBlockShow(false);
    this.checkOrderDetail()
    console.log('tgybyhbhbh')
    console.log(this.fusionUpFttrOrderData.fusionUpFttrGoodData.custList)
    let zwString='';
    let rhGood='';
    let ywGood='';
    let kdGood='';
    if(this.fusionUpFttrOrderData.fusionUpFttrGoodData.custList.length>0){
      for(let i=0;i<this.fusionUpFttrOrderData.fusionUpFttrGoodData.custList.length-1;i++){
        zwString+=this.fusionUpFttrOrderData.fusionUpFttrGoodData.custList[i].commodityName+",";
      }
      zwString+=this.fusionUpFttrOrderData.fusionUpFttrGoodData.custList[this.fusionUpFttrOrderData.fusionUpFttrGoodData.custList.length-1].commodityName;
    }
    if(this.fusionUpFttrOrderData.fusionUpFttrGoodData.commList.length>0){
      for(let i=0;i<this.fusionUpFttrOrderData.fusionUpFttrGoodData.commList.length;i++){
       if("209"==this.fusionUpFttrOrderData.fusionUpFttrGoodData.commList[i].COMM_TYPE){
         rhGood+=this.fusionUpFttrOrderData.fusionUpFttrGoodData.commList[i].COMM_NAME
       }
       else if("60"==this.fusionUpFttrOrderData.fusionUpFttrGoodData.commList[i].COMM_TYPE){
         ywGood+=this.fusionUpFttrOrderData.fusionUpFttrGoodData.commList[i].COMM_NAME
       }
       else if("213"==this.fusionUpFttrOrderData.fusionUpFttrGoodData.commList[i].COMM_TYPE){
         kdGood+=this.fusionUpFttrOrderData.fusionUpFttrGoodData.commList[i].COMM_NAME
       }
      }
    }
    this.showList=[
      {
        name: '客户信息',
        showAll: true,
        itemList:[
          {text:'宽带号码：',value:decrypted(this.fusionUpFttrOrderData.checkNumberData.kdNumberJm)},
          {text:'客户名称：',value:this.fusionUpFttrOrderData.checkNumberData.showCustName}
        ]
      },{
        name: '商品信息',
        showAll: true,
        itemList:[
          {text:'融合商品信息：',value:rhGood},
          {text:'移网商品信息：',value:ywGood},
          {text:'宽带商品信息：',value:kdGood},
        ]
      },{
        name: '装机信息',
        showAll: false,
        itemList:[
          {text:'装机地址：',value:this.fusionUpFttrOrderData.checkNumberData.showInstallAddr}
        ]
      },
      {
        name: '费用信息',
        showAll: true,
        itemList:[
          {text:'商品费用：',value:this.fusionUpFttrOrderData.orderPrice+"元"},
        ]
      }
    ]
  },
  methods: {
    ...mapMutations([
      'setFlowStep',
      'setRobotWorking',
      'setFusionUpFttrOrderData',
        'setRespTipArrQry',
        'setBlockShow'
    ]),
    ...mapActions(['updateChatList']),
    isEmpty(value) {
      let flag = false
      if (value == '' || value == 'undefined' || value == undefined || value == null || value == 'null') {
        flag = true
      }
      return flag
    },

    uniqueByProperty(arr, prop) {
      const uniqueMap = new Map();
      return arr.reduce((acc, current) => {
        const key = current[prop];
        if (!uniqueMap.has(key)) {
          uniqueMap.set(key, true);
          acc.push(current);
        }
        return acc;
      }, []);
    },
    checkOrderDetail(){
   WadeMobile.getSysInfo("PLATFORM").then((info) => {
      this.fusionUpFttrOrderData.platfForm=info;
      this.setFusionUpFttrOrderData(this.fusionUpFttrOrderData);
      console.log("PLATFORM：：" + info);
    }, (err) => {
      console.log("PLATFORM"+"失败：" + err);
    });
   },
    preSubmit(){
      
      if(this.fusionUpFttrOrderData.fusionUpFttrGoodData.custList.length==0){
        this.$toast('请选择定制商品')
        return;
      }
      this.$emit('startLoading', '')
      let zwInfoList={};
      if(this.fusionUpFttrOrderData.fusionUpFttrGoodData.custList.length>0){
        let zw={
          commodityCode:this.fusionUpFttrOrderData.fusionUpFttrGoodData.custList[0].commodityCode,
          commodityName:this.fusionUpFttrOrderData.fusionUpFttrGoodData.custList[0].commodityName,
        }
        zwInfoList=zw;
      }
      let  stdContact=this.fusionUpFttrOrderData.timeData.contactName;
      if(this.isEmpty(stdContact)){
        stdContact=this.fusionUpFttrOrderData.checkNumberData.custName;
      }
      if(stdContact==this.fusionUpFttrOrderData.checkNumberData.showCustName){
        stdContact=this.fusionUpFttrOrderData.checkNumberData.custName;
      }
      let communityAddrInfo={
        exchCode:this.fusionUpFttrOrderData.checkNumberData.innerExchCode,
        addressName:this.fusionUpFttrOrderData.checkNumberData.innerAddr,
      addressCode:this.fusionUpFttrOrderData.checkNumberData.innerAddrCode,
      stdContact:stdContact,
       stdContactPhone:this.fusionUpFttrOrderData.timeData.contactPhone,
        stdBookDay:this.fusionUpFttrOrderData.timeData.stdBookDay,
        
        stdBookTime:this.fusionUpFttrOrderData.timeData.stdBookTime
      }
      let rhRelationMemberMsg={
        userTypeFlag: this.fusionUpFttrOrderData.checkNumberData.userTypeFlag,
        phoneSerialNumber:decrypted(this.fusionUpFttrOrderData.checkNumberData.phoneSerialNumberBtm),
        ywProductName:this.fusionUpFttrOrderData.checkNumberData.ywProductName,
        ywProductId: this.fusionUpFttrOrderData.checkNumberData.ywProductId,
        rhSerialNumber: this.fusionUpFttrOrderData.checkNumberData.rhSerialNumber,
        rhProductId:this.fusionUpFttrOrderData.checkNumberData.rhProductId ,
        rhProductName:this.fusionUpFttrOrderData.checkNumberData.rhProductName ,
        mixTypeCode:this.fusionUpFttrOrderData.checkNumberData.mixTypeCode ,
        kdProductId:this.fusionUpFttrOrderData.checkNumberData.kdProductId ,
        kdProductName:this.fusionUpFttrOrderData.checkNumberData.kdProductName
      }
      if("0"==this.fusionUpFttrOrderData.checkNumberData.userTypeFlag){
        rhRelationMemberMsg={
          userTypeFlag: this.fusionUpFttrOrderData.checkNumberData.userTypeFlag
        }
      }
      let req={
        autoDeduct:this.fusionUpFttrOrderData.autoDeduct,
        changeCommodityInfo:zwInfoList,
        communityAddrInfo:communityAddrInfo,
        serviceNumber:decrypted(this.fusionUpFttrOrderData.checkNumberData.kdNumberJm),
        choiceValue:this.fusionUpFttrOrderData.choiceValue,
        rhRelationMemberMsg:rhRelationMemberMsg,
        orderPrice:this.fusionUpFttrOrderData.orderPrice,
        remark:"备注:"+this.fusionUpFttrOrderData.remark+";预约时间:"+this.fusionUpFttrOrderData.timeData.stdBookDay+" "+this.fusionUpFttrOrderData.timeData.stdBookTime
      }
      
      this.$http.post('/fusionUpFttr/preSubmit',req).then((res)=>{
        this.$emit('endLoading', '')
        if (res.respCode === '0000') {
          this.fusionUpFttrOrderData.orderId=res.respData.orderId;
          this.updateChatList({
            sender: '1',
            type: 'module',
            moduleName: 'TextResponse',
            moduleLevel: 1,
            params: {
              text: '订单提交成功!订单号'+res.respData.orderId
            },
            show: true
          })
          this.setFusionUpFttrOrderData(this.fusionUpFttrOrderData);
          let data = {
            inputType: "1",
            type: '1',
            textInput: "fusionUpFttrOrderDataSubmit",
            notifyFlag: '',
            taskName:'智能融合升套办理FTTR'
          }
          this.$emit('newChatApi', data);
        }
        else{
          this.updateChatList({
            sender: '1',
            type: 'module',
            moduleName: 'TextResponse',
            moduleLevel: 1,
            params: {
              text: res.respMsg
            },
            show: true
          })
        }      })
    }
  }
}
</script>
<style lang="scss">
.card-verification {
  width:100%;
  margin: auto;
  .card-group {
    background-color: white;
    margin: 0px 10px;
    padding:0px 5px;
    .tip {
      .van-cell__value {
        color: black;
        text-align: right;
      }}

    .card-list {
      .van-cell__title {
        font-size: 13px;
        color: #666666;
      }
      .van-cell__value {
        color: black;
        text-align: right;
        font-size: 13px;
      }
      .van-cell {
        color: black !important;
        padding: 0px 16px;
      }
      .van-cell::after {
        color: black !important;
        border-bottom: 0;

      }
    }
    .van-hairline--top-bottom::after, .van-hairline-unset--top-bottom::after {
      border-width: 0 0;
    }
  }
  .card {
    .van-cell__title {
      font-size: 13px;
      color: #000000;
    }
    .checkButton {
      width: 70px;
      height: 30px;
      background: #0081FF;
      border-radius: 4px;
    }
    .checkFont {
      font-size: 13px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 14px;
    }
  }
  .check{
    .van-cell__title {
      font-size: 13px;
      color: #333333;
    }
  }
}
</style>

<style lang="scss" scoped>
.card-verification {
  background-color: rgb(57,159,254);
  .subpreview-box{
    border-radius: 10px;
    margin-bottom: 10px;
    box-shadow: 0 2px 1px 0 #eee;
    .total-title{
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 5px 10px;
      width: 93%;
      border-radius: 10px 10px 0 0;
      margin-bottom: 10px;
      .custom-label-name{
        font-size: 15px;
        line-height: 30px;
        color: #263A5F;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
      }
    }
    
    .custom-title{
      font-family: PingFangSC-Regular;
      font-size: 14px;
      line-height: 26px;
      color: #263A5F;
      padding-left: 14px;
    }
    .flex-shink{
      flex-shrink: 0;
    }
  }
  .desc {
    color: black;
    font-size: 13px;
    line-height: 20px;
  }
  .margin-two {
    bottom: 10px;
    left: 15px;
    right: 15px;
    z-index: 2;
    position: absolute;
  }
  .van-buttons {
    border-radius: 4px;
    height: 44px;
    font-size: 13px;
    color: #FFFFFF;
    background-color: #0081FF;
  }
  .font {
    color:#333333;
  }
  .submit-container{
    .sub{
      border: 2px solid rgba(73,124,246,1);
      border-radius: 5px;
      color: rgba(73,124,246,1);
      margin-top: 20px;
    }
  }
}

</style>