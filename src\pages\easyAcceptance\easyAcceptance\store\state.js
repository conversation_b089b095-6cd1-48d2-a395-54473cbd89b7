export default {
    sessionId: '',
    staffId: '',
    loginInfo: {}, // 鉴权信息
    loginPhoneNumber: '', // 登录手机号
    chatList: [], //对话列表
    numberInfo: {}, // 号码校验信息
    userSelectInfo: {}, // 虚拟用户信息
    tradeId: '', // 业务流水号
    staffDataCodes: '', // 省分权限编码
    remark: '', // 备注
    userAmount: '', // 一证五户数
    certTimes: '', // 一证五次数
    custInfo: {}, // 客户认证数据
    paperlessFlag: false, // 是否完成无纸化签名标识
    savedFeeList: [], // 费用明细
    staffKindCode: '', // 1-自由人员；其他-代理商
    flowStep: 0, // 流程步骤
    busiOrderId: '', // 支付流水号
    activeModuleIndex: 0, // 当前活动模块Index
    needProcessModuleLength: 0, // 需要处理的模块的个数
    commonProduct: {}, // 提交成功的融合定制商品
    iData: { //上次的意图
        fee: "", 
        flow: "", 
        downLoad: "", 
        voice: "" 
    },
    commonChildren: {}, // 提交成功的融合子商品（移网、宽带、IPTV）
    robotWorking: false, // 用来控制机器人正在输出文字或者正在加载内容时，不允许用户输入文字或语音
    instanceId: '123', // 大模型实例ID
    custAuthMockSwitch: false, // 客户认证mock开关
    userInfo: {}, // 三户校验
    secondBroadbandInfo: {}, // 二宽
    recommendedMixProduct: {}, // 缓存热销商品中点击的“融合变更”商品信息
    // 流程引擎相关新增字段
    agentSessionId: "",
    shbmMsgInfo: {},
    needWait: false,
    jzfkOrderData: {
        mainNumberCheckData:{mainNumber:'',brandCode:'',productName:'',custInfo: {}},
        smsCodeCheckData:{},
        goodsSelectData:[],
        numberChooseData:{params:{},numberList:[]},
        custCheckData:{custInfo:{}},
        preSubmitShowData:{locationInfo:{}},
        curTask:{taskId:'',toolCode:'',taskName:''},
        chatList:[]
    },
    formattedDate:'',
    iptvCacheList:{
        iptvChooseCommLists:[]
    },
    showOrderList:false,
    orderInfoLists:[],
    reqDatas:{},
    orderInfoDetail:[],
    iptvOrderData:{
        checkNumberData:{},
        iptvGoodData:{iptvList:[],commodityChooseYWUlList:[]},
        timeData:[],
        orderPrice:0,
        broadNumberByYw:[],
        selectedNumber:''
    },
    yskIptvCacheList:[],
    yskCustCacheList:[],
    yskYwfCacheList:[],  
    yskKdfCacheList:[],
    yskZwCacheList:[],
    wslProductLimitList:[],
    outCallMonetOrderData:{
        singleSwitchParams:{
            commId:'',
            commName:'',
            switchToOutCallFlag:"0",
        },
        checkNumberData:{},
        installAddr:{},
        custWithYwList:[],
        zuWithYwList:[],
        goodData:{zwList:[],commodityChooseYWUlList:[],iptvList:[],custList:[],kdfList:[]},
        timeData:[],
        orderPrice:0,
    },
    zwOrderPrice:0,
    iptvOrderPrice:0,
    kdOrderPrice:0,
    iptvReProductList:[],
    iptvReYwProductList:[],
    zwReProductList:[],
    zwReYwProductList:[],
    zwCacheList:{
        zwChooseCommLists:[]},
    zwOrderData:{
        autoDeduct:'0',
        checkNumberData:{},
        zwGoodData:{zwList:[],commodityChooseYWUlList:[]},
        timeData:[],
        orderPrice:0,
        broadNumberByYw:[],
        selectedNumber:''
    },
    broadUpReProductList:[],
    broadUpOrderData:{
        checkNumberData:{},
        broadUpGoodData:{broadUpList:[],commodityChooseYWUlList:[]},
        timeData:[],
        orderPrice:0,
        broadNumberByYw:[],
        selectedNumber:'',
        remark:'',
        isHandleFlag:"0"
    },
    fusionUpFttrReYwProductList:[],
    fusionUpFttrReProductList:[],
    fusionUpFttrOrderData:{
        autoDeduct:'0',
        checkNumberData:{},
        fusionUpFttrGoodData:{custList:[],commodityChooseYWUlList:[],commList:[]},
        timeData:[],
        orderPrice:0,
        broadNumberByYw:[],
        selectedNumber:'',
        remark:'',
    },
    // 单移网
    singleModeNetworkOrderData: {
        autoDeduct: "0",
        checkNumberData: {},
        fusionUpFttrGoodData: {
            custList: [],
            commodityChooseYWUlList: [],
            commList: [], // 选中套餐
        },
        timeData: [],
        orderPrice: 0,
        broadNumberByYw: [],
        selectedNumber: "",
        remark: "",
    },
    singleModeNetworkYwProductList: [], // 移网商品
    singleModeNetworkProductList: [], // 合约商品
    mqBookFlagData:{bookAddrTimeFlag:"0"},
    chatLoading:true,
    num:0,//
    blockShow:true,
    respTipArrQry:[],
    cacheQryPara:{},
    isFour:4,//号码选择后四位是否为空
    runTimeTaskList: [], // 运行时任务列表
    chatApiParam: {}, // 缓存最新chatapi入参
    curTask: {}, // 缓存当前任务信息
    noDisableModules: ['TextResponse', 'ErrorInfo', 'MenuList', 'Customers', 'AntiFraudRisk', 'SameDocument'] // 不需要加遮罩层的组件
};