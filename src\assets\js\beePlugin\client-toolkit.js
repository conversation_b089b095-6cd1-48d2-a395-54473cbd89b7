import <PERSON> from "./jcl";
var clientTool = {};

clientTool.tool = new function(){
    /*判断是否是DataMap类型*/
    this.isDataMap = function(key){
        return typeof (key) == "object" && (key instanceof Wade.DataMap);
    };
    /*判断是否是Array类型*/
    this.isArray = function(key){
        return typeof (key) == "object" && key.constructor == Array;
    };
    /*提取DatasetList中的json对象*/
    this.parseList = function(list){
        if(!(list instanceof Wade.DatasetList)){
            return list;
        }
        for(var i=0,len=list.items.length;i<len;i++){
            if(list.items[i] instanceof Wade.DataMap){
                list.items[i] = clientTool.tool.parseData(list.items[i]);
            }else if(list.items[i] instanceof Wade.DatasetList){
                list.items[i] = clientTool.tool.parseList(list.items[i]);
            }
        }
        return list.items;
    };
    /*提取DataMap中的json对象*/
    this.parseData = function (param){
        if(!(param instanceof Wade.DataMap)){
            return param;
        }
        for(var key in param.map){
            if(param.map[key] instanceof Wade.DataMap){
                param.map[key] = clientTool.tool.parseData(param.map[key]);
            }else if(param.map[key] instanceof Wade.DatasetList){
                param.map[key] = clientTool.tool.parseList(param.map[key]);
            }
        }
        return param.map;
    };
    this.StrStartWith=function(str,regStr){
        var reg=new RegExp("^"+regStr);
        return reg.test(str);
    };
    this.StrEndWith=function(str,regStr){
        var reg=new RegExp(regStr+"$");
        return reg.test(str);
    };
    this.isDataMapString = function(str){
        if(typeof(str) != "string"){
            return false;
        }
        var trimStr = str.trim();
        return clientTool.tool.StrStartWith(trimStr,"{") && clientTool.tool.StrEndWith(trimStr,"}");
    };
    this.getDataMap = function(key){
        if(clientTool.tool.isDataMap(key)){
            return key;
        }else if(clientTool.tool.isDataMapString(key)){
            return Wade.DataMap(key);
        }else{
            throw "返回数据异常，请尝试重新登陆或联系管理员。(返回的数据为：" + key + ")";
        }
    };
};
export default clientTool;