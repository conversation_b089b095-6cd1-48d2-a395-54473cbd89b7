<template>
    <div>
        <van-button
            @click="getDeviceList"
            type="primary"
        >
            蓝牙连接
        </van-button>
        <van-cell
            title="设备名称"
            :value="btName"
            size="large"
        />
        <van-cell
            title="设备地址"
            :value="address"
            size="large"
        />
    </div>
</template>

<script>
import { mapMutations, mapState } from 'vuex';
import WadeMobile from 'rk-native-plugin';

export default {
    name: 'DeviceList',
    data() {
        return {

        };
    },
    computed: {
        ...mapState(['btName', 'address'])
    },
    methods: {
        ...mapMutations(['initBlueTooth']),
        getDeviceList() {
            WadeMobile.deviceList([]).then((result) => {
                this.initBlueTooth({
                    btName: result.btName,
                    address: result.address
                });
            }).catch((e) => {
                // eslint-disable-next-line no-console
                console.log(e);
            });
        }
    }
};
</script>

<style scoped>

</style>
