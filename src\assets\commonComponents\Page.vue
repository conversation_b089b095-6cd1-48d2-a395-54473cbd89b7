<template>
    <div class="page">
        <!--头部区域-->
        <div style="flex: 0 0 auto;">
            <slot name="header" />
        </div>
        <!--content区域-->
        <div class="page-content" :style="{flex: 1,overflow: enableScroll ? 'scroll' : 'hidden'}">
            <slot />
        </div>
        <!--底部区域-->
        <div class="page-footer" style="flex: 0 0 auto;">
            <slot name="footer" />
        </div>
    </div>
</template>

<script>
export default {
    name: 'Page',
    props: {
        //解决嵌套page组件时，部分手机上内层page的content区域高度异常，导致原本外层不需要滚动，但因内层高度的异常而触发滚动的问题
        //设置enableScroll强制禁用content区域的滚动
        enableScroll: {
            type: Boolean,
            default: true
        }
    }
};
</script>

<style scoped>
    .page {
        height: 100%;
        display: flex;
        flex-direction: column;
    }
</style>