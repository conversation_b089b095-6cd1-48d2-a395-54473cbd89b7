import { Mobile } from 'rk-web-utils';
import WadeMob<PERSON> from 'rk-native-plugin';
// import { checkSubAppLogin } from "./subAppLoginUtil";
// import { beforeCreateUtils } from "./beforeCreateUtils";
import {alertError} from "../bizComponents/funcComponent";
import CryptoJS from "crypto-js";
import http from '@/assets/js/axios.js';
import {Toast} from "vant";

// 登录认证白名单，白名单中的页面将不做登录认证
const LOGIN_AUTH_WHITE_PAGE = [
    'paperlessRepair',
    'takeNumberProtocol',
    'pluginDashboard',
    'comBizDemo',
    'jumpDemo',
    'NodeLink',
    'paperlessSignBySMS'
];

// 异地校验白名单，白名单中的页面将不做异地校验
const SUB_APP_REMOTE_WHITE_PAGE = [
    'paperlessRepair',
    'takeNumberProtocol',
    'pluginDashboard',
    'comBizDemo',
    'jumpDemo',
    'NodeLink',
    'paperlessSignBySMS'
];

export function getCookie(name) {
    if (window.cacheIsolationMode === true) {
        name = Constant.CACHE_PREFIX_ZWT + name;
    }
    let arr,reg=new RegExp("(^| )"+name+"=([^;]*)(;|$)");
    if(arr=document.cookie.match(reg))
        return arr[2];
    else
        return null;
}
let routerLength = 0;
export function fixRouter(router) {
    router.beforeEach(async (to, from, next) => {
        // const sessionId = getQueryString('sessionId')
        // if (sessionId === null && sessionId === '' && sessionId === 'undefined') {
        //     alertError({
        //         title: '登录认证失败',
        //         message: '登录超时，请重新登录。',
        //         confirmButtonText: '关闭子应用'
        //     }).then(() => {
        //         WadeMobile.closeAllSubApp(); // 关闭页面,回到主应用
        //     });
        // } else {
            next()
        // }
    });
    router.afterEach((to) => {
        routerLength ++;
        const options = (to.meta && to.meta.topBar) || '';
        WadeMobile.setH5TopBar(options);
    });
    const back = router.back;
    router.back = function () {
        routerLength--;
        if (routerLength) {
            back.call(router);
        }else {
            Mobile.closeH5();
        }
    }
}

export function getQueryString (name) {
    var url = window.location.href;  
    var cs = url.split('?')[1]; //获取?之后的字符串
    if (cs) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = cs.match(reg);
        if (r != null) return unescape(r[2]); return null;
    } else {
        return null
    }
}

/**
 * 白名单校验，查看当前菜单页面是否在白名单中，若在则返回true
 * 适用于登录认证、异地校验
 * @param whitePageList 白名单列表
 * @returns
 */
export function whitePageValid (whitePageList) {
    const localPathName = location.pathname;
    return whitePageList.findIndex(item => localPathName.includes(`${item}.html`)) > -1;
}
export function getUrlSearchObj() {
    const search = location.search ? location.search.substring(1): '';
    const arr = search.split('&').map(item => {
        const temp = item.split('=');
        const key = temp[0];
        const value = temp[1];
        return {
            key,
            value
        }
    });
    let result = {};
    arr.forEach(item => {
        result[item.key] = item.value;
    });
    return result;
}
export const routeCode = {
    0:"主卡",
    1:"副卡",
    2:"融合主套餐",
    3:"宽带",
    4:"IPTV",
    5:"机顶盒",
    6:"ONU光猫",
    8:"主卡附加",
    9:"定制宽带",
    11:"固定电话",
    13:"资费(服务)成员",
    14:"附加商品",
    15:"CBSS营销活动",
    17:"沃家神眼服务包",
}

export function aes(mode, str, key) {
    if (mode) {
        var aesKey = CryptoJS.enc.Utf8.parse(key);
        var encryptedData  = CryptoJS.AES.encrypt(str, aesKey, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7
        });
        var hexData = encryptedData.ciphertext.toString();
        return hexData;
    }else{
        var aesKey  = CryptoJS.enc.Utf8.parse(key);
        var encryptedHexStr  = CryptoJS.enc.Hex.parse(str);
        var encryptedBase64Str  = CryptoJS.enc.Base64.stringify(encryptedHexStr);
        var decryptedData  = CryptoJS.AES.decrypt(encryptedBase64Str, aesKey, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7
        });
        var text = decryptedData.toString(CryptoJS.enc.Utf8);
        return text;
    }
}

export function base64ToFile(fileData, fileName, fileType) {
    let bstr = atob(fileData);
    let n = bstr.length;
    let u8arr = new Uint8Array(n);
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
    }
    let blob = new Blob([u8arr], {type: fileType});
    blob.lastModifiedDate = new Date();
    blob.name = fileName;
    return blob;
}
export function checkAppVersion(iosAppVersion, androidAppVersion) {
    return new Promise((resolve, reject) => {
        WadeMobile.getAppVersion().then(res => {
            let version = res || '';
            if (WadeMobile.isIOS()) {
                if (Number(version) >= Number(iosAppVersion)) {
                    resolve();
                } else {
                    reject("当前版本较低，请升级至最新版本");
                }
            } else {
                if (Number(version) >= Number(androidAppVersion)) {
                    resolve();
                } else {
                    reject("当前版本较低，请升级至最新版本");
                }
            }
        }).catch(err => {
            reject(err);
        })
    })
}

/**
 * 增加水印
 * @param textColor 字体颜色，十六进制，如0xAEAEAEAE
 * @param textSize 字体大小
 * @param rotation 旋转角度
 * @param alpha 透明度(0-255)
 */
export function showWatermark (textColor = '0x000001', textSize = '16', rotation = '-30', alpha = '13') {
    checkAppVersion(1.72, 1.83).then(() => {
        Mobile.getMemoryCache('STAFF_ID').then((STAFF_ID) => {
            let param = {};
            let date = new Date().Format('yyyy.MM.dd HH:mm:ss');
            let content = `${STAFF_ID} ${date}`;
            param.textColor = textColor;
            param.textSize = textSize;
            param.rotation = rotation;
            param.content = content;
            param.alpha = alpha;
            WadeMobile.showWatermark(param).then(() => {}).catch(() => {});
        }).catch(() => {});
    }).catch(() => {});
}
/**
 * 清除水印
 */
export function hideWatermark () {
    checkAppVersion(1.72, 1.83).then(() => {
        WadeMobile.hideWatermark().then(() => {}).catch(() => {});
    }).catch(() => {});
}

/**
 * 查询全部开关的详情
 * 同时返回 value1 以及 value2
 *
 * @param checkListParam 查询开关列表
 * 例：{ flagName: 'demoSwitch', funName: 'demoSwitch', switchType: '0' }
 * flagName: 接口返回后，取值时的key值，如：res.respData.demoSwitch
 * funName: 开关名
 * switchType: 开关查询类型 SWITCH_TYPE 0-全国 1-省分 2-地市
 *
 * flagName与funName尽量保持一致
 *
 * @type {[{switchType: string, flagName: string, funName: string}]}
 */
export function qryAllSwitchDetail(checkListParam) {
    return new Promise((resolve, reject) => {
        http.post('/paramController/qryAllSwitchDetailBySwitchType', {
            checkListParam: checkListParam
        }).then(res => {
            if (res && res.respCode === '0000') {
                let respData = res.respData || {};
                resolve(respData);
            } else {
                Toast(res.respMsg || '开关详细查询出错');
                reject('failed');
            }
        }).catch(err => {
            Toast(err || '开关详细查询出错了');
            reject(err);
        });
    });
}
/**
 * 查询全部开关以及系统权限
 * 仅value1，以 'true'/'false' 的形式返回，如：res.respData.demoSwitch = 'true'
 *
 * @param checkListParam 查询开关列表
 * 例：{ flagName: 'demoSwitch', dataId: null, funName: 'demoSwitch', switchType: '0' }
 * flagName: 接口返回后，取值时的key值，如：res.respData.demoSwitch
 * dataId: 系统管理权限ID
 * funName: 开关名
 * switchType: 开关查询类型 SWITCH_TYPE 0-全国 1-省分 2-地市
 *
 * flagName与funName尽量保持一致
 *
 * @type {[{switchType: string, dataId: string, flagName: string, funName: string}]}
 */
export function qryAllSwitch(checkListParam) {
    return new Promise((resolve, reject) => {
        http.post('/paramController/qryAllSwitchAndSysRightBySwitchType', {
            checkListParam: checkListParam
        }).then(res => {
            if (res && res.respCode === '0000') {
                let respData = res.respData || {};
                resolve(respData);
            } else {
                Toast(res.respMsg || '开关查询出错');
                reject('failed');
            }
        }).catch(err => {
            Toast(err || '开关查询出错了');
            reject(err);
        });
    });
}
