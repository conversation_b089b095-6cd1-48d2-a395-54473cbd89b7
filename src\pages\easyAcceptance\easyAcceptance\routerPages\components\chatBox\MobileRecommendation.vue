<template>
    <div>
        <div class="part" style="margin-bottom: 5px; display: flex; align-items: end;">
            <div>
                为您推荐<span class="blue"></span>
            </div>
            <div class="title-right" @click="resetNumberList()" style="margin-left: 150px">换一批
                <van-icon name="replay" size="small"/>
            </div>
        </div>
        <div class="part" style="padding-bottom: 20px; border-bottom: 1px solid #eeeeee;">
            <div class="part-content">
                <van-row gutter="10">
                    <van-col span="12" v-for="item in numberList" :key="item.number">
                        <div class="num-card" :class="{'checked': selectedNumber === item.number}"
                             @click="chooseNumber(item)">
                            <span>{{ `${item.number.substr(0, 3)} ${item.number.substr(3, 4)} ${item.number.substr(7, 4)}` }}</span>
                            <span class="nice-num-new" v-if="item.isNice"></span>
                        </div>
                    </van-col>
                </van-row>
            </div>
            <van-button type="primary" block style="margin-top: 15px;" @click="confirm">确定</van-button>
        </div>
        <audio :src="audio" controls ref="audio" style="display: none;" autoplay></audio>
    </div>
</template>


<script>

import {mapActions, mapMutations, mapState} from "vuex";

    export default {
        name: 'MobileRecommendation',
        data() {
            return {
                proChecked: false,
                numberList: [],
                selectedNumber: '',
                audio: ''
            }
        },
        computed: {
            ...mapState([
                'jzfkOrderData',
                'isFour'
            ])
        },

        created() {
            this.$emit('startLoading', '')
            this.eNumberSelect()
            console.log(this.isFour + "==============this.isFour，初始化")
        },
        methods: {
            ...mapMutations([
                'setFlowStep',
                'setRobotWorking',
                'setNum',
                'setJzfkOrderData',
                'setIsFour',
                
            ]),
          ...mapActions(['updateChatList','mobileRecommendationSubmit']),

          resetNumberList() {
                let req = {
                    tailType: '02',
                    goodLevel: '99',
                    numAcount: 16
                }
                this.$http.post('/shjzfkChoiceNumber/choiceNum', req).then(res => {
                    console.log(res.data)
                    this.numberList = []
                    if (res.respData.RESOURCES_INFO.length >= 1) {
                        for (let i = 0; i < res.respData.RESOURCES_INFO.length; i++) {
                            if (i == 0) {
                                this.numberList.push({
                                    number: res.respData.RESOURCES_INFO[i].SERIAL_NUMBER,
                                    isNice: true
                                })
                            } else {
                                this.numberList.push({
                                    number: res.respData.RESOURCES_INFO[i].SERIAL_NUMBER,
                                    isNice: false
                                })
                            }
                        }
                        this.selectedNumber = this.numberList[0].number;
                        this.$emit('endLoading', '')
                    } else {
                        let tip = res.respMsg
                        this.updateChatList({
                            sender: '1',
                            type: 'module',
                            moduleName: 'TextResponse',
                            moduleLevel: 1,
                            params: {text: tip},
                            show: true
                        })
                        this.$emit('endLoading', '')
                    }

                }).catch((error) => {
                    console.error(error)
                    this.updateChatList({
                        sender: '1',
                        type: 'module',
                        moduleName: 'TextResponse',
                        moduleLevel: 1,
                        params: {text: error},
                        show: true
                    })
                    this.$emit('endLoading', '')
                })
            },
            chooseNumber(item) {
                console.log(item)
                this.selectedNumber = item.number
                this.jzfkOrderData.numberChooseData.selectedNumber = this.selectedNumber;
                this.setJzfkOrderData(this.jzfkOrderData);
            },
            eNumberSelect() {
                let req;
                if (Object.keys(this.jzfkOrderData.numberChooseData.params).length === 0 && this.isFour == 4) {
                    req = {
                        endNum: this.jzfkOrderData.mainNumberCheckData.mainNumber.substring(this.jzfkOrderData.mainNumberCheckData.mainNumber.length - 4),
                        tailType: '02',
                        goodLevel: '99',
                        numAcount: 16
                    }
                } else if (Object.keys(this.jzfkOrderData.numberChooseData.params).length === 0 && this.isFour == 3) {
                    req = {
                        tailType: '02',
                        goodLevel: '99',
                        numAcount: 16
                    }
                } else {
                    req = {
                        tailType: '02',
                        goodLevel: '99',
                        numAcount: 16
                    }
                    Object.assign(req, this.jzfkOrderData.numberChooseData.params);
                }

                this.$http.post('/shjzfkChoiceNumber/choiceNum', req).then(res => {
                    console.log(res.data)
                    this.numberList = []

                    if (res.respCode == '0000') {

                        if (res.respData.RESOURCES_INFO.length >= 1) {
                            for (let i = 0; i < res.respData.RESOURCES_INFO.length; i++) {
                                if (i == 0) {
                                    this.numberList.push({
                                        number: res.respData.RESOURCES_INFO[i].SERIAL_NUMBER,
                                        isNice: true
                                    })
                                } else {
                                    this.numberList.push({
                                        number: res.respData.RESOURCES_INFO[i].SERIAL_NUMBER,
                                        isNice: false
                                    })
                                }
                            }
                            this.selectedNumber = this.numberList[0].number;
                            this.jzfkOrderData.numberChooseData.selectedNumber = this.selectedNumber;
                            this.setJzfkOrderData(this.jzfkOrderData);
                            // this.setIsFour(1);
                            this.$emit('endLoading', '')
                        } else {
                            this.setIsFour(3);
                            console.log(this.isFour + "==============为空")
                            this.jzfkOrderData.numberChooseData.params = {}
                            this.setJzfkOrderData(this.jzfkOrderData)
                            let tip = '抱歉，没有找到你想要的号码，自动为你推荐以下号码'
                            this.updateChatList({
                                sender: '1',
                                type: 'module',
                                moduleName: 'TextResponse',
                                moduleLevel: 1,
                                params: {text: tip},
                                show: true
                            })

                            this.updateChatList({
                                sender: '1',
                                type: 'module',
                                moduleName: 'MobileRecommendation',
                                moduleLevel: 1,
                                params: {},
                                show: true
                            })
                        }
                    } else {
                        let tip = res.respMsg
                        this.updateChatList({
                            sender: '1',
                            type: 'module',
                            moduleName: 'TextResponse',
                            moduleLevel: 1,
                            params: {text: tip},
                            show: true
                        })
                        this.$emit('endLoading', '')
                    }

                }).catch((error) => {
                    console.error(error)
                    this.updateChatList({
                        sender: '1',
                        type: 'module',
                        moduleName: 'TextResponse',
                        moduleLevel: 1,
                        params: {text: error},
                        show: true
                    })
                    this.$emit('endLoading', '')
                })
            },
            confirm() {
                if (!this.selectedNumber) {
                    this.$toast('请先选择号码~');
                    return;
                }
                this.jzfkOrderData.numberChooseData.selectedNumber = this.selectedNumber;
                this.setJzfkOrderData(this.jzfkOrderData);
               

                    let data = {
                      inputType: "1",
                      type: '1',
                      textInput: "mobileRecommendationSubmit",
                      notifyFlag: '',
                      taskName:'加装副卡'
                    }
                    this.$emit('newChatApi', data);
            }
        }
    };
</script>

<style lang="scss" scoped>
    /deep/ .van-overlay {
        background-color: rgba(0, 0, 0, .7) !important;
    }

    /deep/ .van-checkbox {
        flex-grow: 0;
        width: 26px;

        .van-icon {
            font-size: 14px;
        }
    }
</style>
