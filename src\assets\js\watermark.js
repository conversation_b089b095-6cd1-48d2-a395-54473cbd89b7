import watermark from "watermark-dom";
// 防抖函数：确保在调整停止后才执行（默认300ms）
function debounce(fn, delay = 300) {
    let timer = null;
    return function(...args) {
        clearTimeout(timer);
        timer = setTimeout(() => fn.apply(this, args), delay);
    };
}
export default{
    init(name){
        const now = new Date();
        // 提取年、月、日
        const year = now.getFullYear();
        const month = now.getMonth() + 1;
        const day = now.getDate();
        const today = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
        const option = {
            watermark_txt: name+" "+today, //水印的内容
            watermark_angle: 45, //水印倾斜度数
            watermark_x_space: 10, //水印x轴间隔
            watermark_y_space: 50, //水印y轴间隔
            watermark_width: 130, //水印宽度
            watermark_height: 80, //水印长度,
        };
         // 使用防抖包装水印加载方法（300ms延迟可调整）
        this.loadMark = debounce(() => watermark.load(option), 300);
        // 添加窗口大小变化监听
        window.addEventListener('resize', this.loadMark);
        // 初始加载水印
        this.loadMark();
    },
    beforeDestory() {
        window.removeEventListener('resize', this.loadMark);
    },
}