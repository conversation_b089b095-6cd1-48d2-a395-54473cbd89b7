import '@/assets/js/globalConfig.js';
import Vue from 'vue';
import Van<PERSON> from 'vant';
import RUX<PERSON> from 'rux-ui-v';
import 'rux-ui-v/dist/rux-ui-v.css';
import App from './App.vue';
import router from './router';
import comComp from '@/assets/commonComponents';
import axios from '@/assets/js/axios.js';

Vue.use(Vant);
Vue.use(RUXUI);
Vue.use(comComp);
Vue.prototype.$http = axios;

new Vue({
    el: '#app',
    router,
    render(h) {
        return  h(App);
    }
});
