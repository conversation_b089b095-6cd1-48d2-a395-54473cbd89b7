import { Mobile } from 'rk-web-utils';
import WadeMobile from 'rk-native-plugin';
import {alertError} from "@/assets/bizComponents/funcComponent";
import http from '@/assets/js/axios.js';
import {Toast} from "vant";
import store from '../../store'
import { getQueryString,getCookie } from '@/assets/js/utils'

let routerLength = 0;
export function fixRouter(router) {
    router.beforeEach(async (to, from, next) => {
        let sessionId ;
        // if (Mobile.isApp()) {
        //     sessionId = getCookie('sessionid');
        // } else {
        //     sessionId = sessionStorage.getItem('SESSION_ID');
        // }
        // if (sessionId === null || sessionId === '' || sessionId === 'undefined') {
        //     alertError({
        //         title: '登录认证失败',
        //         message: '登录超时，请重新登录。',
        //         confirmButtonText: '关闭子应用'
        //     }).then(() => {
        //         WadeMobile.closeAllSubApp(); // 关闭页面,回到主应用
        //     });
        // } else {
            store.commit('setSessionId', sessionId)
            next()
        // }
    });
    router.afterEach((to) => {
        routerLength ++;
        const options = (to.meta && to.meta.topBar) || '';
        WadeMobile.setH5TopBar(options);
    });
    const back = router.back;
    router.back = function () {
        routerLength--;
        if (routerLength) {
            back.call(router);
        }else {
            Mobile.closeH5();
        }
    }
}