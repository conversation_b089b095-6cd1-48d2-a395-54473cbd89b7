export default {
    getStaffId: state => state.staffId,
    getTradeId: state => state.tradeId,
    getJzfkOrderData: state => state.jzfkOrderData,
    getShbmMsgInfo: state => state.shbmMsgInfo,
    getCurTask:state=>state.curTask,
    getChatList:state=>state.chatList,
    getIptvOrderData: state => state.iptvOrderData,
    getIptvCacheList:state =>state.iptvCacheList,
    getZwOrderData: state => state.zwOrderData,
    getZwCacheList:state =>state.zwCacheList,
    getMqBookFlagData:state =>state.mqBookFlagData,
    getFormatedDate:state =>state.formattedDate,
    getOutCallMonetOrderData:state =>state.outCallMonetOrderData,
    getReqDatas:state =>state.reqDatas,
    getFusionUpFttrOrderData:state =>state.fusionUpFttrOrderData,
    getBroadUpOrderData:state =>state.broadUpOrderData,
    getFusionUpFttrReProductList:state =>state.fusionUpFttrReProductList,
    // 单移网
    getSingleModeNetworkOrderData: (state) => state.singleModeNetworkOrderData,


    getIptvReProductList: state =>  state.iptvReProductList,
    getIptvReYwProductList: state =>  state.iptvReYwProductList
    // /**
    //  * 获取开关，仅返回 true/false
    //  */
    // getSwitch: state => keyCode => {
    //     return state.switchParam[keyCode];
    // },
    // /**
    //  * 获取开关详细，返回对象 { value1: '', value2: '' }
    //  */
    // getSwitchDetail: state => keyCode => {
    //     return state.switchDetailParam[keyCode] || {};
    // },
    // /**
    //  * 获取 staffInfo
    //  */
    // getStaffInfo: state => {
    //     return state.staffInfo;
    // },
    // /**
    //  * 获取 provinceCode
    //  */
    // getProvinceCode: state => {
    //     return state.provinceCode;
    // },
    // /**
    //  * 终端串号是否必填
    //  * @returns {boolean}
    //  */
    // isRequiredTerminal: state => {
    //     return state.contractPurchaseCode === 'HBZX003';
    // },
    // /**
    //  * 获取 preOrderState 值
    //  * @param state
    //  */
    // getPreOrderState: state => {
    //     const networkresInfo = state.preOrderInfo.networkresInfo || {};
    //     return Object.keys(networkresInfo).length > 0 && networkresInfo.ADDRESS_DETAIL && state.provinceCode === '11';
    // },
    // /**
    //  * 电子券
    //  * @returns {string}
    //  */
    // dzjNbr_Discnt: state => {
    //     //判空
    //     if (!state.goods.CHILD_INFO) return '';
    //     let _goods = state.goods.CHILD_INFO.filter(item => { return item.COMM_ID == state.fenqiInfo.commId; });
    //     let fenqiInfo = _goods[0];

    //     //判空
    //     if (!fenqiInfo || !fenqiInfo.COMM_PARAMS) return '';
    //     let COMM_PARAM = fenqiInfo.COMM_PARAMS.filter(item => {
    //         return item.ATTR_CODE == 'dzjNbr';
    //     });
    //     if (COMM_PARAM.length > 0) {
    //         return COMM_PARAM[0].ATTR_VALUE || '';
    //     }
    //     return '';
    // },
    // //判空
    // onWhichOrderLine: state => {
    //     if (!state.goods.CHILD_INFO) return '';
    //     let _goods = state.goods.CHILD_INFO.filter(item => { return item.COMM_ID == state.fenqiInfo.commId; });
    //     let fenqiInfo = _goods[0], onWhichOrderLine = '';
    //     if (!fenqiInfo || (fenqiInfo.ROLE_ID !== 0 && !fenqiInfo.ROLE_ID)) return '';
    //     switch (fenqiInfo.ROLE_ID) {
    //     case 0:
    //     case '0':
    //         onWhichOrderLine = '0';
    //         break;
    //     case 3:
    //     case '3':
    //         onWhichOrderLine = '1';
    //         break;
    //     default:
    //         onWhichOrderLine = '';
    //         break;
    //     }
    //     return onWhichOrderLine;
    // },
    // orderLineFlag: state => {
    //     let orderLines = [
    //         'mobileOrderLine',
    //         'otherMobileOrderLine',
    //         'mainOrderLine',
    //         'broadOrderLine',
    //         'fixNumberOrderLine',
    //         'iptvOrderLine',
    //         'netvOrderLine'
    //     ];
    //     let isFlag = [];
    //     orderLines.forEach(orderLine=>{
    //         state[orderLine].forEach(comm => {
    //             if (comm.mPhoneInfo && Object.keys(comm.mPhoneInfo).length > 0) {
    //                 const mode = comm.mPhoneInfo.installMode || '0' ;
    //                 const port = comm.mPhoneInfo.isPort || '0' ;
    //                 isFlag.push(mode + port);
    //             }
    //             if (comm.fixedInfo && Object.keys(comm.fixedInfo).length > 0) {
    //                 const mode = comm.fixedInfo.installMode || '0' ;
    //                 const port = comm.fixedInfo.isPort || '0' ;
    //                 isFlag.push(mode + port);
    //             }
    //             if (comm.commInfoList && comm.commInfoList.length > 0) {
    //                 comm.commInfoList.forEach(commInfo=>{
    //                     isFlag.push(commInfo.commId);
    //                 });
    //             }
    //         });
    //     });
    //     return isFlag.join('');
    // },
    // // 校验是否是fttr商品
    // // isContainWj 是否过滤智家业务
    // checkFttr: state => isContainWj=>{
    //     let broadOrderLine = state.broadOrderLine;
    //     let isFttrBJ = false;
    //     if (broadOrderLine && broadOrderLine.length > 0) {
    //         let commInfoList = broadOrderLine[0].commInfoList.filter(item=>{ return !['3', '4', '5', '6'].includes(item.roleId);});
    //         let wjChildCommIds = broadOrderLine[0].wjChildCommIds;
    //         // 过滤沃家商品
    //         if (!isContainWj && wjChildCommIds != '' && wjChildCommIds != undefined && wjChildCommIds != null) {
    //             commInfoList = commInfoList.filter(item=>{return !wjChildCommIds.includes(item.commId);});
    //         }
    //         _.forEach(commInfoList, commInfo=>{
    //             if (commInfo.commParams && commInfo.commParams.length > 0) {
    //                 _.forEach(commInfo.commParams, params=>{
    //                     if (params.attrCode == 'FTTR_BJ' || params.ATTR_CODE == 'FTTR_BJ') {
    //                         isFttrBJ = true;
    //                     }
    //                 });
    //             }
    //         });
    //     }
    //     return isFttrBJ;
    // }
};