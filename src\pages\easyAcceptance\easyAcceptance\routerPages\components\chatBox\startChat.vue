<template>
  <div class="chat-box">
    <img class="img-title" src="@/assets/images/startChatTitle.png" />
    <div class="hello-title">
      <div> <span class="hello">您好，</span> <span class="hello2">我是智能受理小助手</span></div>
      <div class="hello3">
        具备自然语言理解和业务受理能力，可直接描述您需要办理的业务，试试这样说：我的号码是XXX，需要办理宽带。
      </div>
      <div class="hello4">目前支持的业务范围：</div>
    </div>

    <!-- <div class="tip-box" style="width: 40%" @click="startOpenDupliCard()">
      <span>1、加装副卡</span>
      <img src="@/assets/images/arrow-right.png" />
    </div>
    <div class="tip-box" @click="startOpenIptv()">
      <span>2、超清业务(IPTV、宽视界)智能甩单</span>
      <img src="@/assets/images/arrow-right.png" />
    </div>

    <div class="tip-box" @click="startOpenZwZsd()">
      <span>3、组网业务(WiFi6、FTTR)智能甩单</span>
      <img src="@/assets/images/arrow-right.png" />
    </div>
    <div class="tip-box" @click="startOpenYsk()">
      <span>4、移宽融合业务(老移+新宽)智能甩单</span>
      <img src="@/assets/images/arrow-right.png" />
    </div>
    <div class="tip-box" style="width: 73%" @click="startOpenFusUpFttr()">
      <span>5、融合升套办理FTTR智能甩单</span>
      <img src="@/assets/images/arrow-right.png" />
    </div>
    <div class="tip-box" style="width: 55%" @click="startOpenBroadUp()">
      <span>6、宽带提速智能甩单</span>
      <img src="@/assets/images/arrow-right.png" />
    </div>
    <div class="tip-box" style="width: 46%" @click="startOpenQry()">
      <span>7、智能订单查询</span>
      <img src="@/assets/images/arrow-right.png" />
    </div> -->

    <div class="chat-item" style="margin-top: 5px;">
      <span class="chat-title">新发展业务</span>
      <div class="chat-list">
        <div class="list-item bck-img-1"  @click="startRongHe()">
          <span>融合新装</span>
        </div>
        <div class="list-item bck-img-2"  @click="startYw()">
          <span>移网新装</span>
        </div>
        <div class="list-item bck-img-3"  @click="fuKa()">
          <span>副卡新装</span>
        </div>
      </div>
    </div>
    <div class="chat-item chat-stock-new">
      <span class="chat-title">存量业务</span>
      <div class="chat-list">
        <div class="list-item bck-img-4" @click="startOpenFusUpFttr()">
          <span>融合升套</span>
        </div>
        <div class="list-item bck-img-5" @click="SingleMobileNetwork()">
          <span>单移升套</span>
        </div>
        <div class="list-item bck-img-6" @click="startOpenYsk()">
          <span>老移+新宽</span>
        </div>
      </div>
    </div>
    <div class="chat-item chat-stock-new">
      <span class="chat-title">提值业务</span>
      <div class="chat-list">
        <div class="list-item bck-img-7" @click="startOpenIptv()">
          <span>联通超清</span>
        </div>
        <div class="list-item bck-img-8" @click="startOpenZwZsd()">
          <span>联通组网</span>
        </div>
        <div class="list-item bck-img-9" @click="startOpenBroadUp()">
          <span>宽带提速</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapMutations, mapState } from "vuex";
import WadeMobile from "rk-native-plugin";
import {Wade} from "rk-web-utils";
export default {
  name: "startChat",
  props: {
    params: {
      text: "",
    },
  },
  data() {
    return {
      timer: "",
    };
  },
  computed: {
    ...mapState(["curTask"]),
  },
  methods: {
    ...mapMutations(["updateChatList", "setNeedWait","setBlockShow","setRespTipArrQry"]),
    ...mapActions(["custOrderQuery", "showHotWords"]),
    startOpenDupliCard() {
      if (!this.curTask.taskId) {
        let data = {
          inputType: "1",
          type: "1",
          textInput: "加装副卡",
          notifyFlag: "",
          async: true,
        };
        this.$emit("newChatApi", data);
        this.updateChatList({
          sender: "1",
          type: "module",
          moduleName: "TextResponse",
          moduleLevel: 1,
          params: {
            text: "您正在办理加装副卡业务",
          },
          show: true,
        });
        this.updateChatList({
          sender: "1",
          type: "module",
          moduleName: "TextResponse",
          moduleLevel: 1,
          params: {
            text: "请提前检查是否携带读卡器设备、白卡，以及客户是否携带证件，主卡是否可以正常接收短信。<br/>如确认无误，请输入主卡号码。",
          },
          show: true,
        });
        this.setNeedWait(true);
      } else {
        let data = {
          inputType: "1",
          type: "1",
          textInput: "加装副卡",
          notifyFlag: "",
        };
        this.$emit("newChatApi", data);
      }
    },
    startOpenFusUpFttr() {
      if (!this.curTask.taskId) {
        let data = {
          inputType: "1",
          type: "1",
          textInput: "智能融合FTTR升套",
          notifyFlag: "",
          async: true,
        };
        this.$emit("newChatApi", data);
        this.updateChatList({
          sender: "1",
          type: "module",
          moduleName: "TextResponse",
          moduleLevel: 1,
          params: {
            text: "您正在办理融合升套FTTR智能甩单",
          },
          show: true,
        });
        this.updateChatList({
          sender: "1",
          type: "module",
          moduleName: "TextResponse",
          moduleLevel: 1,
          params: {
            text: "请输入移网号码或固网号码",
          },
          show: true,
        });
        this.setNeedWait(true);
      } else {
        let data = {
          inputType: "1",
          type: "1",
          textInput: "智能融合FTTR升套",
          notifyFlag: "",
        };
        this.$emit("newChatApi", data);
      }
    },

    startOpenIptv() {
      if (!this.curTask.taskId) {
        let data = {
          inputType: "1",
          type: "1",
          textInput: "智能超清甩单",
          notifyFlag: "",
          async: true,
        };
        this.$emit("newChatApi", data);
        this.updateChatList({
          sender: "1",
          type: "module",
          moduleName: "TextResponse",
          moduleLevel: 1,
          params: {
            text: "您正在办理超清智能甩单业务",
          },
          show: true,
        });
        this.updateChatList({
          sender: "1",
          type: "module",
          moduleName: "TextResponse",
          moduleLevel: 1,
          params: {
            text: "请输入宽带号码或移网号码",
          },
          show: true,
        });
        this.setNeedWait(true);
      } else {
        let data = {
          inputType: "1",
          type: "1",
          textInput: "智能超清甩单",
          notifyFlag: "",
        };
        this.$emit("newChatApi", data);
      }
    },
    startOpenYsk() {
      if (!this.curTask.taskId) {
        let data = {
          inputType: "1",
          type: "1",
          textInput: "智能移送宽甩单",
          notifyFlag: "",
          async: true,
        };
        this.$emit("newChatApi", data);
        this.updateChatList({
          sender: "1",
          type: "module",
          moduleName: "TextResponse",
          moduleLevel: 1,
          params: {
            text: "您正在办理移宽融合智能甩单业务",
          },
          show: true,
        });
        this.updateChatList({
          sender: "1",
          type: "module",
          moduleName: "TextResponse",
          moduleLevel: 1,
          params: {
            text: "请输入移网号码",
          },
          show: true,
        });
        this.setNeedWait(true);
      } else {
        let data = {
          inputType: "1",
          type: "1",
          textInput: "智能移送宽甩单",
          notifyFlag: "",
        };
        this.$emit("newChatApi", data);
      }
    },
    startOpenBroadUp() {
      if (!this.curTask.taskId) {
        let data = {
          inputType: "1",
          type: "1",
          textInput: "宽带提速智能甩单",
          notifyFlag: "",
          async: true,
        };
        this.$emit("newChatApi", data);
        this.updateChatList({
          sender: "1",
          type: "module",
          moduleName: "TextResponse",
          moduleLevel: 1,
          params: {
            text: "您正在办理宽带提速智能甩单业务",
          },
          show: true,
        });
        this.updateChatList({
          sender: "1",
          type: "module",
          moduleName: "TextResponse",
          moduleLevel: 1,
          params: {
            text: "请输入您的移网号码或宽带号码",
          },
          show: true,
        });
        this.setNeedWait(true);
      } else {
        let data = {
          inputType: "1",
          type: "1",
          textInput: "宽带提速智能甩单",
          notifyFlag: "",
        };
        this.$emit("newChatApi", data);
      }
    },
    SingleMobileNetwork() {

      if (!this.curTask.taskId) {
        let data = {
          inputType: "1",
          type: "1",
          textInput: "单移网升套智能甩单",
          notifyFlag: "",
          async: true,
        };
        this.$emit("newChatApi", data);
        this.updateChatList({
          sender: "1",
          type: "module",
          moduleName: "TextResponse",
          moduleLevel: 1,
          params: {
            text: "您正在办理单移网升套智能甩单业务",
          },
          show: true,
        });
        this.updateChatList({
          sender: "1",
          type: "module",
          moduleName: "TextResponse",
          moduleLevel: 1,
          params: {
            text: "请输入移网号码",
          },
          show: true,
        });
        this.setNeedWait(true);
      } else {
        let data = {
          inputType: "1",
          type: "1",
          textInput: "单移网升套智能甩单",
          notifyFlag: "",
        };
        this.$emit("newChatApi", data);
      }
    },
    startOpenZwZsd() {
      if (!this.curTask.taskId) {
        let data = {
          inputType: "1",
          type: "1",
          textInput: "智能组网甩单",
          notifyFlag: "",
          async: true,
        };
        this.$emit("newChatApi", data);
        this.updateChatList({
          sender: "1",
          type: "module",
          moduleName: "TextResponse",
          moduleLevel: 1,
          params: {
            text: "您正在办理组网智能甩单业务",
          },
          show: true,
        });
        this.updateChatList({
          sender: "1",
          type: "module",
          moduleName: "TextResponse",
          moduleLevel: 1,
          params: {
            text: "请输入宽带号码或移网号码",
          },
          show: true,
        });
        this.setNeedWait(true);
      } else {
        let data = {
          inputType: "1",
          type: "1",
          textInput: "智能组网甩单",
          notifyFlag: "",
        };
        this.$emit("newChatApi", data);
      }
    },
    async startOpenQry() {
      if (!this.curTask.taskId) {
        let data = {
          inputType: "1",
          type: "1",
          textInput: "智能订单查询",
          notifyFlag: "",
          async: true,
        };
        this.$emit("newChatApi", data);
        let frontFuncRespData = await this.custOrderQuery({});
        console.log(frontFuncRespData);
        this.setNeedWait(true);
        if (frontFuncRespData["flag"] == "1") {
          this.updateChatList({
            sender: "1",
            type: "module",
            moduleName: "TextResponse",
            moduleLevel: 1,
            params: {
              text: "已为您找到最近的订单，您也可以说：查询XX月XX日的订单",
            },
            show: true,
          });
          this.updateChatList({
            sender: "1",
            type: "module",
            moduleName: "OrderConfirmBox",
            moduleLevel: 1,
            params: {},
            show: true,
          });
          this.timer = setTimeout(() => {
            this.setNeedWait(false);
            this.$emit("newChatApi", {
              inputType: "0",
              type: "1",
              textInput: '{"flag":"1"}',
              notifyFlag: "1",
              notNeedOutput: true,
              taskName: "智能订单查询",
            });
          }, 6000);
        } else {
          this.updateChatList({
            sender: "1",
            type: "module",
            moduleName: "TextResponse",
            moduleLevel: 1,
            params: {
              text: "没查到您的订单哦,请说一些别的条件，小助手帮您查询。",
            },
            show: true,
          });
          // this.$emit('newChatApi', {
          //   inputType: "0",
          //   type: '1',
          //   textInput: "{\"flag\": \"2\", \"respMsg\": \"查询结果为空\"}",
          //   notifyFlag: '1',
          //   notNeedOutput:true
          // })
          await this.showHotWords({});
          this.setNeedWait(false);
          this.$emit("newChatApi", {
            inputType: "0",
            type: "1",
            textInput: '{"flag":"1"}',
            notifyFlag: "1",
            notNeedOutput: true,
            taskName: "智能订单查询",
          });
        }
      } else {
        let data = {
          inputType: "1",
          type: "1",
          textInput: "智能订单查询",
          notifyFlag: "",
        };
        this.$emit("newChatApi", data);
      }
    },
    startRongHe(){
      this.updateChatList({
        sender: "1",
        type: "module",
        moduleName: "TextResponse",
        moduleLevel: 1,
        params: {
          text: "即将跳转“商品受理-融合”，请问是否继续？",
        },
        show: true,
      });
      let hotWordsPage=[
        {
          codeDesc: "确定",
          codeValue: "submitGoPageRH"
        },
        {
          codeDesc: "取消",
          codeValue: "refuseGoPage"
        }
      ];
      this.setBlockShow(true);
      this.setRespTipArrQry(hotWordsPage);
      // WadeMobile.openH5('https://wxxapp.chinaunicom.cn:10070/touch_sub_front/openAccountForMix.html?intlAccptFlag=1', null, (result) => {
      //   console.log(result);
      // });
    },
    startYw(){
      this.updateChatList({
        sender: "1",
        type: "module",
        moduleName: "TextResponse",
        moduleLevel: 1,
        params: {
          text: "即将跳转“商品受理-移网”，请问是否继续？",
        },
        show: true,
      });
      let hotWordsPage=[
        {
          codeDesc: "确定",
          codeValue: "submitGoPageYW"
        },
        {
          codeDesc: "取消",
          codeValue: "refuseGoPage"
        }
      ];
      this.setBlockShow(true);
      this.setRespTipArrQry(hotWordsPage);
      // WadeMobile.openH5('https://wxxapp.chinaunicom.cn:10070/touch_sub_front/newBusinessMobile.html?intlAccptFlag=1', null, (result) => {
      //   console.log(result);
      // });
    },
    fuKa(){
      this.updateChatList({
        sender: "1",
        type: "module",
        moduleName: "TextResponse",
        moduleLevel: 1,
        params: {
          text: "即将跳转“加装副卡”，请问是否继续？",
        },
        show: true,
      });
      let hotWordsPage=[
        {
          codeDesc: "确定",
          codeValue: "submitGoPageFK"
        },
        {
          codeDesc: "取消",
          codeValue: "refuseGoPage"
        }
      ];
      this.setBlockShow(true);
      this.setRespTipArrQry(hotWordsPage);
      // WadeMobile.openIpuApp(new Wade.DataMap({
      //   APP_ID: "300038",
      //   MENU_PAGE_ACTION: "addOtherCard",
      //   MENU_WELCOME_PAGE: "welcome/welcome.htm",
      //   EXT_PARAM: JSON.stringify({
      //     intlAccptFlag:"1"
      //   })
      // }));
    }
  },
};
</script>

<style scoped lang="scss">
.chat-box {
  position: relative;
  // background: linear-gradient(to bottom right, #d3e8ff, #d7e1ff);
  //background-image: url('@/assets/images/startChatBackground.png');
  //background-size: cover;
  //background-repeat: no-repeat;
  .img-title {
    position: absolute;
    right: -10px;
    top: -40px;
    width: 96px;
  }
  .hello-title {
    margin-left: 10px;
    font-weight: 600;
    .hello {
      display: inline;
      font-size: 23px;
      margin-top: 5px;
    }
    .hello2 {
      margin-left: -6px;
      display: inline;
      margin-top: 12px;
      font-size: 18px;
      line-height: 19px;
    }
    .hello3 {
      position: relative;
      z-index: 10;
      width: 95%;
      margin-top: 13px;
      font-size: 14px;
      font-weight: 500;
    }
    .hello4{
      font-weight: 500;
    }
  }

  .tip-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 6px;
    padding: 6px;
    margin-top: 20px;
    margin-bottom: 5px;
    width: 93%;

    img {
      width: 18px;
    }
  }
  .chat-item {
    width: 327px;
    background: rgba(255, 255, 255, 0.7);
    padding: 0 7px;
    background: url("@/assets/images/home/<USER>") no-repeat;
    background-size: 100% 100%;
    overflow: visible;
    height: 105px;
    margin: 0 0 -2px -2px;
    .chat-title {
      display: inline-block;
      position: relative;
      font-size: 14px;
      font-weight: 500;
      margin: 5px 0 3px 11px;
    }
    .chat-list {
      display: flex;
      justify-content: space-evenly;
      .list-item {
        width: 96px;
        height: 60px;
        display: flex;
        align-items: center;
        span {
          font-size: 12px;
          font-weight: 400;
          margin-left: 8px;
        }
      }
      .bck-img-1 {
        background: url("@/assets/images/home/<USER>") no-repeat;
        background-size: contain;
      }
      .bck-img-2 {
        background: url("@/assets/images/home/<USER>") no-repeat;
        background-size: contain;
      }
      .bck-img-3 {
        background: url("@/assets/images/home/<USER>") no-repeat;
        background-size: contain;
      }
      .bck-img-4 {
        background: url("@/assets/images/home/<USER>") no-repeat;
        background-size: contain;
      }
      .bck-img-5 {
        background: url("@/assets/images/home/<USER>") no-repeat;
        background-size: contain;
      }
      .bck-img-6 {
        background: url("@/assets/images/home/<USER>") no-repeat;
        background-size: contain;
      }
      .bck-img-7 {
        background: url("@/assets/images/home/<USER>") no-repeat;
        background-size: contain;
      }
      .bck-img-8 {
        background: url("@/assets/images/home/<USER>") no-repeat;
        background-size: contain;
      }
      .bck-img-9 {
        background: url("@/assets/images/home/<USER>") no-repeat;
        background-size: contain;
      }
    }
  }
  .chat-stock-new {
    background: url("@/assets/images/home/<USER>") no-repeat;
    background-size: 100% 100%;
  }
}
</style>
