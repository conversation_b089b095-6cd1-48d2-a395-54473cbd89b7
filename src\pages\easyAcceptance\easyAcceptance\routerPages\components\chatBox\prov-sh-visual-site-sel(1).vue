<template>
    <div class="prov-sh-visual-site-sel">
        <!--        <van-nav-bar title="装机地址查询" class="navbarClass"/>-->
        <div style="height: 700px">
            <div class="villageSearch">
                <van-row type="flex" align="center">
                    <van-col span="18">
                        <van-field
                            v-model="villageName"
                            placeholder="请输入小区别名"
                            label="小区别名："
                            style="display: inline-flex;align-items: center;"
                            @input="clearVillageNameInput"
                        />
                    </van-col>
                    <van-col span="6">
                        <van-button v-if="villageSelTag" class="submitButton" @click="selectVillageNameList" size="small">搜索</van-button>
                        <van-button v-else style="color: #0081ff;" @click="clearAreaResult" size="small">清空</van-button>
                    </van-col>
                </van-row>
            </div>
            <div  v-if="showVillageNameListTag">
                <van-radio-group>
                    <van-cell-group inset>
                        <van-cell
                            v-for="(item,index) in villageNameList"
                            :title="item.chinaName"
                            :value="item.gradeName"
                            :key="index"
                            clickable
                            title-style="color:#0081ff;text-align:center"
                            @click="selectedVillage(item)">
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>
            </div>
            <div class="villageSearch">
                <van-row type="flex" align="center">
                    <van-col span="18">
                        <van-field
                            v-model="villageDetail"
                            placeholder="请输入关键字过滤"
                            label="小区地址："
                            style="display: inline-flex;align-items: center;"
                            type="textarea"
                            rows="1"
                            autosize
                            @input="selectVillageDetailList"
                        />
                    </van-col>
                    <van-col span="6">
                        <van-button  style="color: #0081ff;" @click="clearDetailResult" size="small">清空</van-button>
                    </van-col>
                </van-row>
            </div>
            <div  v-if="showVillageDetailListTag" >
                <van-radio-group >
                    <van-cell-group inset>
                        <van-cell
                            v-for="(item,index) in villageDetailList"
                            :title="item"
                            :key="index"
                            clickable
                            title-style="color:#0081ff;text-align: center"
                            @click="selectedDetail(item)">
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>
            </div>
            <div class="villageSearch">
                <van-row type="flex" align="center">
                    <van-col span="18">
                        <van-field
                            v-model="villageFloor"
                            placeholder="请输入关键字过滤"
                            label="楼栋："
                            style="display: inline-flex;align-items: center;"
                            @input="selectVillageFloorList"
                        />
                    </van-col>
                    <van-col span="6">
                        <van-button style="color: #0081ff;" @click="clearFloorResult" size="small">清空</van-button>
                    </van-col>
                </van-row>
            </div>
            <div  v-if="showVillageFloorListTag" >
                <van-radio-group >
                    <van-cell-group inset>
                        <van-cell
                            v-for="(item,index) in villageFloorList"
                            :title="item"
                            :key="index"
                            title-style="color:#0081ff;text-align: center"
                            clickable
                            @click="selectedFloor(item)">
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>
            </div>
            <div class="villageSearch">
                <van-row type="flex" align="center">
                    <van-col span="18">
                        <van-field
                            v-model="villageRoom"
                            placeholder="请输入关键字过滤"
                            label="楼层："
                            style="display: inline-flex;align-items: center;"
                            @input="selectVillageRoomList"
                        />
                    </van-col>
                    <van-col span="6">
                        <van-button   style="color: #0081ff;" @click="clearRoomResult" size="small">清空</van-button>
                    </van-col>
                </van-row>
            </div>
            <div  v-if="showVillageRoomListTag">
                <van-radio-group>
                    <van-cell-group inset>
                        <van-cell
                            v-for="(item,index) in villageRoomList"
                            :title="item.ROOM_NO"
                            :key="index"
                            clickable
                            title-style="color:#0081ff;text-align: center"
                            @click="selectedRoom(item)">
                        </van-cell>
                    </van-cell-group>
                </van-radio-group>
            </div>
            <div class="villageSearch">
                <van-row type="flex" align="center">
                    <van-col span="24">
                        <van-field
                            v-model="finalAddressText"
                            :disabled="finalAddressTextTag"
                            placeholder="请先选择"
                            label="装机地址："
                            style="display: inline-flex;align-items: center;"
                            type="textarea"
                            rows="1"
                            @blur="checkAddressInputText"
                            autosize
                        />
                    </van-col>

                </van-row>
            </div>
            <div
                class="navbarClass footer" >
                <van-button   block  round class="submitButton" @click="chooseSelAreaAddress">确定</van-button>

            </div>

        </div>
    </div>
</template>

<script>
import { ComponentUtil } from 'rk-web-utils';
/**
     * 组件
     */
export default {
    name: 'prov-sh-visual-site-sel',
    props: {
        textValue: {
            type: String,
            detail: ''
        }
    },
    data() {
        return {
            villageSelTag: true,
            villageAddressList: [],
            villageAddressDetailList: [],
            villageAddressFloorList: [],
            villageAddressRoomList: [],
            isCityTag: true,
            showVillageNameListTag: false,
            showVillageDetailListTag: false,
            showVillageFloorListTag: false,
            showVillageRoomListTag: false,
            villageName: '',
            villageNameCode: '',
            villageDetail: '',
            villageFloor: '',
            villageRoom: '',
            villageNameList: [],
            villageDetailList: [],
            villageFloorList: [],
            villageRoomList: [],
            finalVillageAddressObj: {},
            villageSegimId: '', //小区标准地址id
            floorSegimId: '', //楼栋标准地址id
            chooseSegimId: '',
            finalAddressTextTag: true,
            finalAddressText: '',
            finalAddressTextDefault: ''
        };
    },
    computed: {
        rkComponentId() {
            return '31010100009999010000';
        },
        rkComponentName() {
            return '上海-装机地址可视化查询组件';
        }
    },
    mounted() {
        ComponentUtil.addRKComponentId(this.rkComponentId, this.rkComponentName);
        // this.$emit('textCallback', {
        //     text1: '1',
        //     text2: '2'
        // });
    },
    methods: {
        clearVillageNameInput() {
            if (this.villageName.length == 0 && this.villageAddressList.length > 0) {
                this.clearAreaResult();
            }
        },
        selectVillageNameList() {

            if (this.villageName.length == 0) {
                this.showVillageNameListTag = false;
                this.villageSelTag = true;
                this.$dialog.alert({
                    title: '提示：',
                    message: '请输入小区别名!',
                    confirmButtonColor: '#0081ff'
                }).then(() => {
                    // on close
                });
                return;
            } else {
                this.$toast.loading({
                    message: '加载中...',
                    forbidClick: true,
                    duration: 60000
                });
                this.$http.post('/touch_api_service/transferBack/transferApi',  {
                    'intfName': '31plus1_convergentPlatforms_getStationInfo',
                    'UNI_BSS_BODY': {
                        'GET_STATION_INFO_REQ': {
                            'keyword': this.villageName
                        }
                    }
                }).then(res => {
                    if (res.respCode == '0000' && res.respData.UNI_BSS_BODY.GET_STATION_INFO_RSP.code == 0 && res.respData.UNI_BSS_BODY.GET_STATION_INFO_RSP.data.length > 0) {
                        this.$toast.clear();
                        this.villageSelTag = false;
                        this.showVillageNameListTag = true;
                        this.villageAddressList = res.respData.UNI_BSS_BODY.GET_STATION_INFO_RSP.data;
                        this.villageNameList = this.villageAddressList;
                    } else {
                        this.$toast.clear();
                        this.$dialog.alert({
                            title: '提示：',
                            message: '别名查询失败!请重新查询!',
                            confirmButtonColor: '#0081ff'
                        }).then(() => {
                            // on close
                        });                    }
                }).catch(e => {
                    this.$toast.clear();
                    this.$dialog.alert({
                        title: '提示：',
                        message: '别名查询失败!请重新查询!' + e.respMsg,
                        confirmButtonColor: '#0081ff'
                    }).then(() => {
                    });
                });
            }
        },
        selectVillageDetailList() {
            this.showVillageDetailListTag = true;
            const cityArr = [];
            this.villageAddressDetailList.forEach(item => {
                if ((item).indexOf(this.villageDetail) >= 0) {
                    cityArr.push(item);
                }
            });
            this.villageDetailList = cityArr;
        },
        selectVillageFloorList() {
            this.showVillageFloorListTag = true;
            var cityArr = [];
            if (this.isCityTag && this.villageFloor.length > 0) {
                cityArr = [ '未查到' ];
            } else {
                cityArr = [];
            }
            this.villageAddressFloorList.forEach(item => {
                if ((item).indexOf(this.villageFloor) >= 0) {
                    cityArr.push(item);
                }
            });
            this.villageFloorList = cityArr;
        },
        selectVillageRoomList() {
            this.showVillageRoomListTag = true;
            var cityArr = [ {
                'SEGM_ID': '0',
                'ROOM_NO': '未查到'
            }];
            if (this.isCityTag && this.villageRoom.length > 0) {
                cityArr = [ {
                    'SEGM_ID': '0',
                    'ROOM_NO': '未查到'
                } ];
            } else {
                cityArr = [];
            }
            this.villageAddressRoomList.forEach(item => {
                if ((item.ROOM_NO).indexOf(this.villageRoom) >= 0) {
                    cityArr.push(item);
                }
            });
            this.villageRoomList = cityArr;
        },
        selectedVillage(item) {
            // document.documentElement.scrollTop = 0;
            this.villageName = item.chinaName;
            this.showVillageNameListTag = false;
            this.villageNameCode = item.stationNo;
            if (item.gradeName == '农村') {
                this.isCityTag = false;
            } else {
                this.isCityTag = true;

            }
            this.$toast.loading({
                message: '加载中...',
                forbidClick: true,
                duration: 60000
            });
            this.$http.post('/touch_api_service/transferBack/transferApi',
                {
                    'intfName': '31plus1_convergentPlatforms_listCellAddress',
                    'UNI_BSS_BODY': {
                        'LIST_CELL_ADDRESS_REQ': {
                            'stationCode': this.villageNameCode
                        }
                    }
                }
            ).then(res => {
                if (res.respCode == '0000') {
                    if (res.respData.UNI_BSS_BODY.LIST_CELL_ADDRESS_RSP.code == 0 && !this.isEmpty(res.respData.UNI_BSS_BODY.LIST_CELL_ADDRESS_RSP.data.data.listCell) && res.respData.UNI_BSS_BODY.LIST_CELL_ADDRESS_RSP.data.data.listCell.length > 0) {
                        this.$toast.clear();
                        this.villageAddressDetailList = res.respData.UNI_BSS_BODY.LIST_CELL_ADDRESS_RSP.data.data.listCell;
                        this.villageDetailList = this.villageAddressDetailList;
                        this.selectVillageDetailList();
                    } else {
                        this.$toast.clear();
                        this.$dialog.alert({
                            title: '提示：',
                            message: '小区地址查询失败!请换个地址重试!',
                            confirmButtonColor: '#0081ff'
                        }).then(() => {
                            // on close
                        });
                    }

                } else {
                    this.$toast.clear();
                    this.$dialog.alert({
                        title: '提示：',
                        message: '小区地址查询失败!' + res.respMsg,
                        confirmButtonColor: '#0081ff'
                    }).then(() => {
                        // on close
                    });                    }
            }).catch(e => {
                this.$toast.clear();
                this.$dialog.alert({
                    title: '提示：',
                    message: '小区地址查询失败!请重新查询!' + e.respMsg,
                    confirmButtonColor: '#0081ff'
                }).then(() => {
                });
            });
            // this.selectVillageDetailList();
        },
        selectedDetail(item) {
            this.villageDetail = item ;
            this.finalAddressText = this.villageDetail;
            this.finalAddressTextDefault = this.villageDetail;
            this.showVillageDetailListTag = false;
            this.$toast.loading({
                message: '加载中...',
                forbidClick: true,
                duration: 60000
            });
            this.$http.post('/touch_api_service/transferBack/transferApi',
                {
                    'intfName': '31plus1_convergentPlatforms_listCellAddress',
                    'UNI_BSS_BODY': {
                        'LIST_CELL_ADDRESS_REQ': {
                            'stationCode': this.villageNameCode,
                            'addressName': this.villageDetail
                        }
                    }
                }
            ).then(res => {
                if (res.respCode == '0000') {
                    if (res.respData.UNI_BSS_BODY.LIST_CELL_ADDRESS_RSP.code == 0 && !this.isEmpty(res.respData.UNI_BSS_BODY.LIST_CELL_ADDRESS_RSP.data.data.listBlockNo) && res.respData.UNI_BSS_BODY.LIST_CELL_ADDRESS_RSP.data.data.listBlockNo.length > 0) {
                        this.$toast.clear();
                        this.villageAddressFloorList = res.respData.UNI_BSS_BODY.LIST_CELL_ADDRESS_RSP.data.data.listBlockNo;
                        this.villageFloorList = this.villageAddressFloorList;
                        this.villageSegimId = res.respData.UNI_BSS_BODY.LIST_CELL_ADDRESS_RSP.data.data.segmId;//小区标准地址id
                        this.selectVillageFloorList();
                    } else {
                        this.$toast.clear();
                        this.$dialog.alert({
                            title: '提示：',
                            message: '小区楼栋查询失败!请换个地址重试!',
                            confirmButtonColor: '#0081ff'
                        }).then(() => {
                            // on close
                        });
                    }

                } else {
                    this.$toast.clear();
                    this.$dialog.alert({
                        title: '提示：',
                        message: '小区楼栋查询失败!请重新查询!' + res.respMsg,
                        confirmButtonColor: '#0081ff'
                    }).then(() => {
                        // on close
                    });                    }
            }).catch(e => {
                this.$toast.clear();
                this.$dialog.alert({
                    title: '提示：',
                    message: '小区楼栋查询失败!请重新查询!' + e.respMsg,
                    confirmButtonColor: '#0081ff'
                }).then(() => {
                });
            });
        },
        selectedFloor(item) {
            this.villageFloor = item;
            if (this.villageFloor == '未查到') {
                this.$dialog.alert({
                    title: '提示：',
                    message: '请在下方装机地址处补全！',
                    confirmButtonColor: '#0081ff'
                }).then(() => {
                    this.finalAddressTextTag = false;
                });
                this.showVillageFloorListTag = false;
                this.chooseSegimId = this.villageSegimId;
                return;
            }
            this.finalAddressText = this.villageDetail + this.villageFloor;
            this.finalAddressTextDefault = this.villageDetail + this.villageFloor;
            this.showVillageFloorListTag = false;
            this.finalAddressTextTag = true;
            this.$toast.loading({
                message: '加载中...',
                forbidClick: true,
                duration: 60000
            });
            this.$http.post('/touch_api_service/transferBack/transferApi',
                {
                    'intfName': '31plus1_convergentPlatforms_listCellAddress',
                    'UNI_BSS_BODY': {
                        'LIST_CELL_ADDRESS_REQ': {
                            'stationCode': this.villageNameCode,
                            'roomNo': '',
                            'blockNo': this.villageFloor
                        }
                    }
                }
            ).then(res => {
                if (res.respCode == '0000') {
                    if (res.respData.UNI_BSS_BODY.LIST_CELL_ADDRESS_RSP.code == 0 && !this.isEmpty(res.respData.UNI_BSS_BODY.LIST_CELL_ADDRESS_RSP.data.data)) {
                        this.$toast.clear();
                        let detailText = this.villageDetail;
                        let floorText = this.villageFloor;
                        this.villageAddressRoomList = res.respData.UNI_BSS_BODY.LIST_CELL_ADDRESS_RSP.data.data[detailText][floorText];
                        this.villageRoomList = this.villageAddressRoomList;
                        this.floorSegimId = res.respData.UNI_BSS_BODY.LIST_CELL_ADDRESS_RSP.data.data.segmId;//小区标准地址id
                        this.selectVillageRoomList();
                    } else {
                        this.$toast.clear();
                        this.$dialog.alert({
                            title: '提示：',
                            message: '小区楼层查询失败!请换个地址重试!',
                            confirmButtonColor: '#0081ff'
                        }).then(() => {
                            // on close
                        });
                    }

                } else {
                    this.$toast.clear();
                    this.$dialog.alert({
                        title: '提示：',
                        message: '小区楼层查询失败!请重新查询!' + res.respMsg,
                        confirmButtonColor: '#0081ff'
                    }).then(() => {
                        // on close
                    });                    }
            }).catch(e => {
                this.$toast.clear();
                this.$dialog.alert({
                    title: '提示：',
                    message: '小区楼层查询失败!请重新查询!' + e.respMsg,
                    confirmButtonColor: '#0081ff'
                }).then(() => {
                });
            });
        },
        selectedRoom(item) {
            if (item.SEGM_ID == '0') {
                this.$dialog.alert({
                    title: '提示：',
                    message: '请在下方装机地址处补全！',
                    confirmButtonColor: '#0081ff'
                }).then(() => {
                    this.finalAddressTextTag = false;
                });
                this.showVillageRoomListTag = false;
                this.chooseSegimId = this.floorSegimId;
                return;
            }
            this.villageRoom = item.ROOM_NO;
            this.chooseSegimId = item.SEGM_ID;
            this.finalAddressText = this.villageDetail + this.villageFloor + this.villageRoom;
            this.finalAddressTextDefault = this.villageDetail + this.villageFloor + this.villageRoom;
            this.showVillageRoomListTag = false;
            this.finalAddressTextTag = true;
        },
        clearAreaResult() {
            this.villageSelTag = true;
            this.showVillageNameListTag = false;
            this.showVillageDetailListTag = false;
            this.showVillageFloorListTag = false;
            this.showVillageRoomListTag = false;
            this.villageName = '';
            this.villageDetail = '';
            this.villageFloor = '';
            this.villageRoom = '';
            this.villageNameList = [];
            this.villageDetailList = [];
            this.villageAddressDetailList = [];
            this.villageFloorList = [];
            this.villageAddressFloorList = [];
            this.villageRoomList = [];
            this.villageAddressRoomList = [];
            this.finalVillageAddressObj = {};
            this.finalAddressText = '';
            this.finalAddressTextDefault = '';
            this.chooseSegimId = '';
            this.finalAddressTextTag = true;
            this.isCityTag = true;
        },
        clearDetailResult() {
            this.showVillageDetailListTag = false;
            this.showVillageFloorListTag = false;
            this.showVillageRoomListTag = false;
            this.villageDetail = '';
            this.villageFloor = '';
            this.villageRoom = '';
            this.villageDetailList = [];
            this.villageFloorList = [];
            this.villageRoomList = [];
            this.finalVillageAddressObj = {};
            this.finalAddressText = '';
            this.finalAddressTextDefault = '';
            this.finalAddressTextTag = true;
        },
        clearFloorResult() {
            this.showVillageFloorListTag = false;
            this.showVillageRoomListTag = false;
            this.villageFloor = '';
            this.villageRoom = '';
            this.villageFloorList = [];
            this.villageRoomList = [];
            this.finalVillageAddressObj = {};
            this.finalAddressText = this.villageDetail;
            this.finalAddressTextDefault = this.villageDetail;
            this.finalAddressTextTag = true;
        },
        clearRoomResult() {
            this.showVillageRoomListTag = false;
            this.finalAddressTextTag = true;
            this.villageRoom = '';
            this.villageRoomList = [];
            this.finalVillageAddressObj = {};
            this.finalAddressText = this.villageDetail + this.villageFloor;
            this.finalAddressTextDefault = this.villageDetail + this.villageFloor;
        },
        isEmpty(value) {
            let flag = false;
            if (value == '' || value == 'undefined' || value == undefined || value == null || value == 'null') {
                flag = true;
            }
            return flag;
        },
        checkAddressInputText() {
            if (this.finalAddressText.indexOf(this.finalAddressTextDefault) == -1) {
                this.$dialog.alert({
                    title: '提示：',
                    message: '手输装机地址应包含"' + this.finalAddressTextDefault + '"，请重新输入!',
                    confirmButtonColor: '#0081ff'
                }).then(() => {
                    this.finalAddressText = this.finalAddressTextDefault;
                });
            }
        },
        chooseSelAreaAddress() {
            if (this.isEmpty(this.chooseSegimId)) {
                this.$dialog.alert({
                    title: '提示：',
                    message: '请先选择楼层房间号!如果没有所需地址请选择“未查到"！',
                    confirmButtonColor: '#0081ff'
                }).then(() => {
                    // on close
                });
                return;
            }
            this.finalVillageAddressObj = {
                addressCode: this.chooseSegimId,
                addressDetail: this.finalAddressText
            };
            let addressInfo = {};
            addressInfo = this.finalVillageAddressObj;
            this.$emit('setInstalledAddress', addressInfo);
        }

    }

};
</script>

<style scoped lang="scss">
    // 最外层div
    .prov-sh-visual-site-sel {
        html body{overflow:hidden};
        background-color: #f7f7f7;
        display: flex;
        flex-direction: column; // 设置成上下排列方式
        height: 100vh;
        header,  main,  footer {
            height: 80px;
            line-height: 80px;
            width: 100%;
        }
        // 使用
        footer {
            margin-top: auto;
            height: 60px;
            line-height: 60px;
        }
        .navbarClass{margin-left: 5%;margin-right: 5%;}
        .villageSearch{  margin-left: 5%;margin-right: 5%;margin-bottom: 3%; background-color: white;}
        .submitButton{background-color: #0081ff;color: white;}
    }
</style>

