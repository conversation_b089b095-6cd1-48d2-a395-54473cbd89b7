import '@/assets/js/globalConfig.js';
import Vue from 'vue';
import Vant from 'vant';
import App from './App.vue';
import router from './router';
import store from './store/index';
import comComp from '@/assets/commonComponents';
import axios from '@/assets/js/axios.js';

Vue.use(Vant);
Vue.use(comComp);
Vue.prototype.$http = axios;

new Vue({
    el: '#app',
    router,
    store,
    render(h) {
        return  h(App);
    }
});