<template>
    <div>
        <!-- <div class="header">
            <span style="margin-top:5px; margin-left:5px"><van-icon name="arrow-left" size="20px" @click="goBack" /></span>
            <span class="headerInfo">人工审核信息收集</span>
        </div> -->
        <div class="pic-panel" style="margin-left: 10px;" v-if="showFaceImage">
            <div class="pic-panel-item" id="faceImg">
                <h3 class="pic-title central-audit-title">
                    实人认证检测照片
                </h3>
                <div class="pic-box">
                    <img
                        id="face-img-btn"
                        class="pic"
                        src="../../../images/face.png"
                        @click="faceClick"
                        ref="imagesUrl1"
                    >
                </div>
            </div>
            <div class="pic-panel-item" id="customerImg" v-if="showCustomeImg">
                <h3 class="pic-title central-audit-title">
                    现场照片
                </h3>
                <div class="pic-box">
                    <img
                        id="customer-img-btn"
                        class="pic"
                        src="../../../images/02.png"
                        @click="customerClick"
                        ref="imagesUrl2"
                    >
                </div>
            </div>
        </div>
        <div class="random-panel">
            <h3 class="central-audit-title">
                四位随机数
            </h3>
            <div class="random-content">
                <div class="random-number-wrapper">
                    <ul class="number-list">
                        <li class="number-box" v-for="(item,index) in numList" :key="index">
                            <i class="iconfont icon--6 number">{{ item }}</i>
                        </li>
                    </ul>
                </div>
                <div class="random-btn" id="random-btn" style="margin-left: 5px;">
                    <van-icon name="replay" color="#EEEE00" @click="generateCheckCode" />
                </div>
            </div>
        </div>
        <div style="margin-top: 10px;">
            <van-button type="info" class="videoButten" @click="videoButtonRecord">
                录制视频
            </van-button>
            <div class="vodeoBox">
                <video
                    poster="../../../images/zhanweitu.png"
                    width="100%"
                    height="100%"
                    ref="myVideo"
                    controls
                >
                    <source :src="videoSrcUrl2" type="video/mp4">
                </video>
            </div>
        </div>
        <van-sticky :offset-top="'90vh'" style="left: 14px; width: 100%;">
            <van-button
                type="info"
                style="width: 320px;"
                @click="subMitton"
            >
                确定
            </van-button>
        </van-sticky>
    </div>
</template>

<script>
import { Mobile, Common } from 'rk-web-utils';
import WadeMobile from 'rk-native-plugin';
// import { EncryptUtil } from 'rk-web-utils';
import util from '@/assets/bizComponents/customerAuthentication/CustomerComponents/customerUtil.js';
import axios from 'axios';
import { base64ToFile } from '@/assets/js/utils';
export default {
    data() {
        return {
            newImage: '',
            cardInfo: {},
            control: false,
            videoSourceUrl: '',
            controls: false,
            videoSrcUrl: '',
            videoSrcUrl2: '',
            videoSrc: '',
            watermark: '',
            imageUrl: require('../../../images/face.png'),
            imageUrl2: require('../../../images/02.png'),
            picList: [],
            showFaceImage: true,
            showCustomeImg: true,
            showPlay: true,
            code: '6666',
            staffInfo: {}, // 员工信息
            // cardInfo: Wade.DataMap(), // 身份证信息
            transId: '', // 流水号
            sceneType: '', // 业务办理场景
            // var imgStatus= ''; // 图片识别状态

            // 图片
            realFaceImg: '',
            customerImg: '',
            // 随机数
            // domList = $('.number'),
            numList: [6, 6, 6, 6],
            // 视频
            videoPath: '',
            recordedVideo: '',
            facePicFlag: null,
            custPicFlag: null,
            originImg: '',
            originImgBest: '',
            img: '',
            imgBest: '',
            videoTime: {
                startTime: '0:00',
                endTime: '0:00'
            },
            videoRecordedVideo: '',
            PROVINCE_CODE: '',
            provinceCode: '',
            ossSwitch: false, //是否使用oss上传开关
            portraitVideoCheckSwitch: false //视频校验开关
        };
    },
    mounted() {
        this.getSessionData();
    },
    methods: {
        //开关查询方法
        getSwitch() {
            this.$http.post('/paramController/getSwitch', {
                keyCode: 'oss_switch'
            }).then(res => {
                this.ossSwitch = res.respData === '1';
            }).catch(() => {});
            this.$http.post('/paramController/getSwitch', {
                keyCode: 'portraitVideoCheckSwitch',
                province: this.staffInfo.PROVINCE_CODE
            }).then(res => {
                this.portraitVideoCheckSwitch = res.respData === '1';
            }).catch(() => {});
        },
        getSessionData() {
            let isApp = Mobile.isApp();
            if (isApp) {
                Mobile.getMemoryCache('staffInfo').then(result => {
                    this.staffInfo =  JSON.parse(result);
                    this.getSwitch();
                });
            } else {
                let staffInfoData = JSON.parse(sessionStorage.getItem('staffInfo')); 
                this.staffInfo = staffInfoData;
            }
            let watermark = '仅限办理联通业务使用' + '|' + this.getNowFormatDate() + '|' + this.staffInfo.STAFF_ID;
            let watermarkSplit = watermark.split('|');
            this.realFaceImg = this.$route.query.realFaceImg;
            this.customerImg = this.$route.query.customerImg;
            this.cardInfo = this.$route.query.cardInfo;
            this.transId = this.$route.query.transId;
            this.sceneType = this.$route.query.sceneType,
            this.facePicFlag = this.$route.query.facePicFlag;
            this.custPicFlag = this.$route.query.custPicFlag;
            if (this.facePicFlag == 'false') {
                if (this.realFaceImg.length) {
                    this.realFaceImg = /^data:image/.test(this.realFaceImg) ? this.realFaceImg : 'data:image/jpg;base64,' + this.realFaceImg;
                    let livingImage = new Image();
                    livingImage.src = this.realFaceImg;
                    livingImage.onload = (()=>{
                        this.realFaceImg = this.extractedWithText(livingImage, watermarkSplit, 1);
                        this.$refs.imagesUrl1.src = this.realFaceImg;
                    });
                        
                }
            } else {
                this.showFaceImage = false;
            
            }
            if (this.custPicFlag == 'false') {
                if (this.customerImg.length) {
                    this.customerImg = /^data:image/.test(this.customerImg) ? this.customerImg : 'data:image/jpg;base64,' + this.customerImg;
                    let livingImage2 = new Image();
                    livingImage2.src = this.customerImg;
                    livingImage2.onload = (()=>{
                        this.customerImg = this.extractedWithText(livingImage2, watermarkSplit, 1);
                        this.$refs.imagesUrl2.src = this.customerImg;
                    });
                }
            } else {
                this.showCustomeImg = false;
            }
            this.generateCheckCode();
        },
        //返回上一个页面
        goBack() {
            Mobile.back();
        },
        playButtonTuoDong(e) {
            let touch = e.touches ? e.touches[0] : e;
            this.playJinDutaio(touch);
        },
        playJinDutaio(e) {
            if (this.$refs.myVideo.readyState > 0) {
                let length = e.pageX - this.$refs.playProgressWrap.offsetLeft;
                let percent = length / this.$refs.playProgressWrap.offsetWidth;
                if (percent >= 0 && percent <= 1) {
                    this.$refs.myVideo.currentTime = percent * this.$refs.myVideo.duration;
                    this.updateProgress();
                }
            }
        },
        updateProgress() {
            // this.videoTime.startTime = 
            let formatTimeCurrentTime = this.formatTime(this.$refs.myVideo.currentTime);
            this.videoTime.startTime = formatTimeCurrentTime;
            let percent = this.$refs.myVideo.currentTime / this.$refs.myVideo.duration;
            this.$refs.playProgress.width = percent * (this.$refs.playProgressWrap.offsetWidth) + 'px';
            this.$refs.playProgressButton.left = percent * (this.$refs.playProgressWrap.offsetWidth) + 'px';
            if (this.$refs.myVideo.currentTime >= this.$refs.myVideo.duration) {
                document.querySelector('#play-video-btn i').className = 'iconfont icon-play';
            }
        },
        formatTime(time) {
            let minute = Math.floor(time / 60);
            let second = Math.round(time % 60);
            if (second < 10) {
                second = '0' + second;
            }
            return minute + ':' + second;
        },
        //录制视频
        videoButtonRecord() {
            WadeMobile.recordVideo(0.5, 30).then(path => {
                WadeMobile.getFileBase64(path, 9999999).then(res=>{
                    res.ossPath = 'datas/video/' + this.staffInfo.PROVINCE_CODE;
                    if (this.portraitVideoCheckSwitch) {
                        res.portraitVideoCheckFlag = '1';
                    }
                    // Common.loadingStart('视频上传中.....');
                    // //ossSwitch开关打开走oss上传
                    // if (this.ossSwitch) {
                    //     this.$http.post('/video/plateFormOssUpload', res).then(result => {
                    //         let filePath = result.ossKey.split('/');
                    //         let fileName = filePath[filePath.length - 1];
                    //         axios({
                    //             method: 'put',
                    //             url: EncryptUtil.decrypt(result.url, 'LTGZ_202210_LTGZ'),
                    //             headers: { 'Content-Type': 'video/mp4' },
                    //             data: base64ToFile(res.base64, fileName, 'video/mp4')
                    //         }).then(() => {
                    //             Common.loadingStop();
                    //             // 视频上传成功
                    //             //开关打开调用视频校验接口
                    //             if (this.portraitVideoCheckSwitch) {
                    //                 this.$http.post('/touchService/call', {
                    //                     serviceName: 'videoCheckService.videoVerification',
                    //                     url: EncryptUtil.decrypt(result.url, 'LTGZ_202210_LTGZ')
                    //                 }).then(resp => {
                    //                     if (resp && resp.respCode != '0000') {
                    //                         this.$toast('视频无人像或视频时长不足');
                    //                         return;
                    //                     } else {
                    //                         this.$toast('视频上传成功');
                    //                         this.videoSrcUrl = path;
                    //                         this.videoSrcUrl2 = result.url;
                    //                         this.$refs.myVideo.src = result.url;
                    //                         this.$nextTick(()=>{
                    //                             this.$refs.myVideo.load();
                    //                         });
                    //                     }
                    //                 }).catch(e => {
                    //                     this.$toast('视频校验异常' + e);
                    //                     return;
                    //                 });
                    //             } else {
                    //                 this.$toast('视频上传成功');
                    //                 this.videoSrcUrl = path;
                    //                 this.videoSrcUrl2 = result.url;
                    //                 this.$refs.myVideo.src = result.url;
                    //                 this.$nextTick(()=>{
                    //                     this.$refs.myVideo.load();
                    //                 });
                    //             }
                    //         }).catch(() => {
                    //             Common.loadingStop();
                    //         });
                    //         this.$http.post('/video/completeUpload', { uuid: result.uuid });
                    //     }).catch(() => {});
                    // } else {
                    //     this.$http.post('/video/videoUploadByBase64', res).then(resp=>{
                    //         Common.loadingStop();
                    //         if (this.portraitVideoCheckSwitch && resp.check == '0') {
                    //             this.$toast('视频无人像或视频时长不足');
                    //             return;
                    //         }
                    //         if (resp.respCode == '0') {
                    //             this.$toast('视频上传成功');
                    //             this.videoSrcUrl = path;
                    //             this.videoSrcUrl2 = resp.url;
                    //             this.$refs.myVideo.src = resp.url;
                    //             this.$nextTick(()=>{
                    //                 this.$refs.myVideo.load();
                    //             });
                    //         }
                    //     }).catch(()=>{
                    //         Common.loadingStop();
                    //     });
                    // }
                });
            }, 0.5, 5).catch(err => {
                this.$toast(err);
            });
        },
        //初始话video时间
        initVideo() {
            setTimeout(()=>{
                this.videoTime.startTime = this.formatTime(this.$refs.myVideo.currentTime);
                this.videoTime.endTime = this.formatTime(this.$refs.myVideo.duration);
            }, 3000);
            
        },
        //播放视频
        playPause() {
            if (this.$refs.myVideo.paused || this.$refs.myVideo.ended) {
                if (this.$refs.myVideo.readyState > 0) {
                    if (this.$refs.myVideo.ended) {
                        this.$refs.myVideo.currentTime = 0;
                    }
                    this.$refs.myVideo.play();
                    this.showPlay = false;
                    this.updateProgress();
                } else {
                    this.$toast('视频不可播放');
                    this.showPlay = false;
                }
            } else {
                this.showPlay = true;
                this.$refs.myVideo.pause();
            }
        },
        playPausePlay() {
            this.showPlay = false;
        },
        faceAuthenticationCallBack(face) {
            let faceImg = 'data:image/jpeg;base64,' + face.img;
            let secondLivingFaceImg = 'data:image/jpeg;base64,' + face.img_best;
            this.realFaceImg = util.isEmpty(face.img_best) ? faceImg : secondLivingFaceImg;
            let watermark = '仅限办理联通业务使用' + '|' + this.getNowFormatDate() + '|' + this.staffInfo.STAFF_ID;
            let watermarkSplit = watermark.split('|');
            let livingImage12 = new Image();
            livingImage12.src = this.realFaceImg;
            livingImage12.onload = (()=>{
                this.realFaceImg = this.extractedWithText(livingImage12, watermarkSplit, 1);
                this.$refs.imagesUrl1.src = this.realFaceImg;
            });

        },
        faceClick() {
            WadeMobile.faceAuthentication([0, 0, 1]).then(result => {
                let face = result;
                if (result.retCode == '0') {
                    //人脸回调后的方法
                    this.faceAuthenticationCallBack(face);
                } else {
                    this.img = '';
                    this.imgBest = '';
                    this.originImg = '';
                    this.originImgBest = '';
                    this.realFaceImg = '',
                    this.$toast('实人认证照片采集失败，请重新尝试人脸识别');
                }
            }).catch(() => {
            });
        },
        customerClick() {
            WadeMobile.getIdentifyPhoto('noBorder', 'base64').then(img=>{
                let imageDate = '';
                if (WadeMobile.isIOS()) {
                    imageDate = 'data:image/jpg;base64,' + img;
                } else {
                    imageDate = img;
                }
                if (imageDate.length / 1024 * 3 / 4 < 50) {
                    Mobile.alert('图片质量过低，请重新拍摄');
                    return;
                }
                this.customerImg = imageDate;
                let watermark = '仅限办理联通业务使用' + '|' + this.getNowFormatDate() + '|' + this.staffInfo.STAFF_ID;
                let watermarkSplit = watermark.split('|');
                let livingImage13 = new Image();
                livingImage13.src = this.customerImg;
                livingImage13.onload = (()=>{
                    this.customerImg = this.extractedWithText(livingImage13, watermarkSplit, 1);
                    this.$refs.imagesUrl2.src = this.customerImg;
                });
            }).catch(() => {
            });
        },
        getNowFormatDate() {
            let date = new Date();
            let seperator1 = '-';
            let seperator2 = ':';
            let month = date.getMonth() + 1;
            let strDate = date.getDate();
            if (month >= 1 && month <= 9) {
                month = '0' + month;
            }
            if (strDate >= 0 && strDate <= 9) {
                strDate = '0' + strDate;
            }
            let hours = date.getHours();
            let minutes = date.getMinutes();
            let seconds = date.getSeconds();
            if (hours >= 0 && hours <= 9) {
                hours = '0' + hours;
            }
            if (minutes >= 0 && minutes <= 9) {
                minutes = '0' + minutes;
            }
            if (seconds >= 0 && seconds <= 9) {
                seconds = '0' + seconds;
            }
            let currentdate = date.getFullYear() + seperator1 + month + seperator1 + strDate
                + ' ' + hours + seperator2 + minutes
                + seperator2 + seconds;
            return currentdate;
        },
        // 图片压缩
        extractedWithText(livingImage, watermarkSplit, imgIndex) { // 要使用imgIndex吗？？1还是2
            let width = livingImage.width;
            let height = livingImage.height;
            let baseFontSize = document.documentElement.style.fontSize;
            let fontSize = height / ((1 == imgIndex) ? 30 : 15);
            fontSize = (fontSize * baseFontSize.substring(0, baseFontSize.length - 2) / 37.5).toFixed(0);
            let imageScale = 0.9;
            let canvas = document.createElement('canvas');
            canvas.width = width;
            canvas.height = height;
            let context = canvas.getContext('2d');
            context.drawImage(livingImage, 0, 0, width, height);
            context.font = fontSize + 'px microsoft yahei';
            if (1 == imgIndex) {
                context.fillStyle = 'rgba(255,0,0,0.4)';
            } else {
                context.fillStyle = 'rgba(255,255,255,0.8)';
            }
            let x = (width - watermarkSplit[0].length * fontSize - 1) / 2;
            context.fillText(watermarkSplit[0], x, height - 4 * fontSize);
            context.fillText(watermarkSplit[1], x, height - 2.5 * fontSize);
            context.fillText(watermarkSplit[2], x, height - fontSize);
            let faceImg = canvas.toDataURL('image/jpeg', imageScale);
            let faceFlag = true;
            while (faceFlag) {
                if (faceImg.length / 1024 * 3 / 4 > 200) {
                    imageScale -= 0.05;
                    faceImg = canvas.toDataURL('image/jpeg', imageScale);
                } else {
                    faceFlag = false;
                }
            }
            return faceImg;
        },
        //获取4位随机数
        generateCheckCode() {
            let params = {
                transId: this.transId,
                supermanagerId: this.staffInfo.STAFF_ID,
                supermanagerName: this.staffInfo.STAFF_NAME,
                supermanId: '',
                supermanName: '',
                superNickId: '',
                superNickName: '',
                psptId: this.cardInfo.cardNo,
                psptEndDate: this.cardInfo.period,
                serviceName: 'customerRealNameVerifyService.generateCheckCode'
            };
            Common.loadingStart('生成随机数中...');
            this.$http.post('/touchService/call', params).then(res=>{
                Common.loadingStop();
                res = res.respData.split('');
                this.numList = res;
                this.$toast('验证码已刷新，请重新录制视频');
            }).catch(err=>{
                Common.loadingStop();
                Common.alert(err);
            });
        },
        //提交
        subMitton() {
            if (this.realFaceImg == '') {
                this.$toast('请完成实人认证检测拍照，再点击提交按钮，谢谢');
                return;
            }
            if (this.customerImg == '') {
                this.$toast('请完成现场拍照，再点击提交按钮，谢谢');
                return;
            }
            if (!this.videoSrcUrl) {
                this.$toast('请完成视频录制，再点击提交按钮，谢谢');
                return;
            }
            let params = {
                psptId: this.cardInfo.cardNo,
                transId: this.transId,
                avatarImg: this.cardInfo.avatar,
                realFaceImg: this.realFaceImg,
                customerImg: this.customerImg,
                recordedVideo: this.videoSrcUrl,
                videoUrl: this.videoSrcUrl2,
                serviceName: 'customerRealNameVerifyService.notifyManualAudit'
            };
            Common.loadingStart('信息提交中.....');
            this.$http.post('/touchService/call', params).then(res=>{
                Common.loadingStop();
                if (res.respCode == '0000') {
                    this.$toast('信息提交成功');
                    Mobile.closeH5();
                } else {
                    Common.alert(res.respMsg);
                }
            }).catch(err=>{
                Common.loadingStop();
                this.$toast(err);
            });
            
        }
    }
};
</script>

<style lang="scss" scoped>
/deep/ .van-sticky {
    display: flex;
    justify-content: center;
}

.buttonJindu {
    width: 9px;
    height: 9px;
    border-radius: 50%;
    background: rgb(167, 24, 24);
    position: absolute;
    top: -3px;
    left: -4.5px;
}

.headerInfo {
    font-weight: 500;
    font-size: 20px;
    margin-left: 26%;
}

.playTime {
    margin-left: 8px;
}

.playPause {
    margin-top: 6px;
}

.vantProgress {
    // margin-top: 10px;
    width: 6rem;
    position: relative;
    margin-left: 10px;
}

.header {
    background: #0081ff;
    height: 50px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #c0c0c0;
}

.videoButten {
    margin-left: 2.2rem;
    margin-bottom: 10px;
    width: 200px;
    height: 40px;
}

.vodeoBox {
    width: 9.2533rem;
    height: 5.2rem;
    margin: 0 auto;
}

.videoClass {
    display: flex;
    width: 9.2533rem;
    height: 1rem;
    align-items: center;
    background: #e2e0e0;
    margin-left: 15px;
}

.pic-panel {
    display: flex;
}

.pic-panel-item:nth-child(odd) {
    /* width: 50%; */

    /* padding-right: 5%; */
    width: 33.3%;
    padding-right: 2.5%;
}

.pic-panel-item:nth-child(even) {
    /* width: 50%; */
    padding-left: 2.5%;
    width: 33.3%;

    /* padding-left: 5%; */
}

.pic-panel .pic-box {
    /* padding-left: 100%; */

    /* padding-bottom: 62.5%; */

    /* position: relative; */
    width: 100%;
    height: auto;
}

.pic-panel .pic {
    /* position: absolute; */

    /* top: 0; */

    /* left: 0; */

    /* width: 100%; */

    /* height: 100%; */
    width: 100%;
}

.random-panel {
    margin-top: 0.48rem;
    margin-left: 10px;
}

.random-panel .random-number-wrapper {
    width: 3.1733rem;
    height: 1.2rem;
    padding: 0.0667rem 0.12rem;
    background-color: #cfcfcf;
}

.random-panel .random-number-wrapper .number-list {
    display: flex;
}

.random-panel .random-number-wrapper .number-list .number-box {
    width: 0.6533rem;
    height: 1.0667rem;
    margin: 0 0.04rem;
    background-color: #ffffff;
    border-radius: 2px;
    font-size: 18px;
    text-align: center;
}

.random-panel .number-list .number-box .iconfont {
    font-size: 0.64rem;
    line-height: 1.0667rem;
}

.random-panel .random-btn .iconfont {
    color: #ffc301;
    font-size: 0.3733rem;
    margin-left: 0.2933rem;
}

.random-panel .random-content {
    display: flex;
    align-items: center;
}

.video-panel .record-video-btn {
    font-size: 0.48rem;
    width: 4rem;
    margin: 0.32rem auto;
}

.video-panel .video-box {
    /* padding-left: 100%; */

    /* padding-bottom: 56.2%; */

    /* position: relative; */
    position: relative;
    width: 9.2533rem;
    height: 5.2rem;
    margin: 0 auto;
}

.video-panel .video {
    position: absolute;
    left: 0;
    top: 0;
    display: block;
    width: 100%;
    height: 100%;
}

.video-panel .video-controls {
    display: flex;
    align-items: center;
    width: 9.2533rem;
    height: 0.8rem;
    background: #d8d8d8;
    margin: 0 auto;
}

.video-panel .video-controls #play-video-btn i {
    font-size: 0.5067rem;
    color: #ffc301;
    margin: 0 0.2rem 0 0.16rem;
}

.video-panel .video-controls .play-progress-wrap {
    width: 6.5733rem;

    /* height: 0.08rem; */
    height: 3px;
    border-radius: 1.5px;
    background: #b7b7b7;
    position: relative;
}

.video-panel .video-controls .play-progress {
    width: 0%;

    /* height: 0.08rem; */
    height: 3px;
    border-radius: 1.5px;
    background: #ffc301;
}

.video-panel .video-controls .play-progress-wrap #play-progress-btn {
    width: 9px;
    height: 9px;
    border-radius: 50%;
    background: #ffffff;
    position: absolute;
    top: -3px;
    left: -4.5px;
}

.video-panel .video-controls .play-time {
    width: 1.4933rem;
    font-size: 0.32rem;
    margin-left: 0.16rem;
    display: flex;
}

.central-audit-title {
    font-size: 0.4267rem;
    padding: 2px 0 12px;
    font-weight: 500;
}

.video-panel * {
    touch-action: pan-x;
}
</style>
