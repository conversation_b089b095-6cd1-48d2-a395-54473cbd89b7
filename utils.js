const glob = require('glob');
const path = require('path');
const fs = require('fs');
const HtmlWebpackPlugin = require('html-webpack-plugin');

function getPagesInfo() {
    const pages = [];
    const basePath = path.resolve(__dirname, './src/pages');
    const pagesPath = glob.sync(basePath + '/*/*/index.js');
    for (let page of pagesPath) {
        const pageInfo = {};
        let pageDir = path.dirname(page);
        let categoryDir = path.dirname(pageDir);
        let fileName = path.basename(pageDir);
        let categoryName = path.basename(categoryDir);
        pageInfo.name = fileName;
        pageInfo.category = categoryName;
        const files = fs.readdirSync(pageDir);
        if (files.includes('config.json')) {
            const data = fs.readFileSync(path.resolve(pageDir, 'config.json'));
            const config = JSON.parse(data.toString());
            pageInfo.options = config;
        }
        pageInfo.custTempFile = files.includes('index.html');
        pages.push(pageInfo);
    }
    return pages;
}


function entries() {
    const map = {};
    getPagesInfo().forEach(pageInfo => {
        const pageName = pageInfo.name;
        const filePath = `./src/pages/${pageInfo.category}/${pageName}/index.js`;
        map[pageName] = filePath;
    });
    return map;
}

function htmlPlugins() {
    const plugins = [];
    getPagesInfo().forEach(pageInfo => {
        const pageName = pageInfo.name;
        let templateFilePath = pageInfo.custTempFile ? `./src/pages/${pageInfo.category}/${pageName}/index.html` : './src/assets/config/index.html';
        let conf = {
            template: templateFilePath,
            filename: pageName + '.html',
            chunks: [pageName, 'common', 'chunk-assets', 'vendor', 'chunk-rux', 'runtime', 'chunk-pdfjs'],
            title: pageInfo.options.title || 'title'
        };
        plugins.push(new HtmlWebpackPlugin(conf));
    });
    return plugins;
}

module.exports = {
    getPagesInfo,
    entries,
    htmlPlugins
};
