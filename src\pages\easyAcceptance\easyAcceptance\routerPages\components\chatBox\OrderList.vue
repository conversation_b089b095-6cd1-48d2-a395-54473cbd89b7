<template>
    <div>
        <div>
          <div class="filtercriteria">
            <div class="filtercriteriatext"><van-icon name="arrow-left" size="15" @click="back()" style="margin-right: 20px"/>筛选条件</div>
            <div class="timebucket">起止时间段</div>
            <div class="timebox">
              <div class="starttime">
                <div class="box"><van-icon name="rux-rili1" @click="showstart = true"><span class="text" :value="startDate" >{{startDate}}</span></van-icon></div>
                <van-calendar color="#0081FF" :min-date="minDate" :max-date="maxDate" v-model="showstart" @confirm="onstartConfirm" :default-date="defaultDateS" />
              </div>
              <div class="division"></div>
              <div class="endtime">
                <div class="box"><van-icon name="rux-rili1" @click="showend = true"><span class="text" :value="endDate">{{endDate}}</span></van-icon></div>
                <van-calendar color="#0081FF" :min-date="minDate" :max-date="maxDate" v-model="showend" @confirm="onendConfirm" :default-date="defaultDateE"/>
              </div>
              <div class="surefilter">
                <div class="surefiltersubmit" @click="findRecordPageFun()">查询</div>
              </div>
              <div style="clear:both"></div>
            </div>
           
          </div>
          <div class="pageStyle">
            <van-pull-refresh v-model="isLoading" @refresh="onRefresh">
              <!-- 列表 -->
              <van-list
                  v-if="valueLists.length > 0"
                  v-model="loading"
                  :finished="finished"
                  finished-text="没有更多了"
                  :immediate-check="false"
                  offset="50"
                  @load="onLoad"
              >
                <div :style="computerHeight">
                  <div class="item-content" v-for="(item,index) in valueLists" :key="index" >
                    <div oneline>
                      <span name>下单时间: {{item.acceptDate}}</span>
                    </div>
                    <div class="division"></div>
                    <div class="detailed_contents">
                      <p class="paragraphspacing">副卡套餐：{{item.commName}}</p>
                      <p class="paragraphspacing">副卡号码：{{item.serialNumber}}</p>
                      <p class="paragraphspacing">订单号：{{item.orderId}}</p>
                      <p class="paragraphspacing">业务类型：{{item.tradeTypeCode=='50088'?'副卡新装':''}}</p>
                      <p class="paragraphspacing">商品金额：<b class="font18">0.00</b>元</p>
                      <van-circle v-model="currentRate" :rate="100" :color="colors[item.orderState]"  :speed="100" size="60px" :text="[item.orderState]" >
                        <p :class="item.orderState == 0 ? 'text0' : item.orderState == 9 ? 'text9' : item.orderState == 6 || item.orderState == 7?'text6':'textOther'">{{text[item.orderState]}}</p>
                      </van-circle>
                    </div>
                    <div class="division-button">
                      <div v-show="item.orderState != 2 " style="float: right">
                        <van-button type="info" size="small" @click="toDetail(item)">查看详情</van-button>
                      </div>
                    </div>
                  </div>
                </div>
              </van-list>
              <van-empty
                  image-size="150"
                  v-else
                  description="暂无数据"
                  class="empty"
              />
              <!-- end -->
            </van-pull-refresh>
          </div>

        </div>
   
    </div>
</template>

<script>

import moment from "moment";
import {Toast} from "vant";

export default {
    name: 'OrderList',
    data() {
        return {
          currentRate: 100,
          colors: {
            '0': '#FF713E',
            '9': '#4FD782',
            '6': '#545454',
            '7': '#545454',
          },
          text: {
            '0': '待处理',
            '9': '已竣工',
            '6': '返销',
            '7': '撤单',
            '2': '处理中',
            '4': '作废',
            '5': '待写卡',
          },
          finished: false,
          loading: false,
          isLoading: false,
          valueLists: [],
          pageNum: 1,
          pageSize: 5,
          pages: 2,
          startDate:moment().format('yyyyMM')+'01',
          endDate:moment().format('yyyyMMDD'),
          minDate: new Date(moment().subtract(6, "months")),
          maxDate: new Date(),
          defaultDateS:new Date(moment().format('yyyy-MM')+'-01'),
          defaultDateE:new Date(),
          showstart:false,
          showend:false,
          computerHeight:{
            height: document.documentElement.clientHeight - 60 + "px",
            overflow:"auto"
          },
        }
    },
    created() {
      this.findRecordPageFun();
        // this.params.menuList.forEach((item, index) => {
        //     item.showPopover = false
        //     if (index === 0) {
        //         this.mainMenu = item
        //     } else {
        //         this.otherMenus.push(item)
        //     }
        // })
        // this.$emit('showComponent')
    },
    methods: {
      onRefresh() {
        // 重置页码
        this.pageNum = 1;
        // 重置刷新状态
        this.finished = false;
        this.loading = false;
        // 请求列表
        this.findRecordPageFun();
        this.isLoading=false;
      },
      // 加载更多
      onLoad() {
        this.findRecordPageMore();
      },
      // 获取初始列表
      findRecordPageFun() {
        let reqData = {
          pageNo: this.pageNum,
          pageSize: this.pageSize,
          startTime:this.startDate,
          endTime:this.endDate
        };
        console.log(this.startDate+"========"+this.endDate)
        this.$http.post('/Order/query', reqData).then((res) => {
          if ("0000"==res.respCode) {
            this.pages = res.respData.pageTotal;
            this.valueLists=[];
            this.valueLists.push(...res.respData.orderList);
            this.pageNum = finePage;
            this.loading = false;
          } else {
            this.$dialog.alert({
              message:"未查询到数据"
            })
            this.valueLists = [];
            this.pages = 0;
            this.pageNum = 1;
            this.finished = false;
            this.loading = false;
            
          }
        });
      },
      async findRecordPageMore() {
        // 请求页码+1
        let finePage = this.pageNum + 1;
        // 判断页码极限
        if (finePage > this.pages) return (this.finished = true);
        let params = {
          pageNo: finePage,
          pageSize: this.pageSize,
          startTime:this.startDate,
          endTime:this.endDate
          
        };
        this.$http.post('/Order/query', reqData).then((res) => {
          if ("0000"==res.respCode) {
            this.pages = res.respData.pageTotal;
            this.valueLists.push(...res.respData.orderList);
            this.pageNum = finePage;
            this.loading = false;
          } else {
            this.$dialog.alert({
              message:"未查询到数据"
            })
            this.valueLists = [];
            this.pages = 0;
            this.pageNum = 1;
            this.finished = false;
            this.loading = false;
          }
        });
      },
     
      formatDate(date) {
        let currMonth = date.getMonth() + 1;
        if(currMonth < 10) currMonth = "0"+currMonth;
        let currDay = date.getDate();
        if(currDay < 10) currDay = "0"+currDay;
        return `${date.getFullYear()}${currMonth}${currDay}`;
      },
      isSameMonth(dateStr1, dateStr2) {
        console.log(dateStr1+"===========")
        console.log(dateStr2+"===========")
        const date1 = new Date(dateStr1.slice(0, 4), dateStr1.slice(4, 6) - 1, dateStr1.slice(6, 8));
        const date2 = new Date(dateStr2.slice(0, 4), dateStr2.slice(4, 6) - 1, dateStr2.slice(6, 8));
        // 比较年和月
        return date1.getFullYear() === date2.getFullYear() && date1.getMonth() === date2.getMonth();
      },
      onstartConfirm:function (date) {
        let c=this.startDate;
        console.log(this.isSameMonth(this.formatDate(date),this.endDate));

        if(this.compareDates(this.endDate,this.formatDate(date))<0){
          this.startDate=c;
          this.showstart = false;
          this.$dialog.alert({
            message:"开始时间不能大于结束时间，请重新选择开始时间"
          })
        }
         if(!this.isSameMonth(this.formatDate(date),this.endDate)){
          
          this.showstart = false;
          this.$dialog.alert({
            message:"开始时间与结束时间要在同一个月内"
          })
           this.startDate=this.formatDate(date);
          this.endDate=this.formatDate(date);
          
        }
        else{
          this.showstart = false;
          this.startDate = this.formatDate(date);
        }
      },
      onendConfirm:function (date) {
        let c=this.endDate;
        if(this.compareDates(this.startDate,this.formatDate(date))>0){
          this.endDate=c;
          this.showend = false;
          this.$dialog.alert({
            message:"开始时间不能大于结束时间，请重新选择结束时间"
          })
        }
        else if(!this.isSameMonth(this.startDate,this.formatDate(date))){
          this.showend = false;
          this.$dialog.alert({
            message:"开始时间与结束时间要在同一个月内"
          })
          this.endDate=this.formatDate(date);
          this.startDate=this.formatDate(date);
        }
        else{
          this.showend = false;
          this.endDate = this.formatDate(date);
        }
      },
      compareDates(dateStr1, dateStr2) {
        let date1 = new Date(dateStr1.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3'));
        let date2 = new Date(dateStr2.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3'));
        return date1 - date2;
      },
      back(){
        this.$router.go(-1);
      }
    }
};
</script>

<style scoped lang="scss">
.font18{
  font-size: 18px;
}


.surefilter{
  margin-top: 22px;
  margin-left: 239px;
  width: 80px;
  height: 30px;
  box-sizing: border-box;
  background: #0081FF;
  border-radius: 6px 5px 5px 5px;
}
.surefilter .surefiltersubmit{
  width: 60px;
  height: 20px;
  font-size: 12px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 20px;
  text-align: center;
  padding-top: 5px;
  padding-left: 10px;
}
.timebox{
  margin-top: 13px;
}

.timebox .van-icon-rux-rili1{
  padding-top: 10px;
  padding-right: 10px;
  float: right;
}

.timebox .starttime{
  float: left;
  //width: 119px;
  height: 29px;
  background-color: #F1F1F1;
}

.timebox .endtime{
  float: left;
  //width: 119px;
  height: 29px;
  background-color: #F1F1F1;
}

.timebox .division {
  width: 10px;
  height: 2px;
  background: #D8D8D8;
  margin: 18px 5px;
  float: left;
}
.timebox div.box{
  background-color: #F1F1F1;
  //width: 119px;
  height: 38px;
  line-height: 38px;
}

.timebox span.text{
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #999999;
  padding-left: 8px;
}
.timebucket{
  margin-top: 18px;
  height: 20px;
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 600;
  color: #666666;
  line-height: 20px;
}
.filtercriteria{
  top: 25px;
  left: 28px;
  position: absolute;
}
.pageStyle{
  top: 135px;
  position: absolute;
}

.filtercriteriatext{
  height: 23px;
  font-size: 16px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 600;
  color: #333333;
  line-height: 23px;
}
.haomakuagndiv{
  background-color: #F1F1F1;
  box-sizing: border-box;
  width: 257px;
  height: 38px;
  margin-top: 13px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #999999;
}
.haomakuang{
  width: 257px;
  height: 38px;
  border: none;
  background-color: #F1F1F1;
  padding-left: 8px;
  border-radius: 3px;
}


.haoma{
  padding-top: 16px;
  height: 20px;
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 600;
  color: #666666;
  line-height: 20px;
}

.paragraphspacing{
  padding-bottom: 6px;
}
b{
  color: red;
}
.item-content div[oneline]{
  height: 46px;
}

.item-content .detailed_contents{
  padding-left: 18px;
  padding-top: 9px;
  height: 20px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #7A7F84;
  line-height: 20px;
}


.item-content [oneline] [name]{
  height: 24px;
  font-size: 17px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #333333;
  line-height: 24px;
  padding-top: 15px;
  padding-left: 18px;
  display: inline-block;
}

.item-content [oneline]  [time]{
  float: right;
  padding-top: 19px;
  padding-right: 17px;
  height: 24px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #7A7F84;
  line-height: 24px;
}

.division{
  width: 349px;
  height: 1px;
  background-color: #E4E4E4;
}
.division-button {
  width: 349px;
  height: 1px;
  background-color: #E4E4E4;
  margin-top: 110px;

}

.item-content {
  width: 349px;
  height: 230px;
  background: #FFFFFF;
  box-shadow: 0px 3px 5px 0px rgba(235, 238, 240, 1);
  border-radius: 6px;
  border: 1px solid #FFFFFF;
  margin: 10px 13px 0px;
}

.queryfilterouter {
  width: 375px;
  height: 52px;
  background: #FFFFFF;
}

.r-select .van-icon-rux-shaixuan {
  margin-left: 21px;
  margin-top: 17px;
}

.queryfilter {
  width: 287px;
  height: 32px;
  background: #F5F6F7;
  border-radius: 20px;
  margin-left: 25px;
  margin-top: 10px;
  float: left;
}

.r-select {
  float: left;
}

.queryfilter .van-icon-rux-sousuo1 {
  margin-top: 7.5px;
  margin-left: 255px;
}

</style>

<style  lang="scss">
body{
  background:#F4F4F4;
}
.queryfilter{
  background-color: #F5F6F7;
  overflow: hidden;
  .van-cell{
    padding:6px 0px 6px 11px;
    background-color: #F5F6F7;
    .van-field__right-icon{
      padding-right: 15px;
    }
  }
}
.details-button div button{
  float: right;
  height: 25px;
  margin: 8px 15px 5px 0;
  .van-button--small {
    height: 25px;
  }
}
.van-tabs__line {
  background-color: #1989fa;
}
.van-circle {
  top: -80px;
  left: 260px;
  position: relative;
}

.text0  {
  position: absolute;
  top: 50%;
  left: 0;
  box-sizing: border-box;
  width: 100%;
  padding: 0 0 4px;
  color: #FF713E;
  font-weight: 500;
  font-size: 14px;
  transform: translateY(-50%);
}
.text9  {
  position: absolute;
  top: 50%;
  left: 0;
  box-sizing: border-box;
  width: 100%;
  padding: 0 0 4px;
  color: #4FD782;
  font-weight: 500;
  font-size: 14px;
  transform: translateY(-50%);
}
.text6  {
  position: absolute;
  top: 50%;
  left: 0;
  box-sizing: border-box;
  width: 100%;
  padding: 0 0 4px;
  color: #545454;
  font-weight: 500;
  font-size: 14px;
  transform: translateY(-50%);
}
.textOther  {
  position: absolute;
  top: 50%;
  left: 0;
  box-sizing: border-box;
  width: 100%;
  padding: 0 0 4px;
  color: #1989fa;
  font-weight: 500;
  font-size: 14px;
  transform: translateY(-50%);
}

</style>
