<template>
    <div class="chart-container" ref="chartContainer">
        <div class="chart-content">
            <div class="dialog-box" v-for="(item, index) in chatList" v-show="item.show" :key="index">
                <div
                    v-if="item.sender === '0'"
                    class="client-dialog"
                    :class="{ 'audio-dialog': item.moduleName === 'AudioInput' }"
                    >
                    <!-- <div class="client-dialog-icon"></div> -->
                    <div class="dialog-bubble" :class="{ 'module-bubble': item.type === 'module' }">
                        <div v-if="item.type === 'text'" class="dialog-text">{{ item.content }}</div>
                        <component
                            v-if="item.type === 'module'"
                            :is="item.moduleName"
                            :params="item.params || ''"
                            @startLoading="startLoading"
                            @endLoading="endLoading"
                            @userSend="userSend"
                            @newSysChat="newSysChat"
                            @scrollToBottom="scrollToBottom"
                            @showComponent="showComponent(item)"
                            @deleteComponent="deleteComponent"
                            @newChatApi="newChatApi"
                        />
                    </div>
<!--                    <div class="client-image"></div>-->
                </div>
                <div v-if="item.sender === '1'" class="robot-dialog">
<!--                    <div class="robot-image"></div>-->
                    <div
                        class="dialog-bubble"
                        :style="item.moduleName ==='orderTrack' ? {background:  'linear-gradient(to bottom, #7F93FB, #F8F8F8, #F8F8F8, #F8F8F8)'}:''"
                        :class="{
                            'module-bubble': item.type === 'module',
                            'text-module-bubble': item.moduleName === 'TextResponse',
                            'startchat-module-bubble': item.moduleName === 'startChat',
                            'tip-bubble': item.moduleName === 'TextResponse'&&item.moduleLevel === 2,
                            'selectproduct-module-bubble': item.moduleName === 'selectProduct',
                            'orderConfirmBox-module-bubble': item.moduleName === 'OrderConfirmBox',
                            'no-padding' : item.moduleName === 'selectAddress',
                        }"
                    >
                        <div
                        class="w-mask"
                        v-if="item.type === 'module' && !['TextResponse','startChat'].includes(item.moduleName) && item.disabled"
                        ></div>
                        <div v-if="item.type === 'text'" class="dialog-text">{{ item.content }}</div>
                        <component
                            v-if="item.type === 'module' && (noDisableModules.indexOf(item.moduleName) !== -1 || item.moduleLevel !== 1)"
                            :is="item.moduleName"
                            :params="item.params || ''"
                            @startLoading="startLoading"
                            @endLoading="endLoading"
                            @userSend="userSend"
                            @newSysChat="newSysChat"
                            @scrollToBottom="scrollToBottom"
                            @showComponent="showComponent(item)"
                            @deleteComponent="deleteComponent"
                            @toNextStep="toNextStep"
                            @clickMenu="clickMenu"
                            @newChatApi="newChatApi"
                            @terminateTask="terminateTask"
                        />
                        <component
                            v-else
                            ref="module"
                            :is="item.moduleName"
                            :params="item.params || ''"
                            @startLoading="startLoading"
                            @endLoading="endLoading"
                            @userSend="userSend"
                            @newSysChat="newSysChat"
                            @scrollToBottom="scrollToBottom"
                            @showComponent="showComponent(item)"
                            @deleteComponent="deleteComponent"
                            @toNextStep="toNextStep"
                            @toDisabledModule="item.disabled = true"
                            @newChatApi="newChatApi"
                            @terminateTask="terminateTask"
                        />
                    </div>
                </div>
                <div v-if="item.sender === 'divideLine'" class="divide-line">
                    <van-divider dashed :style="{ color: '#3489FC', borderColor: '#3489FC', padding: '5px', backgroundColor: '#f2f2f2' }">
                        {{ item.text }}
                    </van-divider>
                </div>
            </div>
          <div class="dialog-box" v-if="suggest">
            <div class="robot-dialog" style="flex-direction: column;align-items: baseline;">
              <div class="dialog-bubble" style="font-size: 20px;margin-bottom: 4px"  @click="userInputProcess({textInput:'全部订单',type: '1'})">全部订单<van-icon name="arrow" /></div>
              <div class="dialog-bubble" style="font-size: 20px;margin-bottom: 4px" @click="userInputProcess({textInput:'已完工订单',type: '1'})">已完工订单<van-icon name="arrow" /></div>
              <div class="dialog-bubble" style="font-size: 20px;margin-bottom: 4px" @click="userInputProcess({textInput:'未完工订单',type: '1'})">未完工订单<van-icon name="arrow" /></div>
            </div>
          </div>
        </div>
        <div class="chat-recognizing" v-if="chatLoading">
            <img src="@/assets/images/chat_loading.gif" />
            <div>{{ loadingText }}</div>
        </div>
      <div class="chat-recognizing" v-if="chatLoadingAsis">
        <img src="@/assets/images/chat_loading.gif" />
        <div>{{ loadingText }}</div>
      </div>
        <div class="audio-recognizing" v-show="audioRecognizing" >
          <div class="text">
            <span>语音识别中</span>
            <img src="@/assets/images/chat_loading.gif" /></div>
        </div>
    </div>
</template>
<script>
import { mapMutations, mapState, mapActions } from 'vuex'
import { alertError } from '@/assets/bizComponents/funcComponent.js'
import { getSentenceTime } from './../../assets/js/func.js'
import * as _ from 'lodash';
import AudioInput from './chatBox/AudioInput'
import TextResponse from './chatBox/TextResponse'
import ErrorInfo from './chatBox/ErrorInfo'
import ConfirmBox from './chatBox/ConfirmBox.vue'
import TaskSelect from './chatBox/TaskSelect.vue'
import CommSelect from './chatBox/CommSelect.vue'
import MainCardMsgDetail from './chatBox/MainCardMsgDetail.vue'
import MobileRecommendation from './chatBox/MobileRecommendation.vue'
import OrderInfo from "./chatBox/OrderInfo.vue"
import WadeMobile from 'rk-native-plugin';
import startChat from './chatBox/startChat.vue'
import customerAuthentication
  from "../../../../../assets/bizComponents/customerAuthentication/customerAuthentication.vue";
import {Toast} from "vant";
import IptvNumMsgDetail from "./chatBox/IptvNumMsgDetail.vue";
import IptvGoodChoose from "./chatBox/IptvGoodChoose.vue";
import IptvBookingDate from "./chatBox/IptvBookingDate.vue";
import IptvOrderInfo from "./chatBox/IptvOrderInfo.vue";
import OrderConfirmBox from "./chatBox/OrderConfirmBox.vue";
import ZwOrderInfo from "./chatBox/ZwOrderInfo.vue";
import ZwBookingDate from "./chatBox/ZwBookingDate.vue";
import ZwGoodChoose from "./chatBox/ZwGoodChoose.vue";
import ZwNumMsgDetail from "./chatBox/ZwNumMsgDetail.vue";
import IptvBroadNumChoose from "./chatBox/IptvBroadNumChoose.vue";
import ZwBroadNumChoose from "./chatBox/ZwBroadNumChoose.vue";
import orderTrack from "./chatBox/orderTrack.vue";
import OrderInfoList from "./chatBox/OrderInfoList.vue";
import customerInfo from "./chatBox/IptvCustomerInfo.vue";
import selectDate from "./chatBox/IptvSelectDate.vue";
import IptvSelectProduct from "./chatBox/IptvSelectProduct.vue";
import LptvYwSelectProduct from "./chatBox/lptvYwSelectProduct.vue";
import ZwSelectProduct from "./chatBox/ZwSelectProduct.vue";
import ZwYwSelectProduct from "./chatBox/ZwYwSelectProduct.vue";
import urlStart from "./chatBox/urlStart.vue";
import selectAddress from "./chatBox/selectAddress.vue";
import YskBookingDate from "./chatBox/YskBookingDate.vue";
import YskCustSelectProduct from "./chatBox/YskCustSelectProduct.vue";
import YskIptvProduct from "./chatBox/YskIptvProduct.vue";
import YskKdCheapSelectProduct from "./chatBox/YskKdCheapSelectProduct.vue";
import YskNumMsgDetail from "./chatBox/YskNumMsgDetail.vue";
import YskYwSelectProduct from "./chatBox/YskYwSelectProduct.vue";
import YskZwSelectProduct from "./chatBox/YskZwSelectProduct.vue";
import YskOrderInfo from "./chatBox/YskOrderInfo.vue";
import FusUpFttrOrderInfo from "./chatBox/FusUpFttrOrderInfo.vue";
import FusUpFttrNumMsgDetail from "./chatBox/FusUpFttrNumMsgDetail.vue";
import FusUpFttrBookingDate from "./chatBox/FusUpFttrBookingDate.vue";
import FusUpFttrSelectProduct from "./chatBox/FusUpFttrSelectProduct.vue";
import FusUpFttrYwSelectProduct from "./chatBox/FusUpFttrYwSelectProduct.vue";
import FusUpFttrBroadNumChoose from "./chatBox/FusUpFttrBroadNumChoose.vue";
import BroadUpOrderInfo from "./chatBox/BroadUpOrderInfo.vue";
import BroadUpBookingDate from "./chatBox/BroadUpBookingDate.vue";
import BroadUpNumMsgDetail from "./chatBox/BroadUpNumMsgDetail.vue";
import BroadUpSelectProduct from "./chatBox/BroadUpSelectProduct.vue";
import BroadUpBroadNumChoose from "./chatBox/BroadUpBroadNumChoose.vue";
import SingleModeNetworkYwNumChoose from "./chatBox/SingleModeNetworkYwNumChoose.vue";
import SingleModeNetworkContractSelectProduct from "./chatBox/SingleModeNetworkContractSelectProduct.vue";
import SingleModeNetworkOrderInfo from "./chatBox/SingleModeNetworkOrderInfo.vue";
import SingleModeNetworkSelectProduct from "./chatBox/SingleModeNetworkSelectProduct.vue";
import SingleModeNetworkNumMsgDetail from "./chatBox/SingleModeNetworkNumMsgDetail.vue";
export default {
    name: 'ChatContainer',
    components: {
        AudioInput,
        TextResponse,
        ErrorInfo,
        OrderConfirmBox,
        ConfirmBox,
        TaskSelect,
        CommSelect,
        MainCardMsgDetail,
        MobileRecommendation,
        OrderInfo,
        customerAuthentication,
      IptvNumMsgDetail,
      IptvGoodChoose,
      IptvBookingDate,
      IptvOrderInfo,
        startChat,
      ZwOrderInfo,
      ZwBookingDate,
      ZwGoodChoose,
      ZwNumMsgDetail,
      IptvBroadNumChoose,
      ZwBroadNumChoose,
      orderTrack,
      OrderInfoList,
      customerInfo,
      selectDate,
      IptvSelectProduct,
      LptvYwSelectProduct,
      ZwSelectProduct,
      ZwYwSelectProduct,
      urlStart,
      selectAddress,
      YskBookingDate,
      YskCustSelectProduct,
      YskIptvProduct,
      YskKdCheapSelectProduct,
      YskNumMsgDetail,
      YskYwSelectProduct,
      YskZwSelectProduct,
      YskOrderInfo,
      FusUpFttrOrderInfo,
      FusUpFttrNumMsgDetail,
      FusUpFttrBookingDate,
      FusUpFttrSelectProduct,
      FusUpFttrYwSelectProduct,
      BroadUpOrderInfo,
      BroadUpBookingDate,
      BroadUpNumMsgDetail,
      BroadUpSelectProduct,
      BroadUpBroadNumChoose,
      FusUpFttrBroadNumChoose,
      SingleModeNetworkNumMsgDetail,
      SingleModeNetworkOrderInfo,
      SingleModeNetworkSelectProduct,
      SingleModeNetworkContractSelectProduct,
      SingleModeNetworkYwNumChoose
    },
    data() {
        return {
          chatLoadingAsis:false,
          suggest:false,
          requireType: '',
            loadingText: '',
            audioRecognizing: false,
            phoneNumber: '',
            sessionIdRandom: '',
            toPushRunTimeTaskList: false,
            mixChangeParams: {},
            needWaitData: []
        }
    },
    computed: {
        ...mapState([
            'needWait',
            'chatList',
            'sessionId',
            'staffId',
            'flowStep',
            'activeModuleIndex',
            'loginPhoneNumber',
            'instanceId',
            'agentSessionId',
            'runTimeTaskList',
            'chatApiParam',
            'curTask',
            'noDisableModules',
            'jzfkOrderData',
            'shbmMsgInfo',
            'chatLoading',
            'iptvOrderData',
            'mqBookFlagData',
            'singleModeNetworkOrderData',
            'outCallMonetOrderData'
        ])
    },

    mounted() {
        const date = new Date()
        this.sessionIdRandom = `${date.getFullYear()}${date.getMonth()}${date.getDate()}${date.getHours()}${date.getMinutes()}${date.getSeconds()}`
    },
    watch: {
      chatList: {
            handler(val) {
                if (val.length > 0) {
                    const lastMsg = val[val.length - 1]
                    // 系统发送消息
                    if (lastMsg.sender === '1') {
                        if (lastMsg.next) {
                            setTimeout(() => {
                                this.startLoading()
                                this.updateChatList({
                                    sender: '1',
                                    type: 'module',
                                    moduleName: lastMsg.next,
                                    moduleLevel: 1,
                                    show: false
                                })
                            }, getSentenceTime(lastMsg.params.text))
                        }
                    }
                    this.$nextTick(() => {
                        this.scrollToBottom()
                        this.$forceUpdate()
                    })
                }
            }
        },
      needWait:{
        async handler(val) {
          if (!val && this.needWaitData.length > 0) {
            //继续调用newChatApi接口
            let data = this.needWaitData
            for (let i = 0; i < data.length; i++) {
              await this.newChatApi(data[i])
            }
            this.needWaitData.splice(0, this.needWaitData.length);
          }
        }
      }
    },
    methods: {
        ...mapMutations([
            'setNeedWait',
            'updateChatList',
            'setFlowStep',
            'setRobotWorking',
            'deleteLast',
	        'setRunTimeTaskList',
            'setChatApiParam',
            'setCurTask',
            'backToAnyStep',
            'setJzfkOrderData',
            'setIsFour',
            'showLoading',
            'hideLoading',
            'setIptvOrderData',
            'setMqBookFlagData',
            'setFusionUpFttrOrderData',
            'setOutCallMonetOrderData'
        ]),
        ...mapActions(['updateChatList','mainNumVerify', 'bookTimeSubmit','sendMsg', 'msgCodeVerify', 'viceCardCommoditySubmit','mobileRecommendationSubmit','customerAuthenticationSubmit','iptvNumCheck','iptvCommInfoSubmit',
          'bookTimeSubmit','iptvOrderfoSubmit','zwOrderfoSubmit','zwBookTimeSubmit','zwCommInfoSubmit','zwBroadbandNumCheck','iptvFusionInformationQuery','zwFusionInformationQuery','custOrderQuery','queryOrderDetails',
          'contactMsgSubmit','queryContactMsg','IptvReSelectDate','queryLatestBookDate','getProductData','defaultTimeTagChange','getBookTimeWord',
          'getProductDataZw','contactMsgSubmitZw','queryContactMsgZw','ZwReSelectDate','defaultTimeTagChangeZw','queryLatestBookDateZw','isItMissing','hideContactNodeWords','hideHotWord','showHotWords',
          'getKdProductDataYsk','contactMsgSubmitYsk','queryContactMsgYsk','yskReSelectDate','defaultTimeTagChangeYsk','queryLatestBookDateYsk','yskNumCheck','yskOrderInfoSubmit','selectRrightIptvYsk',
          'getKdfjProductDataYsk', 'getYwfjProductDataYsk','getZwProductDataYsk','getIptvProductDataYsk','getHotWordComm','selectAddressCommit','orderCancelMsgRecall','orderCancelSubmit',
          'fusUpFttrNumCheck','fttrFusionInformationQuery', 'fusUpFttrSendMsg','fusUpFttrMsgCodeVerify','getProductDataFusUpFttr','fusUpFttrOrderInfoSubmit','queryLatestBookDateFusUpFttr','defaultTimeTagChangeFusUpFttr',
          'fusUpFttrReSelectDate','queryContactMsgFusUpFttr','contactMsgSubmitFusUpFttr','bookTimeSubmitFusUpFttr','getYwProductDataFusUpFttr','ywfIsConflict','ywfIsConflictYsk','FusUpYskTip','zwUpTip',
          'qryBroadPhoneByCertCodeZw','qryBroadPhoneByCertCode','qryBroadPhoneByCertCodeFuseUp',
            'broadUpOrderfoSubmit','broadUpBookTimeSubmit','broadUpCommInfoSubmit','broadUpBroadNumCheck','getProductDataBroadUp','contactMsgSubmitBroadUp','queryContactMsgBroadUp','broadUpReSelectDate','defaultTimeTagChangeBroadUp'
            ,'queryLatestBookDateBroadUp','broadUpSendMsg','broadUpMsgCodeVerify','qryBroadPhoneByCertCodeBroadUp','broadUpFusionInformationQuery',
            'broadUpQryIsHandleFlag','broadUpQryPhoneNumberIsDk','wessageMsgSubmitBroadUp','zwSwitchToFusionUpFttr','custOrderQueryForThree',
          "singleModeNetworkNumCheck", "singleModeNetworkSendMsg", "singleModeNetworkCodeVerify", "getSingleModeNetworkList", "getYwSingleModeNetworkList", "SingleModeNetworkpreSubmit", "qryBroadPhoneByCertCodeSingleMobile", "SingleMobileSwitchToFuseFttr", "SingleMobileSwitchToYsk",
          ]),
        // 用户点击组件操作，发送消息
        userSend(data) {
            this.updateChatList({
                sender: '0',
                type: data.type,
                content: data.content
            })
        },
      // 用户输入（语音或文字）处理
        userInputProcess(data) {
            // 输入文字
            if (data.type === '1') {

              if(this.needWait){
                  this.needWaitData.push({
                    textInput: data.textInput,
                    inputType: "0",
                    type: data.type,
                  })
              }else{
                  this.updateChatList({
                      sender: '0',
                      type: 'text',
                      content: data.textInput,
                      show: true
                  })
                this.newChatApi({
                  textInput: data.textInput,
                  inputType: "0",
                  type: data.type,
                })
              }


            }
            // 输入语音
            else {
                if(this.isEmpty(data.base64)){
                    this.updateChatList({
                        sender: '1',
                        type: 'text',
                        content: '抱歉，我没听清您说什么，请您再说一遍。',
                        show: true
                    });
                    return;
                }
              if(this.needWait){
                this.needWaitData.push({
                  // base64Info: data.base64.split("base64,")[1], // 语音base64编码(语音输入时必传) //电脑端使用
                  base64Info: data.base64, // 语音base64编码(语音输入时必传) //联通公众app端使用
                  audiolen: data.size, // 录音文件字节数(语音输入时必传)
                  inputType: "0",
                  type: data.type
                })
              }else{
                this.newChatApi({
                  // base64Info: data.base64.split("base64,")[1], // 语音base64编码(语音输入时必传) //电脑端使用
                  base64Info: data.base64, // 语音base64编码(语音输入时必传) //联通公众app端使用
                  audiolen: data.size, // 录音文件字节数(语音输入时必传)
                  inputType: "0",
                  type: data.type
                })
              }

            }
        },
        // 用户点击快捷提示词处理
        userInputProcessForLabel(data) {
            // 输入文字

                this.updateChatList({
                    sender: '0',
                    type: 'text',
                    content: data.textInput,
                    show: true
                })
                if(this.needWait){
                    this.needWaitData.push({
                        textInput: data.textInputCode,
                        inputType: "1",
                        type: data.type,
                    })
                }else{
                    this.newChatApi({
                        textInput: data.textInputCode,
                        inputType: "1",
                        type: data.type,
                    })
                }


        },
        newChatApi(params_, loadingText, failTip, callBack, showVoice = true) {
            // console.log(this.mixChangeStepList[this.flowStep]);
            // return new Promise((resolve, reject) => {
                if(this.needWait){
                  this.needWaitData.push(params_)
                  return
                }
                let bookAddrTimeFlag = "0";
                if(!this.isEmpty(this.mqBookFlagData)
                    &&!this.isEmpty(this.mqBookFlagData.bookAddrTimeFlag)
                    &&this.mqBookFlagData.bookAddrTimeFlag=="1"){
                    bookAddrTimeFlag = "1";
                 }
                const params = {
                    agentSessionId: this.agentSessionId, // 会话id
                    // inputType: inputType, // 0-文本 1-动作
                    // type: type, // 1-文字输入,2-语音输入
                    // textInput: keyword, // 文字输入内容
                    instanceId: this.instanceId, // 实例id
                    sessionId: this.sessionId,
                    phoneInfo: this.loginPhoneNumber, // 当前登陆人手机（语音输入时必传）
                    reqToken: this.sessionId, // 当前登陆人token
                    nodeCode: '', // 环节编码（taskName为融合变更时必传）
                    notifyFlag: "", // 是否notify标识
                    taskId: this.curTask.taskId ?? '',
                    taskName: this.curTask.taskName ?? '',
                    staffId: this.staffId,
                    toolCode: this.curTask?.toolCode || '',
                    bookAddrTimeFlag:bookAddrTimeFlag,
                    ...params_,
                }
                var qs = require('qs');
                console.log(params);
                
                if (params_.type === '1') {
                    if(!params_.async){
                      this.startLoading(loadingText);
                    }
                } else {
                    this.audioRecognizing = true
                }
                this.scrollToBottom();
                this.$http.post('/smartFlowChat/newTalk', params).then(res => {
                    if (params_.type === '1') {
                      if(!params_.async){
                        this.endLoading();
                      }
                    }else {
                        this.audioRecognizing = false;
                    }
                    if (res.respCode === '0000') {
                        if(params_.type != '1'){
                            this.updateChatList({
                                sender: '0',
                                type: 'text',
                                content: res.respData.voiceToTextResult,
                                show: true
                            })
                        }
                        if (!res.respData?.identifyFail) {
                          const agentResultRespData = res.respData?.agentResultRespData || {}
                          const runTimeTaskList = res.respData?.runTimeTaskList || []
                          // 缓存运行时任务列表
                          this.setRunTimeTaskList(runTimeTaskList);
                          // 更新当前任务的缓存
                          if (agentResultRespData.taskStatus === '03') {
                            this.setCurTask({})
                            if (res.respData?.input != 'commandGoodsSubmit') this.toPushRunTimeTaskList = true
                          } else if (!!agentResultRespData.taskId) {
                            this.setCurTask({
                              taskId: agentResultRespData.taskId,
                              taskName: agentResultRespData.taskName,
                              taskStatus: agentResultRespData.taskStatus,
                              toolCode: agentResultRespData.toolCode || '',
                              outputList: agentResultRespData.outputList || []
                            })
                          }
                            // intentionExpandInfo不为空--意图扩大
                          if (Object.keys(agentResultRespData?.intentionExpandInfo || [] ).length > 0) {
                            let duplicateFlag = '0';
                            if (this.curTask.taskId === agentResultRespData?.intentionExpandInfo[0]?.taskId && agentResultRespData?.intentionExpandInfo[0]?.toolList[0]?.backNodeId === undefined) {
                              duplicateFlag = '2'
                            }
                              this.intentionExpansion(agentResultRespData.intentionExpandInfo, duplicateFlag) // 意图扩大
                            return
                          }
                            const workflow = agentResultRespData?.workflow;
                          if (!workflow) {
                            if (callBack && typeof callBack === 'function') callBack()
                          }
                            if (workflow?.rspcode === '0000') {
                              if (callBack && typeof callBack === 'function') callBack()
                            // 判断 outputType 0-文本；1-前端组件；2-前端执行函数；3-界面
                              if(!params_.async && !params_.notNeedOutput){
                                const outputList = agentResultRespData?.outputList;
                                outputList.forEach(item => {
                                  if (item.outputType === '0') {
                                    // 文本
                                    this.outputProcessOfText(item)
                                  } else if (item.outputType === '1') {
                                    // 前端组件
                                    this.outputProcessOfFrontModule(item)
                                  } else if (item.outputType === '2') {
                                    // 前端执行函数
                                    this.outputProcessOfFrontFunc(item)
                                  } else if (item.outputType === '3') {
                                    // 界面
                                    this.outputProcessOfInterface(item)
                                  }
                                })
                              }else{
                                  this.setNeedWait(false);
                              }

                          } else {
                            this.flowExceptionProcess(workflow.rspMessage || '') // todo-lgc 流程异常：待完善
                          }
                          // resolve(res.respData)
                        } else {
                            // 显示兜底话术
                            if (failTip) { // 初始化查询热销时，兜底话术写死
                              console.log("newChat1")
                              this.optiStatement(failTip);
                            } else {
                              console.log("newChat2")

                              this.optiStatement(res.respData.defaultTip);
                            }
                        }
                    } else {
                      let content=res.respMsg
                       console.log("newChat3")
                      this.optiStatement(content);
                    }
                }).catch(e => {
                    // alertError({
                    //     title: '出错了！',
                    //     message: e,
                    //     confirmButtonText: '报告错误'
                    // });
                    this.updateChatList({
                        sender: '1',
                        type: 'module',
                        moduleName: 'ConfirmBox',
                        moduleLevel: 1,
                        params: {
                            msg: `智能小助手出了点小差。请稍后点击操作`,
                            confirmText: '重试',
                            cancelText: '终止',
                            confirm: ()=> {
                                // 获取上一次提问的参数
                                // 重新调用对话接口
                                this.newChatApi({...this.chatApiParam})
                            },
                            cancel: ()=> {
                                this.terminateTask('04')
                            },
                        },
                        show: true
                    })
                    // reject()
                });
            // })
        },
        // output处理--文本
        outputProcessOfText(data) {
          if(data.type=='system'){
          } else{
            // 直接展示
              // 直接展示
              var textInput =data.output;
              if(textInput.includes("replaceText")){
                  textInput =  data.output.replace("replaceText",data.paramList[0].paramValue);
                  console.log(textInput);
              }
              this.updateChatList({
                  sender: '1',
                  type: 'module',
                  moduleName: 'TextResponse',
                  moduleLevel: 1,
                  params: {
                      text: textInput
                  },
                  show: true
              })
            // 目前先写死
            // if(data.output === "请提前检查是否携带读卡器设备、白卡，以及客户是否携带证件，主卡是否可以正常接收短信。<br/>如确认无误，请输入主卡号码。"){
            //   this.updateChatList({
            //     sender: '1',
            //     type: 'module',
            //     moduleName: 'TextResponse',
            //     moduleLevel: 2,
            //     params: {
            //       text: '主卡和副卡的话费套餐是否相同？'
            //     },
            //     show: true
            //   })
            //   this.updateChatList({
            //     sender: '1',
            //     type: 'module',
            //     moduleName: 'TextResponse',
            //     moduleLevel: 2,
            //     params: {
            //       text: '办理副卡有哪些限制条件？'
            //     },
            //     show: true
            //   })
            // }
          if(!this.isEmpty(this.curTask.taskId)){
            if("加装副卡"==this.curTask.taskName){
              this.saveCacheData();
            }
          }
           
        }
          },
        // output处理--前端组件
        outputProcessOfFrontModule(data) {
            let params = {}
            data.paramList.forEach(item => {
                params[item.paramCode] = item.paramValue || ''
                  console.log(data.paramList.length+"data.paramList.length===========")
                  if('MobileRecommendation'==data.output){
                    if(Object.keys(data.paramList).length>=1){
                      this.jzfkOrderData.numberChooseData.params=params
                      this.setIsFour(3)
                    }
                    else{
                      this.jzfkOrderData.numberChooseData.params={}
                      this.setIsFour(4)
                    }
                    this.setJzfkOrderData(this.jzfkOrderData)
                  }
            })
            if (data.output === 'ConfirmBox') {
                this.updateChatList({
                    sender: '1',
                    type: 'module',
                    moduleName: 'ConfirmBox',
                    moduleLevel: 1,
                    params: {
                        msg: `${params["respMsg"]}。 请点击按钮进行相关操作${params["suggest"]}`,
                        confirmText: '重试',
                        hiddenCancelButton: true,
                        confirm: ()=> {
                            this.newChatApi({
                                inputType: '1',
                                type: '1',
                                textInput: 'retry'
                            })
                        }
                    },
                    show: true
                })
              if(!this.isEmpty(this.curTask.taskId)){
                if("加装副卡"==this.curTask.taskName){
                  this.saveCacheData();
                }
              }
            } 
            else {
              if('IptvBookingDate'==data.output){
                console.log(params,"=======params")
                console.log(params['contactName'])
                if(!this.isEmpty(params)){
                  console.log('cyy---params',params)
                  if(!this.isEmpty(params['contactName'])){
                    this.iptvOrderData.timeData.contactName=params['contactName'];
                    
                  }if(!this.isEmpty(params['bookTime'])){
                    this.iptvOrderData.timeData.choseIndex=params['bookTime'];
                  }if(!this.isEmpty(params['bookDay'])){
                    this.iptvOrderData.timeData.stdBookDay=params['bookDay'];
                  }
                  
                  if(!this.isEmpty(params['contactPhone'])){
                    this.iptvOrderData.timeData.contactPhone=params['contactPhone'];
                  }
                  this.setIptvOrderData(this.iptvOrderData)
                  console.log('cyy-this.iptvOrderData.timeData',this.iptvOrderData.timeData)
                }
              }else {//回溯报错调整 其他环节去除自动填充当前日期
                  this.mqBookFlagData.bookAddrTimeFlag="0";
                  this.setMqBookFlagData(this.mqBookFlagData);
              }

              this.updateChatList({
                    sender: '1',
                    type: 'module',
                    moduleName: data.output,
                    moduleLevel: 1,
                    params: params,
                    show: true
                })
              if(!this.isEmpty(this.curTask.taskId)){
                if("加装副卡"==this.curTask.taskName){
                  this.saveCacheData();
                }
              }
            }
            this.scrollToBottom()
        },
      
        // output处理--前端执行函数
        async outputProcessOfFrontFunc(data) {
            let reqParams = {}
            data.paramList.forEach(paramData => {
                reqParams[paramData["paramCode"]] = paramData["paramValue"]
            })
          
          console.log(reqParams)
              if (this[data.output]) {
                console.log('customerAuthenticationSubmit=================3')
                // 动态调用方法
                let frontFuncRespData = await this[data.output](reqParams);
                if (frontFuncRespData["flag"] !== '1') {
                  if('4'!=frontFuncRespData["validFlag"]){
                      this.newChatApi({
                      inputType: "0",
                      type: '1',
                      textInput: JSON.stringify(frontFuncRespData),
                      notifyFlag: '1'
                    })
                    this.hideLoading()
                    
                    console.log("outputProcessOfFrontFunc")
                    if('bookTimeSubmit'==data.output||'zwBookTimeSubmit'==data.output||'zwCommInfoSubmit'==data.output||'iptvCommInfoSubmit'==data.output||'orderCancelMsgRecall'==data.output||'broadUpCommInfoSubmit'==data.output){
                    }
                    if('custOrderQuery'==data.output){
                      this.mqBookFlagData.bookAddrTimeFlag="1";
                      this.setMqBookFlagData(this.mqBookFlagData);
                    }
                     if('defaultTimeTagChange'==data.output){
                      this.mqBookFlagData.bookAddrTimeFlag="0";
                      this.setMqBookFlagData(this.mqBookFlagData);
                    }

                     if('selectRrightIptvYsk'==data.output){
                      this.updateChatList({
                        sender: '1',
                        type: 'module',
                        moduleName: 'TextResponse',
                        moduleLevel: 1,
                        failureReply: 1,
                        params: {
                          text: frontFuncRespData["respMsg"]
                        },
                        show: true
                      })
                    }
                      if('getProductDataFusUpFttr'==data.output){
                      console.log(selectRrightIptvYsk)
                      this.updateChatList({
                        sender: '1',
                        type: 'module',
                        moduleName:'TextResponse',
                        moduleLevel: 1,
                        params: {
                          text: frontFuncRespData["respMsg"]
                        },
                        show: true
                      })
                    }
                    else{
                        await this.optiStatement(frontFuncRespData["respMsg"] );
                     
                    }
                  }
                  else if('4'==frontFuncRespData["validFlag"]){
                    this.newChatApi({
                      inputType: "0",
                      type: '1',
                      textInput: JSON.stringify(frontFuncRespData),
                      notifyFlag: '1'
                    })
                    this.hideLoading()
                  }
                  return;
                }


                this.newChatApi({
                  inputType: "0",
                  type: '1',
                  textInput: JSON.stringify(frontFuncRespData),
                  notifyFlag: '1'
                })
                  if("zwSwitchToFusionUpFttr" == data.output){
                      this.setCurTask({});
                      this.updateChatList({
                          sender: '1',
                          type: 'module',
                          moduleName: 'TextResponse',
                          moduleLevel: 1,
                          failureReply: 1,
                          params: {
                              text: "您正在办理智能融合FTTR升套业务"
                          },
                          show: true
                      })
                      this.newChatApi({
                          inputType: "0",
                          type: "1",
                          textInput: "办理智能融合升套办理FTTR业务，我的号码是"+frontFuncRespData["serialNumber"],
                          notifyFlag: "",
                      });
                  }
               else if("SingleMobileSwitchToFuseFttr" == data.output){
                  this.setCurTask({})
                  this.newChatApi({
                    inputType: "0",
                    type: "1",
                    changeFlowPhoneNumber: frontFuncRespData["serialNumber"],
                    textInput: "智能融合FTTR升套",
                    notifyFlag: "",
                  });
                }

                else if("SingleMobileSwitchToYsk" == data.output){
                  this.setCurTask({})
                  console.log(this.outCallMonetOrderData,"this.outCallMonetOrderData")
                  let linshiInfo={
                    switchToOutCallFlag:"1",
                    commId:"",
                    commName:""
                  };
                  if(this.singleModeNetworkOrderData.fusionUpFttrGoodData.custList.length>0){
                    linshiInfo={
                      switchToOutCallFlag:"1",
                      commId:this.singleModeNetworkOrderData.fusionUpFttrGoodData.custList[0].commodityCode,
                      commName:this.singleModeNetworkOrderData.fusionUpFttrGoodData.custList[0].commodityName
                    }
                  }
                  this.outCallMonetOrderData.singleSwitchParams=linshiInfo;
                  this.setOutCallMonetOrderData(this.outCallMonetOrderData);
                  this.newChatApi({
                    inputType: "0",
                    type: "1",
                    changeFlowPhoneNumber: frontFuncRespData["serialNumber"],
                    textInput: "智能移送宽甩单",
                    notifyFlag: "",
                  });

                }

              } else {
                console.log('未找到名为 ' + data.output + ' 的函数，请联系管理员');
              }

          if(!this.isEmpty(this.curTask.taskId)){
            if("加装副卡"==this.curTask.taskName){
              this.saveCacheData();
            }
          }
            this.scrollToBottom()
        },
        // output处理--界面
        outputProcessOfInterface(data) {
            // 1.获取页面路径和入参 todo-wq
            // 2.页面跳转 todo-wq
        },
      saveCacheData(){
        this.jzfkOrderData.curTask.taskId=this.curTask.taskId
        this.jzfkOrderData.curTask.toolCode=this.curTask.toolCode
        this.jzfkOrderData.curTask.taskName=this.curTask.taskName
        this.jzfkOrderData.chatList=this.chatList
        this.setJzfkOrderData(this.jzfkOrderData)
        let req1 = {
          operatorId: this.shbmMsgInfo.operatorId,
          jzfkOrderData:JSON.stringify(this.jzfkOrderData)
        }
        this.$http.post('/contineBreak/saveData',req1).then(res=>{
          this.endLoading();
        });
      },
        // 流程异常处理
        flowExceptionProcess(errorMsg) {
         this.chatLoadingAsis=true;
          let req={content:errorMsg}
          this.$http.post('/smartFlowChat/oralAssistant', req).then(res => {
            this.chatLoadingAsis=false;

            if(Object.keys(res.respData).length!=0){
           
              if(!this.isEmpty(res.respData.assistantMsg)){
                this.updateChatList({
                  sender: '1',
                  type: 'module',
                  moduleName: 'ConfirmBox',
                  moduleLevel: 1,
                  params: {
                    msg: `智能小助手出了点小差。请稍后点击操作`,
                    confirmText: '重试',
                    cancelText: '终止',
                    failureReply: 1,
                    confirm: ()=> {
                      // 获取上一次提问的参数
                      // 重新调用对话接口
                      this.newChatApi({...this.chatApiParam})
                    },
                    cancel: ()=> {
                      this.terminateTask('04')
                    },
                  },
                  show: true
                })
              }
              else{
               
                this.updateChatList({
                  sender: '1',
                  type: 'module',
                  moduleName: 'ConfirmBox',
                  moduleLevel: 1,
                  params: {
                    msg: `智能小助手出了点小差。请稍后点击操作`,
                    confirmText: '重试',
                    cancelText: '终止',
                    failureReply: 1,
                    confirm: ()=> {
                      // 获取上一次提问的参数
                      // 重新调用对话接口
                      this.newChatApi({...this.chatApiParam})
                    },
                    cancel: ()=> {
                      this.terminateTask('04')
                    },
                  },
                  show: true
                })
              }
            }
            else{
            
              this.updateChatList({
                sender: '1',
                type: 'module',
                moduleName: 'ConfirmBox',
                moduleLevel: 1,
                params: {
                  msg: `智能小助手出了点小差。请稍后点击操作`,
                  confirmText: '重试',
                  cancelText: '终止',
                  failureReply: 1,
                  confirm: ()=> {
                    // 获取上一次提问的参数
                    // 重新调用对话接口
                    this.newChatApi({...this.chatApiParam})
                  },
                  cancel: ()=> {
                    this.terminateTask('04')
                  },
                },
                show: true
              })
            }
            if(!this.isEmpty(this.curTask.taskId)){
              if("加装副卡"==this.curTask.taskName){
                this.saveCacheData();
              }
            }
          })
        },
      
      optiStatement(content){
        content =content.replace(/[^\d\u4e00-\u9fa5\p{P}\s-]/gu, '');
      this.chatLoadingAsis=true;
        let req={
          content:this.isEmpty(content)?'返回为空数据':content
        }
        this.$http.post('/smartFlowChat/oralAssistant', req).then(res => {
          this.chatLoadingAsis=false;
          if(Object.keys(res.respData).length!=0){
            if(!this.isEmpty(res.respData.assistantMsg)){
              console.log(this.chatLoading+"=============1")
              this.updateChatList({
                sender: '1',
                type: 'module',
                moduleName: 'TextResponse',
                moduleLevel: 1,
                failureReply: 1,
                params: {
                  text: res.respData.assistantMsg
                },
                show: true
              })
            
            }
            else{
             
              this.updateChatList({
                sender: '1',
                type: 'module',
                moduleName: 'TextResponse',
                moduleLevel: 1,
                failureReply: 1,
                params: {
                  text: content
                },
                show: true
              })
              
            }
          }
          else{
           
            this.updateChatList({
              sender: '1',
              type: 'module',
              moduleName: 'TextResponse',
              moduleLevel: 1,
              failureReply: 1,
              params: {
                text: content
              },
              show: true
            })
           
          }
         
          if(!this.isEmpty(this.curTask.taskId)){
            if("加装副卡"==this.curTask.taskName){
              this.saveCacheData();
            }
          }
         
          this.scrollToBottom();
          

        })
      },

      // 意图扩大
        intentionExpansion(intentionExpandInfo = [], duplicateFlag = '0') {
            // 若是意图变更，则更新最近的一个sender:0的chat组件内的taskId
            const { type = '01', taskName = '', taskId = '', toolList = []} = intentionExpandInfo[0] || {}  // type: 01任务 02工作流节点
            if (taskId) {
                const index = (this.chatList || []).slice().reverse().findIndex(v => v.sender === '0')
                if (index != -1) {
                    this.chatList[this.chatList?.length - 1 - index].taskId = taskId
                }
            }
            const msg = duplicateFlag !== '0' ? `是否继续进行${taskName}。 ` : `当前正在进行${this.curTask.taskName || ''}, 是否切换到${taskName}。 `;
            this.updateChatList({
                sender: '1',
                type: 'module',
                moduleName: 'ConfirmBox',
                moduleLevel: 1,
                params: {
                    msg: `${msg}请点击按钮进行相关操作`,
                    confirmText: '确认',
                    cancelText: '取消',
                    confirm: ()=> {
                        const {backNodeId = '', toolCode = '', paramList = []} = toolList[0] || {}
                        // 判断是01任务切换还是02工作流节点切换、取得当前参数
                        if (type=='01') {
                            this.switchTask({taskName, taskId, toolCode, paramList}, duplicateFlag)
                            return
                        }
                        // 重新调用对话接口
                        this.newChatApi({
                            ...this.chatApiParam,
                            taskId,
                            taskName,
                            inputType:"0",
                            textInput:"好的",
                            type:"1",
                            nodeCode: backNodeId
                        })
                    },
                    cancel: ()=> {
                        if (duplicateFlag === '1' || duplicateFlag === '2') {
                            this.getRunTimeTaskList(true, true)
                        } else {
                            // 取到当前任务的最后一次反馈组件重新渲染
                            const chat = this.chatList.slice().reverse().find(v => v.sender === '1' && v.moduleName != 'ConfirmBox' && v.moduleName != 'TaskSelect' && v.moduleName != 'ErrorInfo' && v.taskId == this.curTask.taskId)
                            if (chat) {
                                this.updateChatList({
                                    ...chat,
                                    disabled: false,
                                })
                            } else {
                                this.updateChatList({
                                    sender: '1',
                                    type: 'module',
                                    moduleName: 'TextResponse',
                                    moduleLevel: 1,
                                    params: {
                                        text: '请重新输入信息'
                                    },
                                    show: true
                                })
                            }
                        }
                    },
                },
                show: true
            })
        },
        // 任务终止
        terminateTask(status) {
          this.deleteCacheData()
            if (!this.curTask?.taskId) return
            const params = {
                agentSessionId: this.agentSessionId,
                taskId: this.curTask?.taskId,
                taskStatus: status
            }
            var qs = require('qs');
            this.$http.post('/smartFlowChat/updateTaskStatus', params).then(res => {
                if (res.respCode === '0000') {
                    // 更新缓存的任务列表
                    this.setCurTask({})
                    this.getRunTimeTaskList(true, true)
                } else {
                    // 展示重试
                    this.updateChatList({
                        sender: '1',
                        type: 'module',
                        moduleName: 'ConfirmBox',
                        moduleLevel: 1,
                        params: {
                            msg: `${res.rspMessage || res.detail || res.respDesc || ''}。 请点击按钮进行相关操作`,
                            confirmText: '重试',
                            hiddenCancelButton: true,
                            confirm: ()=> {
                                this.terminateTask(status)
                            },
                        },
                        show: true
                    })
                }
            }).catch(e => {
                alertError({
                    title: '出错了！',
                    message: e,
                    confirmButtonText: '报告错误'
                });
            });
        },
        // 任务切换
        switchTask({ taskName = '', taskId = '', toolCode = '', paramList=[] }, duplicateFlag = '0') {
            // console.log('switchTask toolCode', toolCode);
            // 暂停当前任务-启动新任务-更新当前任务缓存-调用newChatApi
            this.updateTask('', '02').then(res => {
                if (res.respCode === '0000') {
                    // 启动新任务
                    this.updateTask(taskId, '01').then(res1 => {
                        if (res1.respCode === '0000') {

                            this.setCurTask({
                                taskId,
                                taskName,
                                taskStatus: '01',
                                toolCode,
                            });

                            this.updateChatList({
                                sender: 'divideLine',
                                text: `以下为${taskName}`,
                                show: true
                            });

                            // 重新调用对话接口：融合变更
                          if (duplicateFlag === '2') {
                                const params = (this.chatList || []).slice().reverse().find(v => v.sender === '1' && v.moduleName != 'ConfirmBox' && v.moduleName != 'TaskSelect' && v.moduleName != 'ErrorInfo' && v.taskId == taskId) || null
                                if (params) {
                                    this.updateChatList(params)
                                } else {
                                    // 取不到组件信息则渲染：请重新输入信息
                                    this.sendDefaultMsg()
                                }
                            } else {
                              this.chatApiParam.inputType="0";
                                this.newChatApi({
                                    ...this.chatApiParam,
                                    taskId,
                                    taskName,
                                    toolCode,
                                    inputType:"0",
                                    textInput:"好的",
                                    type:"1",
                                    workflow: JSON.stringify({
                                        paramList: paramList?.length ? paramList : [],
                                    })
                                }, null, null, null, false)
                            }
                        } else {
                            this.$toast.fail(res1.respMsg)
                        }
                    }).catch(e => {
                        alertError({
                            title: '出错了！',
                            message: e,
                            confirmButtonText: '报告错误'
                        });
                    });
                } else {
                    this.$toast.fail(res.respMsg)
                }
            }).catch(e => {
                alertError({
                  title: '出错了！',
                  message: e,
                  confirmButtonText: '报告错误'
                });
                this.loading = false;
            });
        },
        // 发送默认信息
        sendDefaultMsg() {
            this.updateChatList({
                sender: '1',
                type: 'module',
                moduleName: 'TextResponse',
                moduleLevel: 1,
                params: {
                  text: '请重新输入信息'
                },
                show: true
            })
        },
        // 更新任务状态
        updateTask(taskId, status) {
            const params = {
                agentSessionId: this.agentSessionId,
                taskId: taskId || this.curTask?.taskId || '',
                taskStatus: status
            }
            if (!params.taskId) return
            var qs = require('qs');
            return this.$http.post('/smartFlowChat/updateTaskStatus', params)
        },
        // 获取运行时列表 isPushModule: 是否往对话框内push任务选择组件
        getRunTimeTaskList(isPushModule=false, showNoTaskTip=false) {
            const params = {
                agentSessionId: this.agentSessionId,
                taskId: this.curTask?.taskId || ''
            }
            var qs = require('qs')
            if (isPushModule) {
                this.startLoading()
            }
            this.$http.post('/smartFlowChat/queryRuntimeList', params).then(res => {
                this.endLoading()
                if (res.respCode === '0000') {
                    this.setRunTimeTaskList(res.respData || [])
                    if (isPushModule && res.respData?.length) {
                        // todo-lgc 渲染任务选择组件
                        this.updateChatList({
                            sender: '1',
                            type: 'module',
                            moduleName: 'TaskSelect',
                            moduleLevel: 1,
                            params: {
                            },
                            show: true
                        })
                    } else if (res.respData?.length === 0 && showNoTaskTip) {
                        this.updateChatList({
                        sender: '1',
                        type: 'module',
                        moduleName: 'TextResponse',
                        moduleLevel: 1,
                        params: {
                            text: '当前没有进行中的任务，可以重新输入您想要办理的业务~'
                        },
                        show: true
                        })
                    }
                }
            })
        },
        // 新增系统消息回复
        newSysChat(data) {
            if (data.sender === '1') {
                this.updateChatList(data)
                this.scrollToBottom()
            } else {
                this.updateChatList(data)
                this.scrollToBottom()
            }
        },
        showComponent(item) {
            item.show = true
            if (this.toPushRunTimeTaskList) {
                this.getRunTimeTaskList(true)
                this.toPushRunTimeTaskList = false
            }
            this.scrollToBottom()
        },
      isEmpty(value) {
        let flag = false
        if (value == '' || value == 'undefined' || value == undefined || value == null || value == 'null') {
          flag = true
        }
        return flag
      },
      deleteCacheData(){
        this.$http.post('/contineBreak/deleteData',{operatorId:this.shbmMsgInfo.operatorId}).then(res=>{
          return new Promise((resolve, reject) => {
            localStorage.removeItem(this.shbmMsgInfo.operatorId+"_jzfk_agentHisSessionId")
            return resolve();
          })
        })
      },
      deleteComponent(error) {
            this.deleteLast();
            this.updateChatList({
                sender: '1',
                type: 'module',
                moduleName: 'TextResponse',
                moduleLevel: 1,
                params: {
                    text: error
                },
                show: true
            })
        },
        scrollToBottom() {
            this.$nextTick(() => {
              let el = this.$parent.$refs.containertotal
              el.scrollTo({ top: el.scrollHeight, behavior: 'smooth'})
            })
        },
        startLoading(text) {
            this.loadingText = text ?? ''
            this.showLoading()
            this.setRobotWorking(true)
            this.scrollToBottom()
        },
        endLoading() {
           this.hideLoading()
            this.setRobotWorking(false)
            this.scrollToBottom()
        },
        robotError(res) {
            this.updateChatList({
                sender: '1',
                type: 'module',
                moduleName: 'TextResponse',
                moduleLevel: 1,
                params: {
                    text: JSON.stringify(res)
                },
                show: true
            })
        },
        toNextStep(data) {
            setTimeout(() => {
                this.startLoading()
                this.scrollToBottom()
                setTimeout(() => {
                    this.endLoading()
                    let step = this.flowStep + 1
                    this.setFlowStep(step, false)
                    this.updateChatList({
                        sender: '1',
                        type: 'module',
                        moduleName: this.mixChangeStepList[step],
                        moduleLevel: 1,
                        params: data.params ?? {},
                        show: false
                    })
                    this.scrollToBottom()
                }, 1000)
            }, getSentenceTime(data.tip ?? ''))
        },
        clickMenu(menu) {
            this.$emit('clickMenu', menu)
        }
    }
};
</script>

<style lang="scss" scoped>
.chart-container {
  //position: absolute;
  //height: calc(100% - 90px);
  //overflow-y: auto;
  padding: 70px 10px 150px 10px;
  //background-color: #f2f2f2;
  //border-radius: 20px 20px 0 0;
  //bottom: 60px;
  width: 100%;
  box-sizing: border-box;
}

.audio-recognizing {
  width: 100%;
  text-align: center;

  .text {
    display: flex;
    align-items: center;
    justify-content: center;
    span{
      color: #77cbff;
      font-size: 13px;
    };
    img {
     width: 80px;
     margin-left: -7px;
    }
  }
}
.chat-recognizing {
  width: 100%;
  text-align: center;
  img {
    width: 80px;
  }

  div {
    position: relative;
    top: -48px;
    color: #77cbff;
    font-size: 13px;
  }
}
</style>