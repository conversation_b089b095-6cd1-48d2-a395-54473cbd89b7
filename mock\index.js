import mockDemo from './mockDemo';

const mock = [
    ...mockDemo
];

const getMockUrl = function(url) {
    let result = null;
    const mocks = mock.find(x => x.url === url);
    if (mocks && mocks.isMock) {
        if (mocks.useBasePath && mocks.mockUrlAuto) {
            result = mocks.mockUrlAuto;
        } else if (mocks.mockUrl) {
            result = mocks.mockUrl;
        }
    }
    return result;
};

export default getMockUrl;
