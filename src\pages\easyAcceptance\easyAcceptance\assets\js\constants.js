// 开关类型
export const SWITCH_TYPE = {
    COUNTRY: '0', // 全国
    PROVINCE: '1', // 省分
    EPARCHY: '2' // 地市
};

// 办理类型（上传照片标识）
export const HANDLING_TYPE = {
    MOBILE: 1, // 主卡
    OTHER_MOBILE: 2, // 副卡
    BROAD: 3, // 宽带
    FIX_NUMBER: 4, // 固话
    IPTV: 5, // IPTV
    NETV: 6 // NETV
};

// 办理方式
export const HANDLE_METHOD = {
    XZ: 0, // 新装
    XR: 1, // 携入（携转）
    NR: 2 // 纳入
};

// 认证类型
export const CUST_TYPE = {
    CUST: 0, // 客户
    AGENT: 1 // 代办人
};

// 商品角色标识
export const ROLE_ID = {
    '-1': '定制商品',
    '0': '主卡',
    '1': '副卡',
    '2': '融合主套餐',
    '3': '宽带',
    '4': 'IPTV',
    '5': '机顶盒',
    '6': 'ONU光猫',
    '8': '主卡附加',
    '9': '定制宽带',
    '11': '固话',
    '13': '资费(服务)成员',
    '14': '附加商品',
    '15': 'CBSS营销活动',
    '17': '沃家神眼服务包',
    '21': 'IPTV主产品',
    '24': '宽带附加',
    '25': '宽带营销活动',
    '26': 'NETV',
    '27': 'NETV设备',
    '-15': '合约活动', //通过关系型查询的，临时定义
    '28': '共享固话',
    '-14': '附加商品', //通过关系型查询的，临时定义
    '-24': '附加商品', //通过关系型查询的，临时定义 宽带合约活动
    '-25': '合约活动' //通过关系型查询的，临时定义 宽带合约活动
};

// 业务场景值
export const BUSINESS_SCENE_VALUE = {
    NEW_BROAD: 'newBroad', // 宽带新装
    INTO_BROAD: 'intoBroad', // 宽带纳入
    NEW_MOBILE: 'newMobile', // 移网新装
    INTO_MOBILE: 'intoMobile', // 移网纳入
    XR_MOBILE: 'xrMobile', // 移网携入
    NEW_VICE_MOBILE: 'newViceMobile', // 副卡新装
    INTO_VICE_MOBILE: 'intoViceMobile', // 副卡纳入
    XR_VICE_MOBILE: 'xrViceMobile', // 副卡携入
    NEW_FIX: 'newFix', // 固话新装
    INTO_FIX: 'intoFix', // 固话纳入
    NEW_IPTV: 'newIptv', // IPTV新装
    INTO_IPTV: 'intoIptv', // IPTV纳入
    NEW_NETV: 'newNetv', // NETV新装
    INTO_NETV: 'intoNetv', // NETV纳入
    PLUS_COMM: 'plusComm' //附加商品
};

/**
 * 例：{ flagName: 'demoSwitch', dataId: null, funName: 'demoSwitch', switchType: SWITCH_TYPE.COUNTRY }
 * flagName: 接口返回后，取值时的key值，如：res.respData.demoSwitch
 * dataId: 系统管理权限ID，没有填 null
 * funName: 开关名
 * switchType: 开关查询类型 SWITCH_TYPE
 *
 * flagName与funName尽量保持一致
 *
 * @type {[{switchType: string, dataId: string, flagName: string, funName: string}]}
 */
export const SWITCH_LIST = [
    // 大范围选号标识
    { flagName: 'niFlagSwitch', dataId: null, funName: 'niFlagSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 贵州商品人员校验开关
    { flagName: 'productStaffSwitch', dataId: null, funName: 'productStaffSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 局向端局分省配置，1：不传
    { flagName: 'AreaAndPointExchIdSwitch', dataId: null, funName: 'AreaAndPointExchIdSwitch', switchType: SWITCH_TYPE.PROVINCE },
    //  重庆宽带下单orderAttr属性传INSTALL_AREA和AREA_EXCH_ID
    { flagName: 'areaExchIdCityCodeByConf', dataId: null, funName: 'areaExchIdCityCodeByConf', switchType: SWITCH_TYPE.PROVINCE },
    // 需要传局向编码的配置为1
    { flagName: 'exchCodeSwitch', dataId: null, funName: 'exchCodeSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 选址在选号前开关
    { flagName: 'qryAddrBChooseNumber', dataId: null, funName: 'qryAddrBChooseNumber', switchType: SWITCH_TYPE.PROVINCE },
    // 固网选号需要传标准地址
    { flagName: 'addressIdFixSwitch', dataId: null, funName: 'addressIdFixSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 地域标识
    { flagName: 'townFlagSwitch', dataId: null, funName: 'townFlagSwitch', switchType: SWITCH_TYPE.COUNTRY },
    { flagName: 'qryProductShowSwitch', dataId: null, funName: 'qryProductShowSwitch', switchType: SWITCH_TYPE.COUNTRY },
    // 金融分期省份开关
    { flagName: 'financialByProvince', dataId: null, funName: 'financialByProvince', switchType: SWITCH_TYPE.PROVINCE },
    // 商品自定义排序_置顶商品_天津
    { flagName: 'specialSortForProvComm', dataId: null, funName: 'specialSortForProvComm', switchType: SWITCH_TYPE.PROVINCE },
    //查询开关和系统管理分配的权限
    { flagName: 'shoppingCartRight', dataId: 'RT_shopping_cart_right', funName: '', switchType: SWITCH_TYPE.PROVINCE },
    // 常态化资源预判全业务接口控制返回接入方式信息
    { flagName: 'brandlistAccessListSwitch', dataId: null, funName: 'brandlistAccessListSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 副卡纳入号码开关
    { flagName: 'viceCardNrSwitch', dataId: null, funName: 'viceCardNrSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 共线固话使用宽带除接入方式外信息
    { flagName: 'qryFixAddrByKdAddr', dataId: null, funName: 'qryFixAddrByKdAddr', switchType: SWITCH_TYPE.PROVINCE },
    // 是否展示阅读并同意协议
    { flagName: 'contractsDisplaySwitch', dataId: null, funName: 'contractsDisplaySwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 详细地址开关
    { flagName: 'installationAddressDetail', dataId: null, funName: 'installationAddressDetail', switchType: SWITCH_TYPE.PROVINCE },
    // 商品受理融合商品选择保存开关
    { flagName: 'openAccountForMixSwitch', dataId: null, funName: 'openAccountForMixSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 商品受理-融合前置校验，山西二级校验
    { flagName: 'orderPreCheckSwitch', dataId: null, funName: 'orderPreCheckSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 分期费用开关，值为true时，分期费用项不加到总费用中
    { flagName: 'ifNotNeedfenqifeeSwitch', dataId: null, funName: 'ifNotNeedfenqifeeSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 辽宁、湖北固话选号模糊匹配
    { flagName: 'qryLineNumByNC', dataId: null, funName: 'qryLineNumByNC', switchType: SWITCH_TYPE.PROVINCE },
    // 配件违约金开关
    { flagName: 'liquidatedDamagesSwitch', dataId: null, funName: 'liquidatedDamagesSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 查询同客客户认证校验开关
    { flagName: 'cardNoAndPsptIdSwitch', dataId: null, funName: 'cardNoAndPsptIdSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 湖北强制实名开关
    { flagName: 'forceRealName', dataId: null, funName: 'forceRealName', switchType: SWITCH_TYPE.PROVINCE },
    // 查询沃家组网开关
    { flagName: 'qrywojiaproductswitch', dataId: null, funName: 'qrywojiaproductswitch', switchType: SWITCH_TYPE.PROVINCE },
    // 查询装维工号的开关
    { flagName: 'installStaffidSwitch', dataId: null, funName: 'installStaffidSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 区局的处理开关
    { flagName: 'areaExchIdSwitch', dataId: null, funName: 'areaExchIdSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 选号是否传商品编
    { flagName: 'addProductIdSwitch', dataId: null, funName: 'addProductIdSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 内蒙iptv优化开关
    { flagName: 'preOrderIptvFlagSwitch', dataId: null, funName: 'preOrderIptvFlagSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 宽固融合开关
    { flagName: 'kgrhSwitch', dataId: null, funName: 'kgrhSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 吉林装维工号校验非必填开关
    { flagName: 'installStaffCheckSwitch', dataId: null, funName: 'installStaffCheckSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 安徽更换装机信息按钮展示开关
    { flagName: 'preOrderKdAdressChangeSwitch', dataId: null, funName: 'preOrderKdAdressChangeSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 协议拍照开关
    { flagName: 'protocolPhotoSwitch', dataId: null, funName: 'protocolPhotoSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 展示金融礼包
    { flagName: 'financialGiftPackage', dataId: null, funName: 'financialGiftPackage', switchType: SWITCH_TYPE.PROVINCE },
    // 集团支撑收入归集
    { flagName: 'dealReveCollGrpForGrp', dataId: null, funName: 'dealReveCollGrpForGrp', switchType: SWITCH_TYPE.PROVINCE },
    // 副卡数量校验开关
    { flagName: 'otherCardnumCheck', dataId: null, funName: 'otherCardnumCheck', switchType: SWITCH_TYPE.PROVINCE },
    // 集团和个人用户合账开关
    { flagName: 'allUserAcctSwitch', dataId: null, funName: 'allUserAcctSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 宽固共线，同步删除开关
    { flagName: 'collinearDeleteSwitch', dataId: null, funName: 'collinearDeleteSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 身份证移网开户风险分级开关
    { flagName: 'selectNumPsptLevelCheck', dataId: null, funName: 'selectNumPsptLevelCheck', switchType: SWITCH_TYPE.PROVINCE },
    // 上海呼叫转移后不弹首月生效方式
    { flagName: 'notShowFirstModeSwitch', dataId: null, funName: 'notShowFirstModeSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 广电宽带受理
    { flagName: 'brandB8WLimitSwitch', dataId: null, funName: 'brandB8WLimitSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 多移网优化开关
    { flagName: 'multiMobileOptimizeSwitch', dataId: null, funName: 'multiMobileOptimizeSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 金融分期备注必填开关
    { flagName: 'orderRemarkMustFillSwitch', dataId: null, funName: 'orderRemarkMustFillSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 公众政企开关
    { flagName: 'GroupOpenAccountMixH5Switch', dataId: null, funName: 'GroupOpenAccountMixH5Switch', switchType: SWITCH_TYPE.PROVINCE },
    // 公众政企开关2
    { flagName: 'groupAccountAlertPrivilege', dataId: 'groupAccountAlertPrivilege', funName: '', switchType: SWITCH_TYPE.PROVINCE },
    // 开关打开，提示“请选择账户”，选择已有账户或新建账户
    { flagName: 'ignoreGroupAcctNumSwitch', dataId: null, funName: 'ignoreGroupAcctNumSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 活体开关
    { flagName: 'staffLivingSwitch', dataId: null, funName: 'staffLivingSwitch', switchType: SWITCH_TYPE.PROVINCE },
    //湖南加装宽带商品开关
    { flagName: 'addBroadbandProducts', dataId: null, funName: 'addBroadbandProducts', switchType: SWITCH_TYPE.PROVINCE },
    //使用紧耦合金融分期组件
    { flagName: 'tightCouplinSwitch', dataId: null, funName: 'tightCouplinSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // iptv资费机顶盒同时选择开关
    { flagName: 'iptvAndSettopboxSelect', dataId: null, funName: 'iptvAndSettopboxSelect', switchType: SWITCH_TYPE.PROVINCE },
    //受理手册展示按钮
    { flagName: 'acceptAPIShowSwicth', dataId: null, funName: 'acceptAPIShowSwicth', switchType: SWITCH_TYPE.PROVINCE },
    //一键生产报错优化开关
    { flagName: 'cpMixBusinessInfoSwitch', dataId: null, funName: 'cpMixBusinessInfoSwitch', switchType: SWITCH_TYPE.PROVINCE },
    //自动选择商品
    { flagName: 'autoChooseProduct', dataId: null, funName: 'autoChooseProduct', switchType: SWITCH_TYPE.PROVINCE },
    //当开关打开时当用户选择IPTV时，
    // 默认选择机顶盒（只有一个时）
    // 用户有宽带或者固话时，
    // ONU则默认选中（只有一个时）
    { flagName: 'broadbandAndIPTVSwitch', dataId: null, funName: 'broadbandAndIPTVSwitch', switchType: SWITCH_TYPE.PROVINCE },
    //固网ONU开关
    { flagName: 'fixedonuSwitch', dataId: null, funName: 'fixedonuSwitch', switchType: SWITCH_TYPE.PROVINCE },
    //合约重复选择全国开关
    { flagName: 'isContractPorductLimit', dataId: null, funName: 'isContractPorductLimit', switchType: SWITCH_TYPE.PROVINCE },
    //前置商品列表
    { flagName: 'mustForwardProductList', dataId: null, funName: 'mustForwardProductList', switchType: SWITCH_TYPE.COUNTRY },
    //产商品标签处理配置
    { flagName: 'labelUseCompositionSwitch', dataId: null, funName: 'labelUseCompositionSwitch', switchType: SWITCH_TYPE.PROVINCE },
    //校园标签
    { flagName: 'schooleLabelId', dataId: null, funName: 'schooleLabelId', switchType: SWITCH_TYPE.PROVINCE },
    //天津88867
    { flagName: 'schoolBroadComm', dataId: null, funName: 'schoolBroadComm', switchType: SWITCH_TYPE.PROVINCE },
    //错误弹窗
    { flagName: 'showQuestionCollection', dataId: null, funName: 'showQuestionCollection', switchType: SWITCH_TYPE.PROVINCE },
    //显示首月资费开关
    { flagName: 'showFirstModeSwitch', dataId: null, funName: 'showFirstModeSwitch', switchType: SWITCH_TYPE.PROVINCE },
    //纳入宽带需要算费开关
    { flagName: 'nrBroadCaclFee', dataId: null, funName: 'nrBroadCaclFee', switchType: SWITCH_TYPE.PROVINCE },
    { flagName: 'caclFeeRole13', dataId: null, funName: 'caclFeeRole13', switchType: SWITCH_TYPE.PROVINCE },
    { flagName: 'caclFeeRole24', dataId: null, funName: 'caclFeeRole24', switchType: SWITCH_TYPE.PROVINCE },
    // 自由溢价开关
    { flagName: 'freePremiumSwitch', dataId: null, funName: 'freePremiumSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 减免费用前端展示为0开关
    { flagName: 'refiefFeeShowZeroSwitch', dataId: null, funName: 'refiefFeeShowZeroSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 查询宽带调测费审批开关
    { flagName: 'commissioningFeeApproveSwitch', dataId: null, funName: 'CommissioningFeeApproveSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 金融解耦费用项校验开关
    { flagName: 'stagesFeeCheckedSwitch', dataId: null, funName: 'stagesFeeCheckedSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 计算预订单费用需要开关
    { flagName: 'preOrderFeeDealSwitch', dataId: null, funName: 'preOrderFeeDealSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 订单预览开关
    { flagName: 'orderPreviewSwitch', dataId: null, funName: 'orderPreviewSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 商品白名单开关
    { flagName: 'BDCommodityWhiteList', dataId: null, funName: 'BDCommodityWhiteList', switchType: SWITCH_TYPE.PROVINCE },
    // 备注内容长度开关
    { flagName: 'controlRemarkLengthSwitch', dataId: null, funName: 'controlRemarkLengthSwitch', switchType: SWITCH_TYPE.PROVINCE },
    //主卡纳入开关
    { flagName: 'mobileRhNrSwitch', dataId: null, funName: 'mobileRhNrSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 【75029-75033】-【创新产品与安调费的依赖 ipyv 融合宽带新装】开关
    { flagName: 'innovationTestSwitch', dataId: null, funName: 'innovationTestSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 处理收入归集开关
    { flagName: 'dealRevenueCollectionGroup', dataId: null, funName: 'dealRevenueCollectionGroup', switchType: SWITCH_TYPE.PROVINCE },
    // 开关打开须校验代办关系人照片是否拍摄
    { flagName: 'agentRelationPicSwitch', dataId: null, funName: 'agentRelationPicSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 7天行程码或本地证件
    { flagName: 'showPhotoPosition', dataId: null, funName: 'showPhotoPosition', switchType: SWITCH_TYPE.PROVINCE },
    // 广东知情牌照片开关
    { flagName: 'gdClientSitePhotoSwitch', dataId: null, funName: 'gdClientSitePhotoSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 广东证明材料照片开关
    { flagName: 'gdClientProofPhotoSwitch', dataId: null, funName: 'gdClientProofPhotoSwitch', switchType: SWITCH_TYPE.EPARCHY },
    // 取消客户认证环节的一证五户报错
    { flagName: 'cancelOneCardCustAuth', dataId: null, funName: 'cancelOneCardCustAuth', switchType: SWITCH_TYPE.PROVINCE },
    //动态隐藏携转菜单
    { flagName: 'isSelectMainSwitch', dataId: null, funName: 'isSelectMainSwitch', switchType: SWITCH_TYPE.PROVINCE },
    //通过商品配置限制移网办理的功能
    { flagName: 'goodsActionLimit', dataId: null, funName: 'goodsActionLimit', switchType: SWITCH_TYPE.PROVINCE },
    // 服务密码开关
    { flagName: 'servicePassword', dataId: null, funName: 'servicePassword', switchType: SWITCH_TYPE.PROVINCE },
    // 产品规则校验开关(25022 福建-套餐档位不符合要求时无法更换需求开关)
    { flagName: 'fjProdLimitSwitch', dataId: null, funName: 'fjProdLimitSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 宽带入网时长校验开关
    { flagName: 'kdInnetDateSwtich', dataId: null, funName: 'kdInnetDateSwtich', switchType: SWITCH_TYPE.PROVINCE },
    // 正向迁移开关
    { flagName: 'forwardMigrationSwitch', dataId: null, funName: 'forwardMigrationSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 集团客户可受理开关
    { flagName: 'groupUserCheckOpenSwitch', dataId: null, funName: 'groupUserCheckOpenSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 屏蔽短信校验开关
    { flagName: 'blockSMSCheckSwitch', dataId: null, funName: 'blockSMSCheckSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 产品规则校验接口开关
    { flagName: 'qryProdLimitSwitch', dataId: null, funName: 'qryProdLimitSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 选号在客户认证之前开关
    { flagName: 'selectNumberPrepose', dataId: null, funName: 'selectNumberPrepose', switchType: SWITCH_TYPE.PROVINCE },
    // 禁止纳入号码多宽带办理
    { flagName: 'stopNrNumber', dataId: null, funName: 'stopNrNumber', switchType: SWITCH_TYPE.PROVINCE },
    // 固话选号取区局（选号使用的区局编码）
    { flagName: 'qryLineNumByAEI', dataId: null, funName: 'qryLineNumByAEI', switchType: SWITCH_TYPE.PROVINCE },
    // 非标地址开关（63889 商品受理融合固话新装非标准地址展示开关）
    { flagName: 'nonStandardGHSwitch', dataId: null, funName: 'nonStandardGHSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 下单传值区局取配置
    { flagName: 'areaExchIdCountySwitch', dataId: null, funName: 'areaExchIdCountySwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 预约时间展示时间段
    { flagName: 'bookTimeSpan', dataId: null, funName: 'bookTimeSpan', switchType: SWITCH_TYPE.PROVINCE },
    // 黑龙江融合下单固话缺少字段修复
    { flagName: 'HLJMissingFieldsSwitch', dataId: null, funName: 'HLJMissingFieldsSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 手动输入地址信息
    { flagName: 'writeAddrWithoutIntf', dataId: null, funName: 'writeAddrWithoutIntf', switchType: SWITCH_TYPE.PROVINCE },
    // 河北固话选址
    { flagName: 'siteInstallFixH5', dataId: null, funName: 'siteInstallFixH5', switchType: SWITCH_TYPE.PROVINCE },
    // 查询开关判断走不走资源预判
    { flagName: 'netResJudgeMiddleSwitch', dataId: null, funName: 'netResJudgeMiddleSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 吉林专属号码开关（59299开关）
    { flagName: 'JLExclusiveNumSwitch', dataId: null, funName: 'JLExclusiveNumSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 是否展示开关（宽带纳入同客户校验）
    { flagName: 'kdIsSameCustSwitch', dataId: null, funName: 'kdIsSameCustSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 固话虚装资费id配置
    { flagName: 'falseOutfitSwitch', dataId: null, funName: 'falseOutfitSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 预订单宽带地址资源预判开关1-资源预判，0-不资源预判
    { flagName: 'preorderResourceJudgment', dataId: null, funName: 'preorderResourceJudgment', switchType: SWITCH_TYPE.PROVINCE },
    // 意向单做资源预判转正时，端局从局向获取
    { flagName: 'pointExchIdFromMoffId', dataId: null, funName: 'pointExchIdFromMoffId', switchType: SWITCH_TYPE.PROVINCE },
    // 处理gis返回额外信息
    { flagName: 'gisExtraInfo', dataId: null, funName: 'gisExtraInfo', switchType: SWITCH_TYPE.PROVINCE },
    // 融合报错提前开关 (65027)
    { flagName: 'errorPreOptimizationSwitch', dataId: null, funName: 'errorPreOptimizationSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 老用户宽带身份证校验
    { flagName: 'oldBroadUserCheckCert', dataId: null, funName: 'oldBroadUserCheckCert', switchType: SWITCH_TYPE.PROVINCE },
    // 78693-湖南搜索与按名称排序功能开关
    { flagName: 'activitySelectionSearchSwitch', dataId: null, funName: 'activitySelectionSearchSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 是否隐藏首页客户认证按钮开关
    { flagName: 'hideMainCustBtnSwitch', dataId: null, funName: 'hideMainCustBtnSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 副卡调用分级认证接口加传属性开关
    { flagName: 'psptLevelAttrFkSwitch', dataId: null, funName: 'psptLevelAttrFkSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 副卡调用分级认证开关 (value2没用)
    { flagName: 'psptLevelFkSwitch', dataId: null, funName: 'psptLevelFkSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // IPTV必选IPTV设备开关
    { flagName: 'iptvAndIptvDeviceSwitch', dataId: null, funName: 'iptvAndIptvDeviceSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 宽带订单行是否取主卡/副卡/固话的照片信息
    { flagName: 'isKeepBroadCustSame', dataId: null, funName: 'isKeepBroadCustSame', switchType: SWITCH_TYPE.PROVINCE },
    // 金融分期不能计费后收
    { flagName: 'hasFinancialNoCharge', dataId: null, funName: 'hasFinancialNoCharge', switchType: SWITCH_TYPE.PROVINCE },
    // 融合商品副卡受理携转展示开关
    { flagName: 'judgeKgByAttr', dataId: null, funName: 'judgeKgByAttr', switchType: SWITCH_TYPE.PROVINCE },
    // 78555查询校园宽带判断开关
    { flagName: 'tjCampusBroadBandSwitch', dataId: null, funName: 'tjCampusBroadBandSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 宽带跳过上传照片
    { flagName: 'skipBroadPicSwitch', dataId: null, funName: 'skipBroadPicSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 主卡纳入前判断是否存在主副卡关系
    { flagName: 'checkZfRelBfNr', dataId: null, funName: 'checkZfRelBfNr', switchType: SWITCH_TYPE.PROVINCE },
    // 证件风险分级查询路由接口
    { flagName: 'psptLevelRouter', dataId: null, funName: 'psptLevelRouter', switchType: SWITCH_TYPE.PROVINCE },
    // 是否展示成卡
    { flagName: 'showIsChengKa', dataId: null, funName: 'showIsChengKa', switchType: SWITCH_TYPE.PROVINCE },
    // 91017 装维人员操作时取消宽带纳入
    { flagName: 'installIgnBroadNrSwitch', dataId: null, funName: 'installIgnBroadNrSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 贵州松耦合宽带校验方式变更开关
    { flagName: 'noNeedSmsCheck', dataId: null, funName: 'noNeedSmsCheck', switchType: SWITCH_TYPE.PROVINCE },
    // 80415 公众中台-联通公众-内蒙-商品受理装维人员操作增加权限控制
    { flagName: 'allChargeTypeRight', dataId: 'allChargeTypeRight', funName: '', switchType: SWITCH_TYPE.PROVINCE },
    // 91565 金融告知书开关
    { flagName: 'showFinancialSwitch', dataId: null, funName: 'showFinancialSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 89829策略弹窗展示开关
    { flagName: 'showStrategySwitch', dataId: null, funName: 'showStrategySwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 支持商品模糊查询开关
    { flagName: 'additionalGoodsSearchSwitch', dataId: null, funName: 'additionalGoodsSearchSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 融合商品副卡受理携转展示开关
    { flagName: 'isSelectOtherSwitch', dataId: null, funName: 'isSelectOtherSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 广西红黄绿等级开关
    { flagName: 'selectGXPsptLevelCheck', dataId: null, funName: 'selectGXPsptLevelCheck', switchType: SWITCH_TYPE.EPARCHY },
    // 使用默认勾选标志，0不使用默认勾选标志
    { flagName: 'useDefaultTagSwitch', dataId: null, funName: 'useDefaultTagSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 中小微商企展示开关
    { flagName: 'checkMerchantInfoForH5', dataId: null, funName: 'checkMerchantInfoForH5', switchType: SWITCH_TYPE.PROVINCE }
];

/**
 * 例：{ flagName: 'demoSwitch', dataId: null, funName: 'demoSwitch', switchType: SWITCH_TYPE.COUNTRY }
 * flagName: 接口返回后，取值时的key值，如：res.respData.demoSwitch
 * dataId: 系统管理权限ID
 * funName: 开关名
 * switchType: 开关查询类型 SWITCH_TYPE
 *
 * flagName与funName尽量保持一致
 *
 * @type {[{switchType: string, dataId: string, flagName: string, funName: string}]}
 */
export const SWITCH_DETAIL_LIST = [
    // 黑龙江选号模糊查询qrytype传0
    { flagName: 'qryNumByNumkeyHLJ', dataId: null, funName: 'qryNumByNumkeyHLJ', switchType: SWITCH_TYPE.PROVINCE },
    // 终端串号展示/隐藏开关
    { flagName: 'terminalChannelsShow', dataId: null, funName: 'terminalChannelsShow', switchType: SWITCH_TYPE.PROVINCE },
    // 修复商品号码关系,解决反复选商品号码出现的一些问题。暂时用开关控制 value2 = businessSceneValue:xrMobile|intoMobile
    { flagName: 'syncSubmitCommListSwitch', dataId: null, funName: 'syncSubmitCommListSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 活体新开关
    { flagName: 'staffLivingScenceSwitch', dataId: null, funName: 'staffLivingScenceSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 查询校园商品开关
    { flagName: 'schoolProductSwitch', dataId: null, funName: 'schoolProductSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // ngnFlag传N
    { flagName: 'ngnFlagN', dataId: null, funName: 'ngnFlagN', switchType: SWITCH_TYPE.PROVINCE },
    // 固话选号ngnflag值
    { flagName: 'ngnFlagValue', dataId: null, funName: 'ngnFlagValue', switchType: SWITCH_TYPE.PROVINCE },
    // 76053固话查询入参配置参数查询
    { flagName: 'selectFixNumberParam', dataId: null, funName: 'selectFixNumberParam', switchType: SWITCH_TYPE.PROVINCE },
    { flagName: 'gwNumberSelection', dataId: null, funName: 'gwNumberSelection', switchType: SWITCH_TYPE.PROVINCE },
    // 天津掌沃通固网业务指派本人装维，开关打开（1/2），展示“是否自装”，并设置默认选择项（1是，2否）
    { flagName: 'isSelfChambering', dataId: null, funName: 'isSelfChambering', switchType: SWITCH_TYPE.PROVINCE },
    // 共线列表开关
    { flagName: 'returnShareTypeList', dataId: null, funName: 'returnShareTypeList', switchType: SWITCH_TYPE.PROVINCE },
    //商品可多选开关
    { flagName: 'subProductMultipleChoiceSwitch', dataId: null, funName: 'subProductMultipleChoiceSwitch', switchType: SWITCH_TYPE.PROVINCE },
    //合约购机编码开关(value2多个|分割)
    { flagName: 'contractPurchaseCode', dataId: null, funName: 'contractPurchaseCode', switchType: SWITCH_TYPE.PROVINCE },
    // 电子发票备注控制开关
    { flagName: 'invoiceRemarkSwitch', dataId: null, funName: 'invoiceRemarkSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 关爱开关
    { flagName: 'guanaiCommLabelIdSwitch', dataId: null, funName: 'guanaiCommLabelIdSwitch', switchType: SWITCH_TYPE.COUNTRY },
    // 证件类型
    { flagName: 'cardTypeListForMix', dataId: null, funName: 'cardTypeListForMix', switchType: SWITCH_TYPE.PROVINCE },
    // 纳入号码客户认证校验开关
    { flagName: 'custAuthForJoinMix', dataId: null, funName: 'custAuthForJoinMix', switchType: SWITCH_TYPE.PROVINCE },
    // 政企纳入号码客户认证校验开关
    { flagName: 'governAuthForJoinMix', dataId: null, funName: 'governAuthForJoinMix', switchType: SWITCH_TYPE.PROVINCE },
    // 实际三要素开关
    { flagName: 'needRuleThreeElements', dataId: null, funName: 'needRuleThreeElements', switchType: SWITCH_TYPE.EPARCHY },
    // 查询支持的选址模式：value1 第一个表示标准选址开关  第二个表示地图选址开关，用|分隔；1表示开 0表示关
    { flagName: 'ZWT_SWITCH_ADDREXEC_SHOW', dataId: null, funName: 'ZWT_SWITCH_ADDREXEC_SHOW', switchType: SWITCH_TYPE.PROVINCE },
    // 查询支持的选址模式：value1 第一个表示标准选址开关  第二个表示地图选址开关，用|分隔；1表示开 0表示关
    { flagName: 'noFixResPreJudge', dataId: null, funName: 'noFixResPreJudge', switchType: SWITCH_TYPE.PROVINCE },
    // 共线方式不展示省分选址
    { flagName: 'showProvUlrExclShareLine', dataId: null, funName: 'showProvUlrExclShareLine', switchType: SWITCH_TYPE.PROVINCE },
    // 装机模式-省分选址查询
    { flagName: 'qryFixAddrByProvince', dataId: null, funName: 'qryFixAddrByProvince', switchType: SWITCH_TYPE.PROVINCE },
    // 查询河北固网选号关键字
    { flagName: 'qryTypeFlagValue', dataId: null, funName: 'qryTypeFlagValue', switchType: SWITCH_TYPE.PROVINCE },
    // 加副卡能否非同客户开关
    { flagName: 'isSameCustSwitch', dataId: null, funName: 'isSameCustSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 纳入方式开关，value1 0：只有号码校验 1：只有客户认证 2：都有
    { flagName: 'includeDispNumsSwitch', dataId: null, funName: 'includeDispNumsSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 其他证明材料开关
    { flagName: 'proofMaterialUploadSwitch', dataId: null, funName: 'proofMaterialUploadSwitch', switchType: SWITCH_TYPE.COUNTRY },
    // 过滤客户认证照片开关
    { flagName: 'filterPicByCardTypeSwitch', dataId: null, funName: 'filterPicByCardTypeSwitch', switchType: SWITCH_TYPE.PROVINCE },
    // 特定场景的固定费用科目勾选计费后收配置
    { flagName: 'BookUpFunctionType', dataId: null, funName: 'BookUpFunctionType', switchType: SWITCH_TYPE.PROVINCE },
    // 查询施工中收费开关
    { flagName: 'ALLCHARGETYPE', dataId: null, funName: 'ALLCHARGETYPE', switchType: SWITCH_TYPE.PROVINCE },
    // 携号转网开关（是否展示【携入号码】）
    // value1 配置10或者20进行权限控制，有权限页面才会展示【携入号码】，没有权限不会展示；其它配置默认展示。
    { flagName: 'showTakeNumberJoin', dataId: null, funName: 'showTakeNumberJoin', switchType: SWITCH_TYPE.PROVINCE },
    // 携号转网开关（是否展示【携入号码】） 河南
    // 开关value1设置1,0开关状态为开/关，value2为设置的每月失效日期
    { flagName: 'carryNoPermissionSwitch', dataId: null, funName: 'carryNoPermissionSwitch', switchType: SWITCH_TYPE.EPARCHY }
];

// 基础订单行
export const BASE_ORDER_LINE = {
    sceneType: '', // 移网新开/携转 sceneType = 200020;移网纳入 sceneType = 200021
    serialNumber: '',
    netTypeCode: '50',
    mainTag: '', //0：主卡  1：副卡
    cancelElemInfoList: [],
    currentCommId: '',
    commId: '',
    commName: '',
    cardNo: '',
    commInfoList: [],
    promInfoList: [],
    terminalInfoList: [],
    idCheckInfo: {},
    orderLineAttr: {}
};

// 移网基础订单行
export const BASE_ORDER_LINE_MPHONE = {
    ...BASE_ORDER_LINE,
    mPhoneInfo: {
        custPicInfo: {},
        agentPicInfo: {}
    }
};

// 固网基础订单行
export const BASE_ORDER_LINE_FIXED = {
    ...BASE_ORDER_LINE,
    fixedInfo: {
        custPicInfo: {},
        agentPicInfo: {}
    }
};
