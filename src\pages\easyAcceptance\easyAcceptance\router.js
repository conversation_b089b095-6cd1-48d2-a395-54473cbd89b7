import Vue from 'vue';
import Router from 'vue-router';
import Main from './routerPages/Main';
// import PdfViewerContainer from '@/assets/bizComponents/PaperlessSign/PdfViewerContainer';
import { fixRouter } from './assets/js/plugin';
import OrderInfoList from "./routerPages/components/chatBox/OrderInfoList.vue";
import orderTrack from "./routerPages/components/chatBox/orderTrack.vue";
import OrderConfirmBox from "./routerPages/components/chatBox/OrderConfirmBox.vue";
import IptvSelectProduct from "./routerPages/components/chatBox/IptvSelectProduct.vue";
import IptvYwSelectProduct from "./routerPages/components/chatBox/lptvYwSelectProduct.vue";
import YskIptvProduct from "./routerPages/components/chatBox/YskIptvProduct.vue";
import YskZwSelectProduct from "./routerPages/components/chatBox/YskZwSelectProduct.vue";
import YskYwSelectProduct from "./routerPages/components/chatBox/YskYwSelectProduct.vue";
import SelectAddress from "./routerPages/components/chatBox/selectAddress.vue";
Vue.use(Router);
let router = new Router({
    routes: [
        {
            // 首页
            path: '/',
            name: 'Main',
            component: Main,
            meta: {}
        },
        {
            // 首页
            path: '/OrderInfoList',
            name: 'OrderInfoList',
            component: OrderInfoList,
            meta: {}
        },
        {
            // 首页
            path: '/OrderConfirmBox',
            name: 'OrderConfirmBox',
            component: OrderConfirmBox,
            meta: {}
        },
        {
            // 首页
            path: '/selectProduct',
            name: 'selectProduct',
            component: IptvSelectProduct,
            meta: {}
        },
        {
            // 首页
            path: '/iptvYwSelectProduct',
            name: 'iptvYwSelectProduct',
            component: IptvYwSelectProduct,
            meta: {}
        },

        {
            // 首页
            path: '/orderTrack',
            name: 'orderTrack',
            component: orderTrack,
            meta: {}
        }
        ,
        {
            // 首页
            path: '/yskYwSelectProduct',
            name: 'yskYwSelectProduct',
            component: YskYwSelectProduct,
            meta: {}
        }
        ,
        {
            // 首页
            path: '/yskIptvProduct',
            name: 'yskIptvProduct',
            component: YskIptvProduct,
            meta: {}
        }
        ,
        {
            // 首页
            path: '/yskZwSelectProduct',
            name: 'yskZwSelectProduct',
            component: YskZwSelectProduct,
            meta: {}
        },
        {
            // 首页
            path: '/selectAddress',
            name: 'selectAddress',
            component: SelectAddress,
            meta: {}
        }
    ]
});

fixRouter(router);
export default router;