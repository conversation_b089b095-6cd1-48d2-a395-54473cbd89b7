<template>
  <div >
    <div class="selectProduct" id="selectProduct">
      <div class="box" v-for="(item,index) in zwInfoList" :key="index" >
        <div>
          <div class="tag" :style="{ backgroundColor: item.color }">{{item.title}}</div>
          <span class="tag1" style="color:red" v-show="isEmpty(item.hotGoods)">{{item.hotGoods}}</span>
        </div>
        <div class="cell" v-if="!item.SHORT_NAME&&!item.RENT" />
        <div class="cell1">{{item.SHORT_NAME}}</div>
        <div class="cell2">{{item.RENT}}</div>
        <div class="cell1 ellipsis" v-if="!item.SHORT_NAME&&!item.RENT"><van-popover placement="top" v-model="item.showPopover" trigger="click">
          <van-grid
              square
              clickable
              :border="false"
              column-num="1"
              style="width: 120px; "
          >
            <van-grid-item
                style="font-weight: bold"
                :text="item.iptvName"
                @click="item.showPopover = false"
            />
          </van-grid>
          <template #reference>
            {{item.iptvName.length>30?item.iptvName.slice(0,30)+'...':item.iptvName}}
          </template>
        </van-popover></div>
        <van-button style="margin-top: 10px" class="btn" @click="goodClick(item,$event)" >{{item.itemTip}}</van-button>
      </div>
    </div>
    <div v-if="showGoodName">您当前选择的商品是：{{showGoodName}}</div>

  </div>
</template>
<script>
import {mapActions, mapMutations, mapState} from "vuex";

export default {
  name:  "BroadUpSelectProduct",
  data(){
    return {
      radio: '',
      colors:['rgb(129,211,248)','rgb(251,6,6)','#71bc8c'],
      selectedGooIds: [],
      choosedIPTV:"",
      iptvInvalid:[],
      iptvInfoCheckList:[],
      zwInfoList: [
      ],
      showGoodName:'',
    }
  },
  mounted() {
    this.broadUpOrderData.broadUpGoodData.broadUpList=[];
    this.broadUpOrderData.broadUpGoodData.commodityChooseYWUlList=[];
    this.setBroadUpOrderData(this.broadUpOrderData);
    this.zwInfoList=[];
    let colorLength=this.colors.length
    console.log(this.broadUpReProductList,"broadUpReProductList")
    if(this.broadUpReProductList.length>0){
      for(let i=0;i<this.broadUpReProductList.length;i++){
        let title='1000M'
        let speed=1000
        if(this.isEmpty(this.broadUpReProductList[i].speed)){
          title='1000M'
          speed=1000
        }
        else{
          title=this.broadUpReProductList[i].speed+'M'
          speed=this.broadUpReProductList[i].speed
        }
        let iptvInfo={
          title:title,
          speed:speed,
          ancestors:this.broadUpReProductList[i].ancestors,
          SHORT_NAME:this.broadUpReProductList[i].SHORT_NAME,
          commodityCode:this.broadUpReProductList[i].commodityCode,
          commType:this.broadUpReProductList[i].commType,
          iptvName:this.broadUpReProductList[i].iptvName,
          RENT:this.broadUpReProductList[i].RENT,
          itemTip:'立即购买',
          color:this.colors[i%colorLength],
          showPopover:false
        }
          this.zwInfoList.push(iptvInfo)
      }
    }
  },
  computed: {
    ...mapState([
      'jzfkOrderData',
      'shbmMsgInfo',
      'iptvCacheList',
      'broadUpReProductList',
      'iptvCacheList',
       'broadUpOrderData'
    ])
  },
  methods:{
    ...mapMutations([
      'setFlowStep',
      'setRobotWorking',
      'setBroadUpOrderData'
    ]),
    ...mapActions(['updateChatList']),
    isEmpty(value) {
      let flag = false
      if (value == '' || value == 'undefined' || value == undefined || value == null || value == 'null') {
        flag = true
      }
      return flag
    },
    zwClickFee(zw){
      let data = {
        commId: zw.commodityCode
      }
      this.$http.post('/broadbandUpSpeed/qryProductFee', data).then(res => {
        if (res.respCode == "0000") {
          // 接收返回参数
          let orderPrice=this.broadUpOrderData.orderPrice;
          orderPrice+=parseInt(res.respData.goodsPrice);
          this.broadUpOrderData.orderPrice=orderPrice;
          this.setBroadUpOrderData(this.broadUpOrderData)
        }
      else{
          this.$toast(res.resMsg);
        }
      }).catch(e => {
       this.$toast(e)
      })
    },
    goodClick(item,e){
      this.showGoodName = item.iptvName
      const $this = $(e.target);
      if ($this.hasClass("btn")) {
        
        $this.addClass("active-btn")
        $this.parent().addClass("red-border")
        $this.removeClass("btn")
        $this.siblings().removeClass("active-btn")
        $this.siblings().removeClass("red-border")
        // 如果未选中，则添加
        item.itemTip='已选择'
        this.broadUpOrderData.broadUpGoodData.broadUpList=[];
        this.selectedGooIds.push(item.commodityCode);
        let zw={
          commodityCode:item.commodityCode,
          commodityName:item.iptvName,
          commType:item.commType,
        kdSpeed:item.speed}
        this.broadUpOrderData.broadUpGoodData.broadUpList.push(zw);
        this.setBroadUpOrderData(this.broadUpOrderData);
        this.zwClickFee(item)
        let data = {
          inputType: "1",
          type: '1',
          textInput: "broadUpGoodSubmit",
          notifyFlag: '',
          taskName:'宽带提速智能甩单'
        }
        this.$emit('newChatApi', data);
      } else {
        $this.parent().removeClass("red-border")
        $this.removeClass("active-btn")
        $this.addClass("btn");
        item.itemTip='立即购买'
        this.selectedGooIds = this.selectedGooIds.filter(i => i !== item.commodityCode);
        this.zwClickFee(item)
      }
    }
  }
}

</script>
<style scoped lang="scss">
.red-border{
  border: rgb(221,248,255) 2px solid;
}
.selectProduct{
  display: flex;
  flex-wrap: nowrap; /* 确保子元素不换行 */
  overflow-x: scroll;  /* 启用水平滚动 */
  -webkit-overflow-scrolling: touch; /* 改善移动设备上滚动的体验 */
  .box {
    position: relative;
    border-radius: 10px;
    margin: 0 10px 10px 0;
    flex: 0 0 calc(22.6%); /* 使用 calc() 考虑 margin */
    max-width: calc(22.6%); /* 同样考虑 margin */
    background: linear-gradient(to bottom right, #fff, #EDF0FF);
    padding: 0 10px 10px 10px;
    height: 200px;
    .tag{
      float:left ;
      color: #fff;
      border-radius: 10px 0 10px 0;
      padding: 0 6px;
      font-size: 11px;
      margin-left: -10px;
      width: 60%;
      text-align: center;
    }

    .tag1{
      float:right ;
      color: red;
      font-weight: bold;
      border-radius: 10px 0 10px 0;
      padding: 0 6px;
      font-size: 11px;
      margin-left: -10px;
      width: 15%;
      text-align: center;
    }
    .cell{
      clear: both;
      margin: 10px 0;
      height: 45px;
      width: 100%;
      text-align: center;
      font-weight: bold;
      font-size: 14px;
      line-height: 18px
    }
    .cell1{
      position: absolute;
      top: 35px;
      margin: 10px 0;
      height: 45px;
      width: 81%;
      text-align: center;
      font-weight: bold;
      font-size: 14px;
      line-height: 18px
    }
    .cell2{
      position: absolute;
      top: 132px;
      margin-bottom: 10px;
      width: 81%;
      text-align: center;
      font-size: 13px;
      line-height: 19px
    }
    .btn{
      position: absolute;
      bottom: 5px;
      border: 2px solid #4494E6;
      border-radius: 10px;
      color: #4494E6;
      height: 25px;
      font-size: 11px;
      padding: 5px 8px;
      background: none;
    }
    .active-btn{
      position: absolute;
      bottom: 5px;
      border: 2px solid #4494E6;
      border-radius: 10px;
      color: #fff;
      background: #4494E6!important;
      height: 25px;
      font-size: 11px;
      padding: 5px 8px;
      background: none;
      margin-left: 10px;
    }
  }

  /* WebKit 浏览器滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  ::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

  /* Firefox 移动端浏览器 */
  .scrollable-element {
    scrollbar-width: auto;
    scrollbar-color: #888 #f1f1f1;
  }

  /* 元素样式 */
  .scrollable-element {
    width: 300px;
    height: 200px;
    overflow: auto;
    border: 1px solid #ccc;
    padding: 10px;
    -webkit-overflow-scrolling: touch;
  }
  @-webkit-keyframes showScrollbar {
    from {
      opacity: 1;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes showScrollbar {
    from {
      opacity: 1;
    }
    to {
      opacity: 1;
    }
  }
}
</style>