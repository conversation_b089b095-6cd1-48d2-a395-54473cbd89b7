const fs = require('fs');
const path = require('path');
const colors = require('colors');
const readline = require('readline');
const categories = require('./category.json');
const glob = require('glob');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});
function question(tip) {
    return new Promise((resolve) => {
        rl.question(tip, (answer) => {
            resolve(answer);
        });
    });
}
async function interactive() {
    categories.forEach((category, index) => {
        // eslint-disable-next-line no-console
        console.log(`序号：${index}  分类：${category.desc}(${category.name})`);
    });
    const categoryId = await question('请输入序号选择菜单分类:');
    const category = categories[parseInt(categoryId)];
    if (!category) {
        // eslint-disable-next-line no-console
        console.log('所选菜单不存在!');
        process.exit();
    }
    const categoryName = category.name;
    const appName = await question('请输入菜单名称：');
    if (!appName) {
        // eslint-disable-next-line no-console
        console.log(colors.red.underline('菜单名称不可以为空！'));
        process.exit();
    }
    const basePath = path.resolve(__dirname, '../src/pages');
    const hasExit = !!glob.sync(`${basePath}/*/${appName}`).length;
    if (hasExit) {
        // eslint-disable-next-line no-console
        console.log(colors.red.underline(`${appName}已存在,请换个名称！`));
        process.exit();
    }
    let needVuex = await question('是否集成vuex？(y/yes or n/no)：');
    needVuex = needVuex.includes('y');
    rl.close();
    return { categoryName, appName, needVuex };
}
interactive().then((result) => {
    const copyMap = new Map([
        ['router.js', 'router.js'],
        ['config.json', 'config.json'],
        ['App.vue', 'App.vue'],
        ['routerPages/Main.vue', 'routerPages/Main.vue']
    ]);
    const willCreatePage = result.appName;

    const category = result.categoryName;
    fs.mkdirSync(path.resolve(__dirname, `../src/pages/${category}/${willCreatePage}/components`), { recursive: true });
    fs.mkdirSync(path.resolve(__dirname, `../src/pages/${category}/${willCreatePage}/routerPages`), { recursive: true });
    if (result.needVuex) {
        fs.mkdirSync(path.resolve(__dirname, `../src/pages/${category}/${willCreatePage}/store`), { recursive: true });
        copyMap.set('index.js', 'index.js');
        copyMap.set('store/index.js', 'store/index.js');
    } else {
        copyMap.set('index_noVuex.js', 'index.js');
    }
    for (let item of copyMap) {
        const sourcePath = path.resolve(__dirname, `template/${item[0]}`);
        const targetPath = path.resolve(__dirname, `../src/pages/${category}/${willCreatePage}/${item[1]}`);
        fs.createReadStream(sourcePath).pipe(fs.createWriteStream(targetPath));
    }
});
