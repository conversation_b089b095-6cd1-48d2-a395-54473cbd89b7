<template>
  <div >
    <div class="selectProduct">
      <div class="box" :style="{border:item.isActive ? '2px solid #3498db':''}" v-for="(item,index) in commodityKDUlList" :key="index"  @click="goodClick1(item)" >
        <div class="tag" :style="{ backgroundColor: item.color }">{{item.title}}</div>
        <div class="cell" v-if="!item.SHORT_NAME&&!item.RENT" />
        <div class="cell1">{{item.SHORT_NAME}}</div>
        <div class="cell2">{{item.RENT}}</div>
        <div class="cell1 ellipsis" v-if="!item.SHORT_NAME&&!item.RENT">{{item.ywGoodName.length>20?item.ywGoodName.slice(0,20)+'...':item.ywGoodName}}</div>
        <div class="success-box" v-if="item.isActive">
          <div class="success"></div>
        </div>
      </div>
    </div>
    <div v-if="showGoodName">您当前选择的商品是：{{showGoodName}}</div>
  </div>
</template>
<script>
import {mapActions, mapMutations, mapState} from "vuex";
export default {
  name:  "YskKdCheapSelectProduct",
  data(){
    return {
      isYwfDiscnt:-1,
      ywfuInfoNoNeedIsShow:true,
      radio: '',
      colors:['rgb(129,211,248)','rgb(251,6,6)','#71bc8c'],
      commodityChooseKDFList: [],
      choosedIPTV:"",
      commodityKDUlList: [],
      showGoodName:'',
    }},
  mounted() {
    this.outCallMonetOrderData.goodData.kdfList=[];
    this.setOutCallMonetOrderData(this.outCallMonetOrderData);
    let colorLength=this.colors.length
    if(this.yskKdfCacheList.length>0){
      for(let i=0;i<this.yskKdfCacheList.length;i++){
        let iptvInfo={}
        if(i==0){
          iptvInfo={
            title:'优惠包',
            ancestors:this.yskKdfCacheList[i].ancestors,
            SHORT_NAME:this.yskKdfCacheList[i].SHORT_NAME,
            kdfjFlag:this.yskKdfCacheList[i].commId,
            ywGoodType:this.yskKdfCacheList[i].commType,
            ywGoodName:this.yskKdfCacheList[i].commName,
            RENT:this.yskKdfCacheList[i].DETAIL,
            isActive:true,
            color:this.colors[i%colorLength]}
        }else{
          iptvInfo={
            title:'优惠包',
            ancestors:this.yskKdfCacheList[i].ancestors,
            SHORT_NAME:this.yskKdfCacheList[i].SHORT_NAME,
            kdfjFlag:this.yskKdfCacheList[i].commId,
            ywGoodType:this.yskKdfCacheList[i].commType,
            ywGoodName:this.yskKdfCacheList[i].commName,
            RENT:this.yskKdfCacheList[i].DETAIL,
            isActive:true,
            color:this.colors[i%colorLength]}
        }
        this.commodityKDUlList.push(iptvInfo)
      }}
    this.showGoodName=this.commodityKDUlList[0].ywGoodName+","+this.commodityKDUlList[1].ywGoodName
    this.commodityChooseKDFList.push(this.commodityKDUlList[0]);
    this.commodityChooseKDFList.push(this.commodityKDUlList[1]);
    this.outCallMonetOrderData.goodData.kdfList=this.commodityChooseKDFList;
    this.setOutCallMonetOrderData(this.outCallMonetOrderData);
    
  },
  computed: {
    ...mapState([
      'jzfkOrderData',
      'shbmMsgInfo',
      'iptvCacheList',
      'yskKdfCacheList',
      'iptvCacheList', 'outCallMonetOrderData'])
  },
  methods:{
    ...mapMutations([
      'setFlowStep',
      'setRobotWorking',
      'setOutCallMonetOrderData'
    ]),
    ...mapActions(['updateChatList']),
    ywfNoNeed(){
      if(this.commodityChooseKDFList.length>0){
        for(let i in this.commodityChooseKDFList){
          let id=this.commodityChooseKDFList[i].kdfjFlag
          $('#'+'YWF'+id).parent().removeClass('active')
        }
      }
      
      let c=[{kdfjFlag:'0',ywGoodName:'不需要'}];
      this.outCallMonetOrderData.goodData.kdfList=c;
      this.setOutCallMonetOrderData(this.outCallMonetOrderData);
      this.commodityChooseKDFList=[];
      this.isYwfDiscnt=-1;
    },
    commodityYWUlClick(item,e) {
      // console.log(this.pageZwPrice)
      const $this = $(e.target);
      // console.log(item)
      if($this.parent().hasClass("active")){
        $this.parent().removeClass('active');
        this.commodityChooseKDFList = this.commodityChooseKDFList.filter(i=>i!==item);
        this.outCallMonetOrderData.goodData.kdfList=this.commodityChooseKDFList;
        this.setOutCallMonetOrderData(this.outCallMonetOrderData);
        console.log("this.outCallMonetOrderData.goodData.kdfList")
        console.log(this.outCallMonetOrderData.goodData.kdfList)

        if(this.commodityChooseKDFList.length>0){
          this.isYwfDiscnt=0;
        }
        else{
          this.isYwfDiscnt=-1;
          this.ywfNoNeed();
          return;
        }
        this.orderPrice=0;
        this.orderPrice = this.pageZwPrice+this.ziFeiPrice+this.pageYwfuPrice;
      }else{
        this.isYwfDiscnt=0;
        $this.parent().addClass("active");
        this.commodityChooseKDFList.push(item);
        this.outCallMonetOrderData.goodData.kdfList=this.commodityChooseKDFList;
        this.setOutCallMonetOrderData(this.outCallMonetOrderData)
        console.log("this.outCallMonetOrderData.goodData.kdfList")
        console.log(this.outCallMonetOrderData.goodData.kdfList)
        this.orderPrice=0;
        this.orderPrice = this.pageZwPrice+this.ziFeiPrice+this.pageYwfuPrice;
      }
      this.feeDesc = this.orderPrice > 0 ? "施工中收费" : " 无须支付";
    },

    goodClick1(item){
      this.showGoodName=""
      item.isActive=!item.isActive
      if (item.isActive) {
        // 如果未选中，则添加
        this.commodityChooseKDFList.push(item);
        for(let i=0;i<this.commodityChooseKDFList.length-1;i++){
          this.showGoodName+=this.commodityChooseKDFList[i].ywGoodName+","
        }
        this.showGoodName+=this.commodityChooseKDFList[this.commodityChooseKDFList.length-1].ywGoodName
        this.outCallMonetOrderData.goodData.kdfList=this.commodityChooseKDFList;
        this.setOutCallMonetOrderData(this.outCallMonetOrderData)
      } else {
        this.commodityChooseKDFList = this.commodityChooseKDFList.filter(i=>i!==item);
        if(this.commodityChooseKDFList.length>0){
          for(let i=0;i<this.commodityChooseKDFList.length-1;i++){
            this.showGoodName+=this.commodityChooseKDFList[i].ywGoodName+","
          }
          this.showGoodName+=this.commodityChooseKDFList[this.commodityChooseKDFList.length-1].ywGoodName
        }else{
          this.showGoodName="";
        }
        this.outCallMonetOrderData.goodData.kdfList=this.commodityChooseKDFList;
        this.setOutCallMonetOrderData(this.outCallMonetOrderData);
      }
    },
    
    goodClick(item,e){
      const $this = $(e.target);
      if ($this.hasClass("btn")) {
        $this.addClass("active-btn")
        $this.parent().addClass("red-border")
        $this.removeClass("btn")
        // 如果未选中，则添加
        item.itemTip='已选择'
        this.commodityChooseKDFList.push(item);
        this.outCallMonetOrderData.goodData.kdfList=this.commodityChooseKDFList;
        this.setOutCallMonetOrderData(this.outCallMonetOrderData)
      } else {
        $this.parent().removeClass("red-border")
        $this.removeClass("active-btn")
        $this.addClass("btn");
        item.itemTip='立即购买'
        this.commodityChooseKDFList = this.commodityChooseKDFList.filter(i=>i!==item);
        this.outCallMonetOrderData.goodData.kdfList=this.commodityChooseKDFList;
        this.setOutCallMonetOrderData(this.outCallMonetOrderData);
      }
    }
  }
}
</script>
<style scoped lang="scss">
.red-border{
  border: rgb(221,248,253) 2px solid;
}
.selectProduct {
  display: flex;
  flex-wrap: nowrap; /* 确保子元素不换行 */
  overflow-x: scroll; /* 启用水平滚动 */
  -webkit-overflow-scrolling: touch; /* 改善移动设备上滚动的体验 */
  .box {
    position: relative;
    border-radius: 10px;
    margin: 0 10px 10px 0;
    flex: 0 0 calc(22.6%); /* 使用 calc() 考虑 margin */
    max-width: calc(22.6%); /* 同样考虑 margin */
    background: linear-gradient(to bottom right, #fff, #EDF0FF);
    padding: 0 10px 10px 10px;
    height: 200px;

    .tag {
      color: #fff;
      border-radius: 10px 0 10px 0;
      padding: 0 6px;
      font-size: 11px;
      margin-left: -10px;
      width: 60%;
      text-align: center;
    }

    .cell {
      margin: 10px 0;
      height: 45px;
      width: 100%;
      text-align: center;
      font-weight: bold;
      font-size: 14px;
      line-height: 18px
    }

    .cell1 {
      position: absolute;
      top: 50px;
      margin: 10px 0;
      height: 45px;
      width: 81%;
      text-align: center;
      font-weight: bold;
      font-size: 14px;
      line-height: 18px
    }

    .cell2 {
      position: absolute;
      top: 120px;
      margin-bottom: 10px;
      width: 81%;
      text-align: center;
      font-size: 13px;
      line-height: 19px
    }

    .btn {
      position: absolute;
      bottom: 5px;
      border: 2px solid #4494E6;
      border-radius: 10px;
      color: #4494E6;
      height: 25px;
      font-size: 11px;
      padding: 5px 8px;
      background: none;
    }

    .active-btn {
      position: absolute;
      bottom: 5px;
      border: 2px solid #4494E6;
      border-radius: 10px;
      color: #fff;
      background: #4494E6 !important;
      height: 25px;
      font-size: 11px;
      padding: 5px 8px;
      background: none;
      margin-left: 10px;
    }
    .success-box{
      position: absolute;
      bottom: 0;
      right: 0;
      .success {
        position: relative;
        width: 25px;
        height: 25px;
        background-image: url('@/assets/images/choosePhoto.png');
        background-position: center center;
        background-repeat: no-repeat;
        background-size: cover;
        clip-path: polygon(0 100%, 100% 0, 100% 100%);
        border-bottom-right-radius: 8px;
      }

    }

  }
}
</style>