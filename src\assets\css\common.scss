:root {
    --blue: #3489FC;
    --red: #FB4940;
    --green: #4CB64A;
    --orange: #FF6024;
    --yellow: #FAAD14;
    --blueBg: #ebf4ff;
}

.blue { color: var(--blue) !important; }
.red { color: var(--red) !important; }

.ml5 { margin-left: 5px !important; }
.ml10 { margin-left: 10px !important; }
.mt5 { margin-top: 5px !important; }
.mt10 { margin-top: 10px !important; }
.mt15 { margin-top: 15px !important; }
.mb5 { margin-bottom: 5px !important; }
.pb15 { padding-bottom: 15px !important; }

.font13 { font-size: 13px !important; }
.font14 { font-size: 14px !important; }
.font22 { font-size: 22px !important; }
.font16 { font-size: 16px !important; }
.font18 { font-size: 18px; }
.font20 { font-size: 20px; }

.bold { font-weight: bold !important; }

.underline { text-decoration: underline; }

// .van-tabs__line {
//     background-color: var(--blue);
// }

// .van-tab--active {
//     color: var(--blue);
//     font-weight: 550;
// }

.van-button--primary {
    background-color: var(--blue);
    border: 1px solid var(--blue);
}

.van-button--block {
    border-radius: 5px;
    height: 40px;
    font-size: 18px;
}

.van-dialog__confirm, .van-dialog__confirm:active {
    color: #ffffff !important;
}

.van-dialog__footer .van-button {
    font-size: 16px;
}

.van-cell {
    padding: 15px 0;

    &:after {
        border-bottom: 1px solid #C7C7C7;
        left: 0;
        right: 0;
    }
}

.van-cell__right-icon {
    line-height: 30px;
}

.van-cell.border {
    border-bottom: 1px solid #C7C7C7;
}

// .van-button--block {
//     font-size: 16px;
// }

// .clearfix:after {
//     content: '';
//     height: 0;
//     display: block;
//     clear: both;
//     visibility: hidden;
// }

.van-radio-group--horizontal {
    -webkit-box-pack: end;
    -webkit-justify-content: flex-end;
    justify-content: flex-end;
}

.van-button--plain.van-button--primary {
    color: var(--blue);
    background-color: #ffffff;
    border-color: var(--blue);
}

.van-toast {
    z-index: 9999!important;
}

.van-tabs__line {
    background-color: var(--blue);
}