import '@/assets/js/globalConfig.js';
import Vue from 'vue';
import Vant from 'vant';
import RUX<PERSON> from 'rux-ui-v';
import 'rux-ui-v/dist/rux-ui-v.css';
import App from './App.vue';
import router from './router';
import store from './store/index';
import comComp from '@/assets/commonComponents';
import axios from '@/assets/js/axios.js';
import sensitive from '@/assets/js/sensitive.js';
import VueClipboard from 'vue-clipboard2'
import './assets/js/iconfont'
// import echarts from 'echarts';

Vue.use(Vant);
Vue.use(RUXUI);
Vue.use(comComp);
Vue.use(VueClipboard);
Vue.prototype.$http = axios;
Vue.prototype.sensitive = sensitive;
// Vue.prototype.$echarts = echarts;
// 项目服务地址
Vue.prototype.envIP = 'https://wappay.easybss.com/woappjlview/jlzwtaih5/easyAcceptance.html#/'
Vue.prototype.mixChangeStepList = [
    'chatStart',
    'numCheck',
    'custAuth',
    'mixRecommendation',
    'userSelect',
    'changeChildPackage',
    'addSecondBroadband',
    'remark',
    'orderPreSubmit',
    'manualCancellation',
    'feeInfo',
    'pay',
    'produce'
]

new Vue({
    el: '#app',
    router,
    store,
    render(h) {
        return  h(App);
    }
});
