<template>
    <div class="container">
        <div class="slidebar-container">
            <van-sidebar v-model="activeKey">
                <van-sidebar-item
                    v-for="(item, index) in pluginsItems"
                    :key="index"
                    :title="item.name"
                />
            </van-sidebar>
        </div>
        <div class="content-container">
            <keep-alive>
                <component :is="activeComponent" />
            </keep-alive>
        </div>
    </div>
</template>
<script>

import ReadCard from '../components/ReadCard';
import GPSLocation from '../components/GPSLocation';
import ScanQrCode from '../components/ScanQrCode';
import Photo from '../components/Photo';
import FaceAuth from '../components/FaceAuth';
import Cache from '../components/Cache';
import DeviceList from '../components/DeviceList';
import ReadIDCard from '../components/ReadIDCard';
import Share from '../components/Share';
import Record from '../components/Record';
import Network from '../components/Network.vue';
import Jump from '../components/Jump';

export default {
    name: 'Main',
    components: {
        GPSLocation, ScanQrCode, Photo,
        ReadCard, FaceAuth, Cache, DeviceList,
        ReadIDCard, Share, Network, Jump, Record
    },
    data() {
        return {
            activeKey: 0,
            pluginsItems: [
                {
                    name: '定位',
                    component: 'GPSLocation'
                },
                {
                    name: '扫码',
                    component: 'ScanQrCode'
                },
                {
                    name: '拍照',
                    component: 'Photo'
                },
                {
                    name: '活体识别',
                    component: 'FaceAuth'
                },
                {
                    name: '缓存/跳转',
                    component: 'Cache'
                },
                {
                    name: '蓝牙连接',
                    component: 'DeviceList'
                },
                {
                    name: '读证',
                    component: 'ReadIDCard'
                },
                {
                    name: '读写卡',
                    component: 'ReadCard'
                },
                {
                    name: '分享',
                    component: 'Share'
                },
                {
                    name: '前端路由',
                    component: 'Jump'
                },
                {
                    name: '网络请求',
                    component: 'Network'
                },
                {
                  name: '录音',
                  component: 'Record'
                }
            ]
        };
    },
    computed: {
        activeComponent() {
            return this.pluginsItems[this.activeKey].component;
        }
    },
    methods: {}
};
</script>

<style scoped lang="scss">
.container {
    display: flex;

    .content-container {
        flex: 1;
    }
}
</style>
