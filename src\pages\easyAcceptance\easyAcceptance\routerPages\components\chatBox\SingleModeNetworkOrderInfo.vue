<!-- 单移网 -->
<template>
  <div class="card-verification" style="background-color: white">
    <div
      class="subpreview-box"
      v-for="(item, index1) in showList"
      :key="index1"
      @click="item.showAll = !item.showAll"
    >
      <div
        :style="{ background: item.showAll ? '#E5F0FF' : '#EDF7FF' }"
        class="total-title"
      >
        <span class="custom-label-name">{{ item.name }}</span>
        <van-icon :name="item.showAll ? 'arrow-up' : 'arrow'" />
      </div>
      <div
        v-for="(item1, index) in item.itemList"
        :key="index"
        style="padding: 0 10px"
      >
        <div v-show="item.showAll" style="display: flex">
          <span class="custom-title flex-shink">{{ item1.text }}</span>
          <span class="custom-title">{{ item1.value }}</span>
        </div>
      </div>
    </div>
    <div class="submit-container">
      <van-button class="sub" @click="preSubmit()" block>提交</van-button>
    </div>
  </div>
</template>

<script>
import WadeMobile from "rk-native-plugin";
import errorTips from "@/assets/bizComponents/errorTips/errorTips.vue";
import { copyText } from "../../../assets/js/func";
import { mapActions, mapMutations, mapState } from "vuex";
export default {
  name: "SingleModeNetworkOrderInfo",
  data() {
    return {
      imageList: [
        require("../../../images/arrow.png"),
        require("../../../images/add.png"),
      ],
      showList: [],
    };
  },
  components: {
    errorTips,
  },
  computed: {
    ...mapState([
      "chatList",
      "sessionId",
      "staffId",
      "flowStep",
      "num",
      "shbmMsgInfo",
      "jzfkOrderData",
      "activeModuleIndex",
      "loginPhoneNumber",
      "instanceId",
      "singleModeNetworkOrderData",
    ]),
  },
  mounted() {
    this.setRespTipArrQry([]);
    this.setBlockShow(false);
    this.checkOrderDetail();
    let iptvString = "";
    let ywfString = "无";
    if (this.singleModeNetworkOrderData.fusionUpFttrGoodData.custList.length > 0) {
      for (
          let i = 0;
          i < this.singleModeNetworkOrderData.fusionUpFttrGoodData.custList.length - 1;
          i++
      ) {
        iptvString +=
            this.singleModeNetworkOrderData.fusionUpFttrGoodData.custList[i].commodityName +
            ",";
      }
      iptvString +=
          this.singleModeNetworkOrderData.fusionUpFttrGoodData.custList[
          this.singleModeNetworkOrderData.fusionUpFttrGoodData.custList.length - 1
              ].commodityName;
    }
    if (this.singleModeNetworkOrderData.fusionUpFttrGoodData.commodityChooseYWUlList.length > 0) {
      ywfString="";
      for (
          let i = 0;
          i < this.singleModeNetworkOrderData.fusionUpFttrGoodData.commodityChooseYWUlList.length - 1;
          i++
      ) {
        ywfString +=
            this.singleModeNetworkOrderData.fusionUpFttrGoodData.commodityChooseYWUlList[i].commodityName +
            ",";
      }
      ywfString +=
          this.singleModeNetworkOrderData.fusionUpFttrGoodData.commodityChooseYWUlList[
          this.singleModeNetworkOrderData.fusionUpFttrGoodData.commodityChooseYWUlList.length - 1
              ].commodityName;
    }
    this.showList = [
      {
        name: "客户信息",
        showAll: true,
        itemList: [
          {
            text: "号码：",
            value:
              this.singleModeNetworkOrderData.checkNumberData.serialNumber,
          },
          {
            text: "客户名称：",
            value: this.singleModeNetworkOrderData.checkNumberData.showCustName,
          },
        ],
      },
      {
        name: "商品信息",
        showAll: true,
        itemList: [
          { text: "移网套餐商品：", value: iptvString },
          { text: "合约商品：", value: ywfString },
        ],
      },
      {
        name: '费用信息',
        showAll: true,
        itemList:[
          {text:'商品费用：',value:this.singleModeNetworkOrderData.orderPrice+"元"},
        ]
      }
    ];
  },

  methods: {

    ...mapMutations([
      "setFlowStep",
      "setRobotWorking",
      "setSingleModeNetworkOrderData",
      "setRespTipArrQry",
      "setBlockShow",
    ]),
    ...mapActions(["updateChatList"]),

    isEmpty(value) {
      let flag = false;
      if (
        value == "" ||
        value == "undefined" ||
        value == undefined ||
        value == null ||
        value == "null"
      ) {
        flag = true;
      }
      return flag;
    },

    uniqueByProperty(arr, prop) {
      const uniqueMap = new Map();
      return arr.reduce((acc, current) => {
        const key = current[prop];
        if (!uniqueMap.has(key)) {
          uniqueMap.set(key, true);
          acc.push(current);
        }
        return acc;
      }, []);
    },
    checkOrderDetail() {
      WadeMobile.getSysInfo("PLATFORM").then(
        (info) => {
          this.singleModeNetworkOrderData.platfForm = info;
          this.setSingleModeNetworkOrderData(this.singleModeNetworkOrderData);
          console.log("PLATFORM：：" + info);
        },
        (err) => {
          console.log("PLATFORM" + "失败：" + err);
        }
      );
    },
    preSubmit() {

      this.$emit("startLoading", "");
      const _data = this.singleModeNetworkOrderData;
      console.log("_data:----------- ", _data);
      const custList = _data.fusionUpFttrGoodData.custList;
      const   contactList= _data.fusionUpFttrGoodData.commodityChooseYWUlList;
      let contactCommodityInfoList=[];
      if(contactList.length>0){
        for(let i=0;i<contactList.length;i++){
          let c={
            commodityCode:contactList[i].commodityCode,
            commodityName:contactList[i].commodityName
          }
          contactCommodityInfoList.push(c)
        }
      }
      let req = {
        //单商品
        singleCommodityInfo: {
          commodityCode: custList[0].commodityCode,
          commodityName: custList[0].commodityName,
        },
        //合约商品
        contactCommodityInfoList: contactCommodityInfoList,
        serviceNumber: _data.checkNumberData.serialNumber,
        orderPrice: _data.orderPrice,
        remark: _data.remark,
        custName: _data.checkNumberData.custName,
      };

      console.log("下单入参---------------",req)
      this.$http.post("/singleMobile/preSubmit", req).then((res) => {
        this.$emit("endLoading", "");
        if (res.respCode === "0000") {
          _data.orderId = res.respData.orderId;
          this.updateChatList({
            sender: "1",
            type: "module",
            moduleName: "TextResponse",
            moduleLevel: 1,
            params: {
              text: "订单已提交并受理成功！CB单号：" + res.respData.orderId,
            },
            show: true,
          });
          this.setSingleModeNetworkOrderData(_data);
          let data = {
            inputType: "1",
            type: "1",
            textInput: "singleModeNetWorkOrderfoSubmit",
            notifyFlag: "",
            taskName: "单移网升套智能甩单",
          };
          this.$emit("newChatApi", data);
        } else {
          this.updateChatList({
            sender: "1",
            type: "module",
            moduleName: "TextResponse",
            moduleLevel: 1,
            params: {
              text: "订单受理失败，失败原因："+res.respMsg,
            },
            show: true,
          });
        }
      });
    },
  },
};
</script>
<style lang="scss">
.card-verification {
  width: 100%;
  margin: auto;
  .card-group {
    background-color: white;
    margin: 0px 10px;
    padding: 0px 5px;
    .tip {
      .van-cell__value {
        color: black;
        text-align: right;
      }
    }

    .card-list {
      .van-cell__title {
        font-size: 13px;
        color: #666666;
      }
      .van-cell__value {
        color: black;
        text-align: right;
        font-size: 13px;
      }
      .van-cell {
        color: black !important;
        padding: 0px 16px;
      }
      .van-cell::after {
        color: black !important;
        border-bottom: 0;
      }
    }
    .van-hairline--top-bottom::after,
    .van-hairline-unset--top-bottom::after {
      border-width: 0 0;
    }
  }
  .card {
    .van-cell__title {
      font-size: 13px;
      color: #000000;
    }
    .checkButton {
      width: 70px;
      height: 30px;
      background: #0081ff;
      border-radius: 4px;
    }
    .checkFont {
      font-size: 13px;
      font-weight: 400;
      color: #ffffff;
      line-height: 14px;
    }
  }
  .check {
    .van-cell__title {
      font-size: 13px;
      color: #333333;
    }
  }
}
</style>

<style lang="scss" scoped>
.card-verification {
  background-color: rgb(57, 159, 254);
  .subpreview-box {
    border-radius: 10px;
    margin-bottom: 10px;
    box-shadow: 0 2px 1px 0 #eee;
    .total-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 5px 10px;
      width: 93%;
      border-radius: 10px 10px 0 0;
      margin-bottom: 10px;
      .custom-label-name {
        font-size: 15px;
        line-height: 30px;
        color: #263a5f;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
      }
    }
    .custom-title {
      font-family: PingFangSC-Regular;
      font-size: 14px;
      line-height: 26px;
      color: #263a5f;
    }
    .flex-shink {
      flex-shrink: 0;
    }
  }
  .desc {
    color: black;
    font-size: 13px;
    line-height: 20px;
  }
  .margin-two {
    bottom: 10px;
    left: 15px;
    right: 15px;
    z-index: 2;
    position: absolute;
  }
  .van-buttons {
    border-radius: 4px;
    height: 44px;
    font-size: 13px;
    color: #ffffff;
    background-color: #0081ff;
  }
  .font {
    color: #333333;
  }
  .submit-container {
    .sub {
      border: 2px solid rgba(73, 124, 246, 1);
      border-radius: 5px;
      color: rgba(73, 124, 246, 1);
      margin-top: 20px;
    }
  }
}
</style>
