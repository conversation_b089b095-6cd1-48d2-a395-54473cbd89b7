<template>
  <div class="chart-container" ref="chartContainer">
    <div class="chart-content">
      <div class="dialog-box" v-for="(item, index) in chatList" v-show="item.show" :key="index">
        <div
          v-if="item.sender === '0'"
          class="client-dialog"
          :class="{ 'audio-dialog': item.moduleName === 'AudioInput' }"
        >
          <!-- <div class="client-dialog-icon"></div> -->
          <div class="dialog-bubble" :class="{ 'module-bubble': item.type === 'module' }">
            <div v-if="item.type === 'text'" class="dialog-text">{{ item.content }}</div>
            <component
              v-if="item.type === 'module'"
              :is="item.moduleName"
              :params="item.params || ''"
              @startLoading="startLoading"
              @endLoading="endLoading"
              @userSend="userSend"
              @newSysChat="newSysChat"
              @scrollToBottom="scrollToBottom"
              @showComponent="showComponent(item)"
              @deleteComponent="deleteComponent"
            />
          </div>
          <div class="client-image"></div>
        </div>
        <div v-if="item.sender === '1'" class="robot-dialog">
          <div class="robot-image"></div>
          <div
            class="dialog-bubble"
            :class="{
              'module-bubble': item.type === 'module',
              'text-module-bubble': item.moduleName === 'TextResponse',
            }"
          >
            <div
              class="w-mask"
              v-if="item.type === 'module' && item.moduleName !== 'TextResponse' && item.disabled"
            ></div>
            <div v-if="item.type === 'text'" class="dialog-text">{{ item.content }}</div>
            <component
              v-if="item.type === 'module' && (item.moduleName === 'TextResponse' || item.moduleLevel !== 1 || item.moduleName === 'ErrorInfo' || item.moduleName === 'MenuList')"
              :is="item.moduleName"
              :params="item.params || ''"
              @startLoading="startLoading"
              @endLoading="endLoading"
              @userSend="userSend"
              @newSysChat="newSysChat"
              @scrollToBottom="scrollToBottom"
              @showComponent="showComponent(item)"
              @deleteComponent="deleteComponent"
              @toNextStep="toNextStep"
              @clickMenu="clickMenu"
            />
            <component
              v-else
              ref="module"
              :is="item.moduleName"
              :params="item.params || ''"
              @startLoading="startLoading"
              @endLoading="endLoading"
              @userSend="userSend"
              @newSysChat="newSysChat"
              @scrollToBottom="scrollToBottom"
              @showComponent="showComponent(item)"
              @deleteComponent="deleteComponent"
              @toNextStep="toNextStep"
              @toDisabledModule="item.disabled = true"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="chat-loading" v-show="chatLoading">
      <img src="@/assets/images/chat_loading.gif" />
    </div>
    <div class="audio-recognizing" v-show="audioRecognizing">
      <img src="@/assets/images/chat_loading.gif" />
      <div>语音识别中</div>
    </div>
  </div>
</template>

<script>
import {mapActions, mapMutations, mapState} from 'vuex'
import { alertError } from '@/assets/bizComponents/funcComponent.js'
import { getSentenceTime } from './../../assets/js/func.js'
import ChangeChildPackage from './chatBox/ChangeChildPackage'
import AudioInput from './chatBox/AudioInput'
import FlowPackageRecommendation from './chatBox/FlowPackageRecommendation'
import PackageRecommendation from './chatBox/PackageRecommendation'
import PackageUsage from './chatBox/PackageUsage'
import MobileRecommendation from './chatBox/MobileRecommendation'
import CustAuth from './chatBox/CustAuth'
import TextResponse from './chatBox/TextResponse'
import PackageDetail from './chatBox/PackageDetail'
import NumCheck from './chatBox/NumCheck'
import NumCheckResult from './chatBox/NumCheckResult'
import MixRecommendation from './chatBox/MixRecommendation'
import UserSelect from './chatBox/UserSelect'
import Remark from './chatBox/Remark'
import OrderPreSubmit from './chatBox/OrderPreSubmit'
import FeeInfo from './chatBox/FeeInfo'
import Produce from './chatBox/Produce'
import FormalSubmit from './chatBox/FormalSubmit'
import PayResultQueryFail from './chatBox/PayResultQueryFail'
import ErrorInfo from './chatBox/ErrorInfo'
import ManualCancellation from './chatBox/ManualCancellation'
import AddFk from './chatBox/AddFk'
import MenuList from './chatBox/MenuList'
import AddSecondBroadband from './chatBox/AddSecondBroadband'
import Customers from '@/assets/bizComponents/SmallTool/Customers'
import QueryIPTVNumber from '@/assets/bizComponents/SmallTool/QueryIPTVNumber'
import AcceptanceForm from '@/assets/bizComponents/SmallTool/AcceptanceForm'
import AntiFraudRisk from '@/assets/bizComponents/SmallTool/AntiFraudRisk'
import SameDocument from '@/assets/bizComponents/SmallTool/SameDocument'
import OneCertificateFiveTimes from '@/assets/bizComponents/SmallTool/OneCertificateFiveTimes'
import SearchByTime from '@/assets/bizComponents/SmallTool/SearchByTime'
import ChargeBack from '@/assets/bizComponents/SmallTool/ChargeBack'
import Pay from '@/assets/bizComponents/SmallTool/Pay'
import AgentBalance from '@/assets/bizComponents/SmallTool/AgentBalance'
import OrderInquiry from '@/assets/bizComponents/SmallTool/OrderInquiry'
import OrderDetailsInquiry from '@/assets/bizComponents/SmallTool/OrderDetailsInquiry'
import VoLTE from '@/assets/bizComponents/SmallTool/VoLTE'
import HistoricalBilling from '@/assets/bizComponents/SmallTool/HistoricalBilling'

export default {
    name: 'ChatContainer',
    components: {
        ChangeChildPackage,
        AudioInput,
        FlowPackageRecommendation,
        PackageRecommendation,
        PackageUsage,
        MobileRecommendation,
        CustAuth,
        TextResponse,
        PackageDetail,
        NumCheck,
        NumCheckResult,
        MixRecommendation,
        UserSelect,
        Remark,
        OrderPreSubmit,
        FeeInfo,
        Produce,
        FormalSubmit,
        PayResultQueryFail,
        ErrorInfo,
        ManualCancellation,
        AddFk,
        MenuList,
        AddSecondBroadband,
        Customers,
        QueryIPTVNumber,
        AcceptanceForm,
        AntiFraudRisk,
        SameDocument,
        OneCertificateFiveTimes,
        SearchByTime,
        ChargeBack,
        Pay,
        AgentBalance,
        OrderInquiry,
        OrderDetailsInquiry,
        VoLTE,
        HistoricalBilling
    },
    data() {
        return {
            requireType: '',
            chatLoading: false,
            audioRecognizing: false,
            phoneNumber: '',
            sessionIdRandom: ''
        }
    },
    computed: {
        ...mapState([
            'chatList',
            'sessionId',
            'staffId',
            'flowStep',
            'activeModuleIndex',
            'loginPhoneNumber',
            'instanceId'
        ])
    },
    mounted() {
        const date = new Date()
        this.sessionIdRandom = `${date.getFullYear()}${date.getMonth()}${date.getDate()}${date.getHours()}${date.getMinutes()}${date.getSeconds()}`
    },
    watch: {
        chatList: {
            handler(val) {
                if (val.length > 0) {
                    const lastMsg = val[val.length - 1]
                    // 系统发送消息
                    if (lastMsg.sender === '1') {
                        if (lastMsg.next) {
                            setTimeout(() => {
                                this.chatLoading = true
                                this.scrollToBottom()
                                this.updateChatList({
                                    sender: '1',
                                    type: 'module',
                                    moduleName: lastMsg.next,
                                    moduleLevel: 1,
                                    show: false
                                })
                            }, getSentenceTime(lastMsg.params.text))
                        }
                    }
                    this.$nextTick(() => {
                        this.scrollToBottom()
                        this.$forceUpdate()
                    })
                }
            }
        },
        chatLoading(val) {
            if (val) {
                this.setRobotWorking(true)
            } else {
                this.setRobotWorking(false)
            }
        }
    },
    methods: {
        ...mapMutations([
            'setFlowStep',
            'setRobotWorking',
            'deleteLast'
        ]),
      ...mapActions([
        'updateChatList'
      ]),
        // 用户点击组件操作，发送消息
        userSend(data) {
            this.updateChatList({
                sender: '0',
                type: data.type,
                content: data.content
            })
        },
        // 用户输入（语音或文字）处理
        userInputProcess(data) {
            // 输入文字
            if (data.type === '1') {
                this.updateChatList({
                    sender: '0',
                    type: 'text',
                    content: data.textInput,
                    show: true
                })
            }
            const params = {
                nodeCode: this.mixChangeStepList[this.flowStep], // 环节编码
                textInput: data.type === '1' ? data.textInput : '', // 输入内容(手动输入时必传)
                base64Info: data.type === '1' ? '' : data.base64.split("base64,")[1], // 语音base64编码(语音输入时必传)
                audiolen: data.type === '1' ? '' : data.size, // 录音文件字节数(语音输入时必传)
                type: data.type, // 1为文字输入,2为语音输入
                instanceId: this.instanceId, // 实例id
                sessionId: this.sessionIdRandom,
                staffId: this.staffId,
                phoneInfo: this.loginPhoneNumber
            }
            var qs = require('qs');
            if (data.type === '1') {
                this.chatLoading = true
            } else {
                this.audioRecognizing = true
            }
            this.$http.post('/smartAccept/qryResultByNodeCode', params).then(res => {
                this.chatLoading = false
                this.audioRecognizing = false
                if (res.respCode === '0000') {
                    // 输入语音
                    if (data.type === '2') {
                        this.updateChatList({
                            sender: '0',
                            type: 'module',
                            moduleName: 'AudioInput',
                            moduleLevel: 1,
                            params: {
                                audio: data.base64,
                                text: res.voiceToTextResult
                            },
                            show: false
                        })
                    }
                    if (res.busiType === 'menu') { // 唤起菜单
                        if (params.textInput === '历史账单查询') {
                            this.updateChatList({
                                sender: '1',
                                type: 'module',
                                moduleName: 'SearchByTime',
                                moduleLevel: 1,
                                params: {
                                    type: 'HistoricalBilling',
                                    showDate: true,
                                    showNum: true
                                },
                                show: true
                            })
                            return;
                        }
                        if (params.textInput === '退单查询1') {
                            this.updateChatList({
                                sender: '1',
                                type: 'module',
                                moduleName: 'SearchByTime',
                                moduleLevel: 1,
                                params: {
                                    type: 'ChargeBack',
                                    showStartDate: true,
                                    showEndDate: true
                                },
                                show: true
                            })
                            return;
                        }
                        if (params.textInput === '退单查询2') {
                            this.updateChatList({
                                sender: '1',
                                type: 'module',
                                moduleName: 'ChargeBack',
                                moduleLevel: 1,
                                params: {
                                    startDate: '2024-08-01',
                                    endDate: '2024-08-11',
                                },
                                show: false
                            })
                            return;
                        }
                        if (params.textInput === '代理商余额查询') {
                            this.updateChatList({
                                sender: '1',
                                type: 'module',
                                moduleName: 'AgentBalance',
                                moduleLevel: 1,
                                params: {},
                                show: false
                            })
                            return;
                        }
                        if (params.textInput === '查询下15568819579的历史受理') {
                            this.updateChatList({
                                sender: '1',
                                type: 'module',
                                moduleName: 'AcceptanceForm',
                                moduleLevel: 1,
                                params: {
                                    serialNumber: '15568819579'
                                },
                                show: false
                            })
                            return;
                        }
                        if (params.textInput === '查询cbss订单') {
                            this.updateChatList({
                                sender: '1',
                                type: 'module',
                                moduleName: 'SearchByTime',
                                moduleLevel: 1,
                                params: {
                                    type: 'OrderInquiry',
                                    showStartDate: true,
                                    showEndDate: true,
                                    showNum: true
                                },
                                show: true
                            })
                            return;
                        }
                        this.updateChatList({
                            sender: '1',
                            type: 'module',
                            moduleName: 'MenuList',
                            moduleLevel: 1,
                            params: {
                                menuList: res.content
                            },
                            show: false
                        })
                    } else {
                        if (this.mixChangeStepList[this.flowStep] === 'chatStart') { // 初始节点
                            let tip = ''
                            if (params.textInput === 'VoLTE查询') {
                                this.updateChatList({
                                    sender: '1',
                                    type: 'module',
                                    moduleName: 'VoLTE',
                                    moduleLevel: 1,
                                    params: {
                                        serialNumber: '15568819579'
                                    },
                                    show: false
                                })
                                return;
                            }
                            if (params.textInput === '缴费查询1') {
                                this.updateChatList({
                                    sender: '1',
                                    type: 'module',
                                    moduleName: 'SearchByTime',
                                    moduleLevel: 1,
                                    params: {
                                        type: 'pay',
                                        showStartDate: true,
                                        showEndDate: true
                                    },
                                    show: true
                                })
                                return;
                            }
                            if (params.textInput === '缴费查询2') {
                                this.updateChatList({
                                    sender: '1',
                                    type: 'module',
                                    moduleName: 'pay',
                                    moduleLevel: 1,
                                    params: {
                                        startDate: '2024-08-01',
                                        endDate: '2024-08-11',
                                    },
                                    show: false
                                })
                                return;
                            }
                            if (params.textInput === '查询下15568819579的客户资料') {
                                this.updateChatList({
                                    sender: '1',
                                    type: 'module',
                                    moduleName: 'Customers',
                                    moduleLevel: 1,
                                    params: {
                                        serialNumber: '15568819579'
                                    },
                                    show: false
                                })
                                return;
                            }
                            if (params.textInput === '查询下15568819579的IPTV账号') {
                                this.updateChatList({
                                    sender: '1',
                                    type: 'module',
                                    moduleName: 'QueryIPTVNumber',
                                    moduleLevel: 1,
                                    params: {
                                        Number: '15568819579'
                                    },
                                    show: false
                                })
                                return;
                            }
                            if (params.textInput === '查询下15568819579的反诈风险') {
                                this.updateChatList({
                                    sender: '1',
                                    type: 'module',
                                    moduleName: 'AntiFraudRisk',
                                    moduleLevel: 1,
                                    params: {
                                        psptId: '220106200002271834'
                                    },
                                    show: false
                                })
                                return;
                            }
                            if (params.textInput === '查询下15568819579的同证件') {
                                let obj = {
                                    sender: '1',
                                    type: 'module',
                                    moduleName: 'SameDocument',
                                    moduleLevel: 1,
                                    params: {
                                        checkType: true ? '0' : '1'
                                    },
                                    show: false
                                }
                                if (true) {
                                    obj.params.psptId = '220106200002271834';
                                } else {
                                    obj.params.serialNumber = '15568819579';
                                }
                                this.updateChatList(obj)
                                return;
                            }
                            if (params.textInput === '查询下15568819579的一证五次') {
                                let obj = {
                                    sender: '1',
                                    type: 'module',
                                    moduleName: 'OneCertificateFiveTimes',
                                    moduleLevel: 1,
                                    params: {
                                        qryType: true ? '2' : '1'
                                    },
                                    show: false
                                }
                                if (true) {
                                    obj.params.psptId = '220106200002271834';
                                } else {
                                    obj.params.serialNumber = '15568819579';
                                }
                                this.updateChatList(obj)
                                return;
                            }
                            if (res.content === 'rongHeBianGeng') {
                                // tip = '请输入您需要校验的手机号码！'
                                this.setFlowStep(1, false)
                                this.updateChatList({
                                    sender: '1',
                                    type: 'module',
                                    moduleName: 'numCheck',
                                    moduleLevel: 1,
                                    params: {},
                                    show: false
                                })
                            } else {
                                tip = '我是联通小沃，我目前仅支持融合变更业务，您可以输入办理融合变更或融合变更，开始办理业务。'
                                this.updateChatList({
                                    sender: '1',
                                    type: 'module',
                                    moduleName: 'TextResponse',
                                    moduleLevel: 1,
                                    params: {
                                        text: tip
                                    },
                                    show: true
                                })
                            }
                        } else {
                            // 号码校验：将用户输入的手机号码，传给号码校验模块
                            if (this.mixChangeStepList[this.flowStep] === 'numCheck' && res.content) {
                                this.$refs.module[this.activeModuleIndex].getNumber(res.content)
                            } else { // 通常为等待用户输入“提交”或者“确定”指令
                                if (res.content.indexOf('Commit') !== -1) {
                                    this.$refs.module[this.activeModuleIndex].submit()
                                } else if (res.content.indexOf('Cancel') !== -1) {
                                    this.$refs.module[this.activeModuleIndex].submit('cancel')
                                }
                                // 用户输入选择融合主商品关键字，重新查询推荐套餐
                                else if(res.content === 'unknown' && this.mixChangeStepList[this.flowStep] === 'mixRecommendation') {
                                    this.chatLoading = true
                                    let keywords = ''
                                    if (data.type === '1') {
                                        keywords = (this.chatList[this.chatList.length - 1]).content
                                    } else {
                                        keywords = res.voiceToTextResult
                                    }
                                    this.updateChatList({
                                        sender: '1',
                                        type: 'module',
                                        moduleName: 'mixRecommendation',
                                        moduleLevel: 1,
                                        params: {
                                            keywords: keywords
                                        },
                                        show: false
                                    })
                                } else {
                                    this.updateChatList({
                                        sender: '1',
                                        type: 'module',
                                        moduleName: 'TextResponse',
                                        moduleLevel: 1,
                                        params: {
                                            text: '小沃无法识别您的指令，请你重新输入，或者直接点击按钮进行操作~'
                                        },
                                        show: true
                                    })
                                }
                            }
                        }
                    }

                } else {
                    // 输入文字
                    if (data.type === '1') {
                        this.updateChatList({
                            sender: '1',
                            type: 'module',
                            moduleName: 'TextResponse',
                            moduleLevel: 1,
                            params: {
                                text: res.respDesc
                            },
                            show: true
                        })
                    }
                    // 输入语音
                    else if (data.type === '2') {
                        this.updateChatList({
                            sender: '0',
                            type: 'module',
                            moduleName: 'AudioInput',
                            moduleLevel: 1,
                            params: {
                                audio: data.base64,
                                text: '语音转文字失败…'
                            },
                            show: true
                        })
                    }
                    // this.robotError(res)
                }
            }).catch(e => {
                this.chatLoading = false
                this.audioRecognizing = false
                alertError({
                    title: '出错了！',
                    message: e,
                    confirmButtonText: '报告错误'
                });
            });
        },
        // 首字母改大写(废弃！！模块名称不区分大小写，不需要转换了！！)
        // firstLetterToUpperCase(str) {
        //     const first = str.substr(0, 1)
        //     const upperFirst = first.toUpperCase()
        //     return upperFirst + str.substr(1, str.length - 1)
        // },
        // 新增系统消息回复
        newSysChat(data) {
            if (data.sender === '1') {
                this.updateChatList(data)
                this.scrollToBottom()
            } else {
                this.updateChatList(data)
                this.scrollToBottom()
            }
        },
        showComponent(item) {
            item.show = true
            this.scrollToBottom()
        },
        deleteComponent(error) {
            this.deleteLast();
            this.updateChatList({
                sender: '1',
                type: 'module',
                moduleName: 'TextResponse',
                moduleLevel: 1,
                params: {
                    text: error
                },
                show: true
            })
        },
        scrollToBottom() {
            this.$nextTick(() => {
                let el = this.$refs.chartContainer
                el.scrollTo({ top: el.scrollHeight, behavior: 'smooth'})
            })
        },
        startLoading() {
            this.chatLoading = true
            this.setRobotWorking(true)
            this.scrollToBottom()
        },
        endLoading() {
            this.chatLoading = false
            this.setRobotWorking(false)
            // this.scrollToBottom()
        },
        robotError(res) {
            this.updateChatList({
                sender: '1',
                type: 'module',
                moduleName: 'TextResponse',
                moduleLevel: 1,
                params: {
                    text: JSON.stringify(res)
                },
                show: true
            })
        },
        toNextStep(data) {
            setTimeout(() => {
                this.chatLoading = true
                this.scrollToBottom()
                setTimeout(() => {
                    this.chatLoading = false
                    let step = this.flowStep + 1
                    this.setFlowStep(step, false)
                    this.updateChatList({
                        sender: '1',
                        type: 'module',
                        moduleName: this.mixChangeStepList[step],
                        moduleLevel: 1,
                        params: data.params ?? {},
                        show: false
                    })
                    this.scrollToBottom()
                }, 1000)
            }, getSentenceTime(data.tip ?? ''))
        },
        clickMenu(menu) {
            this.$emit('clickMenu', menu)
        }
    }
};
</script>

<style lang="scss" scoped>
.chart-container {
  position: absolute;
  height: calc(100% - 100px);
  overflow: auto;
  padding: 10px;
  background-color: #f2f2f2;
  border-radius: 20px 20px 0 0;
  bottom: 60px;
  width: 100%;
  box-sizing: border-box;
}

.audio-recognizing {
  width: 100%;
  text-align: center;

  img {
    width: 80px;
  }

  div {
    position: relative;
    top: -18px;
    color: #77cbff;
    font-size: 13px;
  }
}
</style>