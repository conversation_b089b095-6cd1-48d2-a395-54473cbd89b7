<template>
  <div class="one-key-open-account">
    <div class="form-wrap">
      <div class="card-style" v-if="madeGoodsInfoList!==null&& Object.keys(madeGoodsInfoList).length>=1">
        <div class="contract-wrap" v-if="madeGoodsInfoList!==null&& Object.keys(madeGoodsInfoList).length>=1">
          <div class="made-list">
            <div class="list-item" :class="{'active': mobileGoodsInfo.commId == item.commId}" v-for="(item, index) in madeGoodsInfoList"
                 :key="index" @click="selectItemMade(index, item)">
              <span style="float: left">{{ item.commName }} 
              </span>
              <img src="../../../images/recommand.png" style="width: 20px;height: 20px;text-align: center;margin-top: 5px;display: inline-block"  v-if="item.isNice">
            </div>
          </div>
          
        </div>
      </div>
    </div>
    <van-button type="info" size="primary" style="width: 100%;border-radius: 20px;" @click="confirm()">确定</van-button>
  </div>
</template>

<script>
  import WadeMobile from 'rk-native-plugin';
  import { Mobile } from 'rk-web-utils';
import {confirmReportErr} from "@/assets/bizComponents/funcComponent";
  import {mapActions, mapMutations, mapState} from "vuex";

export default {
  name: "CommSelect",
  data() {
    return {
      memUsername: '',
      localUsername: '',
      serverData: "",
      madeGoodsInfoList: [],
      goodsList:[],
      selectIndexMade:0,
      mobileGoodsInfo:{}
    }
  },
  computed: {
    ...mapState([
      'jzfkOrderData',
        'isFour'
    ])
  },
  methods: {
    ...mapMutations([
      'setFlowStep',
      'setRobotWorking',
      'setJzfkOrderData',
        'setIsFour'
    ]),
    ...mapActions(['updateChatList']),

    getMemCache() {
      Mobile.getMemoryCache("mem-username").then((result) => {
        console.log(result);
        this.memUsername = result;
      })
    },
    getLocalCache() {
      Mobile.getOfflineCache("local-username").then((result) => {
        console.log(result);
        this.localUsername = result;
      })
    },
    close() {

      const param = {
        phone: '13000000000',
        username: 'chinaunicom'
      };
      Mobile.closeH5(JSON.stringify(param));
    },
    selectItemMade(index, item) {
      this.goodsList = this.madeGoodsInfoList;
      this.selectIndexMade = index;
      this.goodsList.map(res => {
        if (res.commName == item.commName) {
          res.selected = !res.selected;
          const mobileGoodsInfo = {
              commName: item.commName || '',
              commId: item.commId || ''
              // firstMonBillModeCode: item.firstMonBillMode || '',
          };
          this.mobileGoodsInfo = mobileGoodsInfo;
          console.log("默认商品mobileGoodsInfo"+JSON.stringify(mobileGoodsInfo))
          this.selectById(mobileGoodsInfo);
          
        } else {
          res.selected = false;
        }
      });
      this.madeGoodsInfoList = JSON.parse(JSON.stringify(this.goodsList))


    },
    confirm(){
      let req={
        endNum: this.jzfkOrderData.mainNumberCheckData.mainNumber.substring(this.jzfkOrderData.mainNumberCheckData.mainNumber.length-4),
        tailType:'02',
        goodLevel:'99',
        numAcount:16}
      this.$http.post('/shjzfkChoiceNumber/choiceNum', req).then(res => {
        if("0000"==res.respCode){
          if(res.respData.RESOURCES_INFO.length<1){
            this.setIsFour(3)
            let data = {
              inputType: "1",
              type: '1',
              textInput: "viceCardCommoditySubmit",
              notifyFlag: ''
            }
            this.$emit('newChatApi', data);
          }
          else{
            this.setIsFour(4)
            let data = {
              inputType: "1",
              type: '1',
              textInput: "viceCardCommoditySubmit",
              notifyFlag: ''
            }
            this.$emit('newChatApi', data);
          }
        }
        else{
          let data = {
            inputType: "1",
            type: '1',
            textInput: "viceCardCommoditySubmit",
            notifyFlag: ''
          }
          this.$emit('newChatApi', data);
        }
      })

    },
    selectById(commInfo){
      this.$http.post('/shjzfkGoodsSelect/getSecondaryCardPackageInfo',  {secondaryCardPackageId:commInfo.commId}).then(res => {
        this.jzfkOrderData.goodsSelectData=res.respData
        this.jzfkOrderData.goodsSelectData.push({commId:commInfo.commId,commName:commInfo.commName,roleId:"-1"})
        this.setJzfkOrderData(this.jzfkOrderData)
        console.log(this.jzfkOrderData)
      })
    },
    getMadeGoodsInfoList(){
      this.madeGoodsInfoList = [];
      this.$http.post('/shjzfkGoodsSelect/qryCoupleCoInfo',  {serialNumber:this.jzfkOrderData.mainNumberCheckData.mainNumber,productId:this.jzfkOrderData.mainNumberCheckData.custInfo.productId}).then(res => {
        let datas = res.respData;
        for (let i = 0; i < datas.length; i++) {
          if(i==0){
            this.madeGoodsInfoList.push({
              commName: datas[i].commName,
              commId: datas[i].commId,
              isNice:true
            })

            
          }
          else{
            this.madeGoodsInfoList.push({
              commName: datas[i].commName,
              commId: datas[i].commId,
              isNice:false
            })
          }
        }
        if (datas.length > 0){
          this.selectItemMade(0, this.madeGoodsInfoList[0]);
        }
        this.$emit('endLoading', '')

        console.log(datas)
      }).catch((error)=>{
        this.$emit('endLoading', '')
        console.error(error)
      })
    }
  },
  created() {
    this.$emit('startLoading', '')
    this.getMadeGoodsInfoList()
  }
  
}
</script>

<style scoped lang="scss">
.one-key-open-account{
  overflow: hidden;
  .content {
    min-height: 50px;
  }

  .demo {
    width: 300px;
    height: 100px;
    background-color: #646566;
  }
  .card-style{
    margin: 0;
    //padding-bottom: 15px;
    width: 100%;
    height: auto;
    background: #FFFFFF;
    border-radius: 8px;
    border: 1px solid #FFFFFF;
  }
  .form-wrap {
    padding: 0;
    background: #F5F5F5;
    .contract-wrap {
      margin: 0;
      /*border-bottom: 1px solid #F7F7F7;*/
      p {
        padding: 10px 0;
        height: 34px;
        line-height: 34px;
        font-size: 14px;
        color: #333;
      }
      .desc{
        color: #ee0a24;
        font-size: 12px;

      }
      .made-list {
        display: flex;
        flex-direction: column;
        .list-item {
          padding: 0 5px;
          height:fit-content;
          line-height: 30px;
          font-size: 14px;
          color: #000;
          margin: 5px 5px;
          background: #F7F7F7;
          border-radius: 4px;
          border: 1px solid #F7F7F7;
        }
        .active {
          color: #0081FF;
          background: rgba(0,129,255,0.10);
          border: 1px solid #0081FF;
        }
      }
      .contract-list {
        display: flex;
        flex-direction: column;
        .list-item {
          padding: 0 10px;
          width: fit-content;
          height: fit-content;
          line-height: 30px;
          font-size: 14px;
          color: #999;
          margin-bottom: 10px;
          background: #F7F7F7;
          border-radius: 4px;
          border: 1px solid #F7F7F7;
        }
        .active {
          color: #0081FF;
          background: rgba(0,129,255,0.10);
          border: 1px solid #0081FF;
        }
      }
    }
    .border {
      background: #FFFFFF;
      border-radius: 4px;
      border: 1px solid #CACACA;
    }
    .blueBorder {
      background: rgba(0, 129, 255, 0.05);
      border-radius: 12px;
      border: 1px solid #0081FF;
      width: 300px;
      margin-left: 20px;
    }
    .notBlueBorder {
      border-radius: 12px;
      width: 300px;
      margin-left: 20px;
    }
    .made-name{
      color: #323233;
      font-size: 0.37333rem;
      line-height: 0.64rem;
    }
    .made-van {
      /*padding: 5px 0px;*/
      /*background: #c8e3fe;*/
      /*border-radius: 12px;*/
      /*font-size: 14px;*/
      align-content: center;
      width: 300px;
      text-align: center;
    }

  }
}
</style>
