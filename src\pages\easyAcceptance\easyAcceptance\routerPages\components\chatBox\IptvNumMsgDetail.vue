<template>
  <div class="card-verification" style="background-color: white">
    <div class="card-group">
      <div style="padding-left: 5px;">您的信息如下</div>
      <div class="msg-blue-card">
        <div class="msg-blue-cell"><span>姓名:</span><span class="val-blue-cell">{{resp.showCustName}}</span></div>
        <div class="msg-blue-cell"><span>宽带套餐:</span><span class="val-blue-cell">{{productName}}</span></div>
        <div class="msg-blue-cell"><span>已订购超清商品:</span><span class="val-blue-cell">{{choosedIPTV}}</span></div>
        <div class="msg-blue-cell"><span>是否为融合用户:</span><span class="val-blue-cell">{{showUserType}}</span></div>
        <div class="msg-blue-cell"><span>装机地址:</span><span class="val-blue-cell">{{resp.showInstallAddr}}</span></div>
        <div class="msg-blue-cell" v-show="resp.userTypeFlag == '1'"><span>套餐档位:</span><span class="val-blue-cell">{{resp.priceLevel}}</span></div>

      </div>
    </div>
  </div>
</template>

<script>

import errorTips from '@/assets/bizComponents/errorTips/errorTips.vue';
import {copyText} from "../../../assets/js/func";
import {mapState} from "vuex";
export default {
  name: "IptvNumMsgDetail",
  data() {
    return {
      imageList: [
        require('../../../images/arrow.png'),
        require('../../../images/add.png')
      ],
      productName:'',
      showUserType:"",
      iptvInvalid:[],
      choosedIPTV:"",
      resp:{
        iptvName:'IPTV1',
        productName:'上海宽带1000M基本套餐(专属)',
        custName_tm:'徐**'
      }
    }
  },
  computed: {
    ...mapState([
      'jzfkOrderData',
        'iptvOrderData'
    ])
  },
  components: {
    errorTips
  },
  mounted() {
    
    this.resp=this.iptvOrderData.checkNumberData
    console.log(this.resp)
    this.showUserType =  this.resp.userTypeFlag === '1' ? "是" : "否";
    if('1'==this.resp.userTypeFlag){
      this.productName=this.resp.kdProductName
    }else{
      this.productName=this.resp.showMainProduct
    }
    if(this.iptvOrderData.checkNumberData.iptvInvalid.length>0){
      this.choosedIPTV="";
      for(var i in this.iptvOrderData.checkNumberData.iptvInvalid){
        if(1==this.iptvOrderData.checkNumberData.iptvInvalid[i].isValidIptv){
          this.choosedIPTV+=this.iptvOrderData.checkNumberData.iptvInvalid[i].threeIptvId+","
        }
      }
    }
    if(""==this.choosedIPTV){
      this.choosedIPTV="无"
    }
    else{
      this.choosedIPTV=this.choosedIPTV.substr(0,this.choosedIPTV.length-1)
    }
  },

  methods: {
    maskPhoneNumber(phoneNumber) {
      return phoneNumber.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    },

    maskPspt(pspt) {
      return pspt.replace(/^(.{4})(.*)(.{4})$/, '$1*********$3');
    },
    maskName(name) {
      return name.substring(0, 1) + '*'.repeat(name.length - 1);
    }

  }
}
</script>
<style lang="scss">
.card-verification {
  width:100%;
  margin: auto;
  .card-group {
    background-color: white;
    margin: 0;
    padding:0;
    .msg-blue-card{
      background: #E9F4FE;
      border-radius: 10px;
      padding: 10px;
      margin: 10px 0;
      .msg-blue-cell{
        display: flex;
        justify-content: space-between;
        margin: 5px 0;
      }
      .val-blue-cell{
        width: 50%;
        text-align: right;
        word-wrap: break-word;
      }
    }
    .tip {
      .van-cell__value {
        color: black;
        text-align: right;
      }}

    .card-list {
      .van-cell__title {
        font-size: 13px;
        color: #666666;
      }
      .van-cell__value {
        color: black;
        text-align: right;
        font-size: 13px;
      }
      .van-cell {
        color: black !important;
        padding: 0px 16px;
      }
      .van-cell::after {
        color: black !important;
        border-bottom: 0;

      }
    }
    .van-hairline--top-bottom::after, .van-hairline-unset--top-bottom::after {
      border-width: 0 0;
    }
  }
  .card {
    .van-cell__title {
      font-size: 13px;
      color: #000000;
    }
    .checkButton {
      width: 70px;
      height: 30px;
      background: #0081FF;
      border-radius: 4px;
    }
    .checkFont {
      font-size: 13px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 14px;
    }
  }
  .check{
    .van-cell__title {
      font-size: 13px;
      color: #333333;
    }
  }
}

</style>

<style lang="scss" scoped>
.card-verification {
  background-color: rgb(57,159,254);
  .desc {
    color: black;
    font-size: 13px;
    line-height: 20px;
  }
  .margin-two {
    bottom: 10px;
    left: 15px;
    right: 15px;
    z-index: 2;
    position: absolute;
  }
  .van-buttons {
    border-radius: 4px;
    height: 44px;
    font-size: 13px;
    color: #FFFFFF;
    background-color: #0081FF;
  }
  .font {
    color:#333333;
  }

}

</style>