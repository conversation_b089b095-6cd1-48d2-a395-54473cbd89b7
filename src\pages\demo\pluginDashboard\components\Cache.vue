<template>
    <div>
        <van-field
            v-model="memUsername"
            center
            clearable
            placeholder="请输入缓存值"
        >
            <template #button>
                <van-button
                    size="small"
                    type="primary"
                    @click="setMemoryCache"
                >
                    设置临时缓存值
                </van-button>
            </template>
        </van-field>
        <van-field
            v-model="localUsername"
            center
            clearable
            placeholder="请输入缓存值"
        >
            <template #button>
                <van-button
                    size="small"
                    type="primary"
                    @click="setLocalMemoryCache"
                >
                    设置永久缓存值
                </van-button>
            </template>
        </van-field>
        <div>
            缓存设置完成后跳转至其他页面，验证获取缓存
        </div>
        <van-button
            @click="jump"
            type="primary"
        >
            跳转
        </van-button>
    </div>
</template>

<script>
import { Mobile } from 'rk-web-utils';

export default {
    name: 'Cache',
    data() {
        return {
            memUsername: '',
            localUsername: ''
        };
    },
    methods: {
        setMemoryCache() {
            Mobile.setMemoryCache('mem-username', this.memUsername);
        },
        setLocalMemoryCache() {
            Mobile.setOfflineCache('local-username', this.localUsername);
        },
        jump() {
            Mobile.openH5('http://***********:8081/jumpDemo.html', null, (result) => {
                // eslint-disable-next-line no-unused-vars
                result = JSON.parse(result);
            });
        }
    }
};
</script>

<style scoped>

</style>
