<template>
  <div >
      <div class="selectProduct">
        <div class="box" :style="{border:item.isActive ? '2px solid #3498db':''}" v-for="(item,index) in commodityYWUlList" :key="index"  >
          <div class="tag" :style="{ backgroundColor: item.color }">{{item.title}}</div>
          <div class="cell1">{{item.SHORT_NAME}}</div>
          <div class="cell2">{{item.RENT}}</div>
          <div class="cell1 ellipsis" v-if="!item.SHORT_NAME&&!item.RENT">{{item.ywGoodName.length>20?item.ywGoodName.slice(0,20)+'...':item.ywGoodName}}</div>
          <van-button class="btn" @click="goodClick1(item,$event)" >{{item.itemTip}}</van-button>
        </div>
      </div>
  </div>
</template>
<script>
import {mapActions, mapMutations, mapState} from "vuex";
export default {
  name:  "LptvYwSelectProduct",
  data(){
    return {
      radio: '',
      colors:['rgb(129,211,248)','rgb(251,6,6)','#71bc8c'],
      commodityChooseYWUlList: [],
      choosedIPTV:"",
      commodityYWUlList: []}},
  mounted() {
    this.iptvOrderData.iptvGoodData.commodityChooseYWUlList=[];
    this.setIptvOrderData(this.iptvOrderData);
    this.commodityYWUlList=[];
    console.log(this.iptvReYwProductList,"iptvReYwProductList")
    let colorLength=this.colors.length
    if(this.iptvReYwProductList.length>0){
      for(let i=0;i<this.iptvReYwProductList.length;i++){
        let iptvInfo={}
        if(i==0){
           iptvInfo={
            title:'优惠包',
            ancestors:this.iptvReYwProductList[i].ancestors,
            SHORT_NAME:this.iptvReYwProductList[i].SHORT_NAME,
            ywGoodId:this.iptvReYwProductList[i].commId,
            ywGoodType:this.iptvReYwProductList[i].commType,
            ywGoodName:this.iptvReYwProductList[i].commName,
            RENT:this.iptvReYwProductList[i].DETAIL, 
             isActive:false,
             itemTip:'立即购买',
            color:this.colors[i%colorLength]}
        }else{
          iptvInfo={
            title:'优惠包',
            ancestors:this.iptvReYwProductList[i].ancestors,
            SHORT_NAME:this.iptvReYwProductList[i].SHORT_NAME,
            ywGoodId:this.iptvReYwProductList[i].commId,
            ywGoodType:this.iptvReYwProductList[i].commType,
            ywGoodName:this.iptvReYwProductList[i].commName,
            RENT:this.iptvReYwProductList[i].DETAIL,
            itemTip:'立即购买',
            isActive:false,
            color:this.colors[i%colorLength]}
        }
        this.commodityYWUlList.push(iptvInfo)
      }}
    // this.commodityChooseYWUlList.push(this.commodityYWUlList[0]);
    // this.iptvOrderData.iptvGoodData.commodityChooseYWUlList=this.commodityChooseYWUlList;
    // this.setIptvOrderData(this.iptvOrderData);
  },
  computed: {
    ...mapState([
      'jzfkOrderData',
      'shbmMsgInfo',
      'iptvOrderData',
      'iptvCacheList',
      'iptvReYwProductList',
      'iptvCacheList', 'iptvOrderData'])
  },
  methods:{
    ...mapMutations([
      'setFlowStep',
      'setRobotWorking',
      'setIptvOrderData'
    ]),
    ...mapActions(['updateChatList']),
    goodClick1(item,e){
      const $this = $(e.target);
      if ($this.hasClass("btn")) {
        item.itemTip='已选择'
        $this.addClass("active-btn")
        $this.parent().addClass("red-border")
        $this.removeClass("btn")
        $this.siblings().removeClass("active-btn")
        $this.siblings().removeClass("red-border")
        // 如果未选中，则添加
        this.commodityChooseYWUlList=[];
        let req1={
          serialNumber:this.iptvOrderData.checkNumberData.phoneSerialNumber,
          commId:item.ywGoodId
        }
        this.$http.post('/mpComm/ywfIsConflict', req1).then(res => {
          if (res.respCode === '0000') {
            this.commodityChooseYWUlList.push(item);
            this.iptvOrderData.iptvGoodData.commodityChooseYWUlList=this.commodityChooseYWUlList;
            this.setIptvOrderData(this.iptvOrderData)
            let data = {
              inputType: "1",
              type: '1',
              textInput: "iptvYwGoodSubmit",
              notifyFlag: '',
              taskName:'智能超清甩单'
            }
            this.$emit('newChatApi', data);
          }
          else{
            $this.parent().removeClass("red-border")
            $this.removeClass("active-btn")
            $this.addClass("btn");
            item.itemTip='立即购买'
            this.updateChatList({
              sender: '1',
              type: 'module',
              moduleName: 'TextResponse',
              moduleLevel: 1,
              params: {
                text: res.respMsg
              },
              show: true
            })
            let data = {
              inputType: "1",
              type: '1',
              textInput: "iptvYwGoodSubmit",
              notifyFlag: '',
              taskName:'智能超清甩单'
            }
            this.$emit('newChatApi', data);
          }
        })
        
      } else {
        $this.parent().removeClass("red-border")
        $this.removeClass("active-btn")
        $this.addClass("btn");
        item.itemTip='立即购买'
        this.commodityChooseYWUlList = this.commodityChooseYWUlList.filter(i=>i!==item);
        this.iptvOrderData.iptvGoodData.commodityChooseYWUlList=this.commodityChooseYWUlList;
        this.setIptvOrderData(this.iptvOrderData);
      }
    },
    
    goodClick(item,e){
      const $this = $(e.target);
      if ($this.hasClass("btn")) {
        $this.addClass("active-btn")
        $this.parent().addClass("red-border")
        $this.removeClass("btn")
        // 如果未选中，则添加
        item.itemTip='已选择'
        this.commodityChooseYWUlList.push(item);
        this.iptvOrderData.iptvGoodData.commodityChooseYWUlList=this.commodityChooseYWUlList;
        this.setIptvOrderData(this.iptvOrderData)
      } else {
        $this.parent().removeClass("red-border")
        $this.removeClass("active-btn")
        $this.addClass("btn");
        item.itemTip='立即购买'
        this.commodityChooseYWUlList = this.commodityChooseYWUlList.filter(i=>i!==item);
        this.iptvOrderData.iptvGoodData.commodityChooseYWUlList=this.commodityChooseYWUlList;
        this.setIptvOrderData(this.iptvOrderData);
      }
    }
  }
}
</script>
<style scoped lang="scss">
.red-border{
  border: rgb(221,248,253) 2px solid;
}
.selectProduct{
  display: flex;
  flex-wrap: nowrap; /* 确保子元素不换行 */
  overflow-x: auto;  /* 启用水平滚动 */
  -webkit-overflow-scrolling: touch; /* 改善移动设备上滚动的体验 */
  .box {
    position: relative;
    border-radius: 10px;
    margin: 0 10px 10px 0;
    flex: 0 0 calc(22.6%); /* 使用 calc() 考虑 margin */
    max-width: calc(22.6%); /* 同样考虑 margin */
    background: linear-gradient(to bottom right, #fff, #EDF0FF);
    padding: 0 10px 10px 10px;
    .tag{
      color: #fff;
      border-radius: 10px 0 10px 0;
      padding: 0 6px;
      font-size: 11px;
      margin-left: -10px;
      width: 60%;
      text-align: center;
    }
    .cell1{
      margin: 10px 0;
      height: 80px;
      width: 100%;
      text-align: center;
      font-weight: bold;
    }
    .cell2{
      margin-bottom: 10px;
      width: 100%;
      text-align: center;
    }
    .btn{
      border: 2px solid #4494E6;
      border-radius: 10px;
      color: #4494E6;
      height: 25px;
      font-size: 11px;
      padding: 5px 8px;
      background: none;
    }
    .active-btn{
      border: 2px solid #4494E6;
      border-radius: 10px;
      color: #fff;
      background: #4494E6!important;
      height: 25px;
      font-size: 11px;
      padding: 5px 8px;
      background: none;
      margin-left: 10px;
    }
    .success-box{
      position: absolute;
      bottom: 0;
      right: 0;
      .success {
        position: relative;
        width: 25px;
        height: 25px;
        background-image: url('@/assets/images/choosePhoto.png');
        background-position: center center;
        background-repeat: no-repeat;
        background-size: cover;
        clip-path: polygon(0 100%, 100% 0, 100% 100%);
        border-bottom-right-radius: 8px;
      }
    }

  }
}
</style>