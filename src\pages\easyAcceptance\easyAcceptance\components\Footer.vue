<template>
    <van-goods-action class="footer" style="padding: 0 10px;z-index:1112;">
<!--        <div style="margin: 0 12px;">
            <van-popover
                v-model="showPopover"
                trigger="click"
                placement="top-start">
                <div class="chat-options">
                    <div class="option-item" @click="toOrderList()">
                        <img src="@/assets/images/icon_16.png" />
                        <span>订单查询</span>
                    </div>
&lt;!&ndash;                    <div class="option-item">&ndash;&gt;
&lt;!&ndash;                        <img src="@/assets/images/icon_17.png" />&ndash;&gt;
&lt;!&ndash;                        <span>问题咨询</span>&ndash;&gt;
&lt;!&ndash;                    </div>&ndash;&gt;
&lt;!&ndash;                    <div class="option-item">&ndash;&gt;
&lt;!&ndash;                        <img src="@/assets/images/icon_18.png" />&ndash;&gt;
&lt;!&ndash;                        <span>预检功能</span>&ndash;&gt;
&lt;!&ndash;                    </div>&ndash;&gt;
                </div>
                <template #reference>
                    <img src="@/assets/images/icon_13.png" style="height: 26px; padding: 17px 0;" />
                </template>
            </van-popover>
        </div>-->
        <img v-show="inputType == '0'" src="@/assets/images/icon_142.png" style="height: 35px;" @click="inputType = '1'" />
        <img v-show="inputType == '1'" src="@/assets/images/icon_19.png" style="height: 35px; margin-right: 10px;" @click="inputType = '0'" />
        <van-field v-show="inputType == '0'" v-model="keyword" class="chat-area" label="" placeholder="开始聊天" :disabled="robotWorking" @keydown.enter.prevent="textSend" @focus="textFocus" />
        <div v-show="inputType == '1'" class="voice-button" :class="{'active': voiceState === '1'}" @touchstart.prevent="speakStart" @touchend.prevent="speakEnd">{{ voiceState === '0' ? '按住 说话' : '请说话，松开发送…' }}</div>
        <img v-show="inputType == '0'" src="@/assets/images/send2.png" style="height: 35px;" @click="textSend" />
    </van-goods-action>
</template>

<script>
import { mapState } from 'vuex'
import Recorder from 'js-audio-recorder';
import WadeMobile from "rk-native-plugin";
import { adapters  } from '@/assets/js/adapters.js';

export default {
    name: 'Footer',
    data() {
        return {
            keyword: '',
            showPopover: false,
            inputType: '0', // 0-文字输入 1-语音输入
            voiceState: '0', // 0-空闲 1-输入中
            recorder: null,
            audioChunks: [],
            isRecording: false,
            speakStartFlag: true
            // audioBlobUrl: null
        }
    },
    computed: {
        ...mapState([
            'robotWorking',
            'chatList'
        ]),
        isApp() {
            return WadeMobile.isIOS() || WadeMobile.isAndroid() || WadeMobile.isWP()
        }
    },
    async created() {},
    methods: {
        // 聊天发送
        textSend() {
            if (!this.keyword) {
                return;
            }
            const data = {
                textInput: this.keyword,
                type: '1'
            }
            this.$emit('userInput', data)
            this.keyword = ''
        },
      
      toOrderList(){
        this.$router.push({
          path: '/OrderList'
        });
      },
        // 语音输入开始
        async speakStart() {
            this.speakStartFlag = true
            if (this.robotWorking) {
                this.speakStartFlag = false
                return;
            }
            this.$emit('showChatBox', false)
            this.voiceState = '1'
            try {
                if (this.isApp) {
                    this.timeOutEvent = setTimeout(() => {
                        try {
                            WadeMobile.audioRecord(false, "wav");
                            this.timer = setTimeout(() => {
                                this.speakEnd();
                            }, 60000);
                        } catch (error) {

                        }
                    }, 100);
                }else {
                    this.recorder = new Recorder({
                        sampleBits: 16,         // 采样位数，支持 8 或 16，默认是16
                        sampleRate: 16000,      // 采样率，支持 11025、16000、22050、24000、44100、48000，根据浏览器默认值，我的chrome是48000
                        numChannels: 1,         // 声道，支持 1 或 2， 默认是1
                        // compiling: true,        // 是否边录边播
                    });
                    this.recorder.start().then(function (event) {
                        // 开始录音
                    });
                }


            } catch (err) {
                console.error("Error accessing media devices:", err);
            }
            this.isRecording = true;
        },
        // 语音输入结束
        async speakEnd(over) {
            if (this.robotWorking || !this.speakStartFlag) {
                return;
            }
            this.voiceState = '0'
            try {
                if (this.isApp) {
                    clearTimeout(this.timeOutEvent);
                    clearTimeout(this.timer);
                    this.isRecording = false;
                    WadeMobile.audioRecordEnd().then((path) => {
                        if (!path) return this.$toast("语音识别返回文件失败");
                        WadeMobile.getFileBase64(path, 9999999).then((res) => {
                            const base64Audio = res.base64;
                            // const size = parseInt(this.base64ByteSize(base64Audio));
                            // const size = base64ToBytes(base64Audio).length;
                            console.log(base64Audio);
                            const data = {
                                base64: base64Audio,
                                size:0,
                                type: '2'
                            }
                            this.$emit('userInput', data);
                            // this.base64Audio = {
                            //     base64Info: base64Audio,
                            //     audiolen: size,
                            // };
                            // console.log(this.base64Audio);
                            // if (this.base64Audio.base64Info){
                            // const audioData = this.base64ToFloat32Array(this.base64Audio.base64Info);
                            // // const audioData = this.base64ToFloat32Array(base64);
                            // console.log('audioData', audioData);
                            //
                            // let buffer = new ArrayBuffer(audioData.length * 2);
                            // let view = new DataView(buffer);
                            // for (let i = 0; i < audioData.length; i++) {
                            //     let s = Math.max(-1, Math.min(1, audioData[i]));
                            //     view.setInt16(i * 2, s < 0 ? s * 0x8000 : s * 0x7FFF, true);
                            // }
                            // websoket2Text(buffer,(data)=>{
                            //     const param = {
                            //         textInput: data,
                            //         type: '1'
                            //     }
                            //     this.$emit('userInput', param);
                            // });

                            // }

                        });
                    });
                }else {
                    this.recorder.stop();
                    // 获取 WAV 数据(Blob)
                    const wavBlob = this.recorder.getWAVBlob();
                    const base64Audio = await this.convertBlobToBase64(wavBlob);
                    const data = {
                        base64: base64Audio,
                        size:0,
                        type: '2'
                    }
                    this.$emit('userInput', data);
                    // // 展示语音radio内容
                    // const audioPlayback = document.getElementById('audioPlayback');
                    // if (audioPlayback) {
                    //     audioPlayback.src = URL.createObjectURL(wavBlob);
                    // }
                    // const reader = new FileReader();
                    // reader.readAsArrayBuffer(wavBlob);
                    // const vm = this;
                    // reader.onload = function () {
                    //     const buffer = reader.result;
                    //     websoket2Text(buffer,(data)=>{
                    //         const param = {
                    //             textInput: data,
                    //             type: '1'
                    //         }
                    //         vm.$emit('userInput', param);
                    //     });
                    // };
                    // // 销毁录音实例，置为null释放资源，fn为回调函数，
                    // this.recorder.destroy().then(() => {
                    //     this.recorder = null;
                    // });
                }
                // 结束录音


                // const data = {
                //     base64: base64Audio,
                //     size: parseInt(this.base64ByteSize(base64Audio.split(',')[1])),
                //     type: '2'
                // }
                // this.$emit('userInput', data)

            } catch (err) {
                console.error("Error accessing media stop:", err);
            }
            this.isRecording = false;
        },
        textFocus(event) {
          
            this.$emit('showChatBox', true)
          adapters.softKeyboard(event)
        },
        convertBlobToBase64(blob) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                // reader.onloadend = () => resolve(reader.result.split(',')[1]);
                reader.onloadend = () => resolve(reader.result);
                reader.onerror = reject;
                reader.readAsDataURL(blob);
            });
        },
        base64ByteSize(base64String) {
            // 移除末尾的填充字符=
            let padding = 0;
            if (base64String.endsWith('==')) {
                padding = 2;
            } else if (base64String.endsWith('=')) {
                padding = 1;
            }
            // 计算有效的Base64字符数
            const validBase64Chars = base64String.length - padding;
            // 计算原始字节数
            const byteSize = (validBase64Chars * 3) / 4;
            return byteSize;
        }
    }
}
</script>

<style lang="scss" scoped>
.van-goods-action {
    height: 60px;
    background-color: #fff;
}

.chat-area {
    width: 240px;
    margin: 0 12px;
    height: 40px;
    line-height: 40px;
    border-radius: 15px;
    padding: 0 12px;
    flex-grow: 1;
    border: 2px solid #548CF1;
}

.chat-options {
    padding: 6px 15px 6px 8px;

    .option-item {
        height: 35px;
        display: flex;
        align-items: center;
        font-size: 15px;
        padding: 0 5px;

        &:not(:last-child) {
            border-bottom: 1px solid #E5E5E5;
        }

        img {
            width: 16px;
            margin-right: 10px;
        }
    }
}

.chat-popup {
    bottom: 60px;
    background-color: #F5F6F7;
    height: calc(100% - 102px);
    padding: 10px;
    box-sizing: border-box;
}

/deep/ .van-overlay {
    bottom: 60px;
    height: calc(100% - 60px);
    background-color: rgba(0, 0, 0, 0);
}

.voice-button {
    width: 240px;
    margin-right: 12px;
    height: 40px;
    line-height: 40px;
    border-radius: 7px;
    background-color: #ffffff;
    text-align: center;
    font-size: 15px;
    flex-grow: 1;
    border: 2px solid #548CF1;
    &.active {
        background-color: var(--blue);
        color: #ffffff;
    }
}
</style>
<style lang="scss">
.footer {
  .van-field__control {
    text-align: left;
  }

  .van-field__control::-webkit-input-placeholder {
    text-align: center;
    direction: ltr;
  }

  .van-field__control::-moz-placeholder {
    text-align: center;
    direction: ltr;
  }

  .van-field__control:-ms-input-placeholder {
    text-align: center;
    direction: ltr;
  }

  .van-field__control::-ms-input-placeholder {
    text-align: center;
    direction: ltr;
  }
}
</style>