<template>
  <div style="background-color: white" >
    <h1 class="bh_title">IPTV信息</h1>
    <VanFieldCheckBox :columns="columnsData"  @checkBookedNotChooseNew="ywfNoNeed" @confirm="handleData"></VanFieldCheckBox>
    <div class="submit-container" v-show="iptvChooseCommLists!==null&& Object.keys(iptvChooseCommLists).length>=1">
      <span>已选择的超清商品:</span>
      <div class="list-item"  v-for="(item, index) in iptvChooseCommLists"
          >
              <span style="float: left">{{ item.iptvName }} 
              </span>
      </div>
    </div>
    <h1 class="bh_title" id="ywFreeGoods">移网附加优惠</h1>
    <div class="one-key-open-account">
      <div class="form-wrap">
        <div class="card-style" >
          <div class="contract-wrap" >
            <div class="made-list">
              <div  class="list-item"   :class="isYwfDiscnt==-1 ? 'active':''" v-show="ywfuInfoNoNeedIsShow" @click="ywfNoNeed()">
                <span href="javascript: void(0)" >不需要</span>
              </div>
              
              <div class="list-item"  v-for="(item, index) in commodityYWUlList"
                   :key="index" @click="commodityYWUlClick(item,$event)"   v-if="commodityYWUlList!==null&& Object.keys(commodityYWUlList).length>=1" >
              <span style="float: left" :id="'YWF'+item.ywGoodId">{{ item.ywGoodName }} 
              </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="submit-container">
        <van-button class="sub" @click="submit()" block>确定</van-button>
      </div>
    </div>
    
  </div>
</template>

<script>

import errorTips from '@/assets/bizComponents/errorTips/errorTips.vue';
import {mapMutations, mapState} from "vuex";
import VanFieldCheckSingleBox from "@/assets/bizComponents/select/VanFieldCheckSingleBox.vue"
import VanFieldCheckBox from "@/assets/bizComponents/select/VanFieldCheckbox.vue"


export default {
  name: "IptvGoodChoose",
  data() {
    return {
      orderPriceNum:0,
      columnsData:[],
      wslBoradBusinessList: [],
      ywfGoodInfoList:[],
      ywfGoodInfo:{},
      isYwfDiscnt:-1,
      commodityYWUlList:[],
      ywfuInfoNoNeedIsShow:true,
      pageYwfuPrice:0,
      choosedIPTV:"",
      iptvInvalid:[],
      iptvInfoCheckList:[],
      pageIptvPrice:0,
      ziFeiPrice:0,
      commodityChooseYWUlList:[],
      iptvChooseCommLists:[],
      iptvInfoList:[],
      iptvInfo:{}
    }
  },
  
  computed: {
    ...mapState([
      'jzfkOrderData',
        'shbmMsgInfo',
        'iptvOrderData',
        'iptvCacheList'
    ])
  },
  components: {
    errorTips,
    VanFieldCheckSingleBox,
    VanFieldCheckBox
  },
  mounted() {
    this.ywfNoNeed();
    this.$emit('startLoading', '')
    this.$http.post('/iptvReceive/iptvGoodsQry', {}).then(res => {
      this.$emit('endLoading', '')
      if("0000"==res.respCode){
       this.columnsData=[];
       if(res.respData.length>0){
         for(let i=0;i<res.respData.length;i++){
           let c={
             value:res.respData[i].commodityCode,
             label:res.respData[i].iptvName,
             commType:res.respData[i].commType
           }
           
           this.columnsData.push(c)
         }
       }
      }
      console.log(this.columnsData)
    })
  },
  methods: {
    ...mapMutations(['setIptvOrderData','setIptvCacheList']),
    submit(){
      if (this.iptvChooseCommLists.length<=0) {
        this.$toast('请至少选择一个超清商品~');
        return;
      }
      
      // if(this.iptvCacheList.iptvChooseCommLists.length>0){
      //   for(let i=0;i<this.iptvCacheList.iptvChooseCommLists.length;i++){
      //     this.iptvClickFee(this.iptvCacheList.iptvChooseCommLists[i],'+')
      //   }
      // }
      
      let data = {
        inputType: "1",
        type: '1',
        textInput: "iptvCommInfoSubmit",
        notifyFlag: '',
        taskName:'智能超清甩单'
      }
      this.$emit('newChatApi', data);
    },
    handleData(val,selectedDataList){
      this.iptvChooseCommLists=[];
      this.iptvCacheList.iptvChooseCommLists=[];
      this.setIptvCacheList(this.iptvCacheList)
      if(selectedDataList.length>0){
     for(let i=0;i<selectedDataList.length;i++){
       let iptv={
         commodityCode:selectedDataList[i].value,
         iptvName:selectedDataList[i].label,
         commType:selectedDataList[i].commType
       }
       // this.iptvClickFee(iptv,'+');最后点击确定调用
       this.iptvChooseCommLists.push(iptv);
       this.iptvCacheList.iptvChooseCommLists=this.iptvChooseCommLists;
       this.setIptvCacheList(this.iptvCacheList)
     }
     this.iptvClickYwfGoods();
      }
    },
    iptvClickYwfGoods(){
      this.iptvCodeString={};
      var iptvCode1=[];
      // console.log(this.iptvChooseCommLists.length+"pokokp")
      if(this.iptvChooseCommLists.length>0){
        for(var i in this.iptvChooseCommLists) {
          this.iptvCodeInfo={};
          this.iptvCodeInfo={
            iptvId: this.iptvChooseCommLists[i].commodityCode,
            iptvName: this.iptvChooseCommLists[i].iptvName,
            commType:this.iptvChooseCommLists[i].commType
            
          }
          iptvCode1.push(this.iptvCodeInfo)
        }
      }
      
      this.iptvCodeString.iptvInfoList=iptvCode1
      let req = {
        iptvCode:JSON.stringify(this.iptvCodeString),
        userCode:this.shbmMsgInfo.operatorId
      }
      this.$emit('startLoading', '')
      this.$http.post('/iptvReceive/getYwfGoodsByIPTV', req).then(res => {
        this.$emit('endLoading', '')
        if (res.respData.code != "0000") {
          this.dialogMsg = res.respMsg
          this.dialogVisible = true
          return;
        }
        this.commodityYWUlList=[];
        this.iptvCodeString={};
        this.iptvCodeInfo={};
        if( this.iptvOrderData.checkNumberData.userTypeFlag=='1'){
          for(var i=0; i<res.respData.ywFreeGoodsListResult.length; i++) {
            var ywGood = res.respData.ywFreeGoodsListResult[i];
            var ywGoodName = ywGood.commName;
            var ywGoodId = ywGood.commId;
            var ywGoodType=ywGood.commType;
            this.commodityYWUlList.push({
              ywGoodId:ywGoodId,
              ywGoodName:ywGoodName,
              ywGoodType:ywGoodType
            })
          }
        }
        console.log(this.commodityYWUlList.length+"--------")
      })
    },
    
    selectItemMade(item,e){
      const $this = $(e.target);
      if($this.parent().hasClass("active")){
        $this.parent().removeClass('active');
        }
      else{
        this.isYwfDiscnt=0;
        $this.parent().addClass("active");
      }
    },
    goodsClick(item,e) {
      const $this = $(e.target);
      var flag = item.commodityCode;//如果不需要，则没有调测费用  0：不需要iptv  其他：需要
      if(flag!=0){
        if($this.parent().hasClass("cur1")){
          $this.parent().removeClass('cur1');
          $this.parent().removeClass('cur');
          this.iptvChooseCommLists = this.iptvChooseCommLists.filter(i=>i!==item);
          if(this.iptvChooseCommLists.length>0){
            this.ywfNoNeed();
            this.iptvClickFee(item,1,"-");
            this.iptvClickYwfGoods();
          }
          else{
            this.iptvClickFee(item,1,"-");
            this.ywfNoNeed();
            this.commodityYWUlList=[];
          }
          this.orderPrice=0;
          this.orderPrice = this.pageIptvPrice+this.ziFeiPrice+this.pageYwfuPrice;
        }
        else{
          $this.parent().addClass("cur1");
          $this.parent().removeClass('cur');
          this.checkBookedNotChoose(item);

          this.orderPrice=0;
          this.orderPrice = this.pageIptvPrice+this.ziFeiPrice+this.pageYwfuPrice;

          // $this.parent().addClass("cur1");
          // $this.parent().removeClass('cur');
          // this.iptvChooseCommLists.push(item);
          // this.iptvClickFee(item,"+");
        }
        // this.iptvClickFee(this.iptvChooseCommLists)
      }
    },
    commodityYWUlClick(item,e) {
      // console.log(this.pageIptvPrice)
      const $this = $(e.target);
      if(this.iptvChooseCommLists.length==0){
        this.$toast("请先选择超清商品");
        return;
      }
      // console.log(item)
      if($this.parent().hasClass("active")){
        $this.parent().removeClass('active');
        this.commodityChooseYWUlList = this.commodityChooseYWUlList.filter(i=>i!==item);
        this.iptvOrderData.iptvGoodData.commodityChooseYWUlList=this.commodityChooseYWUlList;
        this.setIptvOrderData(this.iptvOrderData);
        console.log("this.iptvOrderData.iptvGoodData.commodityChooseYWUlList")
        console.log(this.iptvOrderData.iptvGoodData.commodityChooseYWUlList)

        if(this.commodityChooseYWUlList.length>0){
          this.isYwfDiscnt=0;
        }
        else{
          this.isYwfDiscnt=-1;
          this.ywfNoNeed();
          return;
        }
        this.orderPrice=0;
        this.orderPrice = this.pageIptvPrice+this.ziFeiPrice+this.pageYwfuPrice;
      }else{
        this.isYwfDiscnt=0;
        $this.parent().addClass("active");
        this.commodityChooseYWUlList.push(item);
        this.iptvOrderData.iptvGoodData.commodityChooseYWUlList=this.commodityChooseYWUlList;
        this.setIptvOrderData(this.iptvOrderData)
        console.log("this.iptvOrderData.iptvGoodData.commodityChooseYWUlList")
        console.log(this.iptvOrderData.iptvGoodData.commodityChooseYWUlList)
        this.orderPrice=0;
        this.orderPrice = this.pageIptvPrice+this.ziFeiPrice+this.pageYwfuPrice;
      }
      this.feeDesc = this.orderPrice > 0 ? "施工中收费" : " 无须支付";
    },
    commodityFeeClick(item,sub) {
      // var ywFlag = $this.attr("ywFlag");// 移网优惠资费无需处理
      // var ywGoodId = $this.attr("ywGoodId");//移网优惠资费无需处理
      var ywFlag = item.ywGoodId;
      var ywGoodId = item.ywGoodId;
      // var zwFlag = this.commodityZWUls.zwCommodityId;
      //调用定制商品查询
      let data = {
          "ywQryCommId": ywGoodId,
          "zwQryCommId": "0",
          "staffId":this.shbmMsgInfo.operatorId
      }
      this.$http.post('/outCallMonet/getYwfGoodsFeeByInterface', data).then((res) => {
        if (res.respCode != "0000") {
          this.dialogMsg = res.respMsg
          this.dialogVisible = true
          return;
        }
        // 接收返回参数
        item.ywfPrice=res.respData.ywfFee;
        if(sub=='+'){
          this.pageYwfuPrice=this.pageYwfuPrice+parseInt(res.respData.ywfFee);
        }
        else{
          this.pageYwfuPrice=this.pageYwfuPrice-parseInt(res.respData.ywfFee);
        }
        this.orderPrice=0;
        this.orderPrice =this.pageIptvPrice+this.ziFeiPrice+this.pageYwfuPrice;
        this.feeDesc = this.orderPrice > 0 ? "施工中收费" : " 无须支付";
      }).catch(e => {
        this.dialogMsg = e
        this.dialogVisible = true
      })

    },

    cancelSingleYWU(e,item,index){
      const $this = $(e.target);
      let id=this.commodityChooseYWUlList[index].ywGoodId
      $('#'+'YWF'+id).parent().removeClass('cur1')
      this.commodityChooseYWUlList = this.commodityChooseYWUlList.filter(i=>i!==item);
      if(this.commodityChooseYWUlList.length>0){
        this.commodityFeeClick(item,"-");

      }
      if(this.commodityChooseYWUlList.length==0){
        this.isYwfDiscnt=-1;
        this.commodityFeeClick(item,"-");
        this.ywfNoNeed();
      }
      else{
      }
      this.feeDesc = this.orderPrice > 0 ? "施工中收费" : " 无须支付";
    },
    ywfNoNeed(){
      this.isYwfDiscnt=-1;
      if(this.commodityChooseYWUlList.length>0){
        for(let i in this.commodityChooseYWUlList){
          let id=this.commodityChooseYWUlList[i].ywGoodId
          $('#'+'YWF'+id).parent().removeClass('active')
          this.pageYwfuPrice=0;
          this.orderPrice=0;
          this.orderPrice = this.pageIptvPrice+this.ziFeiPrice+this.pageYwfuPrice;
        }
      }
      let c=[{ywGoodId:'0',ywGoodName:'不需要'}];
      this.iptvOrderData.iptvGoodData.commodityChooseYWUlList=c;
      this.setIptvOrderData(this.iptvOrderData);
      this.commodityChooseYWUlList=[];
     
    },

    cancelSingle(e,item,index){
      const $this = $(e.target);
      let id=this.iptvChooseCommLists[index].commodityCode
      $('#'+'IPTV'+id).parent().removeClass('cur1')
      this.iptvChooseCommLists = this.iptvChooseCommLists.filter(i=>i!==item);
      this.iptvClickFee(item,"-");
    },
    checkBookedNotChooseNew(iptv){
      let data = {
        "reqData": {
          "iptvQryCommId": iptv,
          "iptvInvalidList": JSON.stringify(this.iptvOrderData.iptvInvalid),
          "iptvInfoCheckList":JSON.stringify(this.iptvOrderData.iptvInfoCheckList)
        },
      }
      console.log("checkBookedNotChooseNew")
      this.$http.post('/iptvReceive/checkBookedNotChoose', data).then(res => {
        if (res.respCode != "0000") {
          return false;
        }
        this.ywfNoNeed();
        return true;
      }).catch(e => {
        return false;
      })
    },
    checkBookedNotChoose(iptv){
      let data = {
        "reqData": {
          "iptvQryCommId": iptv.commodityCode,
          "userCode": this.shbmMsgInfo.operatorId,
          "iptvInvalidList": JSON.stringify(this.iptvInvalid),
          "iptvInfoCheckList":JSON.stringify(this.iptvInfoCheckList)
        },
      }

      this.$http.post('/iptv/checkBookedNotChoose', data).then(res => {
        if (res.respCode != "0000") {
          $('#'+'IPTV'+iptv.commodityCode).parent().removeClass('cur1');
          this.dialogMsg = res.respMsg
          this.dialogVisible = true
          return;
        }
        this.iptvChooseCommLists.push(iptv);
        this.iptvClickFee(iptv,"+");
        this.ywfNoNeed();
        this.iptvClickYwfGoods();
      }).catch(e => {
        this.dialogMsg = e
        this.dialogVisible = true
      })
    },
    iptvClickFee(iptv,sub){
      let data = {
          "iptvQryCommId": iptv.commodityCode,
          "iptvJmTag": "0"
      }

      this.$http.post('/iptvReceive/getIptvGoodsFeeByInterface', data).then(res => {
        if (res.respCode != "0000") {
         this.$toast(res.resMsg);
          return;
        }
        // 接收返回参数
        
        iptv.goodsJMPrice=res.respData.goodsJmPrice;
        iptv.goodsPrice=res.respData.goodsPrice;
        this.iptvOrderData.iptvGoodData.iptvList.push(iptv);
        this.setIptvOrderData(this.iptvOrderData);
        if(sub=='+'){
          this.ziFeiPrice=this.ziFeiPrice+parseInt(res.respData.goodsJmPrice);
          this.pageIptvPrice=this.pageIptvPrice+parseInt(res.respData.goodsPrice);
          this.orderPriceNum=this.ziFeiPrice+this.pageIptvPrice;
        }
        else{
          this.ziFeiPrice=this.ziFeiPrice-parseInt(res.respData.goodsJmPrice);
          this.pageIptvPrice=this.pageIptvPrice-parseInt(res.respData.goodsPrice);
          this.orderPriceNum=this.ziFeiPrice+this.pageIptvPrice;

        }
        this.iptvOrderData.orderPrice=this.orderPriceNum;
        this.setIptvOrderData(this.iptvOrderData)
      }).catch(e => {
        this.dialogMsg = e
        this.dialogVisible = true
      })
    },
  }
}
</script>
<style lang="scss">
.submit-container{
  .sub{
    border: 2px solid rgba(73,124,246,1);
    border-radius: 5px;
    color: rgba(73,124,246,1);
    margin-top: 20px;
  }
}

.one-key-open-account{
  overflow: hidden;
  .content {
    min-height: 50px;
  }

  .demo {
    width: 300px;
    height: 100px;
    background-color: #646566;
  }
  .card-style{
    margin: 0;
    //padding-bottom: 15px;
    width: 100%;
    height: auto;
    background: #FFFFFF;
    border-radius: 8px;
    border: 1px solid #FFFFFF;
  }
  .form-wrap {
    padding: 0;
    background: #F5F5F5;
    .contract-wrap {
      margin: 0;
      /*border-bottom: 1px solid #F7F7F7;*/
      p {
        padding: 10px 0;
        height: 34px;
        line-height: 34px;
        font-size: 14px;
        color: #333;
      }
      .desc{
        color: #ee0a24;
        font-size: 12px;

      }
      .made-list {
        display: flex;
        flex-direction: column;
        .list-item {
          padding: 0 10px;
          height:fit-content;
          line-height: 30px;
          font-size: 14px;
          margin: 5px 10px;
          background: #F5F8FA;
          border: 1.5px dashed rgba(203, 203, 203, 1);
          border-radius: 8px;
        }
        .active {
          background: #EDF7FF;
          border: 1px solid rgba(80,148,245,1);
        }
      }
      .contract-list {
        display: flex;
        flex-direction: column;
        .list-item {
          padding: 0 10px;
          width: fit-content;
          height: fit-content;
          line-height: 30px;
          font-size: 14px;
          color: #999;
          margin-bottom: 10px;
          background: #F7F7F7;
          border-radius: 4px;
          border: 1.5px solid #F7F7F7;
        }
        .active {
          color: #0081FF;
          background: rgba(0,129,255,0.10);
          border: 1px solid #0081FF;
        }
      }
    }
    .border {
      background: #FFFFFF;
      border-radius: 4px;
      border: 1px solid #CACACA;
    }
    .blueBorder {
      background: rgba(0, 129, 255, 0.05);
      border-radius: 12px;
      border: 1px solid #0081FF;
      width: 300px;
      margin-left: 20px;
    }
    .notBlueBorder {
      border-radius: 12px;
      width: 300px;
      margin-left: 20px;
    }
    .made-name{
      color: #323233;
      font-size: 0.37333rem;
      line-height: 0.64rem;
    }
    .made-van {
      /*padding: 5px 0px;*/
      /*background: #c8e3fe;*/
      /*border-radius: 12px;*/
      /*font-size: 14px;*/
      align-content: center;
      width: 300px;
      text-align: center;
    }

  }
}


</style>

<style lang="scss" scoped>
.card-verification {
  background-color: rgb(57,159,254);
  .desc {
    color: black;
    font-size: 13px;
    line-height: 20px;
  }
  .margin-two {
    bottom: 10px;
    left: 15px;
    right: 15px;
    z-index: 2;
    position: absolute;
  }
  .van-buttons {
    border-radius: 4px;
    height: 44px;
    font-size: 13px;
    color: #FFFFFF;
    background-color: #0081FF;
  }
  .font {
    color:#333333;
  }

}

</style>