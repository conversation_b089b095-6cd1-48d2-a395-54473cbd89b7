<template>
    <div>
        <div class="part" style="margin-bottom: 0;">
            <p v-html="showContent"></p>
            <!-- <audio :src="audio" controls ref="audio" style="display: none;" autoplay></audio> -->
        </div>
    </div>
</template>

<script>
import { mapMutations } from 'vuex'
// import { txtToVoiceInfo } from '../../../assets/js/func.js';

export default {
    name: 'TextResponse',
    props: {
        params: {
            text: ''
        }
    },
    data() {
        return {
            content: '',
            showContent: '',
            interval: ''
            // audio: ''
        }
    },
    async created() {
        // this.$emit('startLoading', '')
        // let params = {
        //     txtInfo: this.params.text
        // }
        // this.audio = await txtToVoiceInfo(params)
        // this.$emit('endLoading', '')
        // this.$emit('showComponent', '')
        // this.$refs.audio.load()
        this.setRobotWorking(true)
        let count = 0
        this.content = this.params.text;
        this.interval = setInterval(()=> {
            if (count < this.content.length) {
                this.showContent += this.content[count]
                count ++;
                this.$nextTick(() => {
                    this.$emit('scrollToBottom', '')
                })
            } else {
                this.setRobotWorking(false)
                clearInterval(this.interval)
            }
        }, 10)
    },
    methods: {
        ...mapMutations([
            'setRobotWorking'
        ]),
    },
    beforeDestroy() {
        clearInterval(this.interval)
    }
};
</script>

<style lang="scss" scoped>
</style>
