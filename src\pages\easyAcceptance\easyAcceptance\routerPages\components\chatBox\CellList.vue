<template>
  <van-cell-group>
    <van-cell :title="title" :value="values[0] && values[0].commName" />
    <template v-if="other.length">
      <van-cell v-for="item in other" title=" " :value="item.commName" :key="item.commId" />
    </template>
  </van-cell-group>
</template>
<script>
export default {
    name: 'CellList',
    props:{
      title: {
        type: String,
        default: '',
      },
      values:{
        type: Array,
        default: () => []
      }
    },
    computed: {
      other:function(){
        return this.values.length > 1 ? this.values.filter((_,i) => i > 0) : []
      }
    }
}
</script>
