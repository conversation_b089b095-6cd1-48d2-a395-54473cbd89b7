<template>
    <div>
        <van-button
            icon="user-o"
            @click="faceAuth"
            type="primary"
        >
            活体识别
        </van-button>
        <div class="content-container">
            <div class="title">
                img
            </div>
            <div class="tip">
                (base64格式，省略中间部分)
            </div>
            <div class="content">
                {{ img }}
            </div>
            <img
                :src="originImg"
                alt=""
                width="200"
                height="300"
            >
        </div>
        <div class="content-container">
            <div class="title">
                img_best
            </div>
            <div class="tip">
                (base64格式，省略中间部分)
            </div>
            <div class="content">
                {{ imgBest }}
            </div>
            <img
                :src="originImgBest"
                alt=""
                width="200"
                height="300"
            >
        </div>
    </div>
</template>

<script>
import WadeMobile from 'rk-native-plugin';

export default {
    name: 'FaceAuth',
    data() {
        return {
            originImg: '',
            img: '',
            originImgBest: '',
            imgBest: ''
        };
    },
    methods: {
        faceAuth() {
            WadeMobile.faceAuthentication([0, 0, 1]).then((result) => {
                if (result.retCode === '0') {
                    let { img, img_best } = result;
                    this.originImg = 'data:image/jpg;base64,' + img;
                    this.originImgBest = 'data:image/jpg;base64,' + img_best;
                    //此处为了方便展示，截取了部分返回的base64格式的照片
                    img = img.slice(0, 60) + '......' + img.slice(img.length - 60, img.length);
                    img_best = img_best.slice(0, 60) + '......' + img_best.slice(img_best.length - 60, img_best.length);
                    this.img = img;
                    this.imgBest = img_best;
                } else {
                    this.img = '';
                    this.imgBest = '';
                    this.originImg = '';
                    this.originImgBest = '';
                    this.$toast('活体认证照片采集失败，请重新尝试人脸识别');
                }
            }).catch((e) => {
                // eslint-disable-next-line no-console
                console.log(e);
            });
        }
    }
};
</script>

<style scoped lang="scss">
.content-container {
    font-size: 16px;
    padding: 5px;

    .title {
        font-weight: bold;
    }

    .tip {
        color: #cccccc;
        font-size: 14px;
    }

    .content {
        min-height: 100px;
        word-break: break-all;
    }
}
</style>
