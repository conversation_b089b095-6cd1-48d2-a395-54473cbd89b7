<template>
  <div class="card-verification" style="background-color: white">
    <div class="card-group">
      <van-cell-group class="tip" style="margin: 0px">
        <van-cell title="您的主卡套餐信息如下" class="desc"   style=" background-color: white;color: black;padding:5px 0px" />
        <!--          <div style="height: 2px;background-color:rgb(247,247,247)"></div>-->
      </van-cell-group>
      <van-cell-group class="card-list">
        <van-cell  title="套餐名称:" :value=resp.productName  style=" background: linear-gradient(to right, rgba(230,244,255,0.8), rgb(230,244,255));" />
        <van-cell title="姓名:" :value=resp.custName_tm style=" background: linear-gradient(to right, rgba(230,244,255,0.8), rgb(230,244,255));"  />
        <van-cell  title="证件号码:" :value=resp.certCode_tm  style=" background: linear-gradient(to right, rgba(230,244,255,0.8), rgb(230,244,255));" />
        最多选择{{resp.deputyCardNum}}张副卡
      </van-cell-group>

    </div>
  </div>
</template>

<script>

import errorTips from '@/assets/bizComponents/errorTips/errorTips.vue';
import {copyText} from "../../../assets/js/func";
import {mapState} from "vuex";
export default {
  name: "OrderInfo",
  data() {
    return {
      imageList: [
        require('../../../images/arrow.png'),
        require('../../../images/add.png')
      ],
      resp:{
        productName:'',
        certCode:'',
        custName:'',
        deputyCardNum:''
      }
    }
  },
  computed: {
    ...mapState([
      'jzfkOrderData'
    ])
  },
  components: {
    errorTips
  },
  mounted() {
    this.resp=this.jzfkOrderData.mainNumberCheckData.custInfo
  },

  methods: {
    maskPhoneNumber(phoneNumber) {
      return phoneNumber.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    },

    maskPspt(pspt) {
      return pspt.replace(/^(.{4})(.*)(.{4})$/, '$1*********$3');
    },
    maskName(name) {
      return name.substring(0, 1) + '*'.repeat(name.length - 1);
    }

  }
}
</script>
<style lang="scss">
.card-verification {
  width:100%;
  margin: auto;
  .card-group {
    background-color: white;
    margin: 0px 10px;
    padding:0px 5px;
    .tip {
      .van-cell__value {
        color: black;
        text-align: right;
      }}

    .card-list {
      .van-cell__title {
        font-size: 13px;
        color: #666666;
      }
      .van-cell__value {
        color: black;
        text-align: right;
        font-size: 13px;
      }
      .van-cell {
        color: black !important;
        padding: 0px 16px;
      }
      .van-cell::after {
        color: black !important;
        border-bottom: 0;

      }
    }
    .van-hairline--top-bottom::after, .van-hairline-unset--top-bottom::after {
      border-width: 0 0;
    }
  }
  .card {
    .van-cell__title {
      font-size: 13px;
      color: #000000;
    }
    .checkButton {
      width: 70px;
      height: 30px;
      background: #0081FF;
      border-radius: 4px;
    }
    .checkFont {
      font-size: 13px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 14px;
    }
  }
  .check{
    .van-cell__title {
      font-size: 13px;
      color: #333333;
    }
  }
}

</style>

<style lang="scss" scoped>
.card-verification {
  background-color: rgb(57,159,254);
  .desc {
    color: black;
    font-size: 13px;
    line-height: 20px;
  }
  .margin-two {
    bottom: 10px;
    left: 15px;
    right: 15px;
    z-index: 2;
    position: absolute;
  }
  .van-buttons {
    border-radius: 4px;
    height: 44px;
    font-size: 13px;
    color: #FFFFFF;
    background-color: #0081FF;
  }
  .font {
    color:#333333;
  }

}

</style>

