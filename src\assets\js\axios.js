import axios from 'axios';
import { Mobile } from 'rk-web-utils';
import {getCookie, getQueryString} from "./utils";

let sessionid = "111"

// if (Mobile.isApp()) {
//     sessionid = getCookie('sessionid');
// } else {
//     sessionid = sessionStorage.getItem('SESSION_ID')
// }
const instance = axios.create({
    // baseURL: env === 'gray' ? '/preorderAppIntf/app' : '/preorderAppIntf/app',
    baseURL: '/shbm/api',
    headers: {
        'sessionid': sessionid,
        'reqToken': sessionid // todo-wq
        // 'reqToken': 'eaf85bef893c56ba6f41b8240e7d289edb1718ba'
    }
});

instance.interceptors.response.use(function (response) {
    return response.data;
}, function (error) {
    return Promise.reject(error);
});

export default instance;

