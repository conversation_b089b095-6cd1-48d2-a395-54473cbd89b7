#!/bin/sh
echo '************* www.jlwoying10010.com' >> /etc/hosts
echo '************** api.goseek.cn' >> /etc/hosts
echo '************** oapi.dingtalk.com' >> /etc/hosts
echo '************** mobsec-dianhua.baidu.com' >> /etc/hosts
echo '********** sit3.portal.unicom.local' >> /etc/hosts
echo '************ name.portal.unicom.local' >> /etc/hosts
echo '************ sso.portal.unicom.local' >> /etc/hosts
echo '********** jl.smtp.chinaunicom.cn' >> /etc/hosts
echo "wcmdemo"
#/usr/local/tomcat/bin/catalina.sh start && tail -f /etc/hosts
#add 20231219全知可以的探针add wcm
#nohup /opt/qzkj_agent_client/bin/client -c /opt/qzkj_agent_client/config/client.conf >/dev/null &
/usr/sbin/nginx -g 'daemon off;'
