apiVersion: apps/v1
kind: Deployment
metadata:
  name: shbmasr-front-pro
  namespace: shbmasr-pro
  labels:
    app: shbmasr-front-pro
spec:
  replicas: 1
  selector:
    matchLabels:
      app: shbmasr-front-pro
  template:
    metadata:
      name: shbmasr-front-pro
      labels:
        app: shbmasr-front-pro
    spec:
      containers:
        - name: shbmasr-front-pro
          image: 'harbor.dcos.xixian.unicom.local/shzwt/shbmasr-front-pro'
          ports:
            - containerPort: 80
          imagePullPolicy: IfNotPresent
          volumeMounts:
            - name: shbmasr-front-pro
              mountPath: /mnt
      volumes:
        - name: shbmasr-front-pro
          hostPath:
            path: /var/lib/docker/log/shbm/shbmasr-front-pro
      restartPolicy: Always