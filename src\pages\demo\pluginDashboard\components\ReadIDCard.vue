<template>
    <div>
        <van-button
            @click="readIDCard"
            type="primary"
        >
            读取身份证
        </van-button>
        <van-cell
            title="姓名"
            :value="IDCardInfo.name"
            size="large"
        />
        <van-cell
            title="有效期"
            :value="IDCardInfo.period"
            size="large"
        />
        <van-cell
            title="性别"
            :value="IDCardInfo.sex"
            size="large"
        />
        <van-cell
            title="身份证号"
            :value="IDCardInfo.cardNo"
            size="large"
        />
        <van-cell
            title="地址"
            :value="IDCardInfo.address"
            size="large"
        />
        <img
            :src="IDCardInfo.avatar"
            width="200"
            height="300"
            alt=""
        >
    </div>
</template>

<script>
import { mapState } from 'vuex';
import WadeMobile from 'rk-native-plugin';

export default {
    name: 'ReadIDCard',
    data() {
        return {
            IDCardInfo: {
                name: '',
                period: '',
                sex: '',
                address: '',
                avatar: '',
                cardNo: ''
            }
        };
    },
    computed: {
        ...mapState(['btName', 'address', 'readerIP'])
    },
    methods: {
        readIDCard() {
            WadeMobile.readIDCard([this.btName, this.address, '1', this.readerIP]).then((result) => {
                if (WadeMobile.isAndroid()) {
                    this.IDCardInfo = {
                        name: result.name,
                        period: result.effectDate + '-' + result.expireDate,
                        sex: result.sex,
                        address: result.address,
                        avatar: 'data:image/png;base64,' + result.avatar,
                        cardNo: result.cardNo
                    };
                } else if (WadeMobile.isIOS()) {
                    this.IDCardInfo = {
                        name: result.Name,
                        period: result.effectDate + '-' + result.expireDate,
                        sex: result.Sex,
                        address: result.Address,
                        avatar: 'data:image/png;base64,' + result.avatar,
                        cardNo: result.CardNo
                    };
                }
            }).catch((e) => {
                // eslint-disable-next-line no-console
                console.log(e);
            });
        }
    }
};
</script>

<style scoped>

</style>
