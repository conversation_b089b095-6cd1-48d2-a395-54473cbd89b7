<template>
    <div>
        <van-button
            @click="getMemCache"
            type="primary"
        >
            获取临时缓存
        </van-button>
        <div class="content">
            {{ memUsername }}
        </div>
        <van-button
            @click="getLocalCache"
            type="primary"
        >
            获取永久缓存
        </van-button>
        <div class="content">
            {{ localUsername }}
        </div>
        <van-button
            @click="close"
            type="primary"
        >
            关闭当前页面，返回上一页
        </van-button>
    </div>
</template>

<script>
import { Mobile } from 'rk-web-utils';

export default {
    name: 'Main',
    data() {
        return {
            memUsername: '',
            localUsername: '',
            serverData: ''
        };
    },
    methods: {
        getMemCache() {
            Mobile.getMemoryCache('mem-username').then((result) => {
                this.memUsername = result;
            });
        },
        getLocalCache() {
            Mobile.getOfflineCache('local-username').then((result) => {
                this.localUsername = result;
            });
        },
        close() {

            const param = {
                phone: '13000000000',
                username: 'chinaunicom'
            };
            Mobile.closeH5(JSON.stringify(param));
        }
    },
    mounted() {

    }
};
</script>

<style scoped lang="scss">
.content {
    min-height: 50px;
}

.demo {
    width: 300px;
    height: 100px;
    background-color: #646566;
}
</style>
