import { Dialog } from 'vant';
import axios from '@/assets/js/axios.js';
import * as _ from 'lodash';
import pinYin from 'pinyin-match';

let errConfig;
/**
 * 拼音搜索
 * @param  {Array} list 需要搜索的数组
 * @param  {String} value 搜索的关键字
 * @param {String} value 搜索关键字
 * key  list的元素为对象时需要传入对象键名
 */
export function pinYinSearch(list, value, key) {
    let resList = [];
    if (key) {
        resList = list.filter(item => {
            return pinYin.match(item.COMM_NAME, value);
        });
    } else {
        resList = list.filter(item => {
            return pinYin.match(item, value);
        });
    }
        return resList.sort((a,b)=>{
             return a[key].length-b[key].length;
        })
}

// function getReportErrConfig() {
//     return new Promise((resolve, reject) => {
//         if (errConfig) {
//             resolve(errConfig);
//         }else {
//             Mobile.getMemoryCache("errPopConfig").then((errPopConfig) => {
//                 if (errPopConfig) {
//                     errPopConfig = JSON.parse(errPopConfig)
//                     errConfig = errPopConfig;
//                     resolve(errConfig);
//                 }else {
//                     axios.post("/questionCollection/getH5Url", {}).then((res) => {
//                         if (res.respCode === '0000') {
//                             Mobile.getMemoryCache('STAFF_ID').then((staffId) => {
//                                 const config = {
//                                     staffId,
//                                     ...res.respData
//                                 };
//                                 Mobile.setMemoryCache("errPopConfig", config);
//                                 errConfig = config;
//                                 resolve(errConfig);
//                             })
//                         }else {
//                             reject();
//                         }
//                     }).catch(() => {
//                         reject();
//                     })
//                     reject();
//                 }
//             }).catch(() => {
//                 reject();
//             })
//         }
//     })
// }

export function alertError({title= "温馨提示", message= "出错了", confirmButtonText = "关闭", cancelButtonText = "关闭", closeOnPopstate = true}) {
    return new Promise((resolve, reject) => {
        Dialog.alert({
            title,
            message,
            confirmButtonText,
            confirmButtonColor: '#0081ff',
            closeOnPopstate,
        }).then(() => {
            resolve();
        })
        // 错误弹窗则触发听云埋点事件，未使用统一报错弹窗组件的后续考虑对Dialog组件监听实现--20211021
        // tingYunUtil.addEvent(title, message);
    })
}

/***
 * @param title
 * @param message
 * @param confirmButtonText
 * @param cancelButtonText
 * @description 该弹窗层级最高：9999，用于子应用异地校验等需要同时展示的弹窗。因为vant组件中的Dialog弹窗会被相互覆盖。默认展示一个确认按钮，
 * @returns {Promise<unknown>}
 */

export function confirmAsync({title = '温馨提示', message = '出错了', confirmButtonText = "确定" , cancelButtonText = ''}) {
    return new Promise((resolve, reject) => {
        let maskID = 'confirm-' + parseInt(Math.random() * 100000);
        $('body').append('<div class="confirm-mask" id="' + maskID + '"></div>');
        let content = '<div class="confirm-content">' +
            '<div class="confirm-title">' + title + '</div>' +
            '<div class="confirm-msg">' + message + '</div>' +
            '<div class="confirm-btn-area" id="btn-'  + maskID + '"></div></div>';
        // 默认展示一个按钮
        let buttonNode = '<div class="confirm-col1"><button class="confirm-btn confirm-single-btn">' + confirmButtonText + '</button></div>'
        // 传入cancelButtonText字段，展示2个按钮
        if (cancelButtonText) {
            buttonNode = '<div class="confirm-col2"><button class="confirm-btn confirm-left-btn">' + cancelButtonText + '</button></div>' +
                         '<div class="confirm-col2"><button class="confirm-btn confirm-right-btn">' + confirmButtonText + '</button></div>'
        }
        $('#' + maskID).append(content);
        $('#btn-' + maskID).append(buttonNode);
        // 取消按钮
        $('#' + maskID).find('.confirm-left-btn').on('click', ()=> {
            reject();
            $('#' + maskID).remove();
        });
        // 确定按钮
        $('#' + maskID).find('.confirm-right-btn, .confirm-single-btn').on('click', ()=> {
            resolve();
            $('#' + maskID).remove();
        });

        // 弹窗则触发听云埋点事件
        // tingYunUtil.addEvent(title, message);
    });
}

//durType默认为1，1表示自动执行确认的操作，2表示自动执行取消的操作
//duration 是弹框取消的时间，默认为2
export function setTimeAlert({title="温馨提示", message="出错了", confirmButtonText = "关闭", closeOnPopstate = true,duration=2,durType=1}){
    return new Promise((resolve, reject) => {
        Dialog.confirm(
            {
                title,
                message,
                confirmButtonText,
                confirmButtonColor: '#0081ff',
                closeOnPopstate,
            }
        ).then(()=>{
            resolve();
        }).catch(()=>{
            reject();
        })
        setTimeout(function (){
            if(1==durType) {
                resolve();
                Dialog.close();
            }else if(2==durType){
                reject();
                Dialog.close();
            }
        },duration*1000);
    });
}
/**
 * 小驼峰转换（只做key转换）
 * @method trans2CamelCase
 * @param {Object} [sourceData] 源数据
 * @param {Boolean} [keepOriginalType] 保持原数据类型，默认为true；否则，其他非字符串类型的数据转为字符串输出
 * @return Object
 */
export function trans2CamelCase(sourceData, keepOriginalType = true) {
    let typeName = getTypeName(sourceData), goalData;
    if (typeName === "[object Array]") {
        goalData = transArray(sourceData, keepOriginalType);
    } else if (typeName === "[object Object]") {
        goalData = transObject(sourceData, keepOriginalType);
    }
    return goalData;
}

/**
 * 获取数据类型
 * @method getTypeName
 * @param {Object} [param] 值
 * @return String
 */
export function getTypeName(param) {
    return Object.prototype.toString.call(param);
}

function transArray(sourceArray, keepOriginalType) {
    let tempArray = _.cloneDeep(sourceArray);
    tempArray = _.map(tempArray, function (arrValue) {
        let valueType = getTypeName(arrValue);
        if (valueType === "[object Object]") {
            return transObject(arrValue, keepOriginalType);
        } else if (valueType === "[object Array]") {
            return transArray(arrValue);
        }
    });
    return tempArray;
}

function transObject(sourceObject, keepOriginalType) {
    let tempObject = _.cloneDeep(sourceObject);
    tempObject = _.mapKeys(tempObject, function (value, key) {
        return _.camelCase(key);
    });
    _.forIn(tempObject, function (objValue, objKey) {
        let valueType = getTypeName(objValue);
        if (valueType === "[object Array]") {
            tempObject[objKey] = transArray(objValue);
        } else if (valueType === "[object Object]") {
            tempObject[objKey] = transObject(objValue, keepOriginalType);
        }else {
            if (!keepOriginalType && !isEmpty(objValue)) {
                tempObject[objKey] = objValue.toString();
            }
        }
        return tempObject;
    });
    return tempObject;
}

function isEmpty(value) {
    let flag = false
    if (value == '' || value == 'undefined' || value == undefined || value == null || value == 'null') {
        flag = true
    }
    return flag
}

/**
 * 获取当前url中的参数，并转换为对象
 * @method getSearchObject
 * @return Object
 */
export function getSearchObject() {
    const search = window.location.search ? window.location.search.substring(1) : '';
    let searchObj = {};
    search.split('&').map(item => {
        const temp = item.split('=');
        const key = temp[0];
        const value = temp[1];
        let obj = {};
        obj[key] = value;
        if (key && value) {
            searchObj = {...searchObj, ...obj}
        }
    });
    return searchObj;
}

export function getCurrentFormattedTime() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
/**
 * 定位插件封装
 * @returns {*}
 */
// export function location() {
//     return Mobile.getMemoryCache('staffInfo').then(result => {
//         const staffInfo = JSON.parse(result || '{}');
//         const provinceCode = staffInfo.PROVINCE_CODE;
//         return axios.post('/paramController/getSwitch', {
//             'provinceCode': provinceCode,
//             'keyCode': 'locationServiceSwitch'
//         });
//     }).then(res => {
//         let locationServiceSwitch = res.respData || '1';
//         return WadeMobile.location(locationServiceSwitch);
//     }).catch(() => {
//         return WadeMobile.location('1');
//     });
// }
