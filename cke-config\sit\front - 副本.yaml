apiVersion: apps/v1
kind: Deployment
metadata:
  name: front
  labels:
    app: front
spec:
  replicas: 1
  selector:
    matchLabels:
      app: front
  template:
    metadata:
      name: front
      labels:
        app: front
    spec:
      containers:
      - name: front
        image: 'harbor.dcos.xixian.unicom.local/shzwt-sit/front'
        ports:
        - containerPort: 80
        imagePullPolicy: IfNotPresent
        volumeMounts:
        - name: front
          mountPath: /mnt
      volumes:
      - name: front
        persistentVolumeClaim:
          claimName: shzwtsitcfs
          readOnly: false
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: front
  labels:
    app: front
  annotations:
    lb.cke.tg.unicom/target-vports: 80-22601
spec:
  ports:
  - port: 80
    targetPort: 80
  selector:
    app: front