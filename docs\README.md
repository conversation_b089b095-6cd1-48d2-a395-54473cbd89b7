## 开发流程

1. 前置说明

   本工程默认采用vue + vue-router开发，其中vue采用单文件组件的方式开发，因此需熟悉vue单文件组件、vue-router，此外还需要了解webpack、npm命令等。
   node版本推荐使用12.6.0(或node12的其他版本，其余版本可能存在npm包不兼容的问题，有特殊情况需使用其他版本的需自行解决npm包版本兼容性问题)
2. 安装依赖（首次运行需要，后续package.json无新增依赖不需要执行）

   ```text
   //在根目录下执行
   npm install
   ```

3. 生成新的页面模板文件

   ```text
   npm run create
   //根据提示选择分类并输入菜单的名称menuName以及是否集成vuex
   //menuName为新开发的菜单的名称（每个菜单为一个单页应用），不可与已有应用重复，且不可以为common、vendor、runtime（这些名称用于splitChunks）
   //例如需新开发一个现场开户菜单（openAccount），则在提示输入菜单名称后面输入openAccount，按回车继续
   //根据提示输入y或n，选择是否在生成的模板文件中集成vuex，按回车继续
   //执行成功后，会在src/pages目录下生成openAccount目录，其中包含该应用所需的初始模板文件，模板文件的说明见备注
   ```

   * 备注

     生成的模板文件如下所示：

     ```text
     ├─ openAccount		//以menuName命名的目录,该应用的代码在此目录下开发
     │  ├─ components	//包含当前单页应用的组件的目录
     │  ├─ routerPages	//包含当前单页应用的各路由页面组件的目录
     │  │  └─ Main.vue 	//模板自带的一个路由页面组件，可额外增加任意的其他路由页面的组件，同时在router.js中注册路由 
     │  ├─ store	    //包含vuex store的目录（选择集成vuex才会有）
     │  │  └─ index.js 	//vuex的入口文件，可根据实际需求，将state/mutations/actions/getters/modules从外部引入
     │  ├─ App.vue		//挂载路由页面的组件，无特殊需求不需要修改
     │  ├─ router.js	//路由注册文件
     │  ├─ index.html	//虚拟DOM挂载的HTML模板文件，无特殊需求不需要修改
     │  ├─ index.js		//单文件应用入口文件
     │  └─ config.json	//用于设置index.html标签的配置文件（目前仅支持`title`设置标签）
     ```

4. 编译打包文件

   编译打包文件提供了三个命令dev、build、test，三个命令的详细说明如下：

   ```text
   npm run dev
   //npm run dev会在生成的代码中开启sourceMap、禁用代码压缩、默认在8081端口启动静态资源服务器，同时监听frontSrc目录下的源码，在修改代码后自动编译（自动编译速度很快）。
   //在某些IDEA默认自动保存代码的情况下，会频繁的自动编译，编译期间和IDEA建立索引期间可能会影响IDEA的代码提示功能，并影响性能，为解决该问题。可关闭IDEA的自动保存功能，改为手动保存。
   npm run build
   //npm run build会关闭sourceMap，启用代码压缩，用于生产环境的代码构建
   npm run test
   //npm run test会关闭sourceMap，启用代码压缩，用于测试环境的代码构建

   ```

   * 备注
     1. sourceMap用于映射编译生成的代码和源码，便于开发时调试，由于sourceMap会使生成的文件体积变大，因此dev命令仅限开发阶段使用；
     2. 代码压缩会显著减少生成的文件的体积，但压缩时编译打包速度较慢，建议在构建生产环境的代码时再使用npm run build命令；
     3. 部分版本的IDEA在编辑器界面或Terminal工具栏焦点失活状态下不会自动编译文件或显示新生成的文件，需要点击使焦点激活后才会生效，优化办法请自行搜索IDEA相关设置。

5. 本地部署（上一步选择npm run dev时跳过这一步）
    本地部署是为了在特殊情况下，需要本地调试生产包时，方便本地调试使用，绝大多数情况下本地开发时不需要这一步，生产环境中以下功能由nginx提供
    只有上一步选择了npm run dev以外的其他命令时才需要执行当前命令进行本地部署，如果上一步使用了npm run dev编译，则已经使用webpack-dev-server自动完成了部署，不需要进行这一步骤。
    ```text
    npm run server -p 8081 -t http://localhost:3000
    //npm run server -p 静态资源服务器端口号 -t 后端接口服务器地址
    //执行上述命令，会将编译生成在dist目录下的静态资源部署在本地，静态资源服务器地址为“本地IP:8081”,将ajax请求转发至http://localhost:3000
    //-p -t参数可省略，省略时采用默认值，默认值可在proxy.js中修改
    ```
    
6. 访问菜单页面
    每个菜单对应的url为`主机地址:端口号/部署路径/${menuName}.html`
    例如：本地开发时部署路径为“/”，则pluginDashboard菜单对应的url为“http://localhost:8081/pluginDashboard.html”

## 引入客户端插件
```javascript
import {Mobile, WadeMobile} from 'rk-web-utils'
```

## 调用后台接口
1. 模板工程默认采用反向代理解决ajax请求跨域问题
2. 调用后台接口采用axios，其中axios的baseURL已设置为’/api‘，这一设置用于标识请求需要静态资源服务器进行代理转发，转发时的路径重写会去除'/api'，并将请求转发至真正的后台接口服务器上
3. axios已挂载至Vue原型上，在vue组件内可通过this.$http发起网络请求

## 前端开发规范

1. 移动端rem适配
    * 适配方案采用flexible和postcss-pxtorem插件，flexible设置1rem = viewWidth / 10，postcss-pxtorem的rootValue设置为37.5;
    * 为方便开发，蓝湖等UI原型图的宽度设置为375px，开发时直接按原型图中的px设置，postcss-pxtorem会自动转换为rem;
    * postcss-pxtorem的selectorBlackList预设了".ig-""，以.ig-为前缀的css选择器设置的px不进行转换;
    * 只有.vue文件中写在\<style>标签内以及css、scss文件中的css会进行转换，vue组件的template中的样式不会转换;
    * body元素字体大小在375px宽度的屏幕下为16px，并根据屏幕尺寸的变化等比例变化;   
2. 支持scss(配置文件中配置了less，但该配置是为了重写vant组件库的样式，开发业务代码时不可以使用less)
3. 支持vuex
4. 支持es6、es7等新的js语法
5. 获取后端返回的数据时，推荐使用lodash的_.get方法获取，可以避免在后端返回的数据异常，不存在中间节点时，取值出错的情况。
  （后端返回的数据通常是经过多个系统的透传返回的，存在嵌套层级较深的问题，一旦中间某个系统节点的数据异常，最终返回给前端的数据可能会缺失部分节点）
6. 多数菜单都会用到的公共组件
   * 多数菜单都会用到的公共组件可放在src/assets/commonComponents下，框架已将该目录下的所有.vue组件注册为每个页面均可直接使用的全局组件，无需在页面中再次引入并注册为组件
   * commonComponents下的公共组件已抽离到公共代码中，以免多个页面中存在重复资源的问题
7. 业务类的公共组件可放到src/assets/bizComponents下，与src/assets/commonComponents不同，bizComponents下的组件不会自动注册为每个页面均可直接使用的全局组件，使用时需手动引入
7. 与环境相关的变量
   * 模板工程通过webpack.DefinePlugin默认提供了两种环境test和production，执行num run build会采用production环境，其余均为test环境，通过检测ENV可判断编译环境，详情参考src/assets/config/urlConfig.js
8. 注册到router.js中的路由页面如果需要取消keep-alive的缓存，可以将组件的name添加前缀“nc-”
