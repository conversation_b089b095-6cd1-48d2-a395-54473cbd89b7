import WadeMobile from "./wadeMobile";
import <PERSON> from './jcl';
import {safeTrans} from "./util";
//强制更新
var ExpandMobile = (function(){
    return{
        loadingStart:function(message,title,cancelable,err){ //加载进度条
            execute("loadingStart", [message,title,cancelable],err);
        },loadingStop:function(err){ //结束进度条
            execute("loadingStop", [],err);
        },getChoice:function(options,values,title,iconName){
            return new Promise((resolve, reject) => {
                storageCallback("getChoice", (data) => {
                    resolve(safeTrans(data));
                });
                execute("getChoice", [options,values,title,iconName], (err) => {
                    reject(safeTrans(err));
                });
            })
        },tip:function(msg,type,err){
            if(type==undefined){
                type = 0;//0-短提示,-1长提示
            }
            execute("tip", [msg,type],err);
        },alert:function(msg,type,err){
            if(type==undefined){
                type = 0;//0-短提示,-1长提示
            }
            execute("alert", [msg,type],err);
        },getDate:function(date,format){
            if(format==undefined){
                format = "yyyy-MM-dd";
            }
            return new Promise((resolve, reject) => {
                storageCallback("getDate",(data) => {
                    resolve(safeTrans(data));
                });
                execute("getDate", [date,format],(err) => {
                    reject(safeTrans(err));
                });
            })
        },getContactsView:function(data,setting){
            if(data==undefined){
                data = new Wade.DataMap();
            }else {
                data = new Wade.DataMap(data);
            }

            if(setting==undefined){
                setting = new Wade.DataMap();
            }else {
                setting = new Wade.DataMap(setting);
            }
            return new Promise((resolve, reject) => {
                storageCallback("getContactsView",(data) => {
                    resolve(safeTrans(data));
                });
                execute("getContactsView", [data.toString(),setting.toString()],(err) => {
                    reject(safeTrans(err));
                });
            })
        },getPhoto:function(type){//获取照片
            if(type==="base64"){
                type = 0;//0-Base64编码的字符串 1- 文件路径
            }else {
                type = 1;
            }
            return new Promise((resolve, reject) => {
                storageCallback("getPhoto",(data) => {
                    resolve(safeTrans(data));
                });
                execute("getPhoto", [type],(err) => {
                    reject(safeTrans(err));
                });
            })
        },getPhoto2:function(type, maxSize){//获取照片针对协议拍照不清晰修改了压缩算法的方法
            if(type==="base64"){
                type = 0;//0-Base64编码的字符串 1- 文件路径
            }else {
                type = 1;
            }
            maxSize = maxSize ? maxSize : 150;
            return new Promise((resolve, reject) => {
                storageCallback("getPhoto2",(data) => {
                    resolve(safeTrans(data));
                });
                execute("getPhoto2", [type, maxSize],(err) => {
                    reject(safeTrans(err));
                });
            })
        },getIdentifyPhoto:function(frameType,resultType,type){//获取照片
            if (frameType === "idCardFront") {
                frameType = 1;
            }else if (frameType === "idCardBack") {
                frameType = 2;
            }else if (frameType === "noBorder") {
                frameType = 4;
            }else {
                frameType = 0;
            }
            if(type==undefined){
                type = 0;//后置
            }

            if (resultType === "base64") {
                resultType = 1;
            }else {
                resultType = 0;
            }
            return new Promise((resolve, reject) => {
                storageCallback("getIdentifyPhoto",(data) => {
                    if(resultType && WadeMobile.isIOS()){
                        data="data:image/png;base64,"+data;
                    }
                    resolve(safeTrans(data));
                });
                execute("getIdentifyPhoto", [frameType,resultType,type],(err) => {
                    reject(safeTrans(err));
                });
            })
        },getPicture:function(type){//获取照片
            if(type==="base64"){
                type = 0;//0-Base64编码的字符串 1- 文件路径
            }else {
                type = 1;
            }
            return new Promise((resolve, reject) => {
                storageCallback("getPicture",function (result) {
                    resolve(safeTrans(result));
                });
                execute("getPicture", [type],function (err) {
                    reject(safeTrans(err));
                });
            })
        },getPicture2:function(type, maxSize){//获取照片
            if(type==="base64"){
                type = 0;//0-Base64编码的字符串 1- 文件路径
            }else {
                type = 1;
            }
            maxSize = maxSize ? maxSize : 150;
            return new Promise((resolve, reject) => {
                storageCallback("getPicture2",function (result) {
                    resolve(safeTrans(result));
                });
                execute("getPicture2", [type, maxSize],function (err) {
                    reject(safeTrans(err));
                });
            })
        },getClientResourceVersion:function(){
            return new Promise((resolve, reject) => {
                storageCallback("getClientResourceVersion",(data) => {
                    resolve(safeTrans(data));
                });
                execute("getClientResourceVersion",[]);
            })
        },getVideoPath:function(){//获取视频
            return new Promise((resolve, reject) => {
                storageCallback("getVideoPath", (data) => {
                    resolve(safeTrans(data));
                });
                execute("getVideoPath",[]);
            })
        },transImageToBase64:function(path){
            return new Promise((resolve, reject) => {
                storageCallback("transImageToBase64",(data) => {
                    resolve(safeTrans(data));
                });
                execute("transImageToBase64", [path],(err) => {
                    reject(safeTrans(err));
                });
            })
        },compressImage:function(path,fileSize,quality){
            if(fileSize==undefined){
                fileSize = 10;//压缩到10K左右大小
            }
            if(quality==undefined){
                quality = 30;//图片质量30
            }
            return new Promise((resolve, reject) => {
                storageCallback("compressImage",(data) => {
                    resolve(safeTrans(data));
                });
                execute("compressImage", [path,fileSize,quality],(err) => {
                    reject(safeTrans(err));
                });
            })
        },beep:function(count,err){
            execute("beep", [count],err);
        },shock:function(time,err){
            execute("shock", [time],err);
        },call:function(sn,autoCall,err){
            if(autoCall==undefined){
                autoCall = false;// false-跳转至拨打界面,true-直接拨打
            }
            execute("call", [sn,autoCall],err);
        },sms:function(sn,msg,autoSms,err){
            if(autoSms==undefined){
                autoSms = false;// false-跳转至短信界面,true-直接短信
            }
            execute("sms", [sn,msg,autoSms],err);
        },openApp:function(appId,urlParams,installUrl,err){
            execute("openApp", [appId,urlParams,installUrl],err);
        },showKeyBoard:function(type,err){
            execute("showKeyBoard", [type],err);
        },hideKeyBoard:function(err){
            execute("hideKeyBoard", [],err);
        },setTitleView:function(title,err){
            execute("setTitleText", [title],err);
        },getSysInfo:function(key){//TELNUMBER|IMEI|IMSI|SDKVERSION|OSVERSION|PLATFORM|SIMNUMBER
            return new Promise((resolve, reject) => {
                storageCallback("getSysInfo",(data) => {
                    resolve(safeTrans(data));
                });
                execute("getSysInfo", [key],(err) => {
                    reject(safeTrans(err));
                });
            })
        },getNetInfo:function(key){//MAC|IP
            return new Promise((resolve, reject) => {
                storageCallback("getNetInfo",(data) => {
                    resolve(safeTrans(data));
                });
                execute("getNetInfo", [key],(err) => {
                    reject(safeTrans(err));
                });
            })
        },explorer:function(fileType,initPath){
            return new Promise((resolve, reject) => {
                storageCallback("explorer",(data) => {
                    resolve(safeTrans(data));
                });
                execute("explorer",[fileType,initPath]);
            })
        },httpDownloadFile : function(targetFilePath,fileName){//客户端直接访问服务端进行下载
            return new Promise((resolve, reject) => {
                storageCallback("httpDownloadFile",(data) => {
                    resolve(safeTrans(data));
                });
                execute("httpDownloadFile",[targetFilePath,fileName],(err) => {
                    reject(safeTrans(err));
                });
            })
        },location:function(){
            return new Promise((resolve, reject) => {
                storageCallback("location",(data) => {
                    resolve(safeTrans(data));
                });
                execute("location",[],(err) => {
                    reject(safeTrans(err));
                });
            })
        },markMap:function(markParam,isSelect,isJump){
            markParam = new Wade.DataMap(markParam);
            var markParams = new Wade.DatasetList();
            markParams.add(markParam);
            markParam = markParams;
            // if (typeof(markParam)=="object" && (markParam instanceof Wade.DataMap)) {
            //     var markParams = new Wade.DatasetList();
            //     markParams.add(markParam);
            //     markParam = markParams;
            // }
            return new Promise((resolve, reject) => {
                storageCallback("markMap", (data) => {
                    resolve(safeTrans(data));
                });
                execute("markMap", [markParam.toString(),isSelect,isJump], (err) => {
                    reject(safeTrans(err));
                });
            })
        },closeUrl:function(result){
            execute("closeUrl", [result]);
        },selectLocation:function(isLocation,longitude,latitude,scale){
            return new Promise((resolve, reject) => {
                storageCallback("selectLocation",(data) => {
                    resolve(safeTrans(data));
                });
                execute("selectLocation",[isLocation, longitude, latitude, scale]);
            })
        },scanQrCode:function(){
            return new Promise((resolve, reject) => {
                storageCallback("scanQrCode",(data) => {
                    resolve(safeTrans(data));
                });
                execute("scanQrCode",[]);
            })
        },createQrCode:function(content){
            return new Promise((resolve, reject) => {
                storageCallback("createQrCode",(data) => {
                    resolve(safeTrans(data));
                });
                execute("createQrCode",[content]);
            })
        },downloadImg:function(content){
            return new Promise((resolve, reject) => {
                storageCallback("setImageWithURL",(data) => {
                    resolve(safeTrans(data));
                });
                execute("setImageWithURL",[content[0],content[1]]);
            })
        },openUrlWithPlug:function(url){
            return new Promise((resolve, reject) => {
                storageCallback("openUrlWithPlug", (data) => {
                    resolve(safeTrans(data));
                });
                execute("openUrlWithPlug",[url],(err) => {
                    reject(safeTrans(err));
                });
            })
        },
        httpGet:function(url,encode){
            return new Promise((resolve, reject) => {
                storageCallback("httpGet",(data) => {
                    resolve(safeTrans(data));
                });
                execute("httpGet",[url,encode]);
            })
        },removeMemoryCache:function(key,err){
            execute("removeMemoryCache",[key],err);
        },clearMemoryCache:function(err){
            execute("clearMemoryCache",[],err);
        },setMemoryCache:function(key,value,err){
            execute("setMemoryCache",[key,value],err);
        },getMemoryCache:function(key,defValue){
            return new Promise((resolve, reject) => {
                storageCallback("getMemoryCache",(data) => {
                    if (Array.isArray(key)) {
                        resolve(JSON.parse(data));
                    }else if (typeof key === "string") {
                        resolve(data);
                    }else {
                        reject("key类型异常")
                    }
                });
                execute("getMemoryCache",[key,defValue],(err) => {
                    reject(safeTrans(err));
                });
            })
        },setOfflineCache:function(key,value,err){
            execute("setOfflineCache", [key,value,false],err);
        },getOfflineCache:function(key,defValue){
            return new Promise((resolve, reject) => {
                storageCallback("getOfflineCache",(data) => {
                    if (Array.isArray(key)) {
                        resolve(JSON.parse(data));
                    }else if (typeof key === "string") {
                        resolve(data);
                    }else {
                        reject("key类型异常")
                    }
                });
                return execute("getOfflineCache", [key,defValue],(err) => {
                    reject(safeTrans(err));
                });
            })
        },removeOfflineCache:function(key,err){
            execute("removeOfflineCache", [key],err);
        },clearOfflineCache:function(err){
            execute("clearOfflineCache", [],err);
        },writeFile:function(content,fileName,type,isSdcard,err){
            execute("writeFile",[content,fileName,type,isSdcard],err);
        },appendFile:function(content,fileName,type,isSdcard,err){
            execute("appendFile",[content,fileName,type,isSdcard],err);
        },readFile:function(fileName,type,isSdcard,isEscape){
            return new Promise((resolve, reject) => {
                storageCallback("readFile",(data) => {
                    resolve(safeTrans(data));
                });
                execute("readFile",[fileName,type,isSdcard,isEscape],(err) => {
                    reject(safeTrans(err));
                });
            })
        },openFile:function(filename,type,isSdcard,err){
            execute("openFile", [filename,type,isSdcard],err);
        },deleteFile:function(filename,type,isSdcard,err){
            execute("deleteFile", [filename,type,isSdcard],err);
        },getAllFile:function(filename,type,isSdcard){
            return new Promise((resolve, reject) => {
                storageCallback("getAllFile", (data) => {
                    resolve(safeTrans(data));
                });
                execute("getAllFile", [filename,type,isSdcard],(err) => {
                    reject(safeTrans(err));
                });
            })
        },getRelativePath:function(filename,type){
            return new Promise((resolve, reject) => {
                storageCallback("getRelativePath",(data) => {
                    resolve(safeTrans(data));
                });
                execute("getRelativePath", [filename,type],(err) => {
                    reject(safeTrans(err));
                });
            })
        },cleanResource:function(type,isSdcard,err){
            execute("cleanResource",[type,isSdcard],err);
        },shareByBluetooth:function(err){
            execute("shareByBluetooth", [],err);
        },openBrowser:function(url,err){
            execute("openBrowser",[url],err);
        },setSmsListener:function(telString){
            return new Promise((resolve, reject) => {
                storageCallback("setSmsListener", (data) => {
                    resolve(safeTrans(data));
                });
                execute("setSmsListener", [telString],(err) => {
                    reject(safeTrans(err));
                });
            })
        },audioRecord: function (auto, format) {
            if (auto == undefined) {
                auto = false; //false-按住录音,true-自动录音
            }
            return new Promise((resolve, reject) => {
                storageCallback("audioRecord", (data) => {
                    resolve(safeTrans(data));
                });
                // format-音频格式，默认wav
                execute("audioRecord", [auto, format], (err) => {
                    reject(safeTrans(err));
                });
            })
        }, audioPlay: function (audioPath, hasRipple, err) {
            if (hasRipple == undefined) {
                hasRipple = true; //true-弹出波纹,false-无效果
            }
            execute("audioPlay", [audioPath, hasRipple], err);
        }, audioRecordEnd: function () {
            return new Promise((resolve, reject) => {
                storageCallback("audioRecordEnd", (data) => {
                    resolve(safeTrans(data));
                });
                execute("audioRecordEnd", [], (err) => {
                    reject(safeTrans(err));
                });
            })
        },
        audioStop: function () {
            return new Promise((resolve, reject) => {
                storageCallback("audioStop", (data) => {
                    resolve(safeTrans(data));
                });
                execute("audioStop", [], (err) => {
                    reject(safeTrans(err));
                });
            })
        },logCat:function(msg,title,err){
            //将日志输出至LogCat控制台（异步）
            execute("logCat",[msg,title],err);
        },execSQL:function(dbName,sql,bindArgs,limit,offset){
            if(bindArgs == undefined){
                bindArgs = new Wade.DataMap();
            }else {
                bindArgs = new Wade.DataMap(bindArgs);
            }
            if(limit == null)
                limit = "";
            else if(!isNaN(limit))
                limit = "\"" + limit + "\"";

            if(offset == null)
                offset = "";
            else if(!isNaN(offset))
                offset = "\"" + offset + "\"";

            return new Promise((resolve, reject) => {
                storageCallback("execSQL",(data) => {
                    resolve(data);
                });
                execute("execSQL",[dbName,sql,bindArgs.toString(),limit,offset],(err) => {
                    reject(err);
                });
            })
        },insert:function(dbName,table,datas){
            if(datas==undefined){
                datas = new Wade.DataMap();
            }else {
                datas= new Wade.DataMap(datas);
            }
            return new Promise((resolve, reject) => {
                storageCallback("insert",(data) => {
                    resolve(data);
                });
                execute("insert",[dbName,table,datas.toString()],(err) => {
                    reject(err);
                });
            })
        },delete:function(dbName,table,condSQL,conds){
            if(conds==undefined){
                conds = new Wade.DataMap();
            }else {
                conds = new Wade.DataMap(conds);
            }
            return new Promise((resolve, reject) => {
                storageCallback("delete",(data) => {
                    resolve(data);
                });
                execute("delete",[dbName,table,condSQL,conds.toString()],(err) => {
                    reject(err);
                });
            })
        },update:function(dbName,table,datas,condSQL,conds){
            if(datas==undefined){
                datas = new Wade.DataMap();
            }else {
                datas = new Wade.DataMap(datas);
            }
            if(conds==undefined){
                conds = new Wade.DataMap();
            }else {
                conds = new Wade.DataMap(conds);
            }
            return new Promise((resolve, reject) => {
                storageCallback("update",(data) => {
                    resolve(data);
                });
                execute("update",[dbName,table,datas.toString(),condSQL,conds.toString()],(err) => {
                    reject(err);
                });
            })
        },select:function(dbName,table,columns,condSQL,conds,limit,offset){
            if(columns == null){
                columns = [];
            }
            if(conds == null){
                conds = new Wade.DataMap();
            }else {
                conds = new Wade.DataMap(conds);
            }
            if(limit == null)
                limit = "";
            else if(!isNaN(limit))
                limit = "\"" + limit + "\"";

            if(offset == null)
                offset = "";
            else if(!isNaN(offset))
                offset = "\"" + offset + "\"";

            return new Promise((resolve, reject) => {
                storageCallback("select",(data) => {
                    resolve(data);
                });
                execute("select",[dbName,table,columns,condSQL,conds.toString(),limit,offset],(err) => {
                    reject(err);
                });
            })
        },selectFirst:function(dbName,table,columns,condSQL,conds){
            return this.select(dbName,table,columns,condSQL,conds,1,0);
        },registerForPush:function(account){
            return new Promise((resolve, reject) => {
                storageCallback("registerForPush",(data) => {
                    resolve(safeTrans(data));
                });
                execute("registerForPush",[account],(err) => {
                    reject(safeTrans(err));
                });
            })
        },unregisterForPush:function(){
            return new Promise((resolve, reject) => {
                storageCallback("unregisterForPush",(data) => {
                    resolve(safeTrans(data));
                });
                execute("unregisterForPush", []);
            })
        },sendText:function(account,content){
            return new Promise((resolve, reject) => {
                storageCallback("sendText",(data) => {
                    resolve(safeTrans(data));
                });
                execute("sendText", [account,content],(err) => {
                    reject(safeTrans(err));
                });
            })
        },setCallbackForPush:function(callback){
            execute("setCallbackForPush", [callback]);
        },registerForPushWithYunba:function(account){
            return new Promise((resolve, reject) => {
                storageCallback("registerForPushWithYunba",(data) => {
                    resolve(safeTrans(data));
                });
                execute("registerForPushWithYunba",[account],(err) => {
                    reject(safeTrans(err));
                });
            })
        },unregisterForPushWithYunba:function(){
            return new Promise((resolve, reject) => {
                storageCallback("unregisterForPushWithYunba",(data) => {
                    resolve(safeTrans(data));
                });
                execute("unregisterForPushWithYunba", []);
            })
        },sendTextWithYunba:function(account,content){
            return new Promise((resolve, reject) => {
                storageCallback("sendTextWithYunba",(data) => {
                    resolve(safeTrans(data));
                });
                execute("sendTextWithYunba", [account,content],(err) => {
                    reject(safeTrans(err));
                });
            })
        },setCallbackForPushWithYunba:function(callback){
            execute("setCallbackForPushWithYunba", [callback]);
        },aliPay:function(tradeNo,subject,body,price){
            return new Promise((resolve, reject) => {
                storageCallback("aliPay",(data) => {
                    resolve(safeTrans(data));
                });
                execute("aliPay",[tradeNo,subject,body,price],(err) => {
                    reject(safeTrans(err));
                });
            })
        },uploadWithServlet:function(filePath,dataAction,param){
            param = Wade.DataMap(param).toString();
            if(typeof(filePath)=="string"){
                filePath = [filePath];
            }
            return new Promise((resolve, reject) => {
                storageCallback("uploadWithServlet",(data) => {
                    resolve(safeTrans(data));
                });
                execute("uploadWithServlet",[filePath,dataAction,param],(err) => {
                    reject(safeTrans(err));
                });
            })
        },downloadWithServlet:function(savePath,dataAction,param){
            param = Wade.DataMap(param).toString();
            return new Promise((resolve, reject) => {
                storageCallback("downloadWithServlet",(data) => {
                    resolve(safeTrans(data));
                });
                execute("downloadWithServlet",[savePath,dataAction,param],(err) => {
                    reject(safeTrans(err));
                });
            })
        },uploadFile:function(filePath,servletUrl){
            return new Promise((resolve, reject) => {
                storageCallback("uploadFile",(data) => {
                    resolve(safeTrans(data));
                });
                execute("uploadFile",[filePath,servletUrl],(err) => {
                    reject(safeTrans(err));
                });
            })
        },downloadFile:function(savePath,servletUrl){
            return new Promise((resolve, reject) => {
                storageCallback("downloadFile",(data) => {
                    resolve(safeTrans(data));
                });
                execute("downloadFile",[savePath,servletUrl],(err) => {
                    reject(safeTrans(err));
                });
            })
        },recordVideo:function(compressRatio,timeLimit){
            return new Promise((resolve, reject) => {
                storageCallback("recordVideo",(data) => {
                    resolve(safeTrans(data));
                });
                execute("recordVideo",[compressRatio,timeLimit],(err) => {
                    reject(safeTrans(err));
                });
            })
        },playVideo:function(videoPath){
            return new Promise((resolve, reject) => {
                storageCallback("playVideo",(data) => {
                    resolve(safeTrans(data));
                });
                execute("playVideo",[videoPath],(err) => {
                    reject(safeTrans(err));
                });
            })
        },getContacts:function(){
            return new Promise((resolve, reject) => {
                storageCallback("getContacts",(data) => {
                    resolve(safeTrans(data));
                });
                execute("getContacts",[], (err) => {
                    reject(safeTrans(err));
                });
            })
        },openKeyboard:function(funcName, options, err){
            execute("openKeyboard", [funcName, options], err);
        },captureScreen:function(isOpen, err){
            execute("captureScreen",[isOpen], err);
        },getCaptrueScreenStatus:function(){
            return new Promise((resolve, reject) => {
                storageCallback("getCaptrueScreenStatus",(data) => {
                    resolve(safeTrans(data));
                });
                execute("getCaptrueScreenStatus", []);
            })
        },setScreenLock:function(dataParam){
            return new Promise((resolve, reject) => {
                storageCallback("setScreenLock",(data) => {
                    resolve(safeTrans(data));
                });
                execute("setScreenLock",[dataParam],(err) => {
                    reject(safeTrans(err));
                });
            })
        },getScreenLockState:function(){
            return new Promise((resolve, reject) => {
                storageCallback("getScreenLockState",(data) => {
                    resolve(safeTrans(data));
                });
                execute("getScreenLockState",[],(err) => {
                    reject(safeTrans(err));
                });
            })
        },screenUnlock:function(forgetPageAction){
            return new Promise((resolve, reject) => {
                storageCallback("screenUnlock",(data) => {
                    resolve(safeTrans(data));
                });
                execute("screenUnlock",[forgetPageAction],(err) => {
                    reject(safeTrans(err));
                });
            })
        },initNfc:function(cmds,callbackName,err) {
            execute("initNfc", [cmds, callbackName], err);
        },scanSingle:function(){
            return new Promise((resolve, reject) => {
                storageCallback("scanSingle",(data) => {
                    resolve(safeTrans(data));
                });
                execute("scanSingle",[]);
            })
        },scanMultiple:function(){
            return new Promise((resolve, reject) => {
                storageCallback("scanMultiple",(data) => {
                    resolve(safeTrans(data));
                });
                execute("scanMultiple",[]);
            })
        },showNotification:function(content,title,icon,id){
            execute("showNotification",[content,title,icon,id]);
        },startListen:function(){
            return new Promise((resolve, reject) => {
                storageCallback("startListen",(data) => {
                    resolve(safeTrans(data));
                });
                execute("startListen",[],(err) => {
                    reject(safeTrans(err));
                });
            })
        },voiceSpeak:function(content,err){
            execute("voiceSpeak",[content],err);
        },shareTextQQFriend:function(content){
            execute("shareTextQQFriend",[content]);
        },shareTextWeChatFriend:function(content){
            execute("shareTextWeChatFriend",[content]);
        },shareFileQQFriend:function(type,content){
            execute("shareFileQQFriend",[type,content]);
        },shareFileWeChatFriend:function(type,content){
            execute("shareFileWeChatFriend",[type,content]);
        },shareTextMore:function(content){
            execute("shareTextMore",[content]);
        },shareFileMore:function(type,content){
            execute("shareFileMore",[type,content]);
        },shareImageBymail:function(param){
            execute("shareImageBymail",[param]);
        },baiduLocation:function(){
            return new Promise((resolve, reject) => {
                storageCallback("baiduLocation",(data) => {
                    resolve(safeTrans(data));
                });
                execute("baiduLocation",[],(err) => {
                    reject(safeTrans(err));
                });
            })
        },baiduMapLocation:function(){
            return new Promise((resolve, reject) => {
                storageCallback("baiduMapLocation",(data) => {
                    resolve(safeTrans(data));
                });
                execute("baiduMapLocation",[],(err) => {
                    reject(safeTrans(err));
                });
            })
        },baiduMapPosition:function(pointParam,err){
            pointParam = new Wade.DataMap(pointParam);
            execute("baiduMapPosition",[pointParam.toString()],err);
        },addPolygon:function(markParams,err){
            let param = new Wade.DatasetList();
            markParams.forEach((value) => {
                param.add(new Wade.DataMap({
                    Latitude: value[1],
                    Longitude: value[0]
                }))
            });
            execute("addPolygon",[param.toString()],err);
        },clickBaiduMap:function(){
            return new Promise((resolve, reject) => {
                storageCallback("clickBaiduMap",(data) => {
                    resolve(data);
                });
                execute("clickBaiduMap",[],(err) => {
                    reject(err);
                });
            })
        },videoCompressor:function(param){
            execute("videoCompressor",[param]);
        },sweetAlert:function(param){
            execute("sweetAlert",[param]);
        },sweetConfirm:function(param){
            execute("sweetConfirm",[param]);
        },sweetLoading:function(param){
            execute("sweetLoading",[param]);
        },poiCitySearch:function(city,keyword,err){
            execute("poiCitySearch",[city,keyword],err);
        },poiNearbySearch:function(latlon,radius,keyword,err){
            let param = new Wade.DataMap({
                Latitude: latlon[1],
                Longitude: latlon[0]
            });
            execute("poiNearbySearch",[param.toString(),radius,keyword],err);
        },poiBoundsSearch:function(swParam,neParam,keyword,err){
            let param1 = new Wade.DataMap({
                Latitude: swParam[1],
                Longitude: swParam[0]
            });
            let param2 = new Wade.DataMap({
                Latitude: neParam[1],
                Longitude: neParam[0]
            });
            execute("poiBoundsSearch",[param1.toString(),param2.toString(),keyword],err);
        },lbsLocalSearch:function(ak,geoTableId,q,region,err){
            execute("lbsLocalSearch",[ak,geoTableId,q,region],err);
        },lbsNearbySearch:function(ak,geoTableId,q,loc,radius,err){
            execute("lbsNearbySearch",[ak,geoTableId,q,loc,radius],err);
        },lbsBoundsSearch:function(ak,geoTableId,q,bounds,err){
            execute("lbsBoundsSearch",[ak,geoTableId,q,bounds],err);
        },testUnRegister:function(callback){
            execute("testUnRegister",[]);
        },openPathMenu:function(param){
            execute("openPathMenu",[param]);
        },closeIpuApp:function(result){
            execute("closeIpuApp",[result]);
        },closePathMenu:function(){
            execute("closePathMenu",[]);
        },clearBackStack:function(){
            if(WadeMobile.isAndroid()){
                execute("clearBackStack",[]);
            }
        },saveImageToAlbum:function(url) {
            return new Promise((resolve, reject) => {
                storageCallback("saveImageToAlbum",(data) => {
                    resolve(safeTrans(data));
                });
                execute("saveImageToAlbum",[url]);
            })
        }, turnOnLocationService: function (interval, err) {
            //调用开启定位服务并上传位置的插件
            execute("turnOnLocationService", [interval], err);
        }, turnOffLocationService: function (err) {
            //调用关闭定位服务及上传服务的插件
            execute("turnOffLocationService", [], err);
        }, getLocationServiceStatus: function () {
            return new Promise((resolve, reject) => {
                //查询定位和上传服务的开启/关闭状态
                storageCallback("getLocationServiceStatus", (data) => {
                    resolve(safeTrans(data));
                });
                execute("getLocationServiceStatus", [], err => {
                    reject(safeTrans(err));
                });
            })
        },msgoForceOpenGPS: function () {
            execute("msgoForceOpenGPS", []);
        }, getSimCardNum: function (orderId, flow) {
            return new Promise((resolve, reject) => {
                // 本地取号
                storageCallback("getSimCardNum", (data) => {
                    resolve(safeTrans(data));
                });
                execute("getSimCardNum", [orderId, flow]);
            })
        },getNum: function (url, appId) {
            // 蜂行动取号
            return new Promise((resolve, reject) => {
                storageCallback("getNum", (data) => {
                    resolve(safeTrans(data));
                });
                execute("getNum", [url, appId], err => {
                        reject(safeTrans(err));
                    }
                );
            })
        }, detectApps: function (packages) {
            return new Promise((resolve, reject) => {
                // 检测安装的app
                storageCallback("detectApps", (data) => {
                    resolve(safeTrans(data));
                });
                execute("detectApps", [packages], err => {
                    reject(safeTrans(err));
                });
            })
        }, sysLocation: function () {
            // 系统定位（IOS）
            return new Promise((resolve, reject) => {
                storageCallback("sysLocation", (data) => {
                    resolve(safeTrans(data));
                });
                execute("sysLocation", [], err => {
                    reject(safeTrans(err));
                });
            })
        }, getAppName: function () {
            // 获取当前应用名，用于区别“掌沃通”和“蜂行动2.0”
            return new Promise((resolve, reject) => {
                storageCallback("getAppName", (data) => {
                    resolve(safeTrans(data));
                });
                execute("getAppName", [], err => {
                    reject(safeTrans(err));
                });
            })
        }, doRequest: function (ip, port, jsonParam) {
            return new Promise((resolve, reject) => {
                storageCallback("doRequest", (data) => {
                    resolve(safeTrans(data));
                });
                execute("doRequest", [ip, port, jsonParam], err => {
                    reject(safeTrans(err));
                });
            })
        }, initUDPWithPort:  function(port) {
            return new Promise((resolve, reject) => {
                storageCallback("initUDPWithPort", (data) => {
                    resolve(safeTrans(data));
                });
                execute("initUDPWithPort", [port], err => {
                    reject(safeTrans(err));
                });
            });
        }, sendUDPMessage: function(port,name,password) {
            return new Promise((resolve, reject) => {
                storageCallback("sendUDPMessage", (data) => {
                    resolve(safeTrans(data));
                });
                execute("sendUDPMessage", [port,name,password], err => {
                    reject(safeTrans(err));
                });
            });
        },
        /* 打印机api-20210810 */
        // 搜索打印机列表
        printInit: function () {
            return new Promise((resolve, reject) => {
                storageCallback("printInit", data => {
                    resolve(safeTrans(data));
                });
                execute("printInit", [], err => {
                    reject(safeTrans(err));
                });
            });
        },
        // 打开指定打印机
        openPrinter: function (name) {
            return new Promise((resolve, reject) => {
                storageCallback("openPrinter", data => {
                    resolve(safeTrans(data));
                });
                execute("openPrinter", [name], err => {
                    reject(safeTrans(err));
                });
            });
        },
        // 获取打印机信息
        getPrinterInfo: function () {
            return new Promise((resolve, reject) => {
                storageCallback("getPrinterInfo", data => {
                    resolve(safeTrans(data));
                });
                execute("getPrinterInfo", [], err => {
                    reject(safeTrans(err));
                });
            });
        },
        // 设置打印机纸张间隔类型
        setPrintPageGapType: function (gapType) {
            return new Promise((resolve, reject) => {
                storageCallback("setPrintPageGapType", data => {
                    resolve(safeTrans(data));
                });
                execute("setPrintPageGapType", [gapType], err => {
                    reject(safeTrans(err));
                });
            });
        },
        // 设置打印机打印浓度
        setPrintDarkness: function (darkness) {
            return new Promise((resolve, reject) => {
                storageCallback("setPrintDarkness", data => {
                    resolve(safeTrans(data));
                });
                execute("setPrintDarkness", [darkness], err => {
                    reject(safeTrans(err));
                });
            });
        },
        // 关闭当前已连接打印机
        closePrinter: function () {
            return new Promise((resolve, reject) => {
                storageCallback("closePrinter", data => {
                    resolve(safeTrans(data));
                });
                execute("closePrinter", [], err => {
                    reject(safeTrans(err));
                });
            });
        },
        // 开始绘制
        start: function (x, y, orientation) {
            return new Promise((resolve, reject) => {
                storageCallback("start", data => {
                    resolve(safeTrans(data));
                });
                execute("start", [x, y, orientation], err => {
                    reject(safeTrans(err));
                });
            });
        },
        // 结束绘制
        end: function () {
            return new Promise((resolve, reject) => {
                storageCallback("end", data => {
                    resolve(safeTrans(data));
                });
                execute("end", [], err => {
                    reject(safeTrans(err));
                });
            });
        },
        // 提交打印数据并打印
        print: function () {
            return new Promise((resolve, reject) => {
                storageCallback("print", data => {
                    resolve(safeTrans(data));
                });
                execute("print", [], err => {
                    reject(safeTrans(err));
                });
            });
        },
        // 打印文本
        drawText: function (text, x, y, width, height) {
            return new Promise((resolve, reject) => {
                storageCallback("drawText", data => {
                    resolve(safeTrans(data));
                });
                execute("drawText", [text, x, y, width, height], err => {
                    reject(safeTrans(err));
                });
            });
        },
        // 打印一维码
        drawBarCode: function (text, x, y) {
            return new Promise((resolve, reject) => {
                storageCallback("drawBarCode", data => {
                    resolve(safeTrans(data));
                });
                execute("drawBarCode", [text, x, y], err => {
                    reject(safeTrans(err));
                });
            });
        },
        // 打印二维码
        drawQRCode: function (text, x, y, width) {
            return new Promise((resolve, reject) => {
                storageCallback("drawQRCode", data => {
                    resolve(safeTrans(data));
                });
                execute("drawQRCode", [text, x, y, width], err => {
                    reject(safeTrans(err));
                });
            });
        },
        /* 打印机api-end */

        // 浏览PDF文件
        openPdfFileWithBase64: function (base64Content,filePath) {
            return new Promise((resolve, reject) => {
                storageCallback("openPdfFileWithBase64", data => {
                    resolve(safeTrans(data));
                });
                execute("openPdfFileWithBase64", [base64Content, filePath], err => {
                    reject(safeTrans(err));
                });
            })
        },
        // 获取文件Base64编码
        getFileBase64: function (path, fileMaxSize) {
            return new Promise((resolve, reject) => {
                storageCallback("getFileBase64", data => {
                    resolve(safeTrans(data));
                });
                execute("getFileBase64", [path, fileMaxSize], err => {
                    reject(safeTrans(err));
                });
            })
        },
        ftthSpeedTester: function (param) {
            return new Promise((resolve, reject) => {
                storageCallback("ftthSpeedTester", data => {
                    resolve(safeTrans(data));
                });
                execute("ftthSpeedTester", [param], err => {
                    reject(safeTrans(err));
                });
            })
        },
        woSpeedTester: function (param) {
            return new Promise((resolve, reject) => {
                storageCallback("woSpeedTester", data => {
                    resolve(safeTrans(data));
                });
                execute("woSpeedTester", [param], err => {
                    reject(safeTrans(err));
                });
            })
        },
        // 拨打电话，监听得到结果
        callGetRecord: function (tellNum) {
            return new Promise((resolve, reject) => {
                storageCallback("callGetRecord", data => {
                    resolve(safeTrans(data));
                });
                execute("callGetRecord", [tellNum], err => {
                    reject(safeTrans(err));
                });
            })
        },
        // 获取通话记录
        getCallRecords: function (data) {
            return new Promise((resolve, reject) => {
                storageCallback("getCallRecords", data => {
                    resolve(safeTrans(data));
                });
                execute("getCallRecords", [data], err => {
                    reject(safeTrans(err));
                });
            })
        },
        // 拨打电话，得到记录
        callAndGetRecord: function (tellNum) {
            return new Promise((resolve, reject) => {
                storageCallback("callAndGetRecord", data => {
                    resolve(safeTrans(data));
                });
                execute("callAndGetRecord", [tellNum], err => {
                    reject(safeTrans(err));
                });
            })
        },
        // 获取通话记录（特殊）
        getMoreCallRecord: function (callNum, acceptTime) {
            return new Promise((resolve, reject) => {
                storageCallback("getMoreCallRecord", data => {
                    resolve(safeTrans(data));
                });
                execute("getMoreCallRecord", [callNum, acceptTime], err => {
                    reject(safeTrans(err));
                });
            })
        },
        // 跳转到装维助手
        runToZw: function (data) {
            return new Promise((resolve, reject) => {
                storageCallback("runToZw", data => {
                    resolve(safeTrans(data));
                });
                execute("runToZw", [data], err => {
                    reject(safeTrans(err));
                });
            })
        },
        // 标签打印
        yyylabelPrint: function (data) {
            return new Promise((resolve, reject) => {
                storageCallback("yyylabelPrint", data => {
                    resolve(safeTrans(data));
                });
                execute("yyylabelPrint", [data], err => {
                    reject(safeTrans(err));
                });
            })
        }
    };
})();

// var WadeMobile;
function execute(action, args, error, success) {
    /*循环依赖,懒加载*/
    // if(!WadeMobile){
    //     WadeMobile = require("wadeMobile")
    // }
    return WadeMobile.execute(action, args, error, success)
}
function storageCallback(action,callback) {
    /*循环依赖,懒加载*/
    // if(!WadeMobile){
    //     WadeMobile = require("wadeMobile")
    // }
    WadeMobile.callback.storageCallback(action,callback)
}

export default ExpandMobile;
