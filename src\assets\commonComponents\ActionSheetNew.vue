<template>
    <van-action-sheet
        class="action-sheet-new"
        :round="false"
        v-bind="$attrs"
        v-model="show"
        @select="onSelect"
        @cancel="onCancel"
    />
</template>

<script>
export default {
    name: 'ActionSheetNew',
    props: {
        value: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            show: this.value
        };
    },
    watch: {
        show() {
            this.$emit('input', this.show);
        },
        value() {
            this.show = this.value;
        }
    },
    methods: {
        onSelect(item) {
            this.$emit('select', item);
        },
        onCancel() {
            this.$emit('cancel');
        }
    }
};
</script>

<style lang="scss" scoped>
.action-sheet-new {
    /deep/ .van-action-sheet__cancel,
    /deep/ .van-action-sheet__item {
        padding: 0;
        height: 50px;
        line-height: 50px;
        font-size: 18px;

        &:active {
            background-color: #eeeeee;
        }
    }

    /deep/ .van-action-sheet__gap {
        background-color: #f7f7f7;
        height: 6px;
    }
}
</style>