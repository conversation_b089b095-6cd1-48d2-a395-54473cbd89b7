apiVersion: apps/v1
kind: Deployment
metadata:
  name: front
  namespace: shzwt-sit
  labels:
    app: front
spec:
  replicas: 1
  selector:
    matchLabels:
      app: front
  template:
    metadata:
      name: front
      labels:
        app: front
    spec:
      containers:
      - name: front
        image: 'harbor.dcos.xixian.unicom.local/shzwt-sit/front'
        ports:
        - containerPort: 8080
        imagePullPolicy: IfNotPresent
        volumeMounts:
        - name: front
          mountPath: /mnt
      volumes:
        - name: front
          hostPath:
            path: /var/lib/docker/log/shzwt/front-pro
      restartPolicy: Always