<template>
    <div class="container">
        <div>模拟收银台</div>
        <van-button type="primary" @click="back">完成</van-button>
    </div>
</template>

<script>

export default {
    name: 'PayBack2',
    data() {
        return {}
    },
    created() {
    },
    methods: {
        back() {
            this.$router.push({
              path: 'PayBack',
          });
        }
    }
};
</script>

<style lang="scss" scoped>
.container {
    height: 100vh;
    background: #ffffff;
}
</style>
