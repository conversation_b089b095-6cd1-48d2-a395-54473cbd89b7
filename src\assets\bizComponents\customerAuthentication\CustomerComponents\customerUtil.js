import WadeMobile from 'rk-native-plugin';
import { Dialog } from 'vant';
import axios from '@/assets/js/axios.js';
     /** 隐藏号码中间几位
     * @param {Anyone} value 需要校验的值
     */
    function isEmpty(value) {
      let flag = false
      if (value == '' || value == 'undefined' || value == undefined || value == null || value == 'null') {
        flag = true
      }
      return flag
    }

    /** 判断是否重试下单
     * @param {String} respCode 响应编码
     * @param {String} respMsg 响应消息
     * */
    function retryOrder(respCode, respMsg) {
        return new Promise(resolve => {
            if (respCode == '5555' || respMsg == '下单异地校验失败，请打开GPS并到合理地方重试!') {
                Dialog.confirm({
                    title: '提示',
                    message: '下单异地校验失败，请打开GPS并到合理地方重试',
                    confirmButtonText: '重试',
                    confirmButtonColor: '#0081ff',
                    cancelButtonText: '取消'
                }).then(() => {
                    resolve('1');
                }).catch(() => {
                    resolve('0');
                });
            } else {
                resolve('');
            }
        });
    }

    /** 获取位置信息
     * @param datas 入参
     * */
    function getLocationInfo(datas) {
        WadeMobile.location().then(info => {
            // datas.orderLocalInfoCheck = '';
            if (WadeMobile.isAndroid()) {
                datas.GPRSProvince = info.PROVINCE;
                datas.GPRSCity = info.CITY;
                datas.GPRSDistrict = info.DISTRICT;
            } else if (WadeMobile.isIOS()) {
                datas.GPRSProvince = info.Province;
                datas.GPRSCity = info.City;
                datas.GPRSDistrict = info.District;
            }
        }).catch(() => {
        });
    }

    /** 保护姓名隐私加*处理
     * @param {String} name 姓名
     */
    function formatName(name) {
      let reg = /^(.).+(.)$/g;
      let str = name
      if (str.length > 2) {
        return str.replace(reg, '$1*$2')
      } else {
        return str.substr(0, 1) + '*'
      }
    }

    /** 保护身份证号隐私加*处理（保留后两位）
     * @param {Number} ID 身份证号
     */
    function formatID(ID) {
      let str = ID
      let head = str.substr(0, 6)
      let tail = str.substr(16, 18)
      let all = head + '***********' + tail
      return all
    }

    /** 保护身份证号隐私加*处理（保留后四位）
     * @param {Number} ID 身份证号
     */
    function formatIDFour(ID) {
        if (isEmpty(ID)) {
            return '';
        }
        let str = ID;
        let head = str.substr(0, 6);
        let tail = str.substring(14);
        return head + '********' + tail;
    }

    /** 拼接身份证起止有效期
     * @param {String} period 身份证起止有效期
     */
    function formatPeriod(period) {
      let str = period
      let startYear = str.substr(0, 4)
      let startMonth = str.substr(4, 2)
      let startDay = str.substr(6, 2)
      let endYear = str.substr(8, 5)
      let endMonth = str.substr(13, 2)
      let endDay = str.substr(15, 2)
      let result = startYear + '.' + startMonth + '.' + startDay + endYear + '.' + endMonth + '.' + endDay
      return result
    }

    /** 判断身份证是否大于8岁
     * @param {Number} ID 身份证号
     */
    function ageBiggerThen8(ID) {
      if (!ID) {
        return false;
      }
      let bstr = ID.substring(6, 14)
      let _now = new Date()
      let _bir = new Date(parseInt(bstr.substring(0, 4)) + 8, parseInt(bstr.substring(4, 6)) - 1, bstr.substring(6, 8));
      return _now > _bir
    }

    /** 判断身份证是否大于15岁
     * @param {Number} ID 身份证号
     */
    function ageBiggerThen15(ID) {
      if (!ID) {
        return false
      }
      let bstr = ID.substring(6, 14);
      let _now = new Date()
      let _bir = new Date(parseInt(bstr.substring(0, 4)) + 15, parseInt(bstr.substring(4, 6)) - 1, bstr.substring(6, 8));
      return _now >= _bir
    }

    /** 判断身份证是否大于16岁
     * @param {Number} ID 身份证号
     */
    function ageBiggerThen16(ID) {
      if (!ID) {
        return false;
      }
      //一天的毫秒值
      let dayTime = ********;
      let bstr = ID.substring(6, 14);
      let _now = new Date()
      let _bir = new Date(parseInt(bstr.substring(0, 4)) + 16, parseInt(bstr.substring(4, 6)) - 1, bstr.substring(6, 8))
      //_bir的构造函数的返回的时间是0点 对于同一天的比较必然是_now>_bir 所以+1天进行比较
      return _now.getTime() > (_bir.getTime() + dayTime)
    }
    //54715-常态化-联通公众-内部优化-副卡支持代办人
    // async function ageBiggerThen16(ID) {
    //     if (!ID) {
    //         return false;
    //     }
    //     await Mobile.getMemoryCache('staffInfo').then(async staffInfo => {
    //         staffInfo = JSON.parse(staffInfo);
    //         //年龄默认16岁
    //         let ageNum = parseInt('16');
    //         await axios.post('/paramController/getSwitch', {
    //             'keyCode': 'ageForOpenAccount',
    //             'provinceCode': staffInfo['PROVINCE_CODE']
    //         }).then(res=>{
    //             if ('0000' === res.respCode) {
    //                 //年龄默认16岁,如果数据库中配置则取数据库配置数据
    //                 if (res.respData)
    //                 {
    //                     ageNum = parseInt(res.respData);
    //                 }
    //             }
    //             //一天的毫秒值
    //             let dayTime = ********;
    //             let bstr = ID.substring(6, 14);
    //             let _now = new Date()
    //             let _bir = new Date(parseInt(bstr.substring(0, 4)) + ageNum, parseInt(bstr.substring(4, 6)) - 1, bstr.substring(6, 8));
    //             //_bir的构造函数的返回的时间是0点 对于同一天的比较必然是_now>_bir 所以+1天进行比较
    //             return _now.getTime() > (_bir.getTime() + dayTime)
    //
    //         });
    //     }).catch(()=>{
    //         //一天的毫秒值
    //         let ageNum = parseInt('16');
    //         let dayTime = ********;
    //         let bstr = ID.substring(6, 14);
    //         let _now = new Date()
    //         let _bir = new Date(parseInt(bstr.substring(0, 4)) + ageNum, parseInt(bstr.substring(4, 6)) - 1, bstr.substring(6, 8));
    //         //_bir的构造函数的返回的时间是0点 对于同一天的比较必然是_now>_bir 所以+1天进行比较
    //         return _now.getTime() > (_bir.getTime() + dayTime)
    //     });
    // }

    /** 判断身份证是否大于60岁
     * @param {Number} ID 身份证号
     */
    function ageBiggerThen60(ID) {
      if (!ID) {
        return false;
      }
      //一天的毫秒值
      let dayTime = ********;
      let bstr = ID.substring(6, 14);
      let _now = new Date()
      let _bir = new Date(parseInt(bstr.substring(0, 4)) + 60, parseInt(bstr.substring(4, 6)) - 1, bstr.substring(6, 8))
      //_bir的构造函数的返回的时间是0点 对于同一天的比较必然是_now>_bir 所以+1天进行比较
      return _now.getTime() > (_bir.getTime() + dayTime)
    }

    /**
     * 判断身份证是否大于（等于）某个年龄
     * @param ID 身份证号
     * @param compareAge 比较的年龄
     * @param equal 是否判断等于，默认不判断，即不等于
     * @returns {boolean}
     */
    function ageBiggerThen(ID, compareAge, equal = false) {
        if (!ID) {
            return false;
        }
        // 一天的毫秒值
        let bstr = ID.substring(6, 14);
        let _now = new Date();
        // _bir的构造函数的返回的时间是0点
        let _bir = new Date(parseInt(bstr.substring(0, 4)) + parseInt(compareAge), parseInt(bstr.substring(4, 6)) - 1, bstr.substring(6, 8));
        if(equal) {
            return _now.getTime() >= _bir.getTime();
        }
        return _now.getTime() > _bir.getTime();
    }
    // 代办人年龄-使用人年龄>=配置的岁数
    function agentAgeThanUseAge(agentAge, useAge, thanAge) {
      if (!agentAge || !useAge) {
        return false;
      }
      let astr = agentAge.substring(6, 14);
      let ustr = useAge.substring(6, 14);
      let _astrBir = new Date(parseInt(astr.substring(0, 4))+parseInt(thanAge), parseInt(astr.substring(4, 6))-1, astr.substring(6, 8));
      let _ustrBir = new Date(parseInt(ustr.substring(0, 4)), parseInt(ustr.substring(4, 6))-1, ustr.substring(6, 8));
      return _ustrBir >= _astrBir;
    }
    /** 
     格式化当前时间的值给拍照用
     */
    function getNowFormatDate() {
      let date = new Date();
      let seperator1 = "-";
      let seperator2 = ":";
      let month = date.getMonth() + 1;
      let strDate = date.getDate();
      if (month >= 1 && month <= 9) {
        month = "0" + month;
      }
      if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
      }
      let hours = date.getHours();
      let minutes = date.getMinutes();
      let seconds = date.getSeconds();
      if (hours >= 0 && hours <= 9) {
        hours = "0" + hours;
      } if (minutes >= 0 && minutes <= 9) {
        minutes = "0" + minutes;
      } if (seconds >= 0 && seconds <= 9) {
        seconds = "0" + seconds;
      }
      let currentdate = date.getFullYear() + seperator1 + month + seperator1 + strDate + " " + hours + seperator2 + minutes + seperator2 + seconds;
      return currentdate;
    }

    /**
     * 日期格式化
     * @param date
     * @param format yyyy-MM-dd hh:mm:ss.SSS 年-月-日 时:分:秒.毫秒
     * @returns {*}
     */
    function dateFormat(date, format) {
        var o = {
            "M+": date.getMonth() + 1, //月份
            "d+": date.getDate(), //日
            "h+": date.getHours(), //小时
            "m+": date.getMinutes(), //分
            "s+": date.getSeconds(), //秒
            "q+": Math.floor((date.getMonth() + 3) / 3), //季度
            "S": date.getMilliseconds() //毫秒
        };
        if (/(y+)/.test(format)) format = format.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (let k in o)
            if (new RegExp("(" + k + ")").test(format)) format = format.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        return format;
    }

    /**
     * 手机号校验
     * @param phone 手机号
     */
    function verifyPhone(phone) {
        return /^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(phone);
    }

    /**
     * 时间转换，小于10的在前面增加0
     * @param time
     * @returns {string}
     */
    function transformTime(time) {
        return time > 9 ? '' + time : '0' + time;
    }

    let customer = {
        info : {},
        clear() {
          return {}
        }
    }
    export default {
        isEmpty,
        retryOrder,
        getLocationInfo,
        formatName,
        formatID,
        formatIDFour,
        formatPeriod,
        ageBiggerThen,
        ageBiggerThen8,
        ageBiggerThen15,
        ageBiggerThen16,
        ageBiggerThen60,
        getNowFormatDate,
        dateFormat,
        agentAgeThanUseAge,
        verifyPhone,
        transformTime,
        customer
    }