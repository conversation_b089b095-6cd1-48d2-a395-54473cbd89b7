<template>
  <div class="orderInfoList" style="background-color: white">
    <div>订单列表</div>
    <van-popup
        v-model="show"
        position="bottom"
        closeable
        @close="closedShow"
        :close-on-click-overlay="false"
        style="background-image: linear-gradient(to bottom right, #C0DBFF,#DAE9FF, #DAE9FF);"
        :style="{ width: '100%',height: '60%' }">
      <div style="display: flex;align-items: center;justify-content: space-between;margin-top: 40px;padding: 0 10px;">
        <van-search
            v-model="value"
            placeholder="请输入订单号或者业务号码"
            left-icon=""
            clearable
            style="width: 85%"/>
        <van-icon @click="popShow=true" name="filter-o" size="20" style="margin: 5px 10px 0 0"  v-show="!value"/>
        <van-button size="medium" class="search-btn" v-show="value" @click="queryByIdOrSta()">搜索</van-button>
      </div>
   
        <van-list
            :error.sync="error" error-text="请求失败，点击重新加载"
            v-model="loading"
            :finished="finished"
            offset="20"
            :finished-text="finished?'没有更多了':''"
            @load="onLoad"
        >
          <div class="card-group"  v-for="item in resp">
            <van-cell  class="desc"  style="color:#263A5F;padding:5px 0">
              <van-cell-group class="tip">
                <van-cell  class="desc"  style="color:#263A5F;padding:5px 0">
                  <div style="float: left">
                    <span  style="font-weight: bold;color:#263A5F;" >{{item.senceName}}<span style="font-size: 14px !important;font-weight: lighter "> ({{item.orderState}})</span></span>
                  </div>
                  <div  v-if="'1'==item.orderCancelTag" style="float: right">
                    <van-icon :name="require('@/pages/easyAcceptance/easyAcceptance/images/tuidan.png')" />
                    <span  style="color:#497CF6 !important;" @click="cancelOrder(item)">撤单</span>
                  </div>
                  <div v-else style="float: right"></div>
                </van-cell>
              </van-cell-group>

            </van-cell>
            <van-cell-group class="tip1">
              <van-cell class="desc">
                <template #title>
                  <van-icon :name="require('@/pages/easyAcceptance/easyAcceptance/images/name.png')" />
                  <span class="custom-title">{{item.custName+item.serialNumber}}</span>
                </template>
              </van-cell>
            </van-cell-group>
            <van-cell-group class="card-list" style="display: flex;justify-content: space-between;">
              <van-cell style="padding-right: 0;background-color: #E9F3FC;">
                <template #title>
                  <van-icon :name="require('@/pages/easyAcceptance/easyAcceptance/images/time.png')" />
                  <span class="custom-title">{{item.orderTime}}</span>
                </template>
              </van-cell>
              <van-cell style="padding: 0;background-color: #E9F3FC;width: 90%;">
                <template #title>
                  <van-icon :name="require('@/pages/easyAcceptance/easyAcceptance/images/orderId.png')" />
                  <span class="custom-title">{{item.orderId}}</span>
                </template>
              </van-cell>
            </van-cell-group>
            <van-cell-group class="card-list" style="display: flex;justify-content: space-between;">
              <van-cell style="padding-right: 0;background-color: #E9F3FC;">
                <template #title>
                  <van-icon :name="require('@/pages/easyAcceptance/easyAcceptance/images/remark.png')" />
                  <span class="custom-title"><b>备注：</b>{{item.remarkByQry}}</span>
                </template>
              </van-cell>
            </van-cell-group>
          </div>
        </van-list>
    </van-popup>
    <orderListPop :popShow="popShow" @closePopup="popShow=false"  @recivePopData="handlePopData"></orderListPop>
    <van-popup class="popup1" v-model="popupShow1" round position="bottom" :style="{ height: '50%' }">
      <div class="title">请选择一级撤单原因</div>
      <van-radio-group style="padding: 10px;" v-model="classReason">
        <van-cell-group>
          <van-cell v-for="item in classReasonArr" :title="item.codeDesc" clickable @click="onSelect1(item)" style="text-align: center">
<!--            <template #right-icon>-->
<!--              <van-radio :name="item.codeDesc" :icon-size="0" />-->
<!--            </template>-->
            
          </van-cell>
        </van-cell-group>
      </van-radio-group>
<!--      <div style="width: 50%;height: 100px;"></div>-->
<!--      <div class="submit-container" style="padding: 0 15px;position: fixed;bottom: 10px;width: 92%;">-->
<!--        <van-button class="sub" block @click="sureChioce(1)">确定</van-button>-->
<!--      </div>-->
    </van-popup>
    <van-popup class="popup1" v-model="popupShow2" round position="bottom" :style="{ height: '50%' }">
      <div class="title">请选择二级撤单原因</div>
      <van-radio-group style="padding: 10px;" v-model="reasonDetail">
        <van-cell-group>
          <van-cell v-for="item in reasonDetailArr"  clickable @click="onSelect2(item)"  :title="item.codeDesc" style="text-align: center">
           
<!--            <template #right-icon>-->
<!--              <van-radio :name="item.codeDesc" :icon-size="0" />-->
<!--            </template>-->
          </van-cell>
        </van-cell-group>
      </van-radio-group>
<!--      <div style="width: 50%;height: 100px;"></div>-->
<!--      <div class="submit-container" style="padding: 0 15px;position: fixed;bottom: 10px;width: 92%;">-->
<!--        <van-button class="sub" block @click="sureChioce(2)">确定</van-button>-->
<!--      </div>-->
    </van-popup>
  </div>
</template>

<script>
import WadeMobile from "rk-native-plugin";
import errorTips from '@/assets/bizComponents/errorTips/errorTips.vue';
import {copyText} from "../../../assets/js/func";
import {mapActions, mapMutations, mapState} from "vuex";
import OrderTrack from "./orderTrack.vue";
import orderListPop from './orderListPop.vue'
export default {
  name: "OrderInfoList",
  data() {
    return {
      classReasonArr:[],
      classReason:"",
      reasonDetailArr:[],
      reasonDetail:"",
      popupShow1:false,
      popupShow2:false,
      popListShow: false,
      respReasonObj:{},
      show:false,
      value:'',
      imageList: [
        require('../../../images/arrow.png'),
        require('../../../images/add.png')
      ],
      resp:[
        { orderId:'63250122001211',
          serialNumber:'13601720056',
          senceName:'智能组网业务',
          orderTime:'2025-01-23',
          custName:'张*原-',
          status:'处理中',
          addr:'上海市浦东新区********'},
        { orderId:'63250122001212',
          serialNumber:'18601725678',
          senceName:'智能组网业务',
          orderTime:'2025-01-23',
          custName:'张*原-',
          status:'处理中',
   addr:'上海市浦东新区********'},
        { orderId:'63250122001214',
          serialNumber:'18601720056',
          senceName:'智能超清业务',
          orderTime:'2025-01-23',
          custName:'张*原-',
          status:'处理中',
          addr:'上海市浦东新区********'}
      ],
      page: 1,
      loading: false,
      finished: false,
      popShow: false,
      error: false
    }
  },
  props:['popListShow'],
  // watch:{
  //   showOrderList(){
  //     console.log(this.showOrderList,"---------this.showOrderList")
  //     this.show = this.showOrderList;
  //   }
  // },
  components: {
    errorTips,
    OrderTrack,
    orderListPop
  },
  computed: {
    ...mapState([
      'chatList',
      'sessionId',
      'staffId',
      'flowStep',
      'num',
      'shbmMsgInfo',
      'jzfkOrderData',
      'activeModuleIndex',
      'loginPhoneNumber',
      'instanceId',
        'showOrderList',
        'orderInfoLists',
        'mqBookFlagData',
        'cacheQryPara',
        'reqDatas'
    ])
  },
  mounted(){
    this.setReqDatas({});
    this.getReason();
    console.log(this.showOrderList,'this.showOrderList')
   this.show=this.showOrderList;
    this.resp=[];
      for(let i=0;i<this.orderInfoLists.length;i++){
        this.resp.push(this.orderInfoLists[i])
        this.resp[i].custName=this.resp[i].custName+'-'
        if(this.isEmpty( this.resp[i].remarkByQry)){
          this.resp[i].remarkByQry="无"
        }
    }
  },

  methods: {
    ...mapMutations([
      'setFlowStep',
      'setRobotWorking',
      'setJzfkOrderData',
        'setShowOrderList',
        'setMqBookFlagData',
        'setReqDatas'
    ]),
    ...mapActions(['updateChatList']),
    onLoad() {// 异步更新数据
      console.log("yibujiazaikaid")
      let req={
      }
      if(this.value.startsWith("021")){
        req={
          serialNumber:this.value,
          page:this.page
        }
      }
      else if(this.value.length>11){
        req={
          orderId:this.value,
          page:this.page
        }
      }
      else{
        req=this.cacheQryPara;
        req.page=this.page;
      }
      
      this.$http.post('/Order/custOrderQuery',req).then((res)=>{
        if (res.respCode === '0000') {
          if(this.page==1){
            this.resp=[];
            for(let i=0;i<res.respData.length;i++){
              res.respData[i].custName=res.respData[i].custName+'-'
              if(this.isEmpty( res.respData[i].remarkByQry)){
                res.respData[i].remarkByQry="无"
              }
              this.resp.push(res.respData[i])
            }
          }
          else{
            for(let i=0;i<res.respData.length;i++){
              res.respData[i].custName=res.respData[i].custName+'-'
              if(this.isEmpty( res.respData[i].remarkByQry)){
                res.respData[i].remarkByQry="无"
              }
              this.resp.push(res.respData[i])
            }
          }
        }
        else{
          this.finished = true;
        }
        // 加载状态结束
        this.loading = false;
        // 数据全部加载完成
        if(res.respData.length==0)
        {
          this.finished = true;
        }else {
          this.page = this.page + 1;
          this.finished = false;
        }
      })
    },
    getReason(){
      this.$http.post('/mpComm/getReason', {}).then(res => {
        if (res.respCode == "0000") {
          this.respReasonObj=res.respData
          
        }
        else{
          this.$toast(res.resMsg);
        }
      }).catch(e => {
        this.$toast(e)
      })
    },
    sureChioce(type){
      if(type==1){
        this.popupShow1=false;
        this.popupShow2=true;
      }else if(type==2){
        this.popupShow2=false;
        this.closedShow();
        let data = {
          inputType: "1",
          type: '1',
          textInput: "orderCancelAction",
          notifyFlag: '',
          taskName:'智能订单查询'
        }
        this.$emit('newChatApi', data);
      }
    },
    cancelOrder(item){
      this.classReasonArr=[];
      this.classReasonArr=this.respReasonObj.reasonArr
      this.reqDatas.orderId=item.orderId
      this.reqDatas.senceNameCode=item.senceNameCode
      this.popupShow1=true;
      this.setReqDatas( this.reqDatas);

    },
    onSelect1(item){
         this.reasonDetailArr=[];
         this.classReason=item.codeDesc
      this.reqDatas.reason=item.codeValue
      this.reasonDetailArr=this.respReasonObj[item.codeValue]
      this.setReqDatas( this.reqDatas)
      this.popupShow1=false;
      this.popupShow2=true;
    },
    onSelect2(item){
      this.reasonDetail=item.codeDesc
      this.reqDatas.serviceReason=item.codeDesc
      this.reqDatas.reasonCode=item.codeValue
      this.setReqDatas( this.reqDatas);
      this.popupShow2=false;
      this.closedShow();
      let data = {
        inputType: "1",
        type: '1',
        textInput: "orderCancelAction",
        notifyFlag: '',
        taskName:'智能订单查询'
      }
      this.$emit('newChatApi', data);
     
    },
    handlePopData(val){
      this.resp=[];
      for(let i=0;i<val.length;i++){
        this.resp.push(val[i])
        this.resp[i].custName=this.resp[i].custName+'-'
        if(this.isEmpty( this.resp[i].remarkByQry)){
          this.resp[i].remarkByQry="无"
        }
      }
    },
    queryDetail(){
      this.updateChatList({
        sender: '1',
        type: 'module',
        moduleName: 'orderTrack',
        moduleLevel: 1,
        params: {
        },
        show: true
      })
    },

    closedShow(){
        this.show=false;
        this.setShowOrderList(false);
        this.mqBookFlagData.bookAddrTimeFlag="1";
        this.setMqBookFlagData(this.mqBookFlagData);
        console.log(this.mqBookFlagData.bookAddrTimeFlag,"this.mqBookFlagData")
    },
    queryByIdOrSta(){
      this.page=1;
      this.onLoad();
    },

    isEmpty(value) {
      let flag = false
      if (value == '' || value == 'undefined' || value == undefined || value == null || value == 'null') {
        flag = true
      }
      return flag
    },

    uniqueByProperty(arr, prop) {
      const uniqueMap = new Map();
      return arr.reduce((acc, current) => {
        const key = current[prop];
        if (!uniqueMap.has(key)) {
          uniqueMap.set(key, true);
          acc.push(current);
        }
        return acc;
      }, []);
    },
  }
}
</script>
<style lang="scss">
.popup1{
  .title{
    text-align: center;
    font-size: 16px;
    color: #263A5F;
    font-weight: 700;
    width: 96%;
    padding: 0 0 10px 10px;
    border-bottom: 1px solid rgba(213,221,234,1);
  }
}
.orderInfoList {
  width:100%;
  margin: auto;
  .card-group {
    background-color: #E9F3FC;
    padding: 10px 12px 10px 15px;
    border-radius: 8px;
    margin: 0 20px 20px;
    .tip1 {
      clear: both;
      .van-cell {
        padding: 0;
        margin:7px 0px;
      }
      .van-cell__value {
        color: black;
      }}
    .tip {
      .van-cell__value {
        font-weight: normal !important;
        color: #666666;
        font-size: 13px !important;
        text-align: right;
      }}
    .card-list1 {
      font-weight: bolder;
      width:100%;
      .van-cell__title {
        font-size: 15px;
        font-weight: bolder;
        color: #666666;
      }
      .van-cell__value {
        color: #666666;
        font-weight: bolder;
        text-align: right;
        font-size: 15px;
      }
      .van-cell {
        color: black !important;
        padding: 0px 16px;
      }
      .van-cell::after {
        color: black !important;
        border-bottom: 0;

      }
    }

    .card-list {
      display: inline-block;
      width: 100%;
      background: none;
      .detail-btl{
        width: 60px;
        height: 28px;
        font-size: 12px;
        margin-left: 10px;
        float: right;
        margin-bottom: 5px;
        border-radius: 10px;
        background: #0081FF;
        color: #fff;
      }
      .van-cell__title {
        font-size: 13px;
        color: #666666;
      }
      .van-cell__value {
        color: #666666;
        text-align: right;
        font-size: 13px;
      }
      .van-cell {
        color: black !important;
        padding: 0;
        background: #E9F3FC;
      }
      .van-cell::after {
        color: black !important;
        border-bottom: 0;
      }
    }
    .van-hairline--top-bottom::after, .van-hairline-unset--top-bottom::after {
      border-width: 0 0;
    }
  }
  .card {
    .van-cell__title {
      font-size: 13px;
      color: #000000;
    }
    .checkButton {
      width: 70px;
      height: 30px;
      background: #0081FF;
      border-radius: 4px;
    }
    .checkFont {
      font-size: 13px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 14px;
    }
  }
  .check{
    .van-cell__title {
      font-size: 13px;
      color: #333333;
    }
  }
  .van-popup__close-icon{
    color: #000;
  }
  
  .sub{
    border: 2px solid rgba(73,124,246,1);
    border-radius: 5px;
    color: rgba(73,124,246,1);
    //margin-top: 20px;
  }
  .img-title{
    padding-top: 10px;
    width: 60px;
    padding-left: 10%;
  }
  .van-search{
    background: none !important;
    padding-right: 0;
  }
}
</style>

<style lang="scss" scoped>
.orderInfoList {
  background-color: rgb(57,159,254);
  .search-btn{
    padding: 1px 2px;
    margin-right: 10px;
    border-radius: 3px;
    width: 14%;
    height: 34px;
    font-size: 14px;
    background: #0081FF;
    color: #fff;
  }
  .desc {
    color: #263A5F!important; ;
    font-size:14px ;
    line-height: 20px;
    font-weight: bolder ;
    background: #E9F3FC;
  }
  
  .desc1 {
    color: black;
    font-size: 14px;
    line-height: 20px;
  }
  .margin-two {
    bottom: 10px;
    left: 15px;
    right: 15px;
    z-index: 2;
    position: absolute;
  }
  .font {
    color:#333333;
  }

}

</style>