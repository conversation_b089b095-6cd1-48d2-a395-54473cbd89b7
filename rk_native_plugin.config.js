module.exports = {
    // 客户端插件是否开启mock模式，默认为false
    openWadeMock: false,
    // mock模式下，是否模拟App，即isApp的返回值，null为不模拟
    wadeMockIsApp: null,
    // mock模式下，是否模拟IpuApp，即isIpuApp的返回值，null为不模拟
    wadeMockIsIpuApp: null,
    // mock模式下，是否模拟Android，即isAndroid的返回值，null为不模拟
    wadeMockIsAndroid: null,
    // mock模式下，是否模拟iOS，即isIOS的返回值，null为不模拟
    wadeMockIsIOS: false,
    // mock模式下，客户端插件的请求基地址，默认为 ApiPost 地址
    // ApiPost 地址：https://console-mock.apipost.cn/app/mock/project/164a927a-9d7a-4ed0-f9d6-9a6499a79d1f/rkNativePluginMock
    // sub_back 地址：touch_sub_back/pluginMock/checkMock
    wadeMockBaseUrl: 'https://console-mock.apipost.cn/app/mock/project/164a927a-9d7a-4ed0-f9d6-9a6499a79d1f/rkNativePluginMock',
    // mock模式下，请求的 version 入参
    wadeMockVersion: '1.0',
    // mock模式下，sub_back 地址时，getIdentifyPhoto 接口第二个参数的设置
    wadeMockIdentifyPhotoParam: '',
    // 支持mock模式的客户端插件列表，不支持的插件将走原插件流程，默认为[]
    wadeMockFuncList: [
        'getSimCardNum',
        'deviceList',
        'faceAuthentication',
        'getIdentifyPhoto',
        'getPhotox',
        'getPhoto',
        'location',
        'readIDCard',
        'recordVideo',
        'uploadWithServlet'
    ]
};
