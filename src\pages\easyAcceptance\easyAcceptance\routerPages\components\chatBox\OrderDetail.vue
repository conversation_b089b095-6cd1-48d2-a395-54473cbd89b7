<template>
  <div>
    <van-popup v-model="showOrderDetail_" round position="bottom" :close-on-click-overlay="false" :loading="loading" @closed="active = 0; status0Task=''">
      <div class="task_container">
        <div class="close">
          <van-icon name="cross" color="#1989fa" @click="closeFn" size="0.8rem" />
        </div>
        <div class="no-info" v-if="runTimeTaskList.length === 0">暂无进行中的任务！</div>
        <van-tabs v-else :active="active" :ellipsis="false" type="card" color="#1989fa" background="#fff" @change="tabChange">
          <van-tab v-for="item in runTimeTaskList" :key="item.task_id" :title="item.task_name" :disabled="loading">
<!--            <div class="info-content" :style="{height: (activeTask.task_name || '').indexOf('融合变更') == -1 ? '100px' : 'calc(70vh - 50px)'}">-->
            <div class="info-content" :style="{height:  '100px'}">
<!--              <MixChange :showSubmitButton="false" v-if="(item.task_name || '').indexOf('融合变更') != -1" />-->
            </div>
          </van-tab>
        </van-tabs>
        <div class="footer" v-show="runTimeTaskList && runTimeTaskList.length">
          <van-button type="info" :loading="loading" v-if="curTask.taskId != activeTask.task_id" style="margin-right: 10px; flex: 1" @click="switchTaskFn(activeTask)">执行任务</van-button>
          <van-button plain type="info" :loading="loading" block v-if="curTask.taskId == activeTask.task_id" @click="closeTaskFn(activeTask)">终止任务</van-button>
          <van-button plain type="info" :loading="loading" v-else style="margin-left: 10px; flex: 1" @click="closeTaskFn(activeTask)">终止任务</van-button>
        </div>
      </div>
      <van-dialog v-model="dialogVisible" show-cancel-button confirmButtonText="确认" confirmButtonColor="#0081ff" cancelButtonText="取消" :message="message" @confirm="confirmFn" @cancel="dialogVisible=false" @closed="message=''; type=''; task = {}"></van-dialog>
    </van-popup>
  </div>
</template>

<script>
// import MixChange from '../taskModule/MixChange.vue'
import {mapActions, mapMutations, mapState} from 'vuex'
import { alertError } from '@/assets/bizComponents/funcComponent.js'

export default {
  name: 'OrderDetail',
  components: {
    // MixChange
  },
  data() {
    return {
      active: 0,
      isClosed: false,
      loading: false,
      showOrderDetail_: false,
      runTimeTaskList: [],
      dialogVisible: false,
      task: {},
      type: '', // 1终止 2切换
      message: '',
      status0Task: '', // 任务切换时切换前任务状态为00的taskId
    }
  },
  computed: {
    ...mapState([
      // 'runTimeTaskList',
      'chatList',
      'curTask',
      'agentSessionId',
      // 'userSelectInfo',
      // 'remark',
      // 'numberInfo',
      // 'custInfo',
      // 'commonProduct',
      // 'commonChildren',
      'flowStep',
      // 'secondBroadbandInfo'
    ]),
    activeTask() {
      return this.runTimeTaskList[this.active] || {}
    },
  },
  props: {
    showOrderDetail: {
      type: Boolean,
      default: false,
    }
  },
  watch: {
    showOrderDetail: {
      immediate: true,
      handler(v) {
        this.showOrderDetail_ = v
        if (v) {
          this.getRunTimeTaskList()
        }
      }
    },
    curTask(v) {
      console.log('watch curTask', v.taskName);
      this.$forceUpdate()
    },
    runTimeTaskList(v) {
      console.log('watch runTimeTaskList', v);
    },
  },
  methods: {
     ...mapMutations([
      'backToAnyStep',
      'setRunTimeTaskList',
      'setCurTask',
    ]),
    ...mapActions(['updateChatList']),
    tabChange(e) {
      console.log('active', e);
      this.active = e
    },
    // 获取运行时列表 isPushModule: 是否往对话框内push任务选择组件
    getRunTimeTaskList(isPushModule=false, showNoTaskTip=false) {
      const params = {
        agentSessionId: this.agentSessionId,
        taskId: this.curTask?.taskId || ''
      }
      var qs = require('qs')
      this.runTimeTaskList = []
      this.$http.post('/smartFlowChat/queryRuntimeList', params).then(res => {
        if (res.respCode === '0000') {
          this.setRunTimeTaskList(res.respData || [])
          this.runTimeTaskList = res.respData || []
          this.active = 0
          if (isPushModule && res.respData?.length) {
            this.updateChatList({
              sender: '1',
              type: 'module',
              moduleName: 'TaskSelect',
              moduleLevel: 1,
              params: {},
              show: true
            })
          } else if (res.respData?.length === 0 && showNoTaskTip) {
            this.updateChatList({
              sender: '1',
              type: 'module',
              moduleName: 'TextResponse',
              moduleLevel: 1,
              params: {
                text: '当前没有进行中的任务，可以重新输入您想要办理的业务~'
              },
              show: true
            })
          }
        } else {
          this.runTimeTaskList = []
        }
      })
    },
    confirmFn() {
      if (this.type == '1') {
        this.closeTask(this.task)
      } else if (this.type == '2') {
         this.switchTask(this.task)
      }
    },
    closeTaskFn(item) {
      this.type = '1'
      this.task = JSON.parse(JSON.stringify(item))
      this.message =  `确认终止“${item.task_name}”任务吗`
      this.dialogVisible = true
    },
    switchTaskFn(item) {
      this.type = '2'
      this.task = JSON.parse(JSON.stringify(item)) 
      this.message = `确定执行“${item.task_name}”任务吗`
      this.dialogVisible = true
    },
    // 终止任务
    closeTask(task) {
      if (this.loading) return
      this.loading = true;
      // 调用接口更新任务状态
      this.updateTask(task.task_id, '04').then(res => {
        if (res.respCode === '0000') {
          // 更新缓存的任务列表
          if (this.curTask.taskId === task.task_id) {
            this.setCurTask({})
          }
          this.isClosed = true;
          // 重新获取执行中任务列表
          this.getRunTimeTaskList()
        } else {
          this.$toast.fail(res.respMsg)
        }
        this.loading = false;
      }).catch(e => {
          alertError({
              title: '出错了！',
              message: e,
              confirmButtonText: '报告错误'
          });
          this.loading = false;
      });
    },
    switchTask(task) {
      const task_ = JSON.parse(JSON.stringify(task)) 
      if (this.loading) return
      // 调用接口暂停当前任务
      this.loading = true;
      const startTask = () => {
        this.updateTask(task_.task_id, '01').then(res1 => {
          if (res1.code === '0000') {
            // 切换前任务状态为00 则在当前实例缓存一下id
            console.log('task.task_status == 00', task.task_status, );
            if (task.task_status == '00') {
              this.status0Task = task_.task_id
              console.log('this.status0Task', this.status0Task);
            }
            const { tool_list = [], tool_code = '' } = task_
            this.setCurTask({
              taskId: task_.task_id,
              taskName: task_.task_name,
              taskStatus: '01',
              toolCode: (tool_list[0] || {}).tool_code || tool_code || ''
            })
            // 重新获取执行中任务列表
            this.getRunTimeTaskList()
            // 关闭当前弹框
            this.closeDialog(true)
          } else {
            this.$toast.fail(res1.respMsg)
          }
          this.loading = false;
        }).catch(e => {
          alertError({
            title: '出错了！',
            message: e,
            confirmButtonText: '报告错误'
          });
          this.loading = false;
        });
      }
      if (!this.curTask?.taskId) {
        startTask()
      }
      this.updateTask('', '02').then(res => {
        if (res.respCode === '0000') {
          // 启动新任务
          startTask()
        } else {
          this.$toast.fail(res.respMsg)
        }
        this.loading = false;
      }).catch(e => {
        alertError({
          title: '出错了！',
          message: e,
          confirmButtonText: '报告错误'
        });
        this.loading = false;
      });
    },
    closeFn() {
      if (this.loading) return
      this.closeDialog()
    },
    // 关闭弹框
    closeDialog(isSwitch) {
      if (isSwitch && this.status0Task && this.curTask?.taskId == this.status0Task) {
        // 获取该任务的输入文字
        let sender0 = (this.chatList || []).slice().reverse().find(v => v.sender === '0' && v.moduleName != 'ConfirmBox' && v.moduleName != 'TaskSelect' && v.moduleName != 'ErrorInfo' && v.taskId == this.curTask.taskId) || null
        if (!sender0) {
          this.sendDefaultMsg()
        } else {
          const text = sender0?.params?.text || sender0?.content || ''
          this.$emit('callNewChatApi', {
            inputType: '0', // 0-文本 1-动作
            type: '1', // 1-文字输入,2-语音输入
            textInput: text, // 文字输入内容
          })
        }
      } else if (isSwitch) { // 判断任务是否切换
        this.updateChatList({
          sender: 'divideLine',
          text: this.curTask?.taskName ? `以下为${this.curTask?.taskName}` : '我是分割线',
          show: true
        })
        let params = (this.chatList || []).slice().reverse().find(v => v.sender === '1' && v.moduleName != 'ConfirmBox' && v.moduleName != 'TaskSelect' && v.moduleName != 'ErrorInfo' && v.taskId == this.curTask.taskId) || null

        if (params) {
          this.updateChatList(params)
        } else {
          // 取不到组件信息则渲染：请重新输入信息
          this.sendDefaultMsg()
        }
      }
      // 判断当前任务是否被终止 
      if (this.isClosed) {
        // 重新查询运行时任务列表且展示任务选择组件
        this.getRunTimeTaskList(true, true)
      }
      this.$emit('update:showOrderDetail', false)
      this.isClosed = false
    },
    // 发送默认信息
    sendDefaultMsg() {
      this.updateChatList({
        sender: '1',
        type: 'module',
        moduleName: 'TextResponse',
        moduleLevel: 1,
        params: {
          text: '请重新输入信息'
        },
        show: true
      })
    },
    // 更新任务状态
    updateTask(taskId, status) {
      const params = {
        agentSessionId: this.agentSessionId,
        taskId: taskId || this.curTask?.taskId || '',
        taskStatus: status
      }
      if (!params.taskId) return
      var qs = require('qs');
      return this.$http.post('/smartFlowChat/updateTaskStatus', params)
    },
    // // 预订单提交
    // preSubmit() {
    //   this.buttonDisabled = true
    //   this.$emit('preSubmit')
    // },
    // // 自由回溯
    // backTrack(stepCode, stepName) {
    //   Dialog.confirm({
    //     title: '提示',
    //     message: `确定要回溯到【${stepName}】环节吗？您将需要重新进行后续的所有操作。`,
    //     confirmButtonText: '是',
    //     confirmButtonColor: '#0081ff',
    //     cancelButtonText: '否'
    //   }).then(() => {// on confirm
    //     const stepInfo = {
    //       name: stepCode,
    //       stepIndex: this.mixChangeStepList.indexOf(stepCode),
    //       stepList: this.mixChangeStepList,
    //       clearChat: false
    //     }
    //     this.$toast.loading({
    //       message: '正在回溯，请稍等...',
    //       forbidClick: true,
    //       duration: 0
    //     });
    //     Promise.all([this.backToAnyStep(stepInfo)]).then(() => {
    //       this.$toast.clear()
    //       this.$emit('close')
    //       this.updateChatList({
    //         sender: '1',
    //         type: 'module',
    //         moduleName: stepInfo.name,
    //         moduleLevel: 1,
    //         params: {},
    //         show: false
    //       })
    //     })
    //   }).catch(() => {});
    // }
  }
}
</script>

<style lang="scss" scoped>
.info-content {
  min-height: 100px;
  max-height: calc(70vh - 50px);
  overflow: auto;
  padding: 20px 0;
}

.no-info {
  width: 196px;
  text-align: center;
  height: 100%;
  font-size: 16px;
  background: url(@/assets/images/no_data.png) no-repeat top center;
  background-size: 196px 128px;
  padding-top: 145px;
  margin: 20vh auto;
}
.close {
  display: flex;
  justify-content: flex-end;
  padding: 10px 16px;
}
.footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 28px;
}
::v-deep .van-tabs__nav--complete {
  padding-left: unset;
  padding-right: unset;
}
</style>
