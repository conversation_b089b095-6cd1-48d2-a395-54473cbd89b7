<template>
<div class="selectAddress">
  <div>
    <div class="tabs-fillet-corner">
      <div class="tabs-fillet-corner-item-wrap" @click="tabAddrMethed(1)">
        <div class="tabs-fillet-corner-item" :class="activeTab===1 ? 'tabs-fillet-corner-item-active':''">
          <p class="tabs-fillet-corner-item-text">别名查询</p>
        </div>
      </div>
      <div class="tabs-fillet-corner-item-wrap" @click="tabAddrMethed(2)">
        <div class="tabs-fillet-corner-item" :class="activeTab===2 ? 'tabs-fillet-corner-item-active':''">
          <p class="tabs-fillet-corner-item-text">地址关键字查询</p>
        </div>
      </div>
    </div>
  </div>
  <div style="padding: 10px">
    <div v-if="activeTab===1">
     <div>
       <van-field
           ref="communityField"
           label="小区名称"
         style="margin-top: -8px"
         v-model.trim="communityName"
         placeholder="请输入小区名称/别名/地址"
         @input="handleInput">
         <template #right-icon>
           <van-loading v-if="isLoading" size="25px" vertical color="#1989fa" />
         </template>
     </van-field>
     </div>
      <van-cell @click="popIsShow(2)" title="小区地址" is-link :value="communityAddress?communityAddress:'请选择'" />
      <van-cell @click="popIsShow(3)" title="楼栋" is-link :value="building?building:'请选择'" />
      <van-cell @click="popIsShow(4)" title="室号" is-link :value="floor?floor:'请选择'" />
    </div>
    <div v-else>
      <div class="search-box">
        <van-cell @click="popupShow5=true" is-link :value="regionName?regionName:'请选择'"  class='region-type-select' />
        <van-search
            style="margin-top: -8px"
            v-model.trim="addressKey"
            show-action
            shape="round"
            :show-action="false"
            left-icon=""
            placeholder="请输入小区名称"
            @search="addressKeyQuery">
          <!-- 通过插槽触发事件 -->
          <template #right-icon>
            <div class="rIcon">
              <van-icon @click="addressKeyQuery" color="#000" class="van-icon-rux-sousuo1"></van-icon>
            </div>
          </template>
        </van-search>
      </div>
      <van-list  v-model="loading" :error.sync="error" error-text="请求失败，点击重新加载" :finished="finished" finished-text="没有更多了" @load="onLoad" class="address-box">
        <div v-for="(item,index) in addressArr" :key="index" :class="selectIndex === index ? 'active' : 'list-item'">
          <div style="padding:10px" @click="selectOldAddress(item,index)">
            <p class="paragraphspacing">标准地址名称：<span class="info-style">{{ item.addressName }}</span></p>
          </div>
        </div>
      </van-list>
    </div>
  </div>

  <div style="padding: 10px">

    
    <div style="text-align: center;">
      <van-button type="primary" class="btn"  @click="resanticipation4gNew()">资源预判</van-button>
    </div>
    <div v-if="ziyuanShow" class="ziyuan-div">
      <div class="ziyuan-box" >
        <span style="color: #7B7B7B;width: 60%">产品名称：<span style="color: #263A5F;">{{ ziyuanArr[0].brandCode }}</span></span>
        <span style="color: #7B7B7B;">接入方式：<span style="color: #263A5F;">{{ ziyuanArr[0].accessList.accessTypeName }}</span></span>
      </div>
      <div class="ziyuan-box" style="padding-bottom: 3%">
        <span style="color: #7B7B7B;width: 60%">最大可承载带宽：<span style="color: #263A5F;">{{ ziyuanArr[0].accessList.maxRate  }}</span></span>
<!--        <span style="color: #7B7B7B;">局向编码：<span style="color: #263A5F;">{{ ziyuanArr[0].accessList.exchList.exchCode  }}</span></span>-->
      </div>
    </div>

  </div>

  <van-popup class="popup1" v-model="popupShow" round position="bottom" :style="{ height: '50%' }">
    <div class="popup-select">
    <van-field style="margin-left: 5%;width: 90%;border-radius: 10px;padding: 6px;"  v-model="communityFilter" input-align="left" placeholder="请输入搜索内容" @input="filterItems1"/>
    <div class="title">请选择小区名称</div>
    </div>
     <div class="radio-group-list"> <van-radio-group style="padding: 10px;" v-model="communityName1">
      <van-cell-group>
        <van-cell v-for="item in communityNameArr" :title="item.villageName" clickable @click="onSelect0(item)">
          <template #right-icon>
            <van-radio :name="item.villageName" />
          </template>
        </van-cell>
      </van-cell-group>
    </van-radio-group></div>
    <div style="width: 50%;height: 100px;"></div>
    <div class="submit-container" style="padding: 0 15px;position: fixed;bottom: 10px;width: 92%;">
      <van-button class="sub" block @click="sureChioce(1)">确定</van-button>
    </div>
  </van-popup>
  <van-popup class="popup1" v-model="popupShow2" round position="bottom" :style="{ height: '50%' }">
    <div class="popup-select">
     <van-field style="margin-left: 5%;width: 90%;border-radius: 10px;padding: 6px;"  v-model="communityAddressFilter" input-align="left" placeholder="请输入搜索内容" @input="filterItems2"/>
    <div class="title">请选择小区地址</div>
    </div>
    <div class="radio-group-list">
        <van-radio-group style="padding: 10px;" v-model="communityAddress">
        <van-cell-group>
          <van-cell v-for="item in communityAddressArr" :title="item" clickable @click="onSelect(item)">
            <template #right-icon>
              <van-radio :name="item" />
            </template>
          </van-cell>
        </van-cell-group>
      </van-radio-group>
    </div>

    <div style="width: 50%;height: 100px;"></div>
    <div class="submit-container" style="padding: 0 15px;position: fixed;bottom: 10px;width: 92%;">
      <van-button class="sub" block @click="sureChioce(2)">确定</van-button>
    </div>
  </van-popup>
  <van-popup class="popup1" v-model="popupShow5" round position="bottom" :style="{ height: '50%' }">
    <div class="title">请选择区县</div>
    
    <van-radio-group style="padding: 10px;" v-model="regionName">
      <van-cell-group>
        <van-cell v-for="item in columns" :title="item.name" clickable @click="onSelect5(item)">
          <template #right-icon>
            <van-radio :name="item.name" />
          </template>
        </van-cell>
        
      </van-cell-group>
    </van-radio-group>
    <div style="width: 50%;height: 100px;"></div>
    <div class="submit-container" style="padding: 0 15px;position: fixed;bottom: 10px;width: 92%;">
      <van-button class="sub" block @click="popupShow5=false">确定</van-button>
    </div>
  </van-popup>

  <van-popup class="popup1" v-model="popupShow3" round position="bottom" :style="{ height: '50%' }">
    <div class="popup-select">
      <van-field
          style="margin-left: 5%;width: 90%;border-radius: 10px;padding: 6px;"
          v-model="buildingFilter"
          input-align="left"
          placeholder="请输入搜索内容"
          @input="handleFilterChange"
      />
      <div class="title">请选择楼栋</div>
    </div>

    <!-- 添加滚动容器并监听滚动事件 -->
    <div
        class="radio-group-list"
        style="padding: 10px; height: calc(100% - 150px); overflow-y: auto;"
        @scroll="handleScroll"
        ref="scrollContainer"
    >
      <van-radio-group style="padding: 10px;" v-model="building">
        <van-cell-group>
          <!-- 只渲染当前页的数据 -->
          <van-cell
              v-for="(item, index) in buildingArr"
              :key="index"
              :title="item"
              clickable
              @click="onSelect2(item)"
          >
            <template #right-icon>
              <van-radio :name="item" />
            </template>
          </van-cell>

          <!-- 加载中提示 -->
          <div v-if="isLoadingMore" style="text-align: center; padding: 10px;">
            <van-loading type="spinner" size="20" />
            <span style="margin-left: 5px;">加载中...</span>
          </div>

          <!-- 没有更多数据提示 -->
          <div v-if="!hasMore" style="text-align: center; padding: 10px; color: #999;">
            已显示全部内容
          </div>
        </van-cell-group>
      </van-radio-group>
    </div>

    <div style="width: 50%;height: 100px;"></div>
    <div class="submit-container" style="padding: 0 15px;position: fixed;bottom: 10px;width: 92%;">
      <van-button class="sub" block @click="sureChioce(3)">确定</van-button>
    </div>
  </van-popup>
  <van-popup class="popup1" v-model="popupShow4" round position="bottom" :style="{ height: '50%' }">
  <div class="popup-select"> <van-field style="margin-left: 5%;width: 90%;border-radius: 10px;padding: 6px;"  v-model="floorFilter" input-align="left" placeholder="请输入搜索内容" @input="filterItems4"/>
    <div class="title">请选择室号</div></div>
   <div class="radio-group-list">
   <van-radio-group style="padding: 10px;" v-model="floor">
      <van-cell-group>
        <van-cell v-for="item in floorArr" :title="item.ROOM_NO" clickable @click="onSelect3(item)">
          <template #right-icon>
            <van-radio :name="item.ROOM_NO" />
          </template>
        </van-cell>
      </van-cell-group>
    </van-radio-group>
    </div>
    <div style="width: 50%;height: 100px;"></div>
    <div class="submit-container" style="padding: 0 15px;position: fixed;bottom: 10px;width: 92%;">
      <van-button class="sub" block @click="sureChioce(4)">确定</van-button>
    </div>
  </van-popup>
</div>
</template>

<script>
import {mapActions, mapMutations, mapState} from "vuex";
export default {
  data(){
    return {
      communityName1:'',
      popupShow5:false,
      activeTab: 1,
      communityName:'',
      communityAliasName:'',
      communityAddress: '',
      building: '',
      floor: '',
      isLoading:false,
      regionName:'',
      region: {},
      columns: [{name:'嘉定区',code:'A08'}],
      addressKey: '',
      radio:{},
      loading: true,
      error: false,
      finished: true,
      addressArr: [
          ],
      selectIndex: -1,
      installAddress: '',
      ziyuanShow: false,
      popupShow: false,
      popupShow2: false,
      popupShow3: false,
      popupShow4: false,
      showPicker:false,
      aliasCommunityActions:[],
     communityActions:[],
      buildingActions:[],
     floorActions:[],
    addressInfo:{
        ziyuan:{}
    },
      timeoutId:'',
      communityNameArr: [
        
      ],
      ziyuanArr:[],
      communityAddressArr: [
       
      ],
      buildingArr: [],// 当前显示的数据
      floorArr: [],
      communityFilter:'',
      communityFilterList:[],
      communityAddressFilter:'',
      communityAddressFilterList:[],
      floorFilter:'',
      floorFilterList:[],
      buildingFilter:'',
      buildingFilterList:[],// 原始完整数据（2000+条）
      filteredData: [], // 筛选后的数据
      pageSize: 50, // 每次加载的数量
      currentPage: 1, // 当前页码
      isLoadingMore: false, // 是否正在加载
      hasMore: true // 是否还有更多数据
      
    }
  },

  computed: {
    ...mapState([
      'jzfkOrderData',
      'shbmMsgInfo',
      'outCallMonetOrderData',
      'yskIptvCacheList',
      'outCallMonetOrderData'
    ])
  },
  mounted() {
  },
  watch: {
    // 当弹窗显示时初始化数据
    popupShow3(val) {
      if (val) {
        // 重置状态
        this.currentPage = 1;
        this.buildingArr = [];
        this.hasMore = true;
        this.buildingFilter = '';

        // 初始化筛选数据并加载第一页
        this.filteredData = [...this.buildingFilterList];
        this.loadMoreItems();
      }
    }
  },
  methods: {
    ...mapMutations([
      'setFlowStep',
      'setRobotWorking',
      'setOutCallMonetOrderData'
    ]),
    ...mapActions(['updateChatList']),
    popIsShow(type) {
      if (2 == type) {
        if (this.communityAddressArr.length > 0) {
          this.popupShow2 = true
        }
      } else if (3 == type) {
        if (this.buildingArr.length > 0) {
          this.popupShow3 = true
        }
      } else if (4 == type) {
        if (this.floorArr.length > 0) {
          this.popupShow4 = true
        }
      }
    },
    handleInput() {
      this.communityNameArr = [];
      this.buildingArr = [];
      this.communityAddressArr = [];
      this.floorArr = [];
      this.communityFilterList = [];
      this.buildingFilterList = [];
      this.communityAddressFilterList = [];
      this.floorFilterList = [];
      if (!this.isEmpty(this.communityName)) {
        this.isLoading = true
        // 清除之前的定时器
        if (this.timeoutId) {
          clearTimeout(this.timeoutId);
        }
        // 设置新的定时器
        this.timeoutId = setTimeout(() => {
          this.$refs.communityField.blur();
          this.addressByAlias(); // 输入完成后触发
        }, 3000); // 延迟 500ms
      }
    },
    sureChioce(type) {
      if (1 == type) {
        this.popupShow = false;
        if (this.communityAddressArr.length > 1) {
          this.popupShow2 = true;
        }
      } else if (2 == type) {
        this.popupShow2 = false;
        this.popupShow3 = true;
      } else if (3 == type) {
        this.popupShow3 = false;
        this.popupShow4 = true;
      } else if (4 == type) {
        this.popupShow4 = false;
      }
    },
    addressByAlias() {
      this.addressInfo = {
        ziyuan: {}
      };
      this.communityName1 = "";
      this.building = '';
      this.floor = '';
      this.communityAddress = ''
      this.isLoading = true
      console.log(this.communityAliasName + "this.communityAliasName")
      this.$http.post('/mpComm/queryAddressByVillageName', {
        villageKeyWord: this.communityName,
      }).then(res => {
        this.communityNameArr = [];
        this.communityFilterList = [];
        this.isLoading = false
        if ("0000" == res.respCode) {
          this.communityNameArr = res.respData.villageNameList
          this.communityFilterList = res.respData.villageNameList
          if (res.respData.villageNameList.length == 1) {
            this.communityName1 = this.communityName;
            this.onSelect0(this.communityNameArr[0])

          } else {
            this.popupShow = true
          }
        } else {
          this.$toast('没有查到对应小区')
        }
      })
    },
    submit() {
      if (1 == this.activeTab) {
        if (this.isEmpty(this.installAddress) || Object.keys(this.addressInfo.ziyuan).length == 0) {
          this.$toast('请选择装机地址，并进行资源预判')
        } else {
          let data = {
            inputType: "1",
            type: '1',
            textInput: "selectAddr",
            notifyFlag: '',
            taskName: '智能移送宽甩单'
          }
          this.$emit('newChatApi', data);
        }
      } else {
        if (Object.keys(this.addressInfo.ziyuan).length == 0) {
          this.$toast('请选择装机地址，并进行资源预判')
        } else {
          let data = {
            inputType: "1",
            type: '1',
            textInput: "selectAddr",
            notifyFlag: '',
            taskName: '智能移送宽甩单'
          }
          this.$emit('newChatApi', data);
        }

      }
    },
    changeOldContry(val) {
    },
    onSelect5(item) {
      this.addressInfo = {ziyuan: {}}
      this.addressArr = [];
      this.region = item.code
      this.regionName = item.name
    },
    tabAddrMethed(type) {
      this.ziyuanShow = false
      if (1 == type) {
        this.activeTab = 1;
      } else {
        this.activeTab = 2;
        this.addressInfo = {ziyuan: {}};
        this.installAddress = '';
        this.$emit('startLoading', '')
        this.$http.post('/mpComm/queryCityAndCountryCode', {}).then(res => {
          this.columns = [];
          this.$emit('endLoading', '')
          if ("0000" == res.respCode) {
            this.columns = res.respData
            this.region = this.columns[0].code
            this.regionName = this.columns[0].name
          }
        })
      }
    },

    addressKeyQuery() {
      this.$emit('startLoading', '')
      this.$http.post('/mpComm/qryAddrInfoByKeyWord', {
        addressKeyWords: this.addressKey,
        chooseDistrict: this.region
      }).then(res => {
        this.$emit('endLoading', '')
        if ("0000" == res.respCode) {
          this.addressArr = res.respData.addressList
          this.selectOldAddress(res.respData.addressList[0], 0)
        } else {
          this.$toast('没有找到对应小区名称')
        }
        this.finished = true
      })
    },
    onSelect0(action) {
      this.$emit('startLoading', '')
      this.addressInfo = action;
      this.communityName = action.villageName;
      this.communityName1 = action.villageName;

      this.$http.post('/mpComm/queryAddressByVillageCode', {
        villageName: action.villageName,
        villageCodeSel: action.villageCode,
        queryType: "0"
      }).then(res => {
        this.$emit('endLoading', '')
        this.communityAddressArr = [];
        this.communityAddressFilterList = [];
        if ("0000" == res.respCode) {
          if (res.respData.villageList.length > 0) {
            this.communityAddressArr = res.respData.villageList
            this.communityAddressFilterList = res.respData.villageList
            if (res.respData.villageList.length == 1) {
              this.popupShow2 = false
              this.onSelect(this.communityAddressArr[0])
              this.popupShow3 = true
            } else {
              this.popupShow2 = true;
            }
          } else {
            this.$toast('没有找到对应小区地址')
          }
        } else {
          this.$toast(res.respMsg)
        }
      })
    },

    onSelect(action) {
      this.$emit('startLoading', '')
      this.communityAddress = action;
      this.addressInfo.addressName = action;
      this.$http.post('/mpComm/queryAddressByVillageCode', {
        addressName: action,
        villageCodeSel: this.addressInfo.villageCode,
        queryType: "1"
      }).then(res => {
        this.$emit('endLoading', '')
        this.buildingArr = [];
        this.buildingFilterList = [];
        if ("0000" == res.respCode) {
          this.buildingArr = res.respData.listBlockNo
          this.buildingFilterList = res.respData.listBlockNo
        } else {
          this.$toast('没有找到对应楼栋')
        }
      })
    },
    onSelect2(action) {
      this.$emit('startLoading', '')
      this.building = action;
      this.addressInfo.blockNo = action;
      this.$http.post('/mpComm/queryAddressByVillageCode', {
        addressName: this.addressInfo.addressName,
        blockNo: action,
        roomNo: "",
        villageCodeSel: this.addressInfo.villageCode,
        queryType: "2"
      }).then(res => {
        this.$emit('endLoading', '')
        this.floorArr = [];
        this.floorFilterList = [];
        if ("0000" == res.respCode) {
          this.floorArr = res.respData.villageList
          this.floorFilterList = res.respData.villageList
        } else {
          this.$toast('没有找到对应室号')
        }
      })
    },
    onSelect3(action) {
      this.floor = action.ROOM_NO;
      this.addressInfo.SEGM_ID = action.SEGM_ID
      this.addressInfo.STATION_NAME = action.STATION_NAME
      this.addressInfo.ROOM_NO = action.ROOM_NO
    },
    isEmpty(value) {
      let flag = false
      if (value == '' || value == 'undefined' || value == undefined || value == null || value == 'null') {
        flag = true
      }
      return flag
    },
    resanticipation4gNew() {
      if (1 == this.activeTab) {
        if (this.isEmpty(this.communityAddress) || this.isEmpty(this.communityName)) {
          this.$toast('请选择装机地址，并进行资源预判')
        } else {
          this.$http.post('/mpComm/resanticipation4gNew', {
            shareType: 1,
            addressCode: this.addressInfo.SEGM_ID
          }).then(res => {
            this.ziyuanArr = [];
            if ("0000" == res.respCode) {
              if (Object.keys(res.respData.brandList).length != 0) {
                this.ziyuanShow = true
                this.ziyuanArr.push(res.respData.brandList)
                console.log(this.activeTab + "===============activeTab")
                if (this.activeTab == 1) {
                  this.installAddress = this.communityAddress + this.building + this.floor
                  this.addressInfo.fullAddr = this.installAddress
                  this.addressInfo.ziyuan = this.ziyuanArr[0];
                  this.outCallMonetOrderData.installAddr = this.addressInfo;
                  this.setOutCallMonetOrderData(this.outCallMonetOrderData);
                } else {
                  this.installAddress = this.addressInfo.fullAddr
                  this.addressInfo.ziyuan = this.ziyuanArr[0];
                  this.outCallMonetOrderData.installAddr = this.addressInfo;
                  this.setOutCallMonetOrderData(this.outCallMonetOrderData);
                }
                this.updateChatList({
                  sender: '1',
                  type: 'module',
                  moduleName: 'TextResponse',
                  moduleLevel: 1,
                  params: {
                    text: '您的装机地址是：' + this.installAddress
                  },
                  show: true
                })
                this.updateChatList({
                  sender: '1',
                  type: 'module',
                  moduleName: 'TextResponse',
                  moduleLevel: 1,
                  params: {
                    text: res.respData.resAnti4gTip
                  },
                  show: true
                })
                let data = {
                  inputType: "1",
                  type: '1',
                  textInput: "selectAddressSubmit",
                  notifyFlag: '',
                  taskName: '智能超清甩单'
                }
                this.$emit('newChatApi', data);
              } else {
                this.addressInfo = {
                  ziyuan: {}
                };
                this.updateChatList({
                  sender: '1',
                  type: 'module',
                  moduleName: 'TextResponse',
                  moduleLevel: 1,
                  params: {
                    text: '没有可用资源,请重新选择地址'
                  },
                  show: true
                })
                let data = {
                  inputType: "1",
                  type: '1',
                  textInput: "selectAddressError",
                  notifyFlag: '',
                  taskName: '智能移送宽甩单'
                }
                this.$emit('newChatApi', data);
              }
            } else {
              this.addressInfo = {
                ziyuan: {}
              };
              this.updateChatList({
                sender: '1',
                type: 'module',
                moduleName: 'TextResponse',
                moduleLevel: 1,
                params: {
                  text: res.respMsg + ",请重新选择地址"
                },
                show: true
              })
              let data = {
                inputType: "1",
                type: '1',
                textInput: "selectAddressError",
                notifyFlag: '',
                taskName: '智能超清甩单'
              }
              this.$emit('newChatApi', data);
            }
          })
        }
      } else {
        if (this.isEmpty(this.addressInfo.SEGM_ID)) {
          this.$toast('请选择装机地址，并进行资源预判')
        } else {
          this.$http.post('/mpComm/resanticipation4gNew', {
            shareType: 1,
            addressCode: this.addressInfo.SEGM_ID
          }).then(res => {
            this.ziyuanArr = [];
            if ("0000" == res.respCode) {
              if (Object.keys(res.respData.brandList).length != 0) {
                this.ziyuanShow = true
                this.ziyuanArr.push(res.respData.brandList)
                console.log(this.activeTab + "===============activeTab")
                if (this.activeTab == 1) {
                  this.installAddress = this.communityAddress + this.building + this.floor
                  this.addressInfo.fullAddr = this.installAddress
                  this.addressInfo.ziyuan = this.ziyuanArr[0];
                  this.outCallMonetOrderData.installAddr = this.addressInfo;
                  this.setOutCallMonetOrderData(this.outCallMonetOrderData);
                } else {
                  this.installAddress = this.addressInfo.fullAddr
                  this.addressInfo.ziyuan = this.ziyuanArr[0];
                  this.outCallMonetOrderData.installAddr = this.addressInfo;
                  this.setOutCallMonetOrderData(this.outCallMonetOrderData);
                }
                this.updateChatList({
                  sender: '1',
                  type: 'module',
                  moduleName: 'TextResponse',
                  moduleLevel: 1,
                  params: {
                    text: '您的装机地址是：' + this.installAddress
                  },
                  show: true
                })
                this.updateChatList({
                  sender: '1',
                  type: 'module',
                  moduleName: 'TextResponse',
                  moduleLevel: 1,
                  params: {
                    text: res.respData.resAnti4gTip
                  },
                  show: true
                })
                let data = {
                  inputType: "1",
                  type: '1',
                  textInput: "selectAddressSubmit",
                  notifyFlag: '',
                  taskName: '智能超清甩单'
                }
                this.$emit('newChatApi', data);
              } else {
                this.addressInfo = {
                  ziyuan: {}
                };
                this.updateChatList({
                  sender: '1',
                  type: 'module',
                  moduleName: 'TextResponse',
                  moduleLevel: 1,
                  params: {
                    text: '没有可用资源,请重新选择地址'
                  },
                  show: true
                })
                let data = {
                  inputType: "1",
                  type: '1',
                  textInput: "selectAddressError",
                  notifyFlag: '',
                  taskName: '智能移送宽甩单'
                }
                this.$emit('newChatApi', data);
              }
            } else {
              this.addressInfo = {
                ziyuan: {}
              };
              this.updateChatList({
                sender: '1',
                type: 'module',
                moduleName: 'TextResponse',
                moduleLevel: 1,
                params: {
                  text: res.respMsg + ",请重新选择地址"
                },
                show: true
              })

              let data = {
                inputType: "1",
                type: '1',
                textInput: "selectAddressError",
                notifyFlag: '',
                taskName: '智能超清甩单'
              }
              this.$emit('newChatApi', data);
            }
          })
        }

      }
    },
    onLoad() {
    },
    selectOldAddress(item, index) {
      this.ziyuanShow = false;
      this.addressInfo = {
        ziyuan: {}

      }
      this.installAddress = '';
      this.selectIndex = index
      this.addressInfo = item
      this.addressInfo.fullAddr = item.addressName
      this.addressInfo.SEGM_ID = item.addressCode
    },
    filterItems1() {
      console.log(  this.communityFilterList)
      const keyword = this.communityFilter.trim()
      this.communityNameArr= this.communityFilterList.filter(fruit => fruit.villageName.includes(keyword));
      console.log(  this.communityNameArr)
    },
    filterItems2() {
      console.log(  this.communityAddressFilterList)

      const keyword = this.communityAddressFilter.trim()
      this.communityAddressArr= this.communityAddressFilterList.filter(fruit => fruit.includes(keyword));
      console.log(  this.communityAddressArr)
    },
    filterItems3() {
      console.log(  this.buildingFilterList)
      const keyword = this.buildingFilter.trim()
      this.buildingArr= this.buildingFilterList.filter(fruit => fruit.includes(keyword));
      console.log(  this.buildingArr)
    },
    filterItems4() {
      console.log(  this.floorFilterList)
      const keyword = this.floorFilter.trim()
      this.floorArr= this.floorFilterList.filter(fruit => fruit.ROOM_NO.includes(keyword));
      console.log(  this.floorArr)
    },
    handleFilterChange() {
      // 重置分页状态
      this.currentPage = 1;
      this.buildingArr = [];
      this.hasMore = true;

      // 根据搜索内容筛选数据
      if (this.buildingFilter) {
        this.filteredData = this.buildingFilterList.filter(fruit => fruit.includes(this.buildingFilter)
        );
      } else {
        this.filteredData = [...this.buildingFilterList];
      }

      // 加载第一页数据
      this.loadMoreItems();
    },

    // 监听滚动事件
    handleScroll() {
      const container = this.$refs.scrollContainer;
      if (!container) return;

      // 计算滚动到底部的距离（这里设置为200px，可根据需要调整）
      const scrollBottom = container.scrollHeight - container.scrollTop - container.clientHeight;

      // 如果快滚动到底部，且不在加载中，且还有更多数据，则加载更多
      if (scrollBottom < 300 && !this.isLoadingMore && this.hasMore) {
        this.loadMoreItems();
      }
    },

    // 加载更多数据
    loadMoreItems() {
      this.isLoadingMore = true;

      // 模拟加载延迟（实际项目中可能是API请求）
      setTimeout(() => {
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const newItems = this.filteredData.slice(startIndex, endIndex);

        // 添加新数据到显示列表
        this.buildingArr = [...this.buildingArr, ...newItems];

        // 更新状态
        this.currentPage++;


        // 判断是否还有更多数据
        this.hasMore = endIndex < this.filteredData.length;
        console.log(" this.filteredData.length", this.filteredData.length)
        this.isLoadingMore = false;
      }, 2000);

    },
  }
}
</script>

<style scoped lang="scss">

.selectAddress{
  .tabs-fillet-corner {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    position: relative;
    box-sizing: border-box;
    //padding-top: 9px;
    overflow: hidden;
    background: #D8EAFF;
    border-radius: 7px 7px 0 0;
    .tabs-fillet-corner-item-wrap {
      flex: 1;
      height: 39px;
      padding: 0 12px;
    }
    .tabs-fillet-corner-item-wrap:first-child .tabs-fillet-corner-item {
      border-top-left-radius: 0;
    }
    .tabs-fillet-corner-item-wrap:first-child
    .tabs-fillet-corner-item-active::before {
      border-radius: 0;
      transform: skew(0deg);
      left: -17px;
    }
    .tabs-fillet-corner-item-wrap:last-child .tabs-fillet-corner-item {
      border-top-right-radius: 0;
    }

    .tabs-fillet-corner-item-wrap:last-child
    .tabs-fillet-corner-item-active::after {
      border-radius: 0;
      transform: skew(0deg);
      right: -17px;
    }

    .tabs-fillet-corner-item {
      border-radius: 8px 8px 0 0;
      width: 100%;
      height: 100%;
      position: relative;
    }
    .tabs-fillet-corner-item::before,
    .tabs-fillet-corner-item::after {
      content: '';
      position: absolute;
      top: 0;
      width: 12px;
      height: 100%;
      z-index: 9;
      background-color: #D8EAFF;
    }
    .tabs-fillet-corner-item::before {
      border-bottom-left-radius: 6px;
      transform: skew(17deg);
      left: -18px;
    }
    .tabs-fillet-corner-item::after {
      border-bottom-right-radius: 6px;
      transform: skew(-17deg);
      right: -18px;
    }
    .tabs-fillet-corner-item-text {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin: 0 auto;
      font-size: 13px;
      line-height: 39px;
      color: #263A5F;
      text-align: center;
      position: relative;
      cursor: pointer;
      z-index: 99;
      font-weight: 500;
    }
    .tabs-fillet-corner-item-active {
      background: #fff;
      z-index: 2;
    }
    .tabs-fillet-corner-item-active .tabs-fillet-corner-item-text {
      font-weight: 500;
      color: #263A5F;
    }
    .tabs-fillet-corner-item-active::before,
    .tabs-fillet-corner-item-active::after {
      content: '';
      position: absolute;
      top: 0;
      width: 17px;
      height: 100%;
      background: #fff;
      border-radius: 17px 17px 0 0;
    }
    .tabs-fillet-corner-item-active::before {
      transform: skew(-17deg);
      left: -6.5px;
      box-shadow: -5px 15px 0 #fff;
    }
    .tabs-fillet-corner-item-active::after {
      transform: skew(17deg);
      right: -6.5px;
      box-shadow: 5px 15px 0 0 #fff;
    }
  }
  .checkButton {
    width: 55px;
    height: 30px;
    background: #0081FF;
    border-radius: 4px;
    border-style: none;
    color: #FFFFFF;
    font-size: 14px;
    padding: 0;
  }
  
  .rIcon {
    margin-right:20px;
  }
  .paragraphspacing{
    font-size: 14px;
    font-weight: 400;
    padding-bottom: 8px;
    color: #263A5F;
  }
  .info-style{
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #999999;
    line-height: 20px;
    text-align: left;
    word-break: break-all;
  }
  .address-box{
    border-radius: 10px;
    padding: 5px;
    height: 200px;
    overflow: scroll;
  }
  .active {
    position: relative;
    margin-bottom: 10px;
    width: 97%;;
    height: auto;
    background: #E9F4FE;
    border: 1px solid rgba(80,148,245,1);
    border-radius: 10px;
    padding-left: 5px;
  }
  .list-item {
    position: relative;
    margin-bottom: 10px;
    width: 97%;
    height: auto;
    background: #F5F8FA;
    border: 1.5px dashed rgba(203, 203, 203, 1);
    border-radius: 10px;
    padding-left: 5px;
  }
  .btn{
    width: 96%;
    border-radius: 5px;
    margin: 10px auto;
    height: 40px;
  }
  .ziyuan-div{
    background: #EDF7FF;
    border: 1px solid rgba(80,148,245,1);
    border-radius: 8px;
    width: 100%;
    .ziyuan-box{
      display: flex;
      padding: 3% 3% 0 3%;
      font-size: 14px;
      span{
        width: 40%;
      }
    }
  }
  .search-box{
    display: flex;
    align-items: center;
    justify-content: space-between;
    .region-type-select{
      background: #F7F8FA;
      border: none;
      width: 115px;
      padding: 10px;
      border-radius: 5px;
      font-size: 14px;
      margin-right: 10px;
    }
  }
  .search-container {
    display: flex;       /* 启用 Flex 布局 */
    align-items: center; /* 垂直居中 */
  }

  /* 让 van-search 占据剩余空间 */
  .search-container .van-search {
    flex: 1;
  }
  .search-icon{
    width: 24px;
    height: 24px;
    font-size: 24px;
  }
  /* 搜索图标样式 */
  .search-container .search-icon {
    margin-left: 8px;  /* 调整间距 */
    cursor: pointer;   /* 鼠标指针变手型 */
  }

  .van-popup {
    padding: 20px 0;
    border-radius: 20px 20px 0 0;
    background-image: linear-gradient(179deg, #E5F0FF 0%, #FFFFFF 36%);
    box-shadow: 0px -4px 14px 4px rgba(0,0,0,0.09);
    .popup-list{
      max-height:264px;
      overflow-y:auto;
      padding: 5px 15px;
    }
    .van-picker__confirm{
      width: 90%;
      border: 2px solid rgba(73, 124, 246, 1);
      border-radius: 8px;
      color: rgba(73, 124, 246, 1);
      padding: 10px 0;
      margin-left: 4%;
      margin-top: 20px;
    }
  }
  .popup1{
    .title{
      font-size: 16px;
      color: #263A5F;
      font-weight: 500;
      width: 96%;
      padding:10px 10px;
      border-bottom: 1px solid rgba(213,221,234,1);
    }
    .popup-select{
      width: 100%;
      position: fixed;
    }
    .radio-group-list{
      height: 65%;
      overflow: auto;
      margin-top: 75px;
    }
  }
}
</style>
<style>
.selectAddress{
  .van-tabs__nav--card{
    margin: 0;
  }
  .van-tab__text--ellipsis{
    font-size: 13px;
  }
  .van-search{
    padding: 10px 0;
  }
  .van-search__content--round{
    margin-top: 8px;
  }
  .van-cell{
    padding: 6px 5px 5px 5px;
    background: none;
  }
  .van-field__label,.van-cell__title{
    color: #263A5F;
  }
  .van-search__content--round{
    border-radius: 5px;
  }
  .van-popup {
    .van-cell{
      padding: 10px 5px;
    }
    .popup-list{
      .van-cell{
        color: #263A5F;
        background: none;
      }
      .van-field__control {
        text-align: left;
      }

      .van-field__control::-webkit-input-placeholder {
        text-align: center;
        direction: ltr;
      }

      .van-field__control::-moz-placeholder {
        text-align: center;
        direction: ltr;
      }

      .van-field__control:-ms-input-placeholder {
        text-align: center;
        direction: ltr;
      }

      .van-field__control::-ms-input-placeholder {
        text-align: center;
        direction: ltr;
      }
    }
  }
  .van-cell-group{
    background: none;
  }
}
</style>