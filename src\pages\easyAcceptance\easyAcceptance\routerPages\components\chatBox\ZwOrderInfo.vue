<template>
  <div class="card-verification" style="background-color: white">
    <div class="subpreview-box" v-for="(item,index1) in showList" :key="index1" @click="item.showAll=!item.showAll">
      <div :style="{background: item.showAll ? '#E5F0FF':'#EDF7FF'}" class="total-title">
        <span class="custom-label-name">{{ item.name }}</span>
        <van-icon :name="item.showAll ? 'arrow-up':'arrow'" />
      </div>
      <div v-for="(item1,index) in item.itemList" :key="index" style="padding: 0 10px;">
        <div v-show="item.showAll" style="display:flex">
          <span class="custom-title flex-shink">{{ item1.text }}</span>
          <span class="custom-title" >{{item1.value}}</span>
        </div>
      </div>
    </div>
    <div class="submit-container">
      <van-button class="sub" @click="preSubmit()" block>提交</van-button>
    </div>
  </div>
</template>

<script>
import WadeMobile from "rk-native-plugin";
import errorTips from '@/assets/bizComponents/errorTips/errorTips.vue';
import {copyText} from "../../../assets/js/func";
import {mapActions, mapMutations, mapState} from "vuex";
export default {
  name: "ZwOrderInfo",
  data() {
    return {
      imageList: [
        require('../../../images/arrow.png'),
        require('../../../images/add.png')
      ],
      showList: [
      
      ],
   
    }
  },
  components: {
    errorTips
  },
  computed: {
    ...mapState([
      'chatList',
      'sessionId',
      'staffId',
      'flowStep',
      'num',
      'shbmMsgInfo',
      'jzfkOrderData',
      'activeModuleIndex',
      'loginPhoneNumber',
      'instanceId',
        'zwOrderData'
    ])
  },
  mounted(){
    this.setRespTipArrQry([]);
    this.setBlockShow(false);
    this.checkOrderDetail()
    console.log('tgybyhbhbh')
    console.log(this.zwOrderData.zwGoodData.commodityChooseYWUlList)
    console.log(this.zwOrderData.zwGoodData.zwList)
    let zwString='';
    let ywfString='无';
    if(this.zwOrderData.zwGoodData.zwList.length>0){
      for(let i=0;i<this.zwOrderData.zwGoodData.zwList.length-1;i++){
        zwString+=this.zwOrderData.zwGoodData.zwList[i].zwName+",";
      }
      zwString+=this.zwOrderData.zwGoodData.zwList[this.zwOrderData.zwGoodData.zwList.length-1].zwName;
    }
    if(this.zwOrderData.zwGoodData.commodityChooseYWUlList.length>0){
      if(this.zwOrderData.zwGoodData.commodityChooseYWUlList[0].ywGoodId!='0'){
        ywfString='';
        for(let i=0;i<this.zwOrderData.zwGoodData.commodityChooseYWUlList.length-1;i++){
          ywfString+=this.zwOrderData.zwGoodData.commodityChooseYWUlList[i].ywGoodName+",";
        }
        ywfString+=this.zwOrderData.zwGoodData.commodityChooseYWUlList[this.zwOrderData.zwGoodData.commodityChooseYWUlList.length-1].ywGoodName;
      }
    }
    this.showList=[
      {
        name: '客户信息',
        showAll: true,
        itemList:[
          {text:'宽带号码：',value:this.zwOrderData.checkNumberData.serialNumber},
          {text:'客户名称：',value:this.zwOrderData.checkNumberData.showCustName}
        ]
      },{
        name: '商品信息',
        showAll: true,
        itemList:[
          {text:'组网商品：',value:''},
          {text:'    ',value:"    "+zwString},
          {text:'移网附加优惠商品：',value:''},
          {text:'    ',value:"    "+ywfString}
        ]
      },{
        name: '装机信息',
        showAll: true,
        itemList:[
          {text:'装机地址：',value:this.zwOrderData.checkNumberData.showInstallAddrTm}
        ]
      },
    ]
  },
  methods: {
    ...mapMutations([
      'setFlowStep',
      'setRobotWorking',
      'setZwOrderData',
        'setRespTipArrQry',
        'setBlockShow'
    ]),
    ...mapActions(['updateChatList']),
    isEmpty(value) {
      let flag = false
      if (value == '' || value == 'undefined' || value == undefined || value == null || value == 'null') {
        flag = true
      }
      return flag
    },

    uniqueByProperty(arr, prop) {
      const uniqueMap = new Map();
      return arr.reduce((acc, current) => {
        const key = current[prop];
        if (!uniqueMap.has(key)) {
          uniqueMap.set(key, true);
          acc.push(current);
        }
        return acc;
      }, []);
    },
    checkOrderDetail(){
   WadeMobile.getSysInfo("PLATFORM").then((info) => {
      this.zwOrderData.platfForm=info;
      this.setZwOrderData(this.zwOrderData);
      console.log("PLATFORM：：" + info);
    }, (err) => {
      console.log("PLATFORM"+"失败：" + err);
    });
   },
    preSubmit(){
      
      if(this.zwOrderData.zwGoodData.zwList.length==0){
        this.$toast('请选择组网商品')
        return;
      }
      this.$emit('startLoading', '')
      let zwInfoList={};
      if(this.zwOrderData.zwGoodData.zwList.length>0){
        let zw={
          commodityCode:this.zwOrderData.zwGoodData.zwList[0].commodityCode,
          commodityName:this.zwOrderData.zwGoodData.zwList[0].zwName,
        }
        zwInfoList=zw;
      }
      let ywfGoodInfoList=[];
      if(this.zwOrderData.zwGoodData.commodityChooseYWUlList.length==0){
        let ywf={
          ywfFlg:0,
          ywfName:'不需要',
          goodsPrice:0
        };
        ywfGoodInfoList.push(ywf);
      }
      if(this.zwOrderData.zwGoodData.commodityChooseYWUlList.length>0){
        if(this.zwOrderData.zwGoodData.commodityChooseYWUlList[0].ywGoodId!='0'){
          for(let i=0;i<this.zwOrderData.zwGoodData.commodityChooseYWUlList.length;i++){
          let ywf={
            ywfFlg:this.zwOrderData.zwGoodData.commodityChooseYWUlList[i].ywGoodId,
            ywfName:this.zwOrderData.zwGoodData.commodityChooseYWUlList[i].ywGoodName,
            goodsPrice:0
          }
            ywfGoodInfoList.push(ywf);
          }
        }
        else{
            let ywf={
              ywfFlg:0,
              ywfName:this.zwOrderData.zwGoodData.commodityChooseYWUlList[0].ywGoodName,
              goodsPrice:0
                };
                ywfGoodInfoList.push(ywf);
          
        }
      }
      let  stdContact=this.zwOrderData.timeData.contactName;
      if(this.isEmpty(stdContact)){
        stdContact=this.zwOrderData.checkNumberData.custName;
      }
      if(stdContact==this.zwOrderData.checkNumberData.showCustName){
        stdContact=this.zwOrderData.checkNumberData.custName;
      }
      let communityAddrInfo={
        exchCode:this.zwOrderData.checkNumberData.showExchCode,
        addressName:this.zwOrderData.checkNumberData.showInstallAddr,
      addressCode:this.zwOrderData.checkNumberData.showAddrCode,
      stdContact:stdContact,
       stdContactPhone:this.zwOrderData.timeData.contactPhone,
        stdBookDay:this.zwOrderData.timeData.stdBookDay,
        
        stdBookTime:this.zwOrderData.timeData.stdBookTime
      }
      let rhRelationMemberMsg={
        userTypeFlag: this.zwOrderData.checkNumberData.isRHUserTag,
        phoneSerialNumber:this.zwOrderData.checkNumberData.phoneSerialNumber,
        ywProductName:this.zwOrderData.checkNumberData.ywProductName,
        ywProductId: this.zwOrderData.checkNumberData.ywProductId,
        rhSerialNumber: this.zwOrderData.checkNumberData.rhSerialNumber,
        rhProductId:this.zwOrderData.checkNumberData.rhProductId ,
        rhProductName:this.zwOrderData.checkNumberData.rhProductName ,
        mixTypeCode:this.zwOrderData.checkNumberData.mixTypeCode ,
        kdProductId:this.zwOrderData.checkNumberData.kdProductId ,
        kdProductName:this.zwOrderData.checkNumberData.kdProductName
      }
      if("0"==this.zwOrderData.checkNumberData.isRHUserTag){
        rhRelationMemberMsg={
          userTypeFlag: this.zwOrderData.checkNumberData.isRHUserTag
        }
      }
      let req={
        ywfGoodInfoList:ywfGoodInfoList,
        changeCommodityInfo:zwInfoList,
        communityAddrInfo:communityAddrInfo,
        serviceNumber:this.zwOrderData.checkNumberData.serialNumber,
        choiceValue:this.zwOrderData.choiceValue,
        rhRelationMemberMsg:rhRelationMemberMsg,
        orderPrice:this.zwOrderData.orderPrice,
        remark:"备注:"+this.zwOrderData.remark+";预约时间:"+this.zwOrderData.timeData.stdBookDay+" "+this.zwOrderData.timeData.stdBookTime
      }
      
      this.$http.post('/zhzwZsd/preSubmitZsd',req).then((res)=>{
        this.$emit('endLoading', '')
        if (res.respCode === '0000') {
          this.zwOrderData.orderId=res.respData.orderId;
          this.updateChatList({
            sender: '1',
            type: 'module',
            moduleName: 'TextResponse',
            moduleLevel: 1,
            params: {
              text: '订单提交成功!订单号'+res.respData.orderId
            },
            show: true
          })
          this.setZwOrderData(this.zwOrderData);
          let data = {
            inputType: "1",
            type: '1',
            textInput: "zwOrderfoSubmit",
            notifyFlag: '',
            taskName:'智能组网甩单'
          }
          this.$emit('newChatApi', data);
        }
        else{
          this.updateChatList({
            sender: '1',
            type: 'module',
            moduleName: 'TextResponse',
            moduleLevel: 1,
            params: {
              text: res.respMsg
            },
            show: true
          })
        }   
      })
    }
  }
}
</script>
<style lang="scss">
.card-verification {
  width:100%;
  margin: auto;
  .card-group {
    background-color: white;
    margin: 0px 10px;
    padding:0px 5px;
    .tip {
      .van-cell__value {
        color: black;
        text-align: right;
      }}

    .card-list {
      .van-cell__title {
        font-size: 13px;
        color: #666666;
      }
      .van-cell__value {
        color: black;
        text-align: right;
        font-size: 13px;
      }
      .van-cell {
        color: black !important;
        padding: 0px 16px;
      }
      .van-cell::after {
        color: black !important;
        border-bottom: 0;

      }
    }
    .van-hairline--top-bottom::after, .van-hairline-unset--top-bottom::after {
      border-width: 0 0;
    }
  }
  .card {
    .van-cell__title {
      font-size: 13px;
      color: #000000;
    }
    .checkButton {
      width: 70px;
      height: 30px;
      background: #0081FF;
      border-radius: 4px;
    }
    .checkFont {
      font-size: 13px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 14px;
    }
  }
  .check{
    .van-cell__title {
      font-size: 13px;
      color: #333333;
    }
  }
}
</style>

<style lang="scss" scoped>
.card-verification {
  background-color: rgb(57,159,254);
  .subpreview-box{
    border-radius: 10px;
    margin-bottom: 10px;
    box-shadow: 0 2px 1px 0 #eee;
    .total-title{
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 5px 10px;
      width: 93%;
      border-radius: 10px 10px 0 0;
      margin-bottom: 10px;
      .custom-label-name{
        font-size: 15px;
        line-height: 30px;
        color: #263A5F;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
      }
    }
    
    .custom-title{
      font-family: PingFangSC-Regular;
      font-size: 14px;
      line-height: 26px;
      color: #263A5F;
      padding-left: 14px;
    }
    .flex-shink{
      flex-shrink: 0;
    }
  }
  .desc {
    color: black;
    font-size: 13px;
    line-height: 20px;
  }
  .margin-two {
    bottom: 10px;
    left: 15px;
    right: 15px;
    z-index: 2;
    position: absolute;
  }
  .van-buttons {
    border-radius: 4px;
    height: 44px;
    font-size: 13px;
    color: #FFFFFF;
    background-color: #0081FF;
  }
  .font {
    color:#333333;
  }
  .submit-container{
    .sub{
      border: 2px solid rgba(73,124,246,1);
      border-radius: 5px;
      color: rgba(73,124,246,1);
      margin-top: 20px;
    }
  }
}

</style>