<template>
    <div class="header" @click="hideChatContainer">
        <van-nav-bar>
            <template #left>
              <img style="width: 35px;" class="top-icon" src="@/assets/images/profilePicture.png" />
              <img style="width: 90px;" class="top-icon" src="@/assets/images/headTitle.png" />
            </template>
            <template #right>
<!--                <img class="top-icon" src="@/assets/images/setting.png" @click.stop="setUpEnd"/>-->
<!--                <img style="width: 18px;" class="top-icon" src="@/assets/images/document.png" @click.stop="checkOrderDetail" />-->
<!--                <img style="margin-right: 0" class="top-icon" src="@/assets/images/message.png" @click.stop="startNewChat" />-->
                 <img  class="top-icon1" src="@/assets/images/addChat3.png" @click.stop="startOpenQry" />
                 <img style="margin-right: 0" class="top-icon" src="@/assets/images/addChat4.png" @click.stop="startNewChat" />
            </template>
          
        </van-nav-bar>
    </div>
</template>

<script>
    import {mapActions, mapMutations, mapState} from "vuex";

export default {
    name: 'Header',
    props: {
        showBack: {
            type: Boolean,
            required: true
        },
        title: {
            type: String,
            required: true
        }
    },
    data() {
        return {}
    },
    computed:{
        ...mapState(['curTask'
        ])
    },
    methods: {
        ...mapMutations([
            'updateChatList',
            'setNeedWait'
        ]),
        ...mapActions(['custOrderQuery','showHotWords']),
        hideChatContainer() {
            this.$emit('hideChatContainer', '')
        },
      setUpEnd() {
        this.$emit('setUpEnd', '')
      },
        // 点击查看订单详情
        checkOrderDetail() {
            this.$emit('checkOrderDetail', '')
        },
        // 点击新建会话按钮
        startNewChat() {
            this.$emit('startNewChat', '')
        },
        startNewChat3(){

            console.log("startNewChat3");
        },
        async startOpenQry() {
            if(!this.curTask.taskId){
                let data = {
                    inputType: "1",
                    type: '1',
                    textInput: "智能订单查询",
                    notifyFlag: '',
                    async:true,
                }
                this.setNeedWait(true);
                await this.$emit('userInput', data);
                this.setNeedWait(false);
            }else{
                 let data = {
                    inputType: "1",
                    type: '1',
                    textInput: "智能订单查询",
                    notifyFlag: ''
                }
                this.$emit('userInput', data);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.header {
  position: fixed;
  top: 0;
  width: 100%;
  height: 55px;
  z-index: 1112;
  background-image: url('@/assets/images/backgroundTitle.png');
  background-size: cover;
  background-repeat: no-repeat;
  //background: url('@/assets/images/head_bg.png');
    //background-size: cover;
    //height: 180px;
}


.van-hairline--bottom:after {
    border-bottom-width: 0;
}

/deep/.van-nav-bar__title {
    color: #ffffff;
    font-size: 19px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
}

.van-nav-bar {
    background-color: unset;
    padding-top: 10px;
    img {
        width: 25px;
    }
    .top-icon1{
        height: 25px;
        margin-right: 10px;
    }

    .top-icon {
        margin-right: 12px;
    }
}
</style>