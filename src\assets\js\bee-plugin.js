!function(e,n){"object"==typeof exports&&"object"==typeof module?module.exports=n():"function"==typeof define&&define.amd?define([],n):"object"==typeof exports?exports.beePlugin=n():e.beePlugin=n()}(window,(function(){return function(e){var n={};function t(a){if(n[a])return n[a].exports;var r=n[a]={i:a,l:!1,exports:{}};return e[a].call(r.exports,r,r.exports,t),r.l=!0,r.exports}return t.m=e,t.c=n,t.d=function(e,n,a){t.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:a})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,n){if(1&n&&(e=t(e)),8&n)return e;if(4&n&&"object"==typeof e&&e&&e.__esModule)return e;var a=Object.create(null);if(t.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var r in e)t.d(a,r,function(n){return e[n]}.bind(null,r));return a},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},t.p="",t(t.s=10)}([function(e,n,t){"use strict";function a(e){return"string"!=typeof e||"{"!==e[0]&&"["!==e[0]?e:JSON.parse(e)}t.d(n,"a",(function(){return a}))},function(e,n,t){"use strict";var a=t(2),r=t.n(a),o=/^(\s|\u00A0)+|(\s|\u00A0)+$/g,i=Array.prototype.push,s=function(e){return e.replace(/([-.*+?^${}()|[\]\/\\])/g,"\\$1")},c=!1,l=Object.prototype.toString;r.a.Collection=function(e,n){this.items=[],this.map={},this.keys=[],this.length=0,this.allowFunctions=!0===e,n&&(this.getKey=n)},r.a.Collection.prototype={allowFunctions:!1,add:function(e,n){if(1==arguments.length&&(n=arguments[0],e=this.getKey(n)),null!=e){var t=this.map[e];if(void 0!==t)return this.replace(e,n);this.map[e]=n}return this.length++,this.items.push(n),this.keys.push(e),n},getKey:function(e){return e.id},replace:function(e,n){1==arguments.length&&(n=arguments[0],e=this.getKey(n));var t=this.map[e];if(null==e||void 0===t)return this.add(e,n);var a=this.indexOfKey(e);return this.items[a]=n,this.map[e]=n,n},addAll:function(e){if(arguments.length>1||r.a.isArray(e))for(var n=arguments.length>1?arguments:e,t=0,a=n.length;t<a;t++)this.add(n[t]);else for(var o in e)(this.allowFunctions||"function"!=typeof e[o])&&this.add(o,e[o])},putAll:function(e){var n=this;e&&void 0!==e&&void 0!==e.keys&&void 0!==e.items?r.a.each(e.keys,(function(t,a){n.put(a,e.get(a))})):n.addAll(e)},each:function(e,n){for(var t=[].concat(this.items),a=0,r=t.length;a<r&&!1!==e.call(n||t[a],t[a],a,r);a++);},eachKey:function(e,n){for(var t=0,a=this.keys.length;t<a;t++)e.call(n||window,this.keys[t],this.items[t],t,a)},find:function(e,n){for(var t=0,a=this.items.length;t<a;t++)if(e.call(n||window,this.items[t],this.keys[t]))return this.items[t];return null},insert:function(e,n,t){return 2==arguments.length&&(t=arguments[1],n=this.getKey(t)),this.containsKey(n)&&(this.suspendEvents(),this.removeKey(n),this.resumeEvents()),e>=this.length?this.add(n,t):(this.length++,this.items.splice(e,0,t),null!=n&&(this.map[n]=t),this.keys.splice(e,0,n),t)},remove:function(e){return this.removeAt(this.indexOf(e))},removeAt:function(e){if(e<this.length&&e>=0){this.length--;var n=this.items[e];this.items.splice(e,1);var t=this.keys[e];return void 0!==t&&delete this.map[t],this.keys.splice(e,1),n}return!1},removeKey:function(e){return this.removeAt(this.indexOfKey(e))},getCount:function(){return this.length},indexOf:function(e){return r.a.inArray(e,this.items)},indexOfKey:function(e){return r.a.inArray(e,this.keys)},item:function(e){var n=this.map[e],t=void 0!==n?n:"number"==typeof e?this.items[e]:void 0;return"function"!=typeof t||this.allowFunctions?t:null},itemAt:function(e){return this.items[e]},key:function(e){return this.map[e]},contains:function(e){return-1!=this.indexOf(e)},containsKey:function(e){return void 0!==this.map[e]},clear:function(){this.length=0,this.items=[],this.keys=[],this.map={}},first:function(){return this.items[0]},last:function(){return this.items[this.length-1]},_sort:function(e,n,t){var a,r,o="DESC"==String(n).toUpperCase()?-1:1,i=[],s=this.keys,c=this.items;for(t=t||function(e,n){return e-n},a=0,r=c.length;a<r;a++)i[i.length]={key:s[a],value:c[a],index:a};for(i.sort((function(n,a){var r=t(n[e],a[e])*o;return 0===r&&(r=n.index<a.index?-1:1),r})),a=0,r=i.length;a<r;a++)c[a]=i[a].value,s[a]=i[a].key},sort:function(e,n){this._sort("value",e,n)},reorder:function(e){this.suspendEvents();var n=this.items,t=0,a=n.length,r=[],o=[];for(let t in e)r[e[t]]=n[t];for(t=0;t<a;t++)null==e[t]&&o.push(n[t]);for(t=0;t<a;t++)null==r[t]&&(r[t]=o.shift());this.clear(),this.addAll(r),this.resumeEvents()},keySort:function(e,n){this._sort("key",e,n||function(e,n){var t=String(e).toUpperCase(),a=String(n).toUpperCase();return t>a?1:t<a?-1:0})},getRange:function(e,n){var t=this.items;if(t.length<1)return[];var a,r=[];if((e=e||0)<=(n=Math.min(void 0===n?this.length-1:n,this.length-1)))for(a=e;a<=n;a++)r[r.length]=t[a];else for(a=e;a>=n;a--)r[r.length]=t[a];return r},filter:function(e,n,t,a){return n?(n=this.createValueMatcher(n,t,a),this.filterBy((function(t){return t&&n.test(t[e])}))):this.clone()},filterBy:function(e,n){var t=new r.a.Collection;t.getKey=this.getKey;for(var a=this.keys,o=this.items,i=0,s=o.length;i<s;i++)e.call(n||this,o[i],a[i])&&t.add(a[i],o[i]);return t},findIndex:function(e,n,t,a,r){return n?(n=this.createValueMatcher(n,a,r),this.findIndexBy((function(t){return t&&n.test(t[e])}),null,t)):-1},findIndexBy:function(e,n,t){for(var a=this.keys,r=this.items,o=t||0,i=r.length;o<i;o++)if(e.call(n||this,r[o],a[o]))return o;return-1},createValueMatcher:function(e,n,t,a){return e.exec||(e=String(e),!0===n?e=s(e):(e="^"+s(e),!0===a&&(e+="$")),e=new RegExp(e,t?"":"i")),e},clone:function(){for(var e=new r.a.Collection,n=this.keys,t=this.items,a=0,o=t.length;a<o;a++)e.add(n[a],t[a]);return e.getKey=this.getKey,e}},r.a.extend(r.a,{noop:function(){},error:function(e){throw e},isNumber:function(e){return"number"==typeof e&&isFinite(e)},isNumeric:function(e){return!isNaN(parseFloat(e))&&isFinite(e)},isString:function(e){return"string"==typeof e},isBoolean:function(e){return"boolean"==typeof e},isFunction:function(e){return"[object Function]"===l.call(e)},isObject:function(e){return"[object Object]"===l.call(e)},isEmptyObject:function(e){for(var n in e)return!1;return!0},isElement:function(e){return!!e&&void 0!==e.nodeType},isNodeName:function(e,n){return r.a.nodeName(e,n)},nodeName:function(e,n){return e.nodeName&&e.nodeName.toUpperCase()===n.toUpperCase()},isWindow:function(e){return e&&"object"==typeof e&&"setInterval"in e},trim:function(e){return(e||"").replace(o,"")},inArray:function(e,n){if(n.indexOf)return n.indexOf(e);for(var t=0,a=n.length;t<a;t++)if(n[t]===e)return t;return-1},makeArray:function(e,n){var t=n||[];return null!=e&&(null==e.length||"string"==typeof e||r.a.isFunction(e)||"function"!=typeof e&&e.setInterval?i.call(t,e):r.a.merge(t,e)),t},merge:function(e,n){var t=e.length,a=0;if("number"==typeof n.length)for(var r=n.length;a<r;a++)e[t++]=n[a];else for(;void 0!==n[a];)e[t++]=n[a++];return e.length=t,e},grep:function(e,n,t){for(var a=[],r=0,o=e.length;r<o;r++)!t!=!n(e[r],r)&&a.push(e[r]);return a},map:function(e,n,t){for(var a,r=[],o=0,i=e.length;o<i;o++)null!=(a=n(e[o],o,t))&&(r[r.length]=a);return r.concat.apply([],r)},each:function(e,n,t){var a,o=0,i=e.length,s=void 0===i||r.a.isFunction(e);if(t)if(s){for(a in e)if(!1===n.apply(e[a],t))break}else for(;o<i&&!1!==n.apply(e[o++],t););else if(s){for(a in e)if(!1===n.call(e[a],a,e[a]))break}else for(var c=e[0];o<i&&!1!==n.call(c,o,c);c=e[++o]);return e},parseJSON:function(e){return"string"==typeof e&&e?(e=r.a.trim(e),/^[\],:{}\s]*$/.test(e.replace(/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]").replace(/(?:^|:|,)(?:\s*\[)+/g,""))?window.JSON&&window.JSON.parse?window.JSON.parse(e):new Function("return "+e)():void r.a.error("Invalid JSON: "+e)):null},globalEval:function(e){if(e&&rnotwhite.test(e)){var n=document.getElementsByTagName("head")[0]||document.documentElement,t=document.createElement("script");t.type="text/javascript",c?t.appendChild(document.createTextNode(e)):t.text=e,n.insertBefore(t,n.firstChild),n.removeChild(t)}},uaMatch:function(e){e=e.toLowerCase();var n=/(webkit)[ \/]([\w.]+)/.exec(e)||/(opera)(?:.*version)?[ \/]([\w.]+)/.exec(e)||/(msie) ([\w.]+)/.exec(e)||!/compatible/.test(e)&&/(mozilla)(?:.*? rv:([\w.]+))?/.exec(e)||/(gecko)[ \/]([^\s]*)/i.exec(e)||[];return{browser:n[1]||"",version:n[2]||"0"}},browser:{}}),r.a.extend(r.a,{parseJsonString:function(e){if(!e)return"";e=(e=(e=(e=(e=(e=(e=(e=(e=(e=(e=(e=(e=(e=e.replace(/([:{}\[\]\"])[\s|\u00A0]+/g,"$1")).replace(/[\s|\u00A0]+([:{}\[\]\"])/g,"$1")).replace(/,([^\":{}\[\]]+):\"/g,',"$1":"')).replace(/,([^\":{}\[\]]+):\{/g,',"$1":{')).replace(/,([^\":{}\[\]]+):\[/g,',"$1":[')).replace(/\{([^\":{}\[\]]+):\"/g,'{"$1":"')).replace(/\{([^\":{}\[\]]+):\{/g,'{"$1":{')).replace(/\{([^\":{}\[\]]+):\[/g,'{"$1":[')).replace(/\\\":(null|undefined)(,|})/g,'\\":\\"\\"$2')).replace(/\\\":(true|false)(,|})/g,'\\":\\"$1\\"$2')).replace(/\\\":(-)?([0-9\.]+)(,|})/g,'\\":\\"$1$2\\"$3')).replace(/\\\":(null|undefined)(,|})/g,'\\":""$2')).replace(/\\\":(true|false)(,|})/g,'\\":"$1"$2')).replace(/\\\":(-)?([0-9\.]+)(,|})/g,'\\":"$1$2"$3');for(var n="",t=0;t<e.length;t++){var a=e.charAt(t);switch(a){case"\b":n+="\\b";break;case"\f":n+="\\f";break;case"\r":n+="\\r";break;case"\t":n+="\\t";break;case"\n":n+="\\n";break;default:n+=a}}return e=n},parseJsonValue:function(e){if(!e)return e;for(var n="",t=0;t<e.length;t++){var a=e.charAt(t);switch(a){case"\b":n+="\\b";break;case"\f":n+="\\f";break;case"\r":n+="\\r";break;case"\t":n+="\\t";break;case"\n":n+="\\n";break;case'"':n+='\\"';break;case"\\":n+="\\\\";break;default:n+=a}}return n}}),r.a.DataMap=function(e){if(!this.parseString)return new r.a.DataMap(e);r.a.Collection.call(this),e&&(r.a.isString(e)?this.parseString(e):"object"==typeof e&&(e instanceof r.a.DataMap?this.parseObject(e.map):this.parseObject(e)))},r.a.DataMap.prototype=new r.a.Collection,r.a.extend(r.a.DataMap.prototype,{get:function(e,n){var t=this.item(e);return arguments.length>1&&(void 0===t||null==t)?arguments[1]:t},parseString:function(e){return e=r.a.parseJsonString(e),window.JSON&&window.JSON.parse?this.parseObject(window.JSON.parse(e)):new Function("this.parseObject("+e+")").apply(this)},parseObject:function(e){for(var n in e)e.hasOwnProperty(n)&&(e[n]&&r.a.isArray(e[n])?this.add(n,new r.a.DatasetList(e[n])):e[n]&&r.a.isObject(e[n])?this.add(n,new r.a.DataMap(e[n])):this.add(n,null==e[n]||null==e[n]?"":e[n]))}}),r.a.DataMap.prototype.toString=function(){var e=[],n="";for(var t in this.map)n='"'+t+'":',void 0===this.map[t]||null==this.map[t]?n+='""':"string"==typeof this.map[t]?n+='"'+r.a.parseJsonValue(""+this.map[t])+'"':isNaN(this.map[t])?n+=this.map[t].toString():n+=this.map[t],e.push(n);return"{"+e.join(",")+"}"},r.a.DataMap.prototype.put=r.a.DataMap.prototype.add,r.a.DatasetList=function(e){if(!this.parseString)return new r.a.DatasetList(e);this.items=[],this.length=0,"string"==typeof e&&""!=e&&this.parseString(e),"object"==typeof e&&e instanceof Array&&e.length&&this.parseArray(e)},r.a.extend(r.a.DatasetList.prototype,{add:function(e){this.length=this.length+1,this.items.push(e)},item:function(e,n,t){if(e<this.length&&e>=0){var a=this.items[e];return void 0!==a&&a instanceof r.a.DataMap&&arguments.length>1&&"string"==typeof arguments[1]&&""!=arguments[1]?a.get(n,t):a}},each:function(e,n){for(var t=[].concat(this.items),a=0,r=t.length;a<r&&!1!==e.call(n||t[a],t[a],a,r);a+=1);},remove:function(e){return this.removeAt(this.indexOf(e))},removeAt:function(e){e<this.length&&e>=0&&(this.length=this.length-1,this.items.splice(e,1))},indexOf:function(e){if(this.items.indexOf)return this.items.indexOf(e);for(var n=0,t=this.items.length;n<t;n+=1)if(this.items[n]==e)return n;return-1},getCount:function(){return this.length},parseString:function(e){return e=r.a.parseJsonString(e),window.JSON&&window.JSON.parse?this.parseArray(window.JSON.parse(e)):new Function("this.parseArray("+e+")").apply(this)},parseArray:function(e){for(var n=0;n<e.length;n++)e[n]&&r.a.isArray(e[n])?this.add(new r.a.DatasetList(e[n])):e[n]&&r.a.isObject(e[n])?this.add(new r.a.DataMap(e[n])):null!=e[n]&&null!=e[n]&&this.add(e[n])},clear:function(){this.items=[],this.length=0}}),r.a.DatasetList.prototype.toString=function(){for(var e=[],n="",t=0;t<this.items.length;t++)n="",void 0===this.items[t]||null==this.items[t]?n+='""':n="string"==typeof this.items[t]?'"'+r.a.parseJsonValue(this.items[t])+'"':this.items[t].toString(),e.push(n);return"["+e.join(",")+"]"},r.a.DatasetList.prototype.get=r.a.DatasetList.prototype.item,r.a.DatasetList.prototype.put=r.a.DatasetList.prototype.add;var u=/%20/g,f=/^(\w+:)?\/\/([^\/?#]+)/;r.a.extend(r.a,{active:0,param:function(e){var n=[];if(!e||!r.a.isObject(e))return"";for(var t in e)a(t,e[t]);return n.join("&").replace(u,"+");function a(e,n){r.a.isArray(n)?r.a.each(n,(function(n,t){/\[\]$/.test(e)?o(e,t):a(e+"["+("object"==typeof t||r.a.isArray(t)?n:"")+"]",t)})):null!=n&&"object"==typeof n?r.a.each(n,(function(n,t){a(e+"["+n+"]",t)})):o(e,n)}function o(e,t){t=r.a.isFunction(t)?t():t,n[n.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)}},get:function(e,n,t,a){return r.a.isFunction(n)&&(a=a||t,t=n,n=null),r.a.ajaxRequest({type:"GET",url:e,data:n,success:t,dataType:a})},getScript:function(e,n){return r.a.get(e,null,n,"script")},getJSON:function(e,n,t){return r.a.get(e,n,t,"json")},post:function(e,n,t,a){return r.a.isFunction(n)&&(a=a||t,t=n,n={}),r.a.ajaxRequest({type:"POST",url:e,data:n,success:t,dataType:a})},ajaxSetup:function(e){r.a.extend(r.a.ajaxSettings,e)},ajaxSettings:{url:location.href,global:!0,type:"GET",contentType:"application/x-www-form-urlencoded",processData:!0,async:!0,xhr:!window.XMLHttpRequest||"file:"===window.location.protocol&&window.ActiveXObject?function(){try{return new window.ActiveXObject("Microsoft.XMLHTTP")}catch(e){}}:function(){return new window.XMLHttpRequest},accepts:{xml:"application/xml, text/xml",html:"text/html",script:"text/javascript, application/javascript",json:"application/json, text/javascript",text:"text/plain",_default:"*/*"}},lastModified:{},etag:{},ajaxRequest:function(e){var n,t,a,o=r.a.extend(!0,{},r.a.ajaxSettings,e),i=e&&e.context||o,s=o.type.toUpperCase();if(o.data&&o.processData&&"string"!=typeof o.data&&(o.data=r.a.param(o.data,o.traditional)),"jsonp"===o.dataType&&("GET"===s?jsre.test(o.url)||(o.url+=(rquery.test(o.url)?"&":"?")+(o.jsonp||"callback")+"=?"):o.data&&jsre.test(o.data)||(o.data=(o.data?o.data+"&":"")+(o.jsonp||"callback")+"=?"),o.dataType="json"),"json"===o.dataType&&(o.data&&jsre.test(o.data)||jsre.test(o.url))&&(n=o.jsonpCallback||"jsonp"+jsc++,o.data&&(o.data=(o.data+"").replace(jsre,"="+n+"$1")),o.url=o.url.replace(jsre,"="+n+"$1"),o.dataType="script",window[n]=window[n]||function(e){a=e,w(),O(),window[n]=void 0;try{delete window[n]}catch(e){}d&&d.removeChild(h)}),"script"===o.dataType&&null===o.cache&&(o.cache=!1),!1===o.cache&&"GET"===s){var c=now(),l=o.url.replace(rts,"$1_="+c+"$2");o.url=l+(l===o.url?(rquery.test(o.url)?"&":"?")+"_="+c:"")}o.data&&"GET"===s&&(o.url+=(rquery.test(o.url)?"&":"?")+o.data);var u=f.exec(o.url),p=u&&(u[1]&&u[1]!==location.protocol||u[2]!==location.host);if("script"===o.dataType&&"GET"===s&&p){var d=document.getElementsByTagName("head")[0]||document.documentElement,h=document.createElement("script");if(h.src=o.url,o.scriptCharset&&(h.charset=o.scriptCharset),!n){var m=!1;h.onload=h.onreadystatechange=function(){m||this.readyState&&"loaded"!==this.readyState&&"complete"!==this.readyState||(m=!0,w(),O(),h.onload=h.onreadystatechange=null,d&&h.parentNode&&d.removeChild(h))}}d.insertBefore(h,d.firstChild)}else{var g=!1,b=o.xhr();if(b){o.username?b.open(s,o.url,o.async,o.username,o.password):b.open(s,o.url,o.async);try{(o.data||e&&e.contentType)&&b.setRequestHeader("Content-Type",o.contentType+(o.encoding?"; charset="+o.encoding:"")),o.ifModified&&(r.a.lastModified[o.url]&&b.setRequestHeader("If-Modified-Since",r.a.lastModified[o.url]),r.a.etag[o.url]&&b.setRequestHeader("If-None-Match",r.a.etag[o.url])),p||b.setRequestHeader("X-Requested-With","XMLHttpRequest"),b.setRequestHeader("Accept",o.dataType&&o.accepts[o.dataType]?o.accepts[o.dataType]+", */*":o.accepts._default)}catch(e){}if(o.beforeSend&&!1===o.beforeSend.call(i,b,o))return b.abort(),!1;var y=b.onreadystatechange=function(e){if(b&&0!==b.readyState&&"abort"!==e){if(!g&&b&&(4===b.readyState||"timeout"===e)){var i;if(g=!0,b.onreadystatechange=r.a.noop,"success"===(t="timeout"===e?"timeout":r.a.httpSuccess(b)?o.ifModified&&r.a.httpNotModified(b,o.url)?"notmodified":"success":"error"))try{a=r.a.httpData(b,o.dataType,o)}catch(e){t="parsererror",i=e}"success"===t||"notmodified"===t?n||w():r.a.handleError(o,b,t,i),O(),"timeout"===e&&b.abort(),o.async&&(b=null)}}else g||O(),g=!0,b&&(b.onreadystatechange=r.a.noop)};try{var v=b.abort;b.abort=function(){b&&v.call(b),y("abort")}}catch(e){}o.async&&o.timeout>0&&setTimeout((function(){b&&!g&&y("timeout")}),o.timeout);try{b.send("POST"===s||"PUT"===s||"DELETE"===s?o.data:null)}catch(e){r.a.handleError(o,b,null,e),O()}return o.async||y(),b}}function w(){o.success&&o.success.call(i,a,t,b)}function O(){o.complete&&o.complete.call(i,b,t)}},handleError:function(e,n,t,a){e.error&&e.error.call(e.context||e,n,t,a)},httpSuccess:function(e){try{return!e.status&&"file:"===location.protocol||e.status>=200&&e.status<300||304===e.status||1223===e.status||0===e.status}catch(e){}return!1},httpNotModified:function(e,n){var t=e.getResponseHeader("Last-Modified"),a=e.getResponseHeader("Etag");return t&&(r.a.lastModified[n]=t),a&&(r.a.etag[n]=a),304===e.status||0===e.status},httpData:function(e,n,t){var a=e.getResponseHeader("content-type")||"",o="xml"===n||!n&&a.indexOf("xml")>=0,i=o?e.responseXML:e.responseText;return o&&"parsererror"===i.documentElement.nodeName&&r.a.error("parsererror"),t&&t.dataFilter&&(i=t.dataFilter(i,n)),"string"==typeof i&&("json"===n||!n&&a.indexOf("json")>=0?i=r.a.parseJSON(i):("script"===n||!n&&a.indexOf("javascript")>=0)&&r.a.globalEval(i)),i}}),n.a=r.a},function(e,n,t){t(8)(t(9)),e.exports=window.Zepto},function(module,__webpack_exports__,__webpack_require__){"use strict";var _browser_toolkit__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(4),_expandMobile__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(6),_bizMobile__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__(7),_jcl__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__(1),_util__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__(0),deviceType=(sUserAgent=window.navigator.userAgent,arrMessages=/IpuMobile\/(.*)\/(.*)\/(.*)\/(.*)\/Hybrid/gi.exec(sUserAgent),arrMessages&&"i1"==arrMessages[1]?"android"==arrMessages[2]?"a":"ios"==arrMessages[2]?"i":"wp"==arrMessages[2]?"w":null:null),sUserAgent,arrMessages;window.TerminalType||(window.TerminalType=deviceType);var terminalType=window.TerminalType,WadeMobile={isIpuApp:function(){return window.navigator.userAgent.includes("IpuMobile")},isAndroid:function(){return"a"==terminalType},isIOS:function(){return"i"==terminalType},isWP:function(){return"w"==terminalType},isApp:function(){return!!terminalType},getSysInfo:function(e){return new Promise((n,t)=>{WadeMobile.callback.storageCallback("getSysInfo",e=>{n(Object(_util__WEBPACK_IMPORTED_MODULE_4__.a)(e))}),execute("getSysInfo",[e],e=>{t(Object(_util__WEBPACK_IMPORTED_MODULE_4__.a)(e))})})},close:function(e,n){"boolean"!=typeof e&&(e=!0),execute("close",[e],n)},httpRequest:function(e,n,t,a){return"i"==terminalType&&(e=encodeURIComponent(e)),new Promise((r,o)=>{WadeMobile.callback.storageCallback("httpRequest",e=>{r(Object(_util__WEBPACK_IMPORTED_MODULE_4__.a)(e))}),execute("httpRequest",[e,n,t,a],e=>{o(Object(_util__WEBPACK_IMPORTED_MODULE_4__.a)(e))})})},dataRequest:function(e,n,t,a,r){return new Promise((o,i)=>{WadeMobile.callback.storageCallback("dataRequest",e=>{o(Object(_util__WEBPACK_IMPORTED_MODULE_4__.a)(e))}),execute("dataRequest",[e,n,t,a,r],e=>{i(Object(_util__WEBPACK_IMPORTED_MODULE_4__.a)(e))})})},dataRequestWithHost:function(e,n,t,a,r,o){return new Promise((i,s)=>{WadeMobile.callback.storageCallback("dataRequestWithHost",e=>{i(Object(_util__WEBPACK_IMPORTED_MODULE_4__.a)(e))}),execute("dataRequestWithHost",[e,n,t,a,r,o],e=>{s(Object(_util__WEBPACK_IMPORTED_MODULE_4__.a)(e))})})},loadUrl:function(e,n){execute("loadUrl",[encodeURIComponent(e)],n)},openUrl:function(e,n,t,a,r,o){WadeMobile.callback.storageCallback("openUrl",n),execute("openUrl",[encodeURIComponent(e),t,a,r],o)},openCordova:function(e,n,t){WadeMobile.callback.storageCallback("openCordova",n),execute("openCordova",[encodeURIComponent(e)],t)},openPage:function(e,n,t){execute("openPage",[e,n],t)},openTemplate:function(e,n,t){execute("openTemplate",[e,n],t)},loadPage:function(e,n,t){execute("loadPage",[e,n],t)},loadTemplate:function(e,n,t){execute("loadTemplate",[e,n],t)},back:function(e,n){execute("back",[e],n)},backWithCallback:function(e,n,t){execute("backWithCallback",[e,n],t)},getPage:function(e,n){return new Promise((t,a)=>{WadeMobile.callback.storageCallback("getPage",e=>{t(Object(_util__WEBPACK_IMPORTED_MODULE_4__.a)(e))}),execute("getPage",[e,n],e=>{a(Object(_util__WEBPACK_IMPORTED_MODULE_4__.a)(e))})})},getTemplate:function(e,n){return new Promise((t,a)=>{WadeMobile.callback.storageCallback("getTemplate",e=>{t(Object(_util__WEBPACK_IMPORTED_MODULE_4__.a)(e))}),execute("getTemplate",[e,n],e=>{a(Object(_util__WEBPACK_IMPORTED_MODULE_4__.a)(e))})})},storageDataByThread:function(e,n,t,a){execute("storageDataByThread",[e,n,t],a)},openDialog:function(e,n,t,a){return new Promise((r,o)=>{WadeMobile.callback.storageCallback("openDialog",e=>{r(Object(_util__WEBPACK_IMPORTED_MODULE_4__.a)(e))}),execute("openDialog",[e,n,t,a],e=>{o(Object(_util__WEBPACK_IMPORTED_MODULE_4__.a)(e))})})},closeDialog:function(e,n,t){execute("closeDialog",[e,n],t)},openWindow:function(e,n){return new Promise((t,a)=>{WadeMobile.callback.storageCallback("openWindow",e=>{t(Object(_util__WEBPACK_IMPORTED_MODULE_4__.a)(e))}),execute("openWindow",[e,n],e=>{a(Object(_util__WEBPACK_IMPORTED_MODULE_4__.a)(e))})})},closeWindow:function(e,n,t){execute("closeWindow",[e,n],t)},openSlidingMenu:function(e,n,t,a,r,o){return new Promise((i,s)=>{WadeMobile.callback.storageCallback("openSlidingMenu",e=>{i(Object(_util__WEBPACK_IMPORTED_MODULE_4__.a)(e))}),execute("openSlidingMenu",[e,n,t,a,r,o],e=>{s(Object(_util__WEBPACK_IMPORTED_MODULE_4__.a)(e))})})},closeSlidingMenu:function(e,n,t){execute("closeSlidingMenu",[e,n],t)},showLoadingHUD:function(){execute("showLoadingHUD",[])},hideLoadingHUD:function(){execute("hideLoadingHUD",[])},setH5TopBar:function(e){if(document.title=e?function(e){return e?WadeMobile.isIpuApp()?Object.entries(e).map(e=>"copy"===e[0]?"cp="+e[1]:`${e[0]}=${e[1]}`).join("&"):e.title:""}(e):"",WadeMobile.isIOS()){let e=document.createElement("iframe");e.style.display="none";const n="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7";e.setAttribute("src",n);let t=function(){setTimeout((function(){e.removeEventListener("load",t),document.body.removeChild(e)}),0)};e.addEventListener("load",t),document.body.appendChild(e)}}};WadeMobile.customEvents={cancelEvent:function(){var e=_jcl__WEBPACK_IMPORTED_MODULE_3__.a.DataMap();e.put("title","取消成功！"),e.put("content","已经取消了！"),e.put("alertType",1),e.put("cancelable",!0),e.put("imageID",0),WadeMobile.sweetAlert(e)},confirmEvent:function(){var e=_jcl__WEBPACK_IMPORTED_MODULE_3__.a.DataMap();e.put("title","确认成功！"),e.put("content","成功完成了任务！"),e.put("alertType",2),e.put("cancelable",!0),e.put("imageID",0),WadeMobile.sweetAlert(e)},nextEvent:function(){var e=_jcl__WEBPACK_IMPORTED_MODULE_3__.a.DataMap();e.put("title","加载成功！"),e.put("content",""),e.put("alertType",2),e.put("cancelable",!0),e.put("imageID",0),WadeMobile.sweetAlert(e)}};var callbackId=0,callbacks={},callbackDefine={},globalErrorKey=null,isAlert=!0,execute=function(e,n,t,a){n=stringify(n),"a"==terminalType?androidExecute(e,n,t,a):"i"==terminalType?iosExecute(e,n,t,a):"w"==terminalType?winphoneExecute(e,n,t,a):isAlert?(isAlert=!1,alert(e+"无终端类型"),t&&t(e+"无终端类型")):(console.log(e+"无终端类型"),t&&t(e+"无终端类型"))};WadeMobile.execute=execute,"undefined"==typeof PluginManager&&(window.PluginManager={exec:function(e,n,t){prompt("_$$_SafelyJsInterface:_"+JSON.stringify({inf:"PluginManager",func:"exec",args:[e,n,t]}))}});var androidExecute=function(e,n,t,a){var r=e+callbackId++;null!=window._WadeMobileSet_Key_&&(r+=window._WadeMobileSet_Key_);var o=globalErrorKey=r;(a||t)&&(callbacks[o]={success:a,error:t}),WadeMobile.debug&&console.log("action:"+e+" param:"+n),PluginManager.exec(e,o,n),globalErrorKey=null},iosExecute=function(e,n,t,a){var r=globalErrorKey=e+callbackId++;(a||t)&&(callbacks[r]={success:a,error:t}),WadeMobile.debug&&console.log("action:"+e+" param:"+n);var o,i="wade://"+e+"?param="+encodeURIComponent(n)+"&callback="+r,s="WADE_FRAME_"+callbackId%10;(o=document.getElementById(s))||((o=document.createElement("iframe")).setAttribute("id",s),o.setAttribute("width","0"),o.setAttribute("height","0"),o.setAttribute("border","0"),o.setAttribute("frameBorder","0"),o.setAttribute("name",s),document.body.appendChild(o));document.getElementById(s).contentWindow.location=encodeURIComponent(i),globalErrorKey=null},winphoneExecute=function(e,n,t,a){var r=globalErrorKey=e+callbackId++;(a||t)&&(callbacks[r]={success:a,error:t}),WadeMobile.debug&&console.log("action:"+e+" param:"+n),window.external.Notify(stringify([e,r,n])),globalErrorKey=null};function _eval(code,action){WadeMobile.debug&&alert(code);var func=eval(code);"function"==typeof func&&func()}function stringify(e){if("undefined"==typeof JSON){for(var n="[",t=0;t<e.length;t++){t>0&&(n+=",");var a=typeof e[t];if("number"==a||"boolean"==a)n+=e[t];else if(e[t]instanceof Array)n=n+"["+e[t]+"]";else if(e[t]instanceof Object){var r=!0;for(var o in n+="{",e[t])if(null!=e[t][o]){r||(n+=","),n=n+'"'+o+'":';var i=typeof e[t][o];"number"==i||"boolean"==i?n+=e[t][o]:"function"==typeof e[t][o]?n+='""':e[t][o]instanceof Object?n+=stringify(e[t][o]):n=n+'"'+e[t][o]+'"',r=!1}n+="}"}else{var s=e[t].replace(/\\/g,"\\\\");n=n+'"'+(s=s.replace(/"/g,'\\"'))+'"'}}return n+="]"}return JSON.stringify(e)}if(WadeMobile.callback={success:function(e,n){void 0!==n&&callbacks[e]&&(callbacks[e].success&&("function"==typeof callbacks[e].success?(0,callbacks[e].success)(n):_eval(callbacks[e].success+"('"+n+"','"+e+"')")),callbacks[e]&&delete callbacks[e])},error:function(e,n,t){void 0!==n&&(t&&(n=decodeURIComponent(n)),callbacks[e]?(callbacks[e].error&&("function"==typeof callbacks[e].error?(0,callbacks[e].error)(n):_eval(callbacks[e].error+"('"+n+"','"+e+"')")),callbacks[e]&&delete callbacks[e]):alert(n))},storageCallback:function(e,n){n&&(callbackDefine[e+callbackId]={callback:n})},execCallback:function(e,n,t){globalErrorKey=e;var a=callbackDefine[e];a&&((n="null"==n?null:n)&&t&&(n=decodeURIComponent(n)),a.callback&&("function"==typeof a.callback?(0,a.callback)(n):_eval(a.callback+"('"+n+"','"+e+"')")),a&&delete callbackDefine[e]),globalErrorKey=null}},WadeMobile.setKeyListener=function(e,n,t){"back"==e||"menu"==e||"home"==e?(document.addEventListener(e,n,!1),null!=t&&execute("setKeyDownFlag",[e,t])):"slipToRight"==e&&(document.addEventListener(e,n,!1),null!=t&&execute("coverIOSCurrentTemplateSlipToRight",[t]))},WadeMobile.cleanKeyDownFlag=function(e){null!=e&&execute("cleanKeyDownFlag",[e])},WadeMobile.event=function(){if(WadeMobile.isApp()){var e=document.createEvent("Event"),n=document.createEvent("Event"),t=document.createEvent("Event"),a=document.createEvent("Event");return{back:function(){e.initEvent("back",!0,!0),document.dispatchEvent(e)},menu:function(){n.initEvent("menu",!0,!0),document.dispatchEvent(n)},home:function(){t.initEvent("home",!0,!0),document.dispatchEvent(t)},slipToRight:function(){a.initEvent("slipToRight",!0,!0),document.dispatchEvent(a)}}}}(),WadeMobile.backevent=function(){if(WadeMobile.isApp())return{backCall:function(e){var n=document.createEvent("Event");n.initEvent("backCall",!0,!0),n.data=e,document.dispatchEvent(n)}}}(),WadeMobile.setBackCallListener=function(e){document.addEventListener("backCall",e)},window.onerror=function(e,n,t){var a=[];e&&a.push("错误信息:"+e),t&&a.push("错误行号:"+t),globalErrorKey&&a.push("错误关键字:"+globalErrorKey),n&&a.push("错误文件:"+n);var r=a.join("\t\n");console.log(r),window.$beePlugin_errTip&&alert("应用君走丢了，请重试")},Object.assign(WadeMobile,_expandMobile__WEBPACK_IMPORTED_MODULE_1__.a,_bizMobile__WEBPACK_IMPORTED_MODULE_2__.a),!WadeMobile.isApp()){function copySession(e){for(var n in e)window.sessionStorage[n]=e[n]}WadeMobile.initAppConfig=function(e){e=JSON.stringify(e),_browser_toolkit__WEBPACK_IMPORTED_MODULE_0__.a.browser.setMemoryCache("appConfig",e)},window.addEventListener("message",(function(e){var n=e.data;n&&"copySession"==n.type&&(copySession(n.value),top.window.postMessage({type:"redirect",value:""},"*"))}),!1),WadeMobile.openIpuApp=function(e,n,t){(e=e?_jcl__WEBPACK_IMPORTED_MODULE_3__.a.DataMap(e):_jcl__WEBPACK_IMPORTED_MODULE_3__.a.DataMap()).put("MENU_TYPE","I");var a=e.get("APP_ID"),r=e.get("MENU_PAGE_ACTION");if(window.APP_CONFIG&&window.APP_CONFIG[a])return redirectTo(window.APP_CONFIG[a]+"/mobile?action="+r,e.get("EXT_PARAM"),_browser_toolkit__WEBPACK_IMPORTED_MODULE_0__.a);_browser_toolkit__WEBPACK_IMPORTED_MODULE_0__.a.browser.getMemoryCache((function(n){var t=null;if(n&&n.length>0){n=JSON.parse(n);for(var o=0;o<n.length;o++)if(n[o].APP_ID==a){t=n[o];break}}null!=t?redirectTo(t.APP_REQUEST_HOST+t.APP_REQUEST_PATH+"/mobile?action="+r,e.get("EXT_PARAM"),_browser_toolkit__WEBPACK_IMPORTED_MODULE_0__.a):alert("未找到该appId对应的应用信息："+a)}),"appConfig")};var callback=null;function redirectTo(e,n,t){var a={isContext:!0};n&&(a.data=n);var r=e.match("http.*?://(.*?)/")[1];if(window.location.host==r)t.redirect.postPage(e,a);else{var o=document.createElement("iframe");o.name="_subApp",o.id="_subApp",o.style.display="none",o.onload=function(){o.contentWindow.postMessage({type:"copySession",value:copyFromSession()},e),callback&&window.removeEventListener("message",callback),callback=function(n){var r=n.data;r&&"redirect"==r.type&&t.redirect.postPage(e,a)},window.addEventListener("message",callback,!1),setTimeout((function(){console.log("长时间无响应，直接跳转"),t.redirect.postPage(e,a)}),8e3)},a.target="_subApp",document.body.appendChild(o),t.redirect.postPage(e,a)}}function copyFromSession(){var e={},n=window.sessionStorage||{};for(var t in n)n.hasOwnProperty(t)&&"function"!=typeof t&&(e[t]=n[t]);return e}WadeMobile.openH5=function(e){var n={target:"_subApp"},t=document.createElement("iframe");t.name="_subApp",t.id="_subApp",t.style.display="none",t.onload=function(){t.contentWindow.postMessage({type:"copySession",value:copyFromSession()},e),callback&&window.removeEventListener("message",callback),callback=function(n){var t=n.data;t&&"redirect"==t.type&&(location.href=e)},window.addEventListener("message",callback,!1),setTimeout((function(){console.log("长时间无响应，直接跳转"),_browser_toolkit__WEBPACK_IMPORTED_MODULE_0__.a.redirect.getPage(e,n)}),8e3)},document.body.appendChild(t),_browser_toolkit__WEBPACK_IMPORTED_MODULE_0__.a.redirect.getPage(e,n)}}!function(){if(-1!=window.navigator.userAgent.indexOf("IpuMobile/")){if(top!=window){for(var e in null==top.WadeMobileSet&&(top.WadeMobileSet={}),top.WadeMobileSet)try{-1!=e.indexOf("_WadeMobileSet_Key_")&&(!top.WadeMobileSet[e]||top.WadeMobileSet[e].canRemoved&&top.WadeMobileSet[e].canRemoved())&&(console.log("(top set)delete:"+e),delete top.WadeMobileSet[e],console.log("(top set)delete success :"+e))}catch(n){console.log("a error(WadeMobile) : "+n),console.log("(top set)delete:"+e),delete top.WadeMobileSet[e],console.log("(top set)delete success :"+e)}e="_WadeMobileSet_Key_"+(new Date).getTime();window._WadeMobileSet_Key_=e,console.log("in an iframe, window.WadeMobile object is referenced top.WadeMobileSet."+e),top.WadeMobileSet[e]=window.WadeMobile,window.WadeMobile.canRemoved=function(){return!window}}}else console.log('<WadeMobileSet> "IpuMobile/" string does not exist in the userAgent. return.')}(),window.WadeMobile=WadeMobile,__webpack_exports__.a=WadeMobile},function(e,n,t){"use strict";var a=t(1),r=t(5),o={};o.browser=function(){function e(){alert("your browser not support sessionStorage!")}function n(){alert("your browser not support localStorage!")}return{loadingStart:function(e,n){},loadingStop:function(){},confirm:function(e){window.confirm(e)},tip:function(e,n){alert(e)},alert:function(e,n,t){alert(e),t&&t()},setMemoryCache:function(n,t){if(window.sessionStorage)if(r.a.tool.isDataMap(n))for(var a in n.map)window.sessionStorage.setItem(a,n.map[a]);else{if(!t)return;window.sessionStorage.setItem(n,t)}else e()},getMemoryCache:function(n,t,o){if(window.sessionStorage){if(r.a.tool.isArray(t)){for(var i=new a.a.DataMap,s=0,c=t.length;s<c;s++)i.put(t[s],window.sessionStorage.getItem(t[s]));return void n(JSON.parse(i.toString()))}n(window.sessionStorage.getItem(t))}else e()},removeMemoryCache:function(n){if(window.sessionStorage)if(r.a.tool.isArray(n))for(var t=0,a=n.length;t<a;t++)window.sessionStorage.removeItem(n[t]);else window.sessionStorage.removeItem(n);else e()},clearMemoryCache:function(){window.sessionStorage?window.sessionStorage.clear():e()},downloadImg:function(e,n,t){e(n[0]+"?"+t)},getSysInfo:function(e,n,t){e(n)},openUrlWithPlug:function(e,n,t){window.location.href=e},setOfflineCache:function(e,t){if(window.localStorage)if(r.a.tool.isDataMap(e))for(var a in e.map)window.localStorage.setItem(a,e.map[a]);else{if(!t)return;window.localStorage.setItem(e,t)}else n()},getOfflineCache:function(e,t,o){if(window.localStorage)if(r.a.tool.isArray(t)){for(var i=new a.a.DataMap,s=0,c=t.length;s<c;s++)i.put(t[s],window.localStorage.getItem(t[s]));e(JSON.parse(i.toString()))}else e(window.localStorage.getItem(t));else n()},removeOfflineCache:function(e){if(window.localStorage)if(r.a.tool.isArray(e))for(var t=0,a=e.length;t<a;t++)window.localStorage.removeItem(e[t]);else window.localStorage.removeItem(e);else n()},clearOfflineCache:function(){window.localStorage?window.localStorage.clear():n()}}}(),o.ServerPath="mobile",o.redirect={buildUrl:function(e,n,t,a){var r=o.ServerPath;return r+="?action="+e,n&&(a?(n=encodeURI(encodeURI(n.toString())),r+="&isEncode=true"):r+="&isEncode=false",r+="&data="+n),t&&(r+="&isContext="+t),r},toUrl:function(e){e&&(document.location.href=e)},toPage:function(e,n){var t=this.buildUrl(e,n,!0);document.location.href=t},postPage:function(e,n){var t=document.createElement("form");for(var a in t.action=e,t.method="post",t.style.display="none",n&&n.target&&(t.target=n.target,delete n.target),n){var r=document.createElement("input");r.type="hidden",r.name=a,r.value=n[a],t.appendChild(r)}document.body.appendChild(t),t.submit(),t.parentNode.removeChild(t)},getPage:function(e,n){var t=document.createElement("form");t.action=e,t.method="get",t.style.display="none",n&&n.target&&(t.target=n.target,delete n.target),document.body.appendChild(t),t.submit(),t.parentNode.removeChild(t)},openPostWindow:function(e,n,t){var a=document.createElement("form");for(var r in a.action=n,a.method="post",a.style.display="none",a.target=e,t){var o=document.createElement("input");o.type="hidden",o.name=r,o.value=t[r],a.appendChild(o)}document.body.appendChild(a),a.submit(),a.parentNode.removeChild(a)}},o.ajax=function(){function e(e,n){var t=[];for(var a in e){var r=e[a],o=0==n?a:encodeURIComponent(a),i=typeof r;if("undefined"==i||null==r||""==r)t.push(o,"=&");else if("function"!=i&&"object"!=i)t.push(o,"=",0==n?r:encodeURIComponent(r),"&");else if($.isArray(r))if(r.length)for(var s=0,c=r.length;s<c;s++)t.push(o,"=",0==n?void 0===r[s]?"":r[s]:encodeURIComponent(void 0===r[s]?"":r[s]),"&");else t.push(o,"=&")}return t.pop(),t.join("")}return{ajaxSettings:{simple:!1,type:"GET",async:!0,cache:!1,dataType:"text",loading:!0,timeout:18e4,encoding:"UTF-8"},setup:function(e){e&&a.a.isPlainObject(e)&&a.a.extend(this.ajaxSettings,e)},request:function(n,t,r,i,s,c,l){var u=function(e,n,t){e+="?action="+n,t&&(e+="&isContext="+t);var a=e.split("?")[0],r=window.location.href.split("?")[0];return r.indexOf("/mobile/")>0&&"mobiledata"==a?r.replace("/mobile/","/")+e:e}(n,t,l),f=new a.a.DataMap;r&&f.put("data",r.toString());var p=a.a.extend({url:u,data:e(f.map,!0),success:i,error:function(e,n,t){if(p.simple)return void(s&&a.a.isFunction(s)&&s(n,t.getMessage()));throw t}},o.ajax.ajaxSettings,c);"text"!=p.dataType&&(p.simple=!0),function(e){var n=a.a.extend(!0,{},o.ajax.ajaxSettings,e);a.a.ajaxRequest(n)}(p)},get:function(e,n,t,a,r){o.ajax.request("mobiledata",e,n,t,a,r)},post:function(e,n,t,r,i){o.ajax.request("mobiledata",e,n,t,r,a.a.extend({type:"POST",encoding:"UTF-8"},i))},html:function(e,n,t,r,i,s){o.ajax.request("mobile",e,n,t,r,a.a.extend({type:"POST",encoding:"UTF-8"},i),s)}}}(),n.a=o},function(e,n,t){"use strict";var a=t(1),r={};r.tool=new function(){this.isDataMap=function(e){return"object"==typeof e&&e instanceof a.a.DataMap},this.isArray=function(e){return"object"==typeof e&&e.constructor==Array},this.parseList=function(e){if(!(e instanceof a.a.DatasetList))return e;for(var n=0,t=e.items.length;n<t;n++)e.items[n]instanceof a.a.DataMap?e.items[n]=r.tool.parseData(e.items[n]):e.items[n]instanceof a.a.DatasetList&&(e.items[n]=r.tool.parseList(e.items[n]));return e.items},this.parseData=function(e){if(!(e instanceof a.a.DataMap))return e;for(var n in e.map)e.map[n]instanceof a.a.DataMap?e.map[n]=r.tool.parseData(e.map[n]):e.map[n]instanceof a.a.DatasetList&&(e.map[n]=r.tool.parseList(e.map[n]));return e.map},this.StrStartWith=function(e,n){return new RegExp("^"+n).test(e)},this.StrEndWith=function(e,n){return new RegExp(n+"$").test(e)},this.isDataMapString=function(e){if("string"!=typeof e)return!1;var n=e.trim();return r.tool.StrStartWith(n,"{")&&r.tool.StrEndWith(n,"}")},this.getDataMap=function(e){if(r.tool.isDataMap(e))return e;if(r.tool.isDataMapString(e))return a.a.DataMap(e);throw"返回数据异常，请尝试重新登陆或联系管理员。(返回的数据为："+e+")"}},n.a=r},function(e,n,t){"use strict";var a=t(3),r=t(1),o=t(0),i={loadingStart:function(e,n,t,a){s("loadingStart",[e,n,t],a)},loadingStop:function(e){s("loadingStop",[],e)},getChoice:function(e,n,t,a){return new Promise((r,i)=>{c("getChoice",e=>{r(Object(o.a)(e))}),s("getChoice",[e,n,t,a],e=>{i(Object(o.a)(e))})})},tip:function(e,n,t){null==n&&(n=0),s("tip",[e,n],t)},alert:function(e,n,t){null==n&&(n=0),s("alert",[e,n],t)},getDate:function(e,n){return null==n&&(n="yyyy-MM-dd"),new Promise((t,a)=>{c("getDate",e=>{t(Object(o.a)(e))}),s("getDate",[e,n],e=>{a(Object(o.a)(e))})})},getContactsView:function(e,n){return e=null==e?new r.a.DataMap:new r.a.DataMap(e),n=null==n?new r.a.DataMap:new r.a.DataMap(n),new Promise((t,a)=>{c("getContactsView",e=>{t(Object(o.a)(e))}),s("getContactsView",[e.toString(),n.toString()],e=>{a(Object(o.a)(e))})})},getPhoto:function(e){return e="base64"===e?0:1,new Promise((n,t)=>{c("getPhoto",e=>{n(Object(o.a)(e))}),s("getPhoto",[e],e=>{t(Object(o.a)(e))})})},getPhoto2:function(e,n){return e="base64"===e?0:1,n=n||150,new Promise((t,a)=>{c("getPhoto2",e=>{t(Object(o.a)(e))}),s("getPhoto2",[e,n],e=>{a(Object(o.a)(e))})})},getIdentifyPhoto:function(e,n,t){return e="idCardFront"===e?1:"idCardBack"===e?2:"noBorder"===e?4:0,null==t&&(t=0),n="base64"===n?1:0,new Promise((r,i)=>{c("getIdentifyPhoto",e=>{n&&a.a.isIOS()&&(e="data:image/png;base64,"+e),r(Object(o.a)(e))}),s("getIdentifyPhoto",[e,n,t],e=>{i(Object(o.a)(e))})})},getPicture:function(e){return e="base64"===e?0:1,new Promise((n,t)=>{c("getPicture",(function(e){n(Object(o.a)(e))})),s("getPicture",[e],(function(e){t(Object(o.a)(e))}))})},getPicture2:function(e,n){return e="base64"===e?0:1,n=n||150,new Promise((t,a)=>{c("getPicture2",(function(e){t(Object(o.a)(e))})),s("getPicture2",[e,n],(function(e){a(Object(o.a)(e))}))})},getClientResourceVersion:function(){return new Promise((e,n)=>{c("getClientResourceVersion",n=>{e(Object(o.a)(n))}),s("getClientResourceVersion",[])})},getVideoPath:function(){return new Promise((e,n)=>{c("getVideoPath",n=>{e(Object(o.a)(n))}),s("getVideoPath",[])})},transImageToBase64:function(e){return new Promise((n,t)=>{c("transImageToBase64",e=>{n(Object(o.a)(e))}),s("transImageToBase64",[e],e=>{t(Object(o.a)(e))})})},compressImage:function(e,n,t){return null==n&&(n=10),null==t&&(t=30),new Promise((a,r)=>{c("compressImage",e=>{a(Object(o.a)(e))}),s("compressImage",[e,n,t],e=>{r(Object(o.a)(e))})})},beep:function(e,n){s("beep",[e],n)},shock:function(e,n){s("shock",[e],n)},call:function(e,n,t){null==n&&(n=!1),s("call",[e,n],t)},sms:function(e,n,t,a){null==t&&(t=!1),s("sms",[e,n,t],a)},openApp:function(e,n,t,a){s("openApp",[e,n,t],a)},showKeyBoard:function(e,n){s("showKeyBoard",[e],n)},hideKeyBoard:function(e){s("hideKeyBoard",[],e)},setTitleView:function(e,n){s("setTitleText",[e],n)},getSysInfo:function(e){return new Promise((n,t)=>{c("getSysInfo",e=>{n(Object(o.a)(e))}),s("getSysInfo",[e],e=>{t(Object(o.a)(e))})})},getNetInfo:function(e){return new Promise((n,t)=>{c("getNetInfo",e=>{n(Object(o.a)(e))}),s("getNetInfo",[e],e=>{t(Object(o.a)(e))})})},explorer:function(e,n){return new Promise((t,a)=>{c("explorer",e=>{t(Object(o.a)(e))}),s("explorer",[e,n])})},httpDownloadFile:function(e,n){return new Promise((t,a)=>{c("httpDownloadFile",e=>{t(Object(o.a)(e))}),s("httpDownloadFile",[e,n],e=>{a(Object(o.a)(e))})})},location:function(){return new Promise((e,n)=>{c("location",n=>{e(Object(o.a)(n))}),s("location",[],e=>{n(Object(o.a)(e))})})},markMap:function(e,n,t){e=new r.a.DataMap(e);var a=new r.a.DatasetList;return a.add(e),e=a,new Promise((a,r)=>{c("markMap",e=>{a(Object(o.a)(e))}),s("markMap",[e.toString(),n,t],e=>{r(Object(o.a)(e))})})},closeUrl:function(e){s("closeUrl",[e])},selectLocation:function(e,n,t,a){return new Promise((r,i)=>{c("selectLocation",e=>{r(Object(o.a)(e))}),s("selectLocation",[e,n,t,a])})},scanQrCode:function(){return new Promise((e,n)=>{c("scanQrCode",n=>{e(Object(o.a)(n))}),s("scanQrCode",[])})},createQrCode:function(e){return new Promise((n,t)=>{c("createQrCode",e=>{n(Object(o.a)(e))}),s("createQrCode",[e])})},downloadImg:function(e){return new Promise((n,t)=>{c("setImageWithURL",e=>{n(Object(o.a)(e))}),s("setImageWithURL",[e[0],e[1]])})},openUrlWithPlug:function(e){return new Promise((n,t)=>{c("openUrlWithPlug",e=>{n(Object(o.a)(e))}),s("openUrlWithPlug",[e],e=>{t(Object(o.a)(e))})})},httpGet:function(e,n){return new Promise((t,a)=>{c("httpGet",e=>{t(Object(o.a)(e))}),s("httpGet",[e,n])})},removeMemoryCache:function(e,n){s("removeMemoryCache",[e],n)},clearMemoryCache:function(e){s("clearMemoryCache",[],e)},setMemoryCache:function(e,n,t){s("setMemoryCache",[e,n],t)},getMemoryCache:function(e,n){return new Promise((t,a)=>{c("getMemoryCache",n=>{Array.isArray(e)?t(JSON.parse(n)):"string"==typeof e?t(n):a("key类型异常")}),s("getMemoryCache",[e,n],e=>{a(Object(o.a)(e))})})},setOfflineCache:function(e,n,t){s("setOfflineCache",[e,n,!1],t)},getOfflineCache:function(e,n){return new Promise((t,a)=>(c("getOfflineCache",n=>{Array.isArray(e)?t(JSON.parse(n)):"string"==typeof e?t(n):a("key类型异常")}),s("getOfflineCache",[e,n],e=>{a(Object(o.a)(e))})))},removeOfflineCache:function(e,n){s("removeOfflineCache",[e],n)},clearOfflineCache:function(e){s("clearOfflineCache",[],e)},writeFile:function(e,n,t,a,r){s("writeFile",[e,n,t,a],r)},appendFile:function(e,n,t,a,r){s("appendFile",[e,n,t,a],r)},readFile:function(e,n,t,a){return new Promise((r,i)=>{c("readFile",e=>{r(Object(o.a)(e))}),s("readFile",[e,n,t,a],e=>{i(Object(o.a)(e))})})},openFile:function(e,n,t,a){s("openFile",[e,n,t],a)},deleteFile:function(e,n,t,a){s("deleteFile",[e,n,t],a)},getAllFile:function(e,n,t){return new Promise((a,r)=>{c("getAllFile",e=>{a(Object(o.a)(e))}),s("getAllFile",[e,n,t],e=>{r(Object(o.a)(e))})})},getRelativePath:function(e,n){return new Promise((t,a)=>{c("getRelativePath",e=>{t(Object(o.a)(e))}),s("getRelativePath",[e,n],e=>{a(Object(o.a)(e))})})},cleanResource:function(e,n,t){s("cleanResource",[e,n],t)},shareByBluetooth:function(e){s("shareByBluetooth",[],e)},openBrowser:function(e,n){s("openBrowser",[e],n)},setSmsListener:function(e){return new Promise((n,t)=>{c("setSmsListener",e=>{n(Object(o.a)(e))}),s("setSmsListener",[e],e=>{t(Object(o.a)(e))})})},audioRecord:function(e,n){return null==e&&(e=!1),new Promise((t,a)=>{c("audioRecord",e=>{t(Object(o.a)(e))}),s("audioRecord",[e,n],e=>{a(Object(o.a)(e))})})},audioPlay:function(e,n,t){null==n&&(n=!0),s("audioPlay",[e,n],t)},audioRecordEnd:function(){return new Promise((e,n)=>{c("audioRecordEnd",n=>{e(Object(o.a)(n))}),s("audioRecordEnd",[],e=>{n(Object(o.a)(e))})})},audioStop:function(){return new Promise((e,n)=>{c("audioStop",n=>{e(Object(o.a)(n))}),s("audioStop",[],e=>{n(Object(o.a)(e))})})},logCat:function(e,n,t){s("logCat",[e,n],t)},execSQL:function(e,n,t,a,o){return t=null==t?new r.a.DataMap:new r.a.DataMap(t),null==a?a="":isNaN(a)||(a='"'+a+'"'),null==o?o="":isNaN(o)||(o='"'+o+'"'),new Promise((r,i)=>{c("execSQL",e=>{r(e)}),s("execSQL",[e,n,t.toString(),a,o],e=>{i(e)})})},insert:function(e,n,t){return t=null==t?new r.a.DataMap:new r.a.DataMap(t),new Promise((a,r)=>{c("insert",e=>{a(e)}),s("insert",[e,n,t.toString()],e=>{r(e)})})},delete:function(e,n,t,a){return a=null==a?new r.a.DataMap:new r.a.DataMap(a),new Promise((r,o)=>{c("delete",e=>{r(e)}),s("delete",[e,n,t,a.toString()],e=>{o(e)})})},update:function(e,n,t,a,o){return t=null==t?new r.a.DataMap:new r.a.DataMap(t),o=null==o?new r.a.DataMap:new r.a.DataMap(o),new Promise((r,i)=>{c("update",e=>{r(e)}),s("update",[e,n,t.toString(),a,o.toString()],e=>{i(e)})})},select:function(e,n,t,a,o,i,l){return null==t&&(t=[]),o=null==o?new r.a.DataMap:new r.a.DataMap(o),null==i?i="":isNaN(i)||(i='"'+i+'"'),null==l?l="":isNaN(l)||(l='"'+l+'"'),new Promise((r,u)=>{c("select",e=>{r(e)}),s("select",[e,n,t,a,o.toString(),i,l],e=>{u(e)})})},selectFirst:function(e,n,t,a,r){return this.select(e,n,t,a,r,1,0)},registerForPush:function(e){return new Promise((n,t)=>{c("registerForPush",e=>{n(Object(o.a)(e))}),s("registerForPush",[e],e=>{t(Object(o.a)(e))})})},unregisterForPush:function(){return new Promise((e,n)=>{c("unregisterForPush",n=>{e(Object(o.a)(n))}),s("unregisterForPush",[])})},sendText:function(e,n){return new Promise((t,a)=>{c("sendText",e=>{t(Object(o.a)(e))}),s("sendText",[e,n],e=>{a(Object(o.a)(e))})})},setCallbackForPush:function(e){s("setCallbackForPush",[e])},registerForPushWithYunba:function(e){return new Promise((n,t)=>{c("registerForPushWithYunba",e=>{n(Object(o.a)(e))}),s("registerForPushWithYunba",[e],e=>{t(Object(o.a)(e))})})},unregisterForPushWithYunba:function(){return new Promise((e,n)=>{c("unregisterForPushWithYunba",n=>{e(Object(o.a)(n))}),s("unregisterForPushWithYunba",[])})},sendTextWithYunba:function(e,n){return new Promise((t,a)=>{c("sendTextWithYunba",e=>{t(Object(o.a)(e))}),s("sendTextWithYunba",[e,n],e=>{a(Object(o.a)(e))})})},setCallbackForPushWithYunba:function(e){s("setCallbackForPushWithYunba",[e])},aliPay:function(e,n,t,a){return new Promise((r,i)=>{c("aliPay",e=>{r(Object(o.a)(e))}),s("aliPay",[e,n,t,a],e=>{i(Object(o.a)(e))})})},uploadWithServlet:function(e,n,t){return t=r.a.DataMap(t).toString(),"string"==typeof e&&(e=[e]),new Promise((a,r)=>{c("uploadWithServlet",e=>{a(Object(o.a)(e))}),s("uploadWithServlet",[e,n,t],e=>{r(Object(o.a)(e))})})},downloadWithServlet:function(e,n,t){return t=r.a.DataMap(t).toString(),new Promise((a,r)=>{c("downloadWithServlet",e=>{a(Object(o.a)(e))}),s("downloadWithServlet",[e,n,t],e=>{r(Object(o.a)(e))})})},uploadFile:function(e,n){return new Promise((t,a)=>{c("uploadFile",e=>{t(Object(o.a)(e))}),s("uploadFile",[e,n],e=>{a(Object(o.a)(e))})})},downloadFile:function(e,n){return new Promise((t,a)=>{c("downloadFile",e=>{t(Object(o.a)(e))}),s("downloadFile",[e,n],e=>{a(Object(o.a)(e))})})},recordVideo:function(e,n){return new Promise((t,a)=>{c("recordVideo",e=>{t(Object(o.a)(e))}),s("recordVideo",[e,n],e=>{a(Object(o.a)(e))})})},playVideo:function(e){return new Promise((n,t)=>{c("playVideo",e=>{n(Object(o.a)(e))}),s("playVideo",[e],e=>{t(Object(o.a)(e))})})},getContacts:function(){return new Promise((e,n)=>{c("getContacts",n=>{e(Object(o.a)(n))}),s("getContacts",[],e=>{n(Object(o.a)(e))})})},openKeyboard:function(e,n,t){s("openKeyboard",[e,n],t)},captureScreen:function(e,n){s("captureScreen",[e],n)},getCaptrueScreenStatus:function(){return new Promise((e,n)=>{c("getCaptrueScreenStatus",n=>{e(Object(o.a)(n))}),s("getCaptrueScreenStatus",[])})},setScreenLock:function(e){return new Promise((n,t)=>{c("setScreenLock",e=>{n(Object(o.a)(e))}),s("setScreenLock",[e],e=>{t(Object(o.a)(e))})})},getScreenLockState:function(){return new Promise((e,n)=>{c("getScreenLockState",n=>{e(Object(o.a)(n))}),s("getScreenLockState",[],e=>{n(Object(o.a)(e))})})},screenUnlock:function(e){return new Promise((n,t)=>{c("screenUnlock",e=>{n(Object(o.a)(e))}),s("screenUnlock",[e],e=>{t(Object(o.a)(e))})})},initNfc:function(e,n,t){s("initNfc",[e,n],t)},scanSingle:function(){return new Promise((e,n)=>{c("scanSingle",n=>{e(Object(o.a)(n))}),s("scanSingle",[])})},scanMultiple:function(){return new Promise((e,n)=>{c("scanMultiple",n=>{e(Object(o.a)(n))}),s("scanMultiple",[])})},showNotification:function(e,n,t,a){s("showNotification",[e,n,t,a])},startListen:function(){return new Promise((e,n)=>{c("startListen",n=>{e(Object(o.a)(n))}),s("startListen",[],e=>{n(Object(o.a)(e))})})},voiceSpeak:function(e,n){s("voiceSpeak",[e],n)},shareTextQQFriend:function(e){s("shareTextQQFriend",[e])},shareTextWeChatFriend:function(e){s("shareTextWeChatFriend",[e])},shareFileQQFriend:function(e,n){s("shareFileQQFriend",[e,n])},shareFileWeChatFriend:function(e,n){s("shareFileWeChatFriend",[e,n])},shareTextMore:function(e){s("shareTextMore",[e])},shareFileMore:function(e,n){s("shareFileMore",[e,n])},shareImageBymail:function(e){s("shareImageBymail",[e])},baiduLocation:function(){return new Promise((e,n)=>{c("baiduLocation",n=>{e(Object(o.a)(n))}),s("baiduLocation",[],e=>{n(Object(o.a)(e))})})},baiduMapLocation:function(){return new Promise((e,n)=>{c("baiduMapLocation",n=>{e(Object(o.a)(n))}),s("baiduMapLocation",[],e=>{n(Object(o.a)(e))})})},baiduMapPosition:function(e,n){s("baiduMapPosition",[(e=new r.a.DataMap(e)).toString()],n)},addPolygon:function(e,n){let t=new r.a.DatasetList;e.forEach(e=>{t.add(new r.a.DataMap({Latitude:e[1],Longitude:e[0]}))}),s("addPolygon",[t.toString()],n)},clickBaiduMap:function(){return new Promise((e,n)=>{c("clickBaiduMap",n=>{e(n)}),s("clickBaiduMap",[],e=>{n(e)})})},videoCompressor:function(e){s("videoCompressor",[e])},sweetAlert:function(e){s("sweetAlert",[e])},sweetConfirm:function(e){s("sweetConfirm",[e])},sweetLoading:function(e){s("sweetLoading",[e])},poiCitySearch:function(e,n,t){s("poiCitySearch",[e,n],t)},poiNearbySearch:function(e,n,t,a){s("poiNearbySearch",[new r.a.DataMap({Latitude:e[1],Longitude:e[0]}).toString(),n,t],a)},poiBoundsSearch:function(e,n,t,a){let o=new r.a.DataMap({Latitude:e[1],Longitude:e[0]}),i=new r.a.DataMap({Latitude:n[1],Longitude:n[0]});s("poiBoundsSearch",[o.toString(),i.toString(),t],a)},lbsLocalSearch:function(e,n,t,a,r){s("lbsLocalSearch",[e,n,t,a],r)},lbsNearbySearch:function(e,n,t,a,r,o){s("lbsNearbySearch",[e,n,t,a,r],o)},lbsBoundsSearch:function(e,n,t,a,r){s("lbsBoundsSearch",[e,n,t,a],r)},testUnRegister:function(e){s("testUnRegister",[])},openPathMenu:function(e){s("openPathMenu",[e])},closeIpuApp:function(e){s("closeIpuApp",[e])},closePathMenu:function(){s("closePathMenu",[])},clearBackStack:function(){a.a.isAndroid()&&s("clearBackStack",[])},saveImageToAlbum:function(e){return new Promise((n,t)=>{c("saveImageToAlbum",e=>{n(Object(o.a)(e))}),s("saveImageToAlbum",[e])})},turnOnLocationService:function(e,n){s("turnOnLocationService",[e],n)},turnOffLocationService:function(e){s("turnOffLocationService",[],e)},getLocationServiceStatus:function(){return new Promise((e,n)=>{c("getLocationServiceStatus",n=>{e(Object(o.a)(n))}),s("getLocationServiceStatus",[],e=>{n(Object(o.a)(e))})})},msgoForceOpenGPS:function(){s("msgoForceOpenGPS",[])},getSimCardNum:function(e,n){return new Promise((t,a)=>{c("getSimCardNum",e=>{t(Object(o.a)(e))}),s("getSimCardNum",[e,n])})},getNum:function(e,n){return new Promise((t,a)=>{c("getNum",e=>{t(Object(o.a)(e))}),s("getNum",[e,n],e=>{a(Object(o.a)(e))})})},detectApps:function(e){return new Promise((n,t)=>{c("detectApps",e=>{n(Object(o.a)(e))}),s("detectApps",[e],e=>{t(Object(o.a)(e))})})},sysLocation:function(){return new Promise((e,n)=>{c("sysLocation",n=>{e(Object(o.a)(n))}),s("sysLocation",[],e=>{n(Object(o.a)(e))})})},getAppName:function(){return new Promise((e,n)=>{c("getAppName",n=>{e(Object(o.a)(n))}),s("getAppName",[],e=>{n(Object(o.a)(e))})})},doRequest:function(e,n,t){return new Promise((a,r)=>{c("doRequest",e=>{a(Object(o.a)(e))}),s("doRequest",[e,n,t],e=>{r(Object(o.a)(e))})})},initUDPWithPort:function(e){return new Promise((n,t)=>{c("initUDPWithPort",e=>{n(Object(o.a)(e))}),s("initUDPWithPort",[e],e=>{t(Object(o.a)(e))})})},sendUDPMessage:function(e,n,t){return new Promise((a,r)=>{c("sendUDPMessage",e=>{a(Object(o.a)(e))}),s("sendUDPMessage",[e,n,t],e=>{r(Object(o.a)(e))})})},printInit:function(){return new Promise((e,n)=>{c("printInit",n=>{e(Object(o.a)(n))}),s("printInit",[],e=>{n(Object(o.a)(e))})})},openPrinter:function(e){return new Promise((n,t)=>{c("openPrinter",e=>{n(Object(o.a)(e))}),s("openPrinter",[e],e=>{t(Object(o.a)(e))})})},getPrinterInfo:function(){return new Promise((e,n)=>{c("getPrinterInfo",n=>{e(Object(o.a)(n))}),s("getPrinterInfo",[],e=>{n(Object(o.a)(e))})})},setPrintPageGapType:function(e){return new Promise((n,t)=>{c("setPrintPageGapType",e=>{n(Object(o.a)(e))}),s("setPrintPageGapType",[e],e=>{t(Object(o.a)(e))})})},setPrintDarkness:function(e){return new Promise((n,t)=>{c("setPrintDarkness",e=>{n(Object(o.a)(e))}),s("setPrintDarkness",[e],e=>{t(Object(o.a)(e))})})},closePrinter:function(){return new Promise((e,n)=>{c("closePrinter",n=>{e(Object(o.a)(n))}),s("closePrinter",[],e=>{n(Object(o.a)(e))})})},start:function(e,n,t){return new Promise((a,r)=>{c("start",e=>{a(Object(o.a)(e))}),s("start",[e,n,t],e=>{r(Object(o.a)(e))})})},end:function(){return new Promise((e,n)=>{c("end",n=>{e(Object(o.a)(n))}),s("end",[],e=>{n(Object(o.a)(e))})})},print:function(){return new Promise((e,n)=>{c("print",n=>{e(Object(o.a)(n))}),s("print",[],e=>{n(Object(o.a)(e))})})},drawText:function(e,n,t,a,r){return new Promise((i,l)=>{c("drawText",e=>{i(Object(o.a)(e))}),s("drawText",[e,n,t,a,r],e=>{l(Object(o.a)(e))})})},drawBarCode:function(e,n,t){return new Promise((a,r)=>{c("drawBarCode",e=>{a(Object(o.a)(e))}),s("drawBarCode",[e,n,t],e=>{r(Object(o.a)(e))})})},drawQRCode:function(e,n,t,a){return new Promise((r,i)=>{c("drawQRCode",e=>{r(Object(o.a)(e))}),s("drawQRCode",[e,n,t,a],e=>{i(Object(o.a)(e))})})},openPdfFileWithBase64:function(e,n){return new Promise((t,a)=>{c("openPdfFileWithBase64",e=>{t(Object(o.a)(e))}),s("openPdfFileWithBase64",[e,n],e=>{a(Object(o.a)(e))})})},getFileBase64:function(e,n){return new Promise((t,a)=>{c("getFileBase64",e=>{t(Object(o.a)(e))}),s("getFileBase64",[e,n],e=>{a(Object(o.a)(e))})})},ftthSpeedTester:function(e){return new Promise((n,t)=>{c("ftthSpeedTester",e=>{n(Object(o.a)(e))}),s("ftthSpeedTester",[e],e=>{t(Object(o.a)(e))})})},woSpeedTester:function(e){return new Promise((n,t)=>{c("woSpeedTester",e=>{n(Object(o.a)(e))}),s("woSpeedTester",[e],e=>{t(Object(o.a)(e))})})},callGetRecord:function(e){return new Promise((n,t)=>{c("callGetRecord",e=>{n(Object(o.a)(e))}),s("callGetRecord",[e],e=>{t(Object(o.a)(e))})})},getCallRecords:function(e){return new Promise((n,t)=>{c("getCallRecords",e=>{n(Object(o.a)(e))}),s("getCallRecords",[e],e=>{t(Object(o.a)(e))})})},callAndGetRecord:function(e){return new Promise((n,t)=>{c("callAndGetRecord",e=>{n(Object(o.a)(e))}),s("callAndGetRecord",[e],e=>{t(Object(o.a)(e))})})},getMoreCallRecord:function(e,n){return new Promise((t,a)=>{c("getMoreCallRecord",e=>{t(Object(o.a)(e))}),s("getMoreCallRecord",[e,n],e=>{a(Object(o.a)(e))})})},runToZw:function(e){return new Promise((n,t)=>{c("runToZw",e=>{n(Object(o.a)(e))}),s("runToZw",[e],e=>{t(Object(o.a)(e))})})},yyylabelPrint:function(e){return new Promise((n,t)=>{c("yyylabelPrint",e=>{n(Object(o.a)(e))}),s("yyylabelPrint",[e],e=>{t(Object(o.a)(e))})})}};function s(e,n,t,r){return a.a.execute(e,n,t,r)}function c(e,n){a.a.callback.storageCallback(e,n)}n.a=i},function(e,n,t){"use strict";var a=t(3),r=t(1),o=t(0),i={openIpuApp:function(e,n,t){(e=e?r.a.DataMap(e):r.a.DataMap()).put("MENU_TYPE","I"),c("openIpuApp",n),s("openIpuApp",[e.toString()],t)},closeIpuApp:function(e){s("closeIpuApp",[e])},openNativeApp:function(e,n){s("openNativeApp",[(e=e?r.a.DataMap(e):"").toString()],n)},openNative:function(e,n){s("openNative",[e],n)},openNativeTab:function(e,n){s("openNativeTab",[e],n)},openTopNativeTab:function(e,n){s("openTopNativeTab",e,n)},openRemoteURL:function(e,n){s("openRemoteURL",[e],n)},openPushInUrl:function(e,n,t,a,r){c("openPushInUrl",r),n=n||[],s("openPushInUrl",[e,...n],t,a)},closePushInUrl:function(e){s("closePushInUrl",[e])},openH5:function(e,n,t,a){c("openH5",t),s("openH5",[e,n],a)},closeH5:function(e){s("closeH5",[e])},closeAllSubApp:function(){s("closeAllSubApp",[])},initAppConfig:function(e,n){s("initAppConfig",[(e=e?r.a.DatasetList(e):"").toString()],n)},readIDCard:function(e){return new Promise((n,t)=>{c("readIDCard",e=>{n(Object(o.a)(e))}),s("readIDCard",e,e=>{t(Object(o.a)(e))})})},getAppVersion:function(){return new Promise((e,n)=>{c("getAppVersion",n=>{e(Object(o.a)(n))}),s("getAppVersion",[],e=>{n(Object(o.a)(e))})})},writeCard:function(e){return new Promise((n,t)=>{c("writeCard",e=>{n(Object(o.a)(e))}),s("writeCard",e,e=>{t(Object(o.a)(e))})})},readCard:function(e){return new Promise((n,t)=>{c("readCard",e=>{n(Object(o.a)(e))}),s("readCard",e,e=>{t(Object(o.a)(e))})})},deviceList:function(e){return e=e||[],new Promise((n,t)=>{c("deviceList",e=>{n(Object(o.a)(e))}),s("deviceList",e,e=>{t(Object(o.a)(e))})})},btDtList:function(e){return new Promise((n,t)=>{c("btDtList",e=>{n(Object(o.a)(e))}),s("btDtList",e,e=>{t(Object(o.a)(e))})})},updateGrayInfo:function(e){return e=e||[],new Promise((n,t)=>{c("updateGrayInfo",e=>{n(Object(o.a)(e))}),s("updateGrayInfo",e,e=>{t(Object(o.a)(e))})})},faceAuthentication:function(e){return e=e||[],new Promise((n,t)=>{c("faceAuthentication",e=>{n(Object(o.a)(e))}),s("faceAuthentication",e,e=>{t(Object(o.a)(e))})})},wxPay:function(e){return new Promise((n,t)=>{c("wxPay",e=>{n(Object(o.a)(e))}),s("wxPay",e,e=>{t(Object(o.a)(e))})})},selectMap:function(e){return new Promise((n,t)=>{c("selectMap",e=>{n(Object(o.a)(e))}),s("selectMap",e,e=>{t(Object(o.a)(e))})})},zfbPay:function(e){return new Promise((n,t)=>{c("zfbPay",e=>{n(Object(o.a)(e))}),s("zfbPay",e,e=>{t(Object(o.a)(e))})})},jShare:function(e){return new Promise((n,t)=>{c("jShare",e=>{n(Object(o.a)(e))}),s("jShare",e,e=>{t(Object(o.a)(e))})})},jPush:function(e){return new Promise((n,t)=>{c("jPush",e=>{n(Object(o.a)(e))}),s("jPush",e,e=>{t(Object(o.a)(e))})})},closeCountLog:function(e,n){s("closeCountLog",[e],n)},forceUpdateAPP:function(e){return new Promise((n,t)=>{c("forceUpdateAPP",e=>{n(Object(o.a)(e))}),s("forceUpdateAPP",e,e=>{t(Object(o.a)(e))})})},setMobileConfig:function(e){return e=e||[],new Promise((n,t)=>{c("setMobileConfig",e=>{n(Object(o.a)(e))}),s("setMobileConfig",e,e=>{t(Object(o.a)(e))})})},resetMobileConfig:function(){return new Promise((e,n)=>{c("resetMobileConfig",n=>{e(Object(o.a)(n))}),s("resetMobileConfig",[],e=>{n(Object(o.a)(e))})})},setMobileConfigNoRestart:function(e){return e=e||[],new Promise((n,t)=>{c("setMobileConfigNoRestart",e=>{n(Object(o.a)(e))}),s("setMobileConfigNoRestart",e,e=>{t(Object(o.a)(e))})})},resetMobileConfigNoRestart:function(){return new Promise((e,n)=>{c("resetMobileConfigNoRestart",n=>{e(Object(o.a)(n))}),s("resetMobileConfigNoRestart",[],e=>{n(Object(o.a)(e))})})},sysLocation:function(){return new Promise((e,n)=>{c("sysLocation",n=>{e(Object(o.a)(n))}),s("sysLocation",[],e=>{n(Object(o.a)(e))})})},clearData:function(){return new Promise((e,n)=>{c("clearData",n=>{e(Object(o.a)(n))}),s("clearData",[],e=>{n(Object(o.a)(e))})})},resetSimCard:function(e){return new Promise((n,t)=>{c("resetSimCard",e=>{n(Object(o.a)(e))}),s("resetSimCard",e,e=>{t(Object(o.a)(e))})})},getAuthorization:function(e){return new Promise((n,t)=>{c("getAuthorization",e=>{n(Object(o.a)(e))}),s("getAuthorization",[e],e=>{t(Object(o.a)(e))})})}};function s(e,n,t,r){return a.a.execute(e,n,t,r)}function c(e,n,t,r){a.a.callback.storageCallback(e,n,t,r)}n.a=i},function(e,n){e.exports=function(e){function n(e){"undefined"!=typeof console&&(console.error||console.log)("[Script Loader]",e)}try{"undefined"!=typeof execScript&&"undefined"!=typeof attachEvent&&"undefined"==typeof addEventListener?execScript(e):"undefined"!=typeof eval?eval.call(null,e):n("EvalError: No eval function available")}catch(e){n(e)}}},function(e,n){e.exports="/* Zepto v1.2.0 - zepto event ajax form ie - zeptojs.com/license */\n(function(global, factory) {\n  if (typeof define === 'function' && define.amd)\n    define(function() { return factory(global) })\n  else\n    factory(global)\n}(this, function(window) {\n  var Zepto = (function() {\n  var undefined, key, $, classList, emptyArray = [], concat = emptyArray.concat, filter = emptyArray.filter, slice = emptyArray.slice,\n    document = window.document,\n    elementDisplay = {}, classCache = {},\n    cssNumber = { 'column-count': 1, 'columns': 1, 'font-weight': 1, 'line-height': 1,'opacity': 1, 'z-index': 1, 'zoom': 1 },\n    fragmentRE = /^\\s*<(\\w+|!)[^>]*>/,\n    singleTagRE = /^<(\\w+)\\s*\\/?>(?:<\\/\\1>|)$/,\n    tagExpanderRE = /<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\\w:]+)[^>]*)\\/>/ig,\n    rootNodeRE = /^(?:body|html)$/i,\n    capitalRE = /([A-Z])/g,\n\n    // special attributes that should be get/set via method calls\n    methodAttributes = ['val', 'css', 'html', 'text', 'data', 'width', 'height', 'offset'],\n\n    adjacencyOperators = [ 'after', 'prepend', 'before', 'append' ],\n    table = document.createElement('table'),\n    tableRow = document.createElement('tr'),\n    containers = {\n      'tr': document.createElement('tbody'),\n      'tbody': table, 'thead': table, 'tfoot': table,\n      'td': tableRow, 'th': tableRow,\n      '*': document.createElement('div')\n    },\n    readyRE = /complete|loaded|interactive/,\n    simpleSelectorRE = /^[\\w-]*$/,\n    class2type = {},\n    toString = class2type.toString,\n    zepto = {},\n    camelize, uniq,\n    tempParent = document.createElement('div'),\n    propMap = {\n      'tabindex': 'tabIndex',\n      'readonly': 'readOnly',\n      'for': 'htmlFor',\n      'class': 'className',\n      'maxlength': 'maxLength',\n      'cellspacing': 'cellSpacing',\n      'cellpadding': 'cellPadding',\n      'rowspan': 'rowSpan',\n      'colspan': 'colSpan',\n      'usemap': 'useMap',\n      'frameborder': 'frameBorder',\n      'contenteditable': 'contentEditable'\n    },\n    isArray = Array.isArray ||\n      function(object){ return object instanceof Array }\n\n  zepto.matches = function(element, selector) {\n    if (!selector || !element || element.nodeType !== 1) return false\n    var matchesSelector = element.matches || element.webkitMatchesSelector ||\n                          element.mozMatchesSelector || element.oMatchesSelector ||\n                          element.matchesSelector\n    if (matchesSelector) return matchesSelector.call(element, selector)\n    // fall back to performing a selector:\n    var match, parent = element.parentNode, temp = !parent\n    if (temp) (parent = tempParent).appendChild(element)\n    match = ~zepto.qsa(parent, selector).indexOf(element)\n    temp && tempParent.removeChild(element)\n    return match\n  }\n\n  function type(obj) {\n    return obj == null ? String(obj) :\n      class2type[toString.call(obj)] || \"object\"\n  }\n\n  function isFunction(value) { return type(value) == \"function\" }\n  function isWindow(obj)     { return obj != null && obj == obj.window }\n  function isDocument(obj)   { return obj != null && obj.nodeType == obj.DOCUMENT_NODE }\n  function isObject(obj)     { return type(obj) == \"object\" }\n  function isPlainObject(obj) {\n    return isObject(obj) && !isWindow(obj) && Object.getPrototypeOf(obj) == Object.prototype\n  }\n\n  function likeArray(obj) {\n    var length = !!obj && 'length' in obj && obj.length,\n      type = $.type(obj)\n\n    return 'function' != type && !isWindow(obj) && (\n      'array' == type || length === 0 ||\n        (typeof length == 'number' && length > 0 && (length - 1) in obj)\n    )\n  }\n\n  function compact(array) { return filter.call(array, function(item){ return item != null }) }\n  function flatten(array) { return array.length > 0 ? $.fn.concat.apply([], array) : array }\n  camelize = function(str){ return str.replace(/-+(.)?/g, function(match, chr){ return chr ? chr.toUpperCase() : '' }) }\n  function dasherize(str) {\n    return str.replace(/::/g, '/')\n           .replace(/([A-Z]+)([A-Z][a-z])/g, '$1_$2')\n           .replace(/([a-z\\d])([A-Z])/g, '$1_$2')\n           .replace(/_/g, '-')\n           .toLowerCase()\n  }\n  uniq = function(array){ return filter.call(array, function(item, idx){ return array.indexOf(item) == idx }) }\n\n  function classRE(name) {\n    return name in classCache ?\n      classCache[name] : (classCache[name] = new RegExp('(^|\\\\s)' + name + '(\\\\s|$)'))\n  }\n\n  function maybeAddPx(name, value) {\n    return (typeof value == \"number\" && !cssNumber[dasherize(name)]) ? value + \"px\" : value\n  }\n\n  function defaultDisplay(nodeName) {\n    var element, display\n    if (!elementDisplay[nodeName]) {\n      element = document.createElement(nodeName)\n      document.body.appendChild(element)\n      display = getComputedStyle(element, '').getPropertyValue(\"display\")\n      element.parentNode.removeChild(element)\n      display == \"none\" && (display = \"block\")\n      elementDisplay[nodeName] = display\n    }\n    return elementDisplay[nodeName]\n  }\n\n  function children(element) {\n    return 'children' in element ?\n      slice.call(element.children) :\n      $.map(element.childNodes, function(node){ if (node.nodeType == 1) return node })\n  }\n\n  function Z(dom, selector) {\n    var i, len = dom ? dom.length : 0\n    for (i = 0; i < len; i++) this[i] = dom[i]\n    this.length = len\n    this.selector = selector || ''\n  }\n\n  // `$.zepto.fragment` takes a html string and an optional tag name\n  // to generate DOM nodes from the given html string.\n  // The generated DOM nodes are returned as an array.\n  // This function can be overridden in plugins for example to make\n  // it compatible with browsers that don't support the DOM fully.\n  zepto.fragment = function(html, name, properties) {\n    var dom, nodes, container\n\n    // A special case optimization for a single tag\n    if (singleTagRE.test(html)) dom = $(document.createElement(RegExp.$1))\n\n    if (!dom) {\n      if (html.replace) html = html.replace(tagExpanderRE, \"<$1></$2>\")\n      if (name === undefined) name = fragmentRE.test(html) && RegExp.$1\n      if (!(name in containers)) name = '*'\n\n      container = containers[name]\n      container.innerHTML = '' + html\n      dom = $.each(slice.call(container.childNodes), function(){\n        container.removeChild(this)\n      })\n    }\n\n    if (isPlainObject(properties)) {\n      nodes = $(dom)\n      $.each(properties, function(key, value) {\n        if (methodAttributes.indexOf(key) > -1) nodes[key](value)\n        else nodes.attr(key, value)\n      })\n    }\n\n    return dom\n  }\n\n  // `$.zepto.Z` swaps out the prototype of the given `dom` array\n  // of nodes with `$.fn` and thus supplying all the Zepto functions\n  // to the array. This method can be overridden in plugins.\n  zepto.Z = function(dom, selector) {\n    return new Z(dom, selector)\n  }\n\n  // `$.zepto.isZ` should return `true` if the given object is a Zepto\n  // collection. This method can be overridden in plugins.\n  zepto.isZ = function(object) {\n    return object instanceof zepto.Z\n  }\n\n  // `$.zepto.init` is Zepto's counterpart to jQuery's `$.fn.init` and\n  // takes a CSS selector and an optional context (and handles various\n  // special cases).\n  // This method can be overridden in plugins.\n  zepto.init = function(selector, context) {\n    var dom\n    // If nothing given, return an empty Zepto collection\n    if (!selector) return zepto.Z()\n    // Optimize for string selectors\n    else if (typeof selector == 'string') {\n      selector = selector.trim()\n      // If it's a html fragment, create nodes from it\n      // Note: In both Chrome 21 and Firefox 15, DOM error 12\n      // is thrown if the fragment doesn't begin with <\n      if (selector[0] == '<' && fragmentRE.test(selector))\n        dom = zepto.fragment(selector, RegExp.$1, context), selector = null\n      // If there's a context, create a collection on that context first, and select\n      // nodes from there\n      else if (context !== undefined) return $(context).find(selector)\n      // If it's a CSS selector, use it to select nodes.\n      else dom = zepto.qsa(document, selector)\n    }\n    // If a function is given, call it when the DOM is ready\n    else if (isFunction(selector)) return $(document).ready(selector)\n    // If a Zepto collection is given, just return it\n    else if (zepto.isZ(selector)) return selector\n    else {\n      // normalize array if an array of nodes is given\n      if (isArray(selector)) dom = compact(selector)\n      // Wrap DOM nodes.\n      else if (isObject(selector))\n        dom = [selector], selector = null\n      // If it's a html fragment, create nodes from it\n      else if (fragmentRE.test(selector))\n        dom = zepto.fragment(selector.trim(), RegExp.$1, context), selector = null\n      // If there's a context, create a collection on that context first, and select\n      // nodes from there\n      else if (context !== undefined) return $(context).find(selector)\n      // And last but no least, if it's a CSS selector, use it to select nodes.\n      else dom = zepto.qsa(document, selector)\n    }\n    // create a new Zepto collection from the nodes found\n    return zepto.Z(dom, selector)\n  }\n\n  // `$` will be the base `Zepto` object. When calling this\n  // function just call `$.zepto.init, which makes the implementation\n  // details of selecting nodes and creating Zepto collections\n  // patchable in plugins.\n  $ = function(selector, context){\n    return zepto.init(selector, context)\n  }\n\n  function extend(target, source, deep) {\n    for (key in source)\n      if (deep && (isPlainObject(source[key]) || isArray(source[key]))) {\n        if (isPlainObject(source[key]) && !isPlainObject(target[key]))\n          target[key] = {}\n        if (isArray(source[key]) && !isArray(target[key]))\n          target[key] = []\n        extend(target[key], source[key], deep)\n      }\n      else if (source[key] !== undefined) target[key] = source[key]\n  }\n\n  // Copy all but undefined properties from one or more\n  // objects to the `target` object.\n  $.extend = function(target){\n    var deep, args = slice.call(arguments, 1)\n    if (typeof target == 'boolean') {\n      deep = target\n      target = args.shift()\n    }\n    args.forEach(function(arg){ extend(target, arg, deep) })\n    return target\n  }\n\n  // `$.zepto.qsa` is Zepto's CSS selector implementation which\n  // uses `document.querySelectorAll` and optimizes for some special cases, like `#id`.\n  // This method can be overridden in plugins.\n  zepto.qsa = function(element, selector){\n    var found,\n        maybeID = selector[0] == '#',\n        maybeClass = !maybeID && selector[0] == '.',\n        nameOnly = maybeID || maybeClass ? selector.slice(1) : selector, // Ensure that a 1 char tag name still gets checked\n        isSimple = simpleSelectorRE.test(nameOnly)\n    return (element.getElementById && isSimple && maybeID) ? // Safari DocumentFragment doesn't have getElementById\n      ( (found = element.getElementById(nameOnly)) ? [found] : [] ) :\n      (element.nodeType !== 1 && element.nodeType !== 9 && element.nodeType !== 11) ? [] :\n      slice.call(\n        isSimple && !maybeID && element.getElementsByClassName ? // DocumentFragment doesn't have getElementsByClassName/TagName\n          maybeClass ? element.getElementsByClassName(nameOnly) : // If it's simple, it could be a class\n          element.getElementsByTagName(selector) : // Or a tag\n          element.querySelectorAll(selector) // Or it's not simple, and we need to query all\n      )\n  }\n\n  function filtered(nodes, selector) {\n    return selector == null ? $(nodes) : $(nodes).filter(selector)\n  }\n\n  $.contains = document.documentElement.contains ?\n    function(parent, node) {\n      return parent !== node && parent.contains(node)\n    } :\n    function(parent, node) {\n      while (node && (node = node.parentNode))\n        if (node === parent) return true\n      return false\n    }\n\n  function funcArg(context, arg, idx, payload) {\n    return isFunction(arg) ? arg.call(context, idx, payload) : arg\n  }\n\n  function setAttribute(node, name, value) {\n    value == null ? node.removeAttribute(name) : node.setAttribute(name, value)\n  }\n\n  // access className property while respecting SVGAnimatedString\n  function className(node, value){\n    var klass = node.className || '',\n        svg   = klass && klass.baseVal !== undefined\n\n    if (value === undefined) return svg ? klass.baseVal : klass\n    svg ? (klass.baseVal = value) : (node.className = value)\n  }\n\n  // \"true\"  => true\n  // \"false\" => false\n  // \"null\"  => null\n  // \"42\"    => 42\n  // \"42.5\"  => 42.5\n  // \"08\"    => \"08\"\n  // JSON    => parse if valid\n  // String  => self\n  function deserializeValue(value) {\n    try {\n      return value ?\n        value == \"true\" ||\n        ( value == \"false\" ? false :\n          value == \"null\" ? null :\n          +value + \"\" == value ? +value :\n          /^[\\[\\{]/.test(value) ? $.parseJSON(value) :\n          value )\n        : value\n    } catch(e) {\n      return value\n    }\n  }\n\n  $.type = type\n  $.isFunction = isFunction\n  $.isWindow = isWindow\n  $.isArray = isArray\n  $.isPlainObject = isPlainObject\n\n  $.isEmptyObject = function(obj) {\n    var name\n    for (name in obj) return false\n    return true\n  }\n\n  $.isNumeric = function(val) {\n    var num = Number(val), type = typeof val\n    return val != null && type != 'boolean' &&\n      (type != 'string' || val.length) &&\n      !isNaN(num) && isFinite(num) || false\n  }\n\n  $.inArray = function(elem, array, i){\n    return emptyArray.indexOf.call(array, elem, i)\n  }\n\n  $.camelCase = camelize\n  $.trim = function(str) {\n    return str == null ? \"\" : String.prototype.trim.call(str)\n  }\n\n  // plugin compatibility\n  $.uuid = 0\n  $.support = { }\n  $.expr = { }\n  $.noop = function() {}\n\n  $.map = function(elements, callback){\n    var value, values = [], i, key\n    if (likeArray(elements))\n      for (i = 0; i < elements.length; i++) {\n        value = callback(elements[i], i)\n        if (value != null) values.push(value)\n      }\n    else\n      for (key in elements) {\n        value = callback(elements[key], key)\n        if (value != null) values.push(value)\n      }\n    return flatten(values)\n  }\n\n  $.each = function(elements, callback){\n    var i, key\n    if (likeArray(elements)) {\n      for (i = 0; i < elements.length; i++)\n        if (callback.call(elements[i], i, elements[i]) === false) return elements\n    } else {\n      for (key in elements)\n        if (callback.call(elements[key], key, elements[key]) === false) return elements\n    }\n\n    return elements\n  }\n\n  $.grep = function(elements, callback){\n    return filter.call(elements, callback)\n  }\n\n  if (window.JSON) $.parseJSON = JSON.parse\n\n  // Populate the class2type map\n  $.each(\"Boolean Number String Function Array Date RegExp Object Error\".split(\" \"), function(i, name) {\n    class2type[ \"[object \" + name + \"]\" ] = name.toLowerCase()\n  })\n\n  // Define methods that will be available on all\n  // Zepto collections\n  $.fn = {\n    constructor: zepto.Z,\n    length: 0,\n\n    // Because a collection acts like an array\n    // copy over these useful array functions.\n    forEach: emptyArray.forEach,\n    reduce: emptyArray.reduce,\n    push: emptyArray.push,\n    sort: emptyArray.sort,\n    splice: emptyArray.splice,\n    indexOf: emptyArray.indexOf,\n    concat: function(){\n      var i, value, args = []\n      for (i = 0; i < arguments.length; i++) {\n        value = arguments[i]\n        args[i] = zepto.isZ(value) ? value.toArray() : value\n      }\n      return concat.apply(zepto.isZ(this) ? this.toArray() : this, args)\n    },\n\n    // `map` and `slice` in the jQuery API work differently\n    // from their array counterparts\n    map: function(fn){\n      return $($.map(this, function(el, i){ return fn.call(el, i, el) }))\n    },\n    slice: function(){\n      return $(slice.apply(this, arguments))\n    },\n\n    ready: function(callback){\n      // need to check if document.body exists for IE as that browser reports\n      // document ready when it hasn't yet created the body element\n      if (readyRE.test(document.readyState) && document.body) callback($)\n      else document.addEventListener('DOMContentLoaded', function(){ callback($) }, false)\n      return this\n    },\n    get: function(idx){\n      return idx === undefined ? slice.call(this) : this[idx >= 0 ? idx : idx + this.length]\n    },\n    toArray: function(){ return this.get() },\n    size: function(){\n      return this.length\n    },\n    remove: function(){\n      return this.each(function(){\n        if (this.parentNode != null)\n          this.parentNode.removeChild(this)\n      })\n    },\n    each: function(callback){\n      emptyArray.every.call(this, function(el, idx){\n        return callback.call(el, idx, el) !== false\n      })\n      return this\n    },\n    filter: function(selector){\n      if (isFunction(selector)) return this.not(this.not(selector))\n      return $(filter.call(this, function(element){\n        return zepto.matches(element, selector)\n      }))\n    },\n    add: function(selector,context){\n      return $(uniq(this.concat($(selector,context))))\n    },\n    is: function(selector){\n      return this.length > 0 && zepto.matches(this[0], selector)\n    },\n    not: function(selector){\n      var nodes=[]\n      if (isFunction(selector) && selector.call !== undefined)\n        this.each(function(idx){\n          if (!selector.call(this,idx)) nodes.push(this)\n        })\n      else {\n        var excludes = typeof selector == 'string' ? this.filter(selector) :\n          (likeArray(selector) && isFunction(selector.item)) ? slice.call(selector) : $(selector)\n        this.forEach(function(el){\n          if (excludes.indexOf(el) < 0) nodes.push(el)\n        })\n      }\n      return $(nodes)\n    },\n    has: function(selector){\n      return this.filter(function(){\n        return isObject(selector) ?\n          $.contains(this, selector) :\n          $(this).find(selector).size()\n      })\n    },\n    eq: function(idx){\n      return idx === -1 ? this.slice(idx) : this.slice(idx, + idx + 1)\n    },\n    first: function(){\n      var el = this[0]\n      return el && !isObject(el) ? el : $(el)\n    },\n    last: function(){\n      var el = this[this.length - 1]\n      return el && !isObject(el) ? el : $(el)\n    },\n    find: function(selector){\n      var result, $this = this\n      if (!selector) result = $()\n      else if (typeof selector == 'object')\n        result = $(selector).filter(function(){\n          var node = this\n          return emptyArray.some.call($this, function(parent){\n            return $.contains(parent, node)\n          })\n        })\n      else if (this.length == 1) result = $(zepto.qsa(this[0], selector))\n      else result = this.map(function(){ return zepto.qsa(this, selector) })\n      return result\n    },\n    closest: function(selector, context){\n      var nodes = [], collection = typeof selector == 'object' && $(selector)\n      this.each(function(_, node){\n        while (node && !(collection ? collection.indexOf(node) >= 0 : zepto.matches(node, selector)))\n          node = node !== context && !isDocument(node) && node.parentNode\n        if (node && nodes.indexOf(node) < 0) nodes.push(node)\n      })\n      return $(nodes)\n    },\n    parents: function(selector){\n      var ancestors = [], nodes = this\n      while (nodes.length > 0)\n        nodes = $.map(nodes, function(node){\n          if ((node = node.parentNode) && !isDocument(node) && ancestors.indexOf(node) < 0) {\n            ancestors.push(node)\n            return node\n          }\n        })\n      return filtered(ancestors, selector)\n    },\n    parent: function(selector){\n      return filtered(uniq(this.pluck('parentNode')), selector)\n    },\n    children: function(selector){\n      return filtered(this.map(function(){ return children(this) }), selector)\n    },\n    contents: function() {\n      return this.map(function() { return this.contentDocument || slice.call(this.childNodes) })\n    },\n    siblings: function(selector){\n      return filtered(this.map(function(i, el){\n        return filter.call(children(el.parentNode), function(child){ return child!==el })\n      }), selector)\n    },\n    empty: function(){\n      return this.each(function(){ this.innerHTML = '' })\n    },\n    // `pluck` is borrowed from Prototype.js\n    pluck: function(property){\n      return $.map(this, function(el){ return el[property] })\n    },\n    show: function(){\n      return this.each(function(){\n        this.style.display == \"none\" && (this.style.display = '')\n        if (getComputedStyle(this, '').getPropertyValue(\"display\") == \"none\")\n          this.style.display = defaultDisplay(this.nodeName)\n      })\n    },\n    replaceWith: function(newContent){\n      return this.before(newContent).remove()\n    },\n    wrap: function(structure){\n      var func = isFunction(structure)\n      if (this[0] && !func)\n        var dom   = $(structure).get(0),\n            clone = dom.parentNode || this.length > 1\n\n      return this.each(function(index){\n        $(this).wrapAll(\n          func ? structure.call(this, index) :\n            clone ? dom.cloneNode(true) : dom\n        )\n      })\n    },\n    wrapAll: function(structure){\n      if (this[0]) {\n        $(this[0]).before(structure = $(structure))\n        var children\n        // drill down to the inmost element\n        while ((children = structure.children()).length) structure = children.first()\n        $(structure).append(this)\n      }\n      return this\n    },\n    wrapInner: function(structure){\n      var func = isFunction(structure)\n      return this.each(function(index){\n        var self = $(this), contents = self.contents(),\n            dom  = func ? structure.call(this, index) : structure\n        contents.length ? contents.wrapAll(dom) : self.append(dom)\n      })\n    },\n    unwrap: function(){\n      this.parent().each(function(){\n        $(this).replaceWith($(this).children())\n      })\n      return this\n    },\n    clone: function(){\n      return this.map(function(){ return this.cloneNode(true) })\n    },\n    hide: function(){\n      return this.css(\"display\", \"none\")\n    },\n    toggle: function(setting){\n      return this.each(function(){\n        var el = $(this)\n        ;(setting === undefined ? el.css(\"display\") == \"none\" : setting) ? el.show() : el.hide()\n      })\n    },\n    prev: function(selector){ return $(this.pluck('previousElementSibling')).filter(selector || '*') },\n    next: function(selector){ return $(this.pluck('nextElementSibling')).filter(selector || '*') },\n    html: function(html){\n      return 0 in arguments ?\n        this.each(function(idx){\n          var originHtml = this.innerHTML\n          $(this).empty().append( funcArg(this, html, idx, originHtml) )\n        }) :\n        (0 in this ? this[0].innerHTML : null)\n    },\n    text: function(text){\n      return 0 in arguments ?\n        this.each(function(idx){\n          var newText = funcArg(this, text, idx, this.textContent)\n          this.textContent = newText == null ? '' : ''+newText\n        }) :\n        (0 in this ? this.pluck('textContent').join(\"\") : null)\n    },\n    attr: function(name, value){\n      var result\n      return (typeof name == 'string' && !(1 in arguments)) ?\n        (0 in this && this[0].nodeType == 1 && (result = this[0].getAttribute(name)) != null ? result : undefined) :\n        this.each(function(idx){\n          if (this.nodeType !== 1) return\n          if (isObject(name)) for (key in name) setAttribute(this, key, name[key])\n          else setAttribute(this, name, funcArg(this, value, idx, this.getAttribute(name)))\n        })\n    },\n    removeAttr: function(name){\n      return this.each(function(){ this.nodeType === 1 && name.split(' ').forEach(function(attribute){\n        setAttribute(this, attribute)\n      }, this)})\n    },\n    prop: function(name, value){\n      name = propMap[name] || name\n      return (1 in arguments) ?\n        this.each(function(idx){\n          this[name] = funcArg(this, value, idx, this[name])\n        }) :\n        (this[0] && this[0][name])\n    },\n    removeProp: function(name){\n      name = propMap[name] || name\n      return this.each(function(){ delete this[name] })\n    },\n    data: function(name, value){\n      var attrName = 'data-' + name.replace(capitalRE, '-$1').toLowerCase()\n\n      var data = (1 in arguments) ?\n        this.attr(attrName, value) :\n        this.attr(attrName)\n\n      return data !== null ? deserializeValue(data) : undefined\n    },\n    val: function(value){\n      if (0 in arguments) {\n        if (value == null) value = \"\"\n        return this.each(function(idx){\n          this.value = funcArg(this, value, idx, this.value)\n        })\n      } else {\n        return this[0] && (this[0].multiple ?\n           $(this[0]).find('option').filter(function(){ return this.selected }).pluck('value') :\n           this[0].value)\n      }\n    },\n    offset: function(coordinates){\n      if (coordinates) return this.each(function(index){\n        var $this = $(this),\n            coords = funcArg(this, coordinates, index, $this.offset()),\n            parentOffset = $this.offsetParent().offset(),\n            props = {\n              top:  coords.top  - parentOffset.top,\n              left: coords.left - parentOffset.left\n            }\n\n        if ($this.css('position') == 'static') props['position'] = 'relative'\n        $this.css(props)\n      })\n      if (!this.length) return null\n      if (document.documentElement !== this[0] && !$.contains(document.documentElement, this[0]))\n        return {top: 0, left: 0}\n      var obj = this[0].getBoundingClientRect()\n      return {\n        left: obj.left + window.pageXOffset,\n        top: obj.top + window.pageYOffset,\n        width: Math.round(obj.width),\n        height: Math.round(obj.height)\n      }\n    },\n    css: function(property, value){\n      if (arguments.length < 2) {\n        var element = this[0]\n        if (typeof property == 'string') {\n          if (!element) return\n          return element.style[camelize(property)] || getComputedStyle(element, '').getPropertyValue(property)\n        } else if (isArray(property)) {\n          if (!element) return\n          var props = {}\n          var computedStyle = getComputedStyle(element, '')\n          $.each(property, function(_, prop){\n            props[prop] = (element.style[camelize(prop)] || computedStyle.getPropertyValue(prop))\n          })\n          return props\n        }\n      }\n\n      var css = ''\n      if (type(property) == 'string') {\n        if (!value && value !== 0)\n          this.each(function(){ this.style.removeProperty(dasherize(property)) })\n        else\n          css = dasherize(property) + \":\" + maybeAddPx(property, value)\n      } else {\n        for (key in property)\n          if (!property[key] && property[key] !== 0)\n            this.each(function(){ this.style.removeProperty(dasherize(key)) })\n          else\n            css += dasherize(key) + ':' + maybeAddPx(key, property[key]) + ';'\n      }\n\n      return this.each(function(){ this.style.cssText += ';' + css })\n    },\n    index: function(element){\n      return element ? this.indexOf($(element)[0]) : this.parent().children().indexOf(this[0])\n    },\n    hasClass: function(name){\n      if (!name) return false\n      return emptyArray.some.call(this, function(el){\n        return this.test(className(el))\n      }, classRE(name))\n    },\n    addClass: function(name){\n      if (!name) return this\n      return this.each(function(idx){\n        if (!('className' in this)) return\n        classList = []\n        var cls = className(this), newName = funcArg(this, name, idx, cls)\n        newName.split(/\\s+/g).forEach(function(klass){\n          if (!$(this).hasClass(klass)) classList.push(klass)\n        }, this)\n        classList.length && className(this, cls + (cls ? \" \" : \"\") + classList.join(\" \"))\n      })\n    },\n    removeClass: function(name){\n      return this.each(function(idx){\n        if (!('className' in this)) return\n        if (name === undefined) return className(this, '')\n        classList = className(this)\n        funcArg(this, name, idx, classList).split(/\\s+/g).forEach(function(klass){\n          classList = classList.replace(classRE(klass), \" \")\n        })\n        className(this, classList.trim())\n      })\n    },\n    toggleClass: function(name, when){\n      if (!name) return this\n      return this.each(function(idx){\n        var $this = $(this), names = funcArg(this, name, idx, className(this))\n        names.split(/\\s+/g).forEach(function(klass){\n          (when === undefined ? !$this.hasClass(klass) : when) ?\n            $this.addClass(klass) : $this.removeClass(klass)\n        })\n      })\n    },\n    scrollTop: function(value){\n      if (!this.length) return\n      var hasScrollTop = 'scrollTop' in this[0]\n      if (value === undefined) return hasScrollTop ? this[0].scrollTop : this[0].pageYOffset\n      return this.each(hasScrollTop ?\n        function(){ this.scrollTop = value } :\n        function(){ this.scrollTo(this.scrollX, value) })\n    },\n    scrollLeft: function(value){\n      if (!this.length) return\n      var hasScrollLeft = 'scrollLeft' in this[0]\n      if (value === undefined) return hasScrollLeft ? this[0].scrollLeft : this[0].pageXOffset\n      return this.each(hasScrollLeft ?\n        function(){ this.scrollLeft = value } :\n        function(){ this.scrollTo(value, this.scrollY) })\n    },\n    position: function() {\n      if (!this.length) return\n\n      var elem = this[0],\n        // Get *real* offsetParent\n        offsetParent = this.offsetParent(),\n        // Get correct offsets\n        offset       = this.offset(),\n        parentOffset = rootNodeRE.test(offsetParent[0].nodeName) ? { top: 0, left: 0 } : offsetParent.offset()\n\n      // Subtract element margins\n      // note: when an element has margin: auto the offsetLeft and marginLeft\n      // are the same in Safari causing offset.left to incorrectly be 0\n      offset.top  -= parseFloat( $(elem).css('margin-top') ) || 0\n      offset.left -= parseFloat( $(elem).css('margin-left') ) || 0\n\n      // Add offsetParent borders\n      parentOffset.top  += parseFloat( $(offsetParent[0]).css('border-top-width') ) || 0\n      parentOffset.left += parseFloat( $(offsetParent[0]).css('border-left-width') ) || 0\n\n      // Subtract the two offsets\n      return {\n        top:  offset.top  - parentOffset.top,\n        left: offset.left - parentOffset.left\n      }\n    },\n    offsetParent: function() {\n      return this.map(function(){\n        var parent = this.offsetParent || document.body\n        while (parent && !rootNodeRE.test(parent.nodeName) && $(parent).css(\"position\") == \"static\")\n          parent = parent.offsetParent\n        return parent\n      })\n    }\n  }\n\n  // for now\n  $.fn.detach = $.fn.remove\n\n  // Generate the `width` and `height` functions\n  ;['width', 'height'].forEach(function(dimension){\n    var dimensionProperty =\n      dimension.replace(/./, function(m){ return m[0].toUpperCase() })\n\n    $.fn[dimension] = function(value){\n      var offset, el = this[0]\n      if (value === undefined) return isWindow(el) ? el['inner' + dimensionProperty] :\n        isDocument(el) ? el.documentElement['scroll' + dimensionProperty] :\n        (offset = this.offset()) && offset[dimension]\n      else return this.each(function(idx){\n        el = $(this)\n        el.css(dimension, funcArg(this, value, idx, el[dimension]()))\n      })\n    }\n  })\n\n  function traverseNode(node, fun) {\n    fun(node)\n    for (var i = 0, len = node.childNodes.length; i < len; i++)\n      traverseNode(node.childNodes[i], fun)\n  }\n\n  // Generate the `after`, `prepend`, `before`, `append`,\n  // `insertAfter`, `insertBefore`, `appendTo`, and `prependTo` methods.\n  adjacencyOperators.forEach(function(operator, operatorIndex) {\n    var inside = operatorIndex % 2 //=> prepend, append\n\n    $.fn[operator] = function(){\n      // arguments can be nodes, arrays of nodes, Zepto objects and HTML strings\n      var argType, nodes = $.map(arguments, function(arg) {\n            var arr = []\n            argType = type(arg)\n            if (argType == \"array\") {\n              arg.forEach(function(el) {\n                if (el.nodeType !== undefined) return arr.push(el)\n                else if ($.zepto.isZ(el)) return arr = arr.concat(el.get())\n                arr = arr.concat(zepto.fragment(el))\n              })\n              return arr\n            }\n            return argType == \"object\" || arg == null ?\n              arg : zepto.fragment(arg)\n          }),\n          parent, copyByClone = this.length > 1\n      if (nodes.length < 1) return this\n\n      return this.each(function(_, target){\n        parent = inside ? target : target.parentNode\n\n        // convert all methods to a \"before\" operation\n        target = operatorIndex == 0 ? target.nextSibling :\n                 operatorIndex == 1 ? target.firstChild :\n                 operatorIndex == 2 ? target :\n                 null\n\n        var parentInDocument = $.contains(document.documentElement, parent)\n\n        nodes.forEach(function(node){\n          if (copyByClone) node = node.cloneNode(true)\n          else if (!parent) return $(node).remove()\n\n          parent.insertBefore(node, target)\n          if (parentInDocument) traverseNode(node, function(el){\n            if (el.nodeName != null && el.nodeName.toUpperCase() === 'SCRIPT' &&\n               (!el.type || el.type === 'text/javascript') && !el.src){\n              var target = el.ownerDocument ? el.ownerDocument.defaultView : window\n              target['eval'].call(target, el.innerHTML)\n            }\n          })\n        })\n      })\n    }\n\n    // after    => insertAfter\n    // prepend  => prependTo\n    // before   => insertBefore\n    // append   => appendTo\n    $.fn[inside ? operator+'To' : 'insert'+(operatorIndex ? 'Before' : 'After')] = function(html){\n      $(html)[operator](this)\n      return this\n    }\n  })\n\n  zepto.Z.prototype = Z.prototype = $.fn\n\n  // Export internal API functions in the `$.zepto` namespace\n  zepto.uniq = uniq\n  zepto.deserializeValue = deserializeValue\n  $.zepto = zepto\n\n  return $\n})()\n\nwindow.Zepto = Zepto\nwindow.$ === undefined && (window.$ = Zepto)\n\n;(function($){\n  var _zid = 1, undefined,\n      slice = Array.prototype.slice,\n      isFunction = $.isFunction,\n      isString = function(obj){ return typeof obj == 'string' },\n      handlers = {},\n      specialEvents={},\n      focusinSupported = 'onfocusin' in window,\n      focus = { focus: 'focusin', blur: 'focusout' },\n      hover = { mouseenter: 'mouseover', mouseleave: 'mouseout' }\n\n  specialEvents.click = specialEvents.mousedown = specialEvents.mouseup = specialEvents.mousemove = 'MouseEvents'\n\n  function zid(element) {\n    return element._zid || (element._zid = _zid++)\n  }\n  function findHandlers(element, event, fn, selector) {\n    event = parse(event)\n    if (event.ns) var matcher = matcherFor(event.ns)\n    return (handlers[zid(element)] || []).filter(function(handler) {\n      return handler\n        && (!event.e  || handler.e == event.e)\n        && (!event.ns || matcher.test(handler.ns))\n        && (!fn       || zid(handler.fn) === zid(fn))\n        && (!selector || handler.sel == selector)\n    })\n  }\n  function parse(event) {\n    var parts = ('' + event).split('.')\n    return {e: parts[0], ns: parts.slice(1).sort().join(' ')}\n  }\n  function matcherFor(ns) {\n    return new RegExp('(?:^| )' + ns.replace(' ', ' .* ?') + '(?: |$)')\n  }\n\n  function eventCapture(handler, captureSetting) {\n    return handler.del &&\n      (!focusinSupported && (handler.e in focus)) ||\n      !!captureSetting\n  }\n\n  function realEvent(type) {\n    return hover[type] || (focusinSupported && focus[type]) || type\n  }\n\n  function add(element, events, fn, data, selector, delegator, capture){\n    var id = zid(element), set = (handlers[id] || (handlers[id] = []))\n    events.split(/\\s/).forEach(function(event){\n      if (event == 'ready') return $(document).ready(fn)\n      var handler   = parse(event)\n      handler.fn    = fn\n      handler.sel   = selector\n      // emulate mouseenter, mouseleave\n      if (handler.e in hover) fn = function(e){\n        var related = e.relatedTarget\n        if (!related || (related !== this && !$.contains(this, related)))\n          return handler.fn.apply(this, arguments)\n      }\n      handler.del   = delegator\n      var callback  = delegator || fn\n      handler.proxy = function(e){\n        e = compatible(e)\n        if (e.isImmediatePropagationStopped()) return\n        e.data = data\n        var result = callback.apply(element, e._args == undefined ? [e] : [e].concat(e._args))\n        if (result === false) e.preventDefault(), e.stopPropagation()\n        return result\n      }\n      handler.i = set.length\n      set.push(handler)\n      if ('addEventListener' in element)\n        element.addEventListener(realEvent(handler.e), handler.proxy, eventCapture(handler, capture))\n    })\n  }\n  function remove(element, events, fn, selector, capture){\n    var id = zid(element)\n    ;(events || '').split(/\\s/).forEach(function(event){\n      findHandlers(element, event, fn, selector).forEach(function(handler){\n        delete handlers[id][handler.i]\n      if ('removeEventListener' in element)\n        element.removeEventListener(realEvent(handler.e), handler.proxy, eventCapture(handler, capture))\n      })\n    })\n  }\n\n  $.event = { add: add, remove: remove }\n\n  $.proxy = function(fn, context) {\n    var args = (2 in arguments) && slice.call(arguments, 2)\n    if (isFunction(fn)) {\n      var proxyFn = function(){ return fn.apply(context, args ? args.concat(slice.call(arguments)) : arguments) }\n      proxyFn._zid = zid(fn)\n      return proxyFn\n    } else if (isString(context)) {\n      if (args) {\n        args.unshift(fn[context], fn)\n        return $.proxy.apply(null, args)\n      } else {\n        return $.proxy(fn[context], fn)\n      }\n    } else {\n      throw new TypeError(\"expected function\")\n    }\n  }\n\n  $.fn.bind = function(event, data, callback){\n    return this.on(event, data, callback)\n  }\n  $.fn.unbind = function(event, callback){\n    return this.off(event, callback)\n  }\n  $.fn.one = function(event, selector, data, callback){\n    return this.on(event, selector, data, callback, 1)\n  }\n\n  var returnTrue = function(){return true},\n      returnFalse = function(){return false},\n      ignoreProperties = /^([A-Z]|returnValue$|layer[XY]$|webkitMovement[XY]$)/,\n      eventMethods = {\n        preventDefault: 'isDefaultPrevented',\n        stopImmediatePropagation: 'isImmediatePropagationStopped',\n        stopPropagation: 'isPropagationStopped'\n      }\n\n  function compatible(event, source) {\n    if (source || !event.isDefaultPrevented) {\n      source || (source = event)\n\n      $.each(eventMethods, function(name, predicate) {\n        var sourceMethod = source[name]\n        event[name] = function(){\n          this[predicate] = returnTrue\n          return sourceMethod && sourceMethod.apply(source, arguments)\n        }\n        event[predicate] = returnFalse\n      })\n\n      event.timeStamp || (event.timeStamp = Date.now())\n\n      if (source.defaultPrevented !== undefined ? source.defaultPrevented :\n          'returnValue' in source ? source.returnValue === false :\n          source.getPreventDefault && source.getPreventDefault())\n        event.isDefaultPrevented = returnTrue\n    }\n    return event\n  }\n\n  function createProxy(event) {\n    var key, proxy = { originalEvent: event }\n    for (key in event)\n      if (!ignoreProperties.test(key) && event[key] !== undefined) proxy[key] = event[key]\n\n    return compatible(proxy, event)\n  }\n\n  $.fn.delegate = function(selector, event, callback){\n    return this.on(event, selector, callback)\n  }\n  $.fn.undelegate = function(selector, event, callback){\n    return this.off(event, selector, callback)\n  }\n\n  $.fn.live = function(event, callback){\n    $(document.body).delegate(this.selector, event, callback)\n    return this\n  }\n  $.fn.die = function(event, callback){\n    $(document.body).undelegate(this.selector, event, callback)\n    return this\n  }\n\n  $.fn.on = function(event, selector, data, callback, one){\n    var autoRemove, delegator, $this = this\n    if (event && !isString(event)) {\n      $.each(event, function(type, fn){\n        $this.on(type, selector, data, fn, one)\n      })\n      return $this\n    }\n\n    if (!isString(selector) && !isFunction(callback) && callback !== false)\n      callback = data, data = selector, selector = undefined\n    if (callback === undefined || data === false)\n      callback = data, data = undefined\n\n    if (callback === false) callback = returnFalse\n\n    return $this.each(function(_, element){\n      if (one) autoRemove = function(e){\n        remove(element, e.type, callback)\n        return callback.apply(this, arguments)\n      }\n\n      if (selector) delegator = function(e){\n        var evt, match = $(e.target).closest(selector, element).get(0)\n        if (match && match !== element) {\n          evt = $.extend(createProxy(e), {currentTarget: match, liveFired: element})\n          return (autoRemove || callback).apply(match, [evt].concat(slice.call(arguments, 1)))\n        }\n      }\n\n      add(element, event, callback, data, selector, delegator || autoRemove)\n    })\n  }\n  $.fn.off = function(event, selector, callback){\n    var $this = this\n    if (event && !isString(event)) {\n      $.each(event, function(type, fn){\n        $this.off(type, selector, fn)\n      })\n      return $this\n    }\n\n    if (!isString(selector) && !isFunction(callback) && callback !== false)\n      callback = selector, selector = undefined\n\n    if (callback === false) callback = returnFalse\n\n    return $this.each(function(){\n      remove(this, event, callback, selector)\n    })\n  }\n\n  $.fn.trigger = function(event, args){\n    event = (isString(event) || $.isPlainObject(event)) ? $.Event(event) : compatible(event)\n    event._args = args\n    return this.each(function(){\n      // handle focus(), blur() by calling them directly\n      if (event.type in focus && typeof this[event.type] == \"function\") this[event.type]()\n      // items in the collection might not be DOM elements\n      else if ('dispatchEvent' in this) this.dispatchEvent(event)\n      else $(this).triggerHandler(event, args)\n    })\n  }\n\n  // triggers event handlers on current element just as if an event occurred,\n  // doesn't trigger an actual event, doesn't bubble\n  $.fn.triggerHandler = function(event, args){\n    var e, result\n    this.each(function(i, element){\n      e = createProxy(isString(event) ? $.Event(event) : event)\n      e._args = args\n      e.target = element\n      $.each(findHandlers(element, event.type || event), function(i, handler){\n        result = handler.proxy(e)\n        if (e.isImmediatePropagationStopped()) return false\n      })\n    })\n    return result\n  }\n\n  // shortcut methods for `.bind(event, fn)` for each event type\n  ;('focusin focusout focus blur load resize scroll unload click dblclick '+\n  'mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave '+\n  'change select keydown keypress keyup error').split(' ').forEach(function(event) {\n    $.fn[event] = function(callback) {\n      return (0 in arguments) ?\n        this.bind(event, callback) :\n        this.trigger(event)\n    }\n  })\n\n  $.Event = function(type, props) {\n    if (!isString(type)) props = type, type = props.type\n    var event = document.createEvent(specialEvents[type] || 'Events'), bubbles = true\n    if (props) for (var name in props) (name == 'bubbles') ? (bubbles = !!props[name]) : (event[name] = props[name])\n    event.initEvent(type, bubbles, true)\n    return compatible(event)\n  }\n\n})(Zepto)\n\n;(function($){\n  var jsonpID = +new Date(),\n      document = window.document,\n      key,\n      name,\n      rscript = /<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi,\n      scriptTypeRE = /^(?:text|application)\\/javascript/i,\n      xmlTypeRE = /^(?:text|application)\\/xml/i,\n      jsonType = 'application/json',\n      htmlType = 'text/html',\n      blankRE = /^\\s*$/,\n      originAnchor = document.createElement('a')\n\n  originAnchor.href = window.location.href\n\n  // trigger a custom event and return false if it was cancelled\n  function triggerAndReturn(context, eventName, data) {\n    var event = $.Event(eventName)\n    $(context).trigger(event, data)\n    return !event.isDefaultPrevented()\n  }\n\n  // trigger an Ajax \"global\" event\n  function triggerGlobal(settings, context, eventName, data) {\n    if (settings.global) return triggerAndReturn(context || document, eventName, data)\n  }\n\n  // Number of active Ajax requests\n  $.active = 0\n\n  function ajaxStart(settings) {\n    if (settings.global && $.active++ === 0) triggerGlobal(settings, null, 'ajaxStart')\n  }\n  function ajaxStop(settings) {\n    if (settings.global && !(--$.active)) triggerGlobal(settings, null, 'ajaxStop')\n  }\n\n  // triggers an extra global event \"ajaxBeforeSend\" that's like \"ajaxSend\" but cancelable\n  function ajaxBeforeSend(xhr, settings) {\n    var context = settings.context\n    if (settings.beforeSend.call(context, xhr, settings) === false ||\n        triggerGlobal(settings, context, 'ajaxBeforeSend', [xhr, settings]) === false)\n      return false\n\n    triggerGlobal(settings, context, 'ajaxSend', [xhr, settings])\n  }\n  function ajaxSuccess(data, xhr, settings, deferred) {\n    var context = settings.context, status = 'success'\n    settings.success.call(context, data, status, xhr)\n    if (deferred) deferred.resolveWith(context, [data, status, xhr])\n    triggerGlobal(settings, context, 'ajaxSuccess', [xhr, settings, data])\n    ajaxComplete(status, xhr, settings)\n  }\n  // type: \"timeout\", \"error\", \"abort\", \"parsererror\"\n  function ajaxError(error, type, xhr, settings, deferred) {\n    var context = settings.context\n    settings.error.call(context, xhr, type, error)\n    if (deferred) deferred.rejectWith(context, [xhr, type, error])\n    triggerGlobal(settings, context, 'ajaxError', [xhr, settings, error || type])\n    ajaxComplete(type, xhr, settings)\n  }\n  // status: \"success\", \"notmodified\", \"error\", \"timeout\", \"abort\", \"parsererror\"\n  function ajaxComplete(status, xhr, settings) {\n    var context = settings.context\n    settings.complete.call(context, xhr, status)\n    triggerGlobal(settings, context, 'ajaxComplete', [xhr, settings])\n    ajaxStop(settings)\n  }\n\n  function ajaxDataFilter(data, type, settings) {\n    if (settings.dataFilter == empty) return data\n    var context = settings.context\n    return settings.dataFilter.call(context, data, type)\n  }\n\n  // Empty function, used as default callback\n  function empty() {}\n\n  $.ajaxJSONP = function(options, deferred){\n    if (!('type' in options)) return $.ajax(options)\n\n    var _callbackName = options.jsonpCallback,\n      callbackName = ($.isFunction(_callbackName) ?\n        _callbackName() : _callbackName) || ('Zepto' + (jsonpID++)),\n      script = document.createElement('script'),\n      originalCallback = window[callbackName],\n      responseData,\n      abort = function(errorType) {\n        $(script).triggerHandler('error', errorType || 'abort')\n      },\n      xhr = { abort: abort }, abortTimeout\n\n    if (deferred) deferred.promise(xhr)\n\n    $(script).on('load error', function(e, errorType){\n      clearTimeout(abortTimeout)\n      $(script).off().remove()\n\n      if (e.type == 'error' || !responseData) {\n        ajaxError(null, errorType || 'error', xhr, options, deferred)\n      } else {\n        ajaxSuccess(responseData[0], xhr, options, deferred)\n      }\n\n      window[callbackName] = originalCallback\n      if (responseData && $.isFunction(originalCallback))\n        originalCallback(responseData[0])\n\n      originalCallback = responseData = undefined\n    })\n\n    if (ajaxBeforeSend(xhr, options) === false) {\n      abort('abort')\n      return xhr\n    }\n\n    window[callbackName] = function(){\n      responseData = arguments\n    }\n\n    script.src = options.url.replace(/\\?(.+)=\\?/, '?$1=' + callbackName)\n    document.head.appendChild(script)\n\n    if (options.timeout > 0) abortTimeout = setTimeout(function(){\n      abort('timeout')\n    }, options.timeout)\n\n    return xhr\n  }\n\n  $.ajaxSettings = {\n    // Default type of request\n    type: 'GET',\n    // Callback that is executed before request\n    beforeSend: empty,\n    // Callback that is executed if the request succeeds\n    success: empty,\n    // Callback that is executed the the server drops error\n    error: empty,\n    // Callback that is executed on request complete (both: error and success)\n    complete: empty,\n    // The context for the callbacks\n    context: null,\n    // Whether to trigger \"global\" Ajax events\n    global: true,\n    // Transport\n    xhr: function () {\n      return new window.XMLHttpRequest()\n    },\n    // MIME types mapping\n    // IIS returns Javascript as \"application/x-javascript\"\n    accepts: {\n      script: 'text/javascript, application/javascript, application/x-javascript',\n      json:   jsonType,\n      xml:    'application/xml, text/xml',\n      html:   htmlType,\n      text:   'text/plain'\n    },\n    // Whether the request is to another domain\n    crossDomain: false,\n    // Default timeout\n    timeout: 0,\n    // Whether data should be serialized to string\n    processData: true,\n    // Whether the browser should be allowed to cache GET responses\n    cache: true,\n    //Used to handle the raw response data of XMLHttpRequest.\n    //This is a pre-filtering function to sanitize the response.\n    //The sanitized response should be returned\n    dataFilter: empty\n  }\n\n  function mimeToDataType(mime) {\n    if (mime) mime = mime.split(';', 2)[0]\n    return mime && ( mime == htmlType ? 'html' :\n      mime == jsonType ? 'json' :\n      scriptTypeRE.test(mime) ? 'script' :\n      xmlTypeRE.test(mime) && 'xml' ) || 'text'\n  }\n\n  function appendQuery(url, query) {\n    if (query == '') return url\n    return (url + '&' + query).replace(/[&?]{1,2}/, '?')\n  }\n\n  // serialize payload and append it to the URL for GET requests\n  function serializeData(options) {\n    if (options.processData && options.data && $.type(options.data) != \"string\")\n      options.data = $.param(options.data, options.traditional)\n    if (options.data && (!options.type || options.type.toUpperCase() == 'GET' || 'jsonp' == options.dataType))\n      options.url = appendQuery(options.url, options.data), options.data = undefined\n  }\n\n  $.ajax = function(options){\n    var settings = $.extend({}, options || {}),\n        deferred = $.Deferred && $.Deferred(),\n        urlAnchor, hashIndex\n    for (key in $.ajaxSettings) if (settings[key] === undefined) settings[key] = $.ajaxSettings[key]\n\n    ajaxStart(settings)\n\n    if (!settings.crossDomain) {\n      urlAnchor = document.createElement('a')\n      urlAnchor.href = settings.url\n      // cleans up URL for .href (IE only), see https://github.com/madrobby/zepto/pull/1049\n      urlAnchor.href = urlAnchor.href\n      settings.crossDomain = (originAnchor.protocol + '//' + originAnchor.host) !== (urlAnchor.protocol + '//' + urlAnchor.host)\n    }\n\n    if (!settings.url) settings.url = window.location.toString()\n    if ((hashIndex = settings.url.indexOf('#')) > -1) settings.url = settings.url.slice(0, hashIndex)\n    serializeData(settings)\n\n    var dataType = settings.dataType, hasPlaceholder = /\\?.+=\\?/.test(settings.url)\n    if (hasPlaceholder) dataType = 'jsonp'\n\n    if (settings.cache === false || (\n         (!options || options.cache !== true) &&\n         ('script' == dataType || 'jsonp' == dataType)\n        ))\n      settings.url = appendQuery(settings.url, '_=' + Date.now())\n\n    if ('jsonp' == dataType) {\n      if (!hasPlaceholder)\n        settings.url = appendQuery(settings.url,\n          settings.jsonp ? (settings.jsonp + '=?') : settings.jsonp === false ? '' : 'callback=?')\n      return $.ajaxJSONP(settings, deferred)\n    }\n\n    var mime = settings.accepts[dataType],\n        headers = { },\n        setHeader = function(name, value) { headers[name.toLowerCase()] = [name, value] },\n        protocol = /^([\\w-]+:)\\/\\//.test(settings.url) ? RegExp.$1 : window.location.protocol,\n        xhr = settings.xhr(),\n        nativeSetHeader = xhr.setRequestHeader,\n        abortTimeout\n\n    if (deferred) deferred.promise(xhr)\n\n    if (!settings.crossDomain) setHeader('X-Requested-With', 'XMLHttpRequest')\n    setHeader('Accept', mime || '*/*')\n    if (mime = settings.mimeType || mime) {\n      if (mime.indexOf(',') > -1) mime = mime.split(',', 2)[0]\n      xhr.overrideMimeType && xhr.overrideMimeType(mime)\n    }\n    if (settings.contentType || (settings.contentType !== false && settings.data && settings.type.toUpperCase() != 'GET'))\n      setHeader('Content-Type', settings.contentType || 'application/x-www-form-urlencoded')\n\n    if (settings.headers) for (name in settings.headers) setHeader(name, settings.headers[name])\n    xhr.setRequestHeader = setHeader\n\n    xhr.onreadystatechange = function(){\n      if (xhr.readyState == 4) {\n        xhr.onreadystatechange = empty\n        clearTimeout(abortTimeout)\n        var result, error = false\n        if ((xhr.status >= 200 && xhr.status < 300) || xhr.status == 304 || (xhr.status == 0 && protocol == 'file:')) {\n          dataType = dataType || mimeToDataType(settings.mimeType || xhr.getResponseHeader('content-type'))\n\n          if (xhr.responseType == 'arraybuffer' || xhr.responseType == 'blob')\n            result = xhr.response\n          else {\n            result = xhr.responseText\n\n            try {\n              // http://perfectionkills.com/global-eval-what-are-the-options/\n              // sanitize response accordingly if data filter callback provided\n              result = ajaxDataFilter(result, dataType, settings)\n              if (dataType == 'script')    (1,eval)(result)\n              else if (dataType == 'xml')  result = xhr.responseXML\n              else if (dataType == 'json') result = blankRE.test(result) ? null : $.parseJSON(result)\n            } catch (e) { error = e }\n\n            if (error) return ajaxError(error, 'parsererror', xhr, settings, deferred)\n          }\n\n          ajaxSuccess(result, xhr, settings, deferred)\n        } else {\n          ajaxError(xhr.statusText || null, xhr.status ? 'error' : 'abort', xhr, settings, deferred)\n        }\n      }\n    }\n\n    if (ajaxBeforeSend(xhr, settings) === false) {\n      xhr.abort()\n      ajaxError(null, 'abort', xhr, settings, deferred)\n      return xhr\n    }\n\n    var async = 'async' in settings ? settings.async : true\n    xhr.open(settings.type, settings.url, async, settings.username, settings.password)\n\n    if (settings.xhrFields) for (name in settings.xhrFields) xhr[name] = settings.xhrFields[name]\n\n    for (name in headers) nativeSetHeader.apply(xhr, headers[name])\n\n    if (settings.timeout > 0) abortTimeout = setTimeout(function(){\n        xhr.onreadystatechange = empty\n        xhr.abort()\n        ajaxError(null, 'timeout', xhr, settings, deferred)\n      }, settings.timeout)\n\n    // avoid sending empty string (#319)\n    xhr.send(settings.data ? settings.data : null)\n    return xhr\n  }\n\n  // handle optional data/success arguments\n  function parseArguments(url, data, success, dataType) {\n    if ($.isFunction(data)) dataType = success, success = data, data = undefined\n    if (!$.isFunction(success)) dataType = success, success = undefined\n    return {\n      url: url\n    , data: data\n    , success: success\n    , dataType: dataType\n    }\n  }\n\n  $.get = function(/* url, data, success, dataType */){\n    return $.ajax(parseArguments.apply(null, arguments))\n  }\n\n  $.post = function(/* url, data, success, dataType */){\n    var options = parseArguments.apply(null, arguments)\n    options.type = 'POST'\n    return $.ajax(options)\n  }\n\n  $.getJSON = function(/* url, data, success */){\n    var options = parseArguments.apply(null, arguments)\n    options.dataType = 'json'\n    return $.ajax(options)\n  }\n\n  $.fn.load = function(url, data, success){\n    if (!this.length) return this\n    var self = this, parts = url.split(/\\s/), selector,\n        options = parseArguments(url, data, success),\n        callback = options.success\n    if (parts.length > 1) options.url = parts[0], selector = parts[1]\n    options.success = function(response){\n      self.html(selector ?\n        $('<div>').html(response.replace(rscript, \"\")).find(selector)\n        : response)\n      callback && callback.apply(self, arguments)\n    }\n    $.ajax(options)\n    return this\n  }\n\n  var escape = encodeURIComponent\n\n  function serialize(params, obj, traditional, scope){\n    var type, array = $.isArray(obj), hash = $.isPlainObject(obj)\n    $.each(obj, function(key, value) {\n      type = $.type(value)\n      if (scope) key = traditional ? scope :\n        scope + '[' + (hash || type == 'object' || type == 'array' ? key : '') + ']'\n      // handle data in serializeArray() format\n      if (!scope && array) params.add(value.name, value.value)\n      // recurse into nested objects\n      else if (type == \"array\" || (!traditional && type == \"object\"))\n        serialize(params, value, traditional, key)\n      else params.add(key, value)\n    })\n  }\n\n  $.param = function(obj, traditional){\n    var params = []\n    params.add = function(key, value) {\n      if ($.isFunction(value)) value = value()\n      if (value == null) value = \"\"\n      this.push(escape(key) + '=' + escape(value))\n    }\n    serialize(params, obj, traditional)\n    return params.join('&').replace(/%20/g, '+')\n  }\n})(Zepto)\n\n;(function($){\n  $.fn.serializeArray = function() {\n    var name, type, result = [],\n      add = function(value) {\n        if (value.forEach) return value.forEach(add)\n        result.push({ name: name, value: value })\n      }\n    if (this[0]) $.each(this[0].elements, function(_, field){\n      type = field.type, name = field.name\n      if (name && field.nodeName.toLowerCase() != 'fieldset' &&\n        !field.disabled && type != 'submit' && type != 'reset' && type != 'button' && type != 'file' &&\n        ((type != 'radio' && type != 'checkbox') || field.checked))\n          add($(field).val())\n    })\n    return result\n  }\n\n  $.fn.serialize = function(){\n    var result = []\n    this.serializeArray().forEach(function(elm){\n      result.push(encodeURIComponent(elm.name) + '=' + encodeURIComponent(elm.value))\n    })\n    return result.join('&')\n  }\n\n  $.fn.submit = function(callback) {\n    if (0 in arguments) this.bind('submit', callback)\n    else if (this.length) {\n      var event = $.Event('submit')\n      this.eq(0).trigger(event)\n      if (!event.isDefaultPrevented()) this.get(0).submit()\n    }\n    return this\n  }\n\n})(Zepto)\n\n;(function(){\n  // getComputedStyle shouldn't freak out when called\n  // without a valid element as argument\n  try {\n    getComputedStyle(undefined)\n  } catch(e) {\n    var nativeGetComputedStyle = getComputedStyle\n    window.getComputedStyle = function(element, pseudoElement){\n      try {\n        return nativeGetComputedStyle(element, pseudoElement)\n      } catch(e) {\n        return null\n      }\n    }\n  }\n})()\n  return Zepto\n}))\n"},function(e,n,t){"use strict";t.r(n),t.d(n,"WadeMobile",(function(){return h})),t.d(n,"Mobile",(function(){return m})),t.d(n,"Common",(function(){return g})),t.d(n,"Wade",(function(){return b}));var a=t(3),r=t(5),o=t(1),i=new function(){this.isApp=function(){return a.a.isApp()},this.isAndroid=function(){return a.a.isAndroid()},this.isIOS=function(){return a.a.isIOS()},this.closeApp=function(){a.a.close(!1)},this.dataRequest=function(e,n){return n=n?o.a.DataMap(n):"",a.a.dataRequest(e,n.toString(),null,null,null)},this.dataRequestWithHost=function(e,n,t){return t=t?o.a.DataMap(t):"",a.a.dataRequestWithHost(e,n,t.toString(),null,null,null)},this.loadUrl=function(e,n){a.a.loadUrl(e,n)},this.openUrl=function(e,n,t,r,o,i){a.a.openUrl(e,n,t,r,o,i)},this.closeUrl=function(e){a.a.closeUrl(e)},this.openH5=a.a.openH5,this.closeH5=a.a.closeH5,this.openPage=function(e,n,t){n=n?o.a.DataMap(n):"",this.savePostParam(n),a.a.openPage(e,n.toString(),t)},this.loadPage=function(e,n,t){n=n?o.a.DataMap(n):"",a.a.loadPage(e,n.toString(),t)},this.openTemplate=function(e,n,t){n=n?o.a.DataMap(n):"",this.savePostParam(n),a.a.openTemplate(e,n.toString(),t)},this.loadTemplate=function(e,n,t){n=n?o.a.DataMap(n):"",a.a.loadTemplate(e,n.toString(),t)},this.getTemplate=function(e,n){return"string"!=typeof(n=n?o.a.DataMap(n):"")&&(n=n.toString()),a.a.getTemplate(e,n)},this.getPage=function(e,n){return"string"!=typeof(n=n?o.a.DataMap(n):"")&&(n=n.toString()),a.a.getPage(e,n)},this.back=function(e,n){a.a.back(e,n)},this.backWithCallback=function(e,n,t){a.a.backWithCallback(e,n,t)},this.savePostParam=function(e){this.setMemoryCache("_page_param",e?e.toString():"")},this.getPostParam=function(){return this.getMemoryCache("_page_param").then(e=>e?JSON.parse(e):e)},this.loadingStart=function(e,n){a.a.loadingStart(e,n)},this.loadingStop=function(){a.a.loadingStop()},this.confirm=function(e,n,t,a){f.confirm(e,n,t,a)},this.tip=function(e,n){f.tip(e,n)},this.alert=function(e,n,t){f.alert(e,n,t)},this.setMemoryCache=function(e,n){1===arguments.length&&(e=o.a.DataMap(e)),r.a.tool.isDataMap(e)?a.a.setMemoryCache(e.map):a.a.setMemoryCache(e,n)},this.getMemoryCache=function(e,n){return a.a.getMemoryCache(e,n)},this.removeMemoryCache=function(e){a.a.removeMemoryCache(e)},this.clearMemoryCache=function(){a.a.clearMemoryCache()},this.downloadImg=function(e){return a.a.downloadImg(e)},this.getSysInfo=function(e){return a.a.getSysInfo(e)},this.openUrlWithPlug=function(e){return a.a.openUrlWithPlug(e)},this.setOfflineCache=function(e,n){1===arguments.length&&(e=o.a.DataMap(e)),r.a.tool.isDataMap(e)?a.a.setOfflineCache(e.map):a.a.setOfflineCache(e,n)},this.getOfflineCache=function(e,n){return a.a.getOfflineCache(e,n)},this.removeOfflineCache=function(e){a.a.removeOfflineCache(e)},this.clearOfflineCache=function(){a.a.clearOfflineCache()},this.openDialog=function(e,n,t,r){return n=n?o.a.DataMap(n):"",t=t||.5,r=r||.5,this.savePostParam(n),a.a.openDialog(e,n.toString(),t,r)},this.closeDialog=function(e){a.a.closeDialog(e)},this.openWindow=function(e,n){return n=n?o.a.DataMap(n):"",this.savePostParam(n),a.a.openWindow(e,n.toString())},this.closeWindow=function(e){void 0!==e&&null!=e?(e=o.a.DataMap(e).toString(),a.a.closeWindow(e)):a.a.closeWindow()},this.openSlidingMenu=function(e,n,t){n=n||"";let r="left"===(t=t||"left")?0:1;return a.a.openSlidingMenu(e,n,.5,1,r,0)},this.closeSlidingMenu=function(e){a.a.closeSlidingMenu(e)},this.execSQL=function(e,n,t,r,o){return a.a.execSQL(e,n,t,r,o)},this.insert=function(e,n,t){return a.a.insert(e,n,t)},this.delete=function(e,n,t,r){return a.a.delete(e,n,t,r)},this.update=function(e,n,t,r,o){return a.a.update(e,n,t,r,o)},this.select=function(e,n,t,r,o,i,s){return a.a.select(e,n,t,r,o,i,s)},this.selectFirst=function(e,n,t,r,o){return a.a.selectFirst(e,n,t,r,o)},this.setScreenLock=function(e){return e=o.a.DataMap(e),a.a.setScreenLock(e.toString())},this.getScreenLockState=function(){return a.a.getScreenLockState()},this.screenUnlock=function(e){return a.a.screenUnlock(e)},this.openKeyboard=function(e,n,t){a.a.openKeyboard(e,n,t)},this.openNative=function(e,n){e=o.a.DataMap(e),a.a.openNative(e.toString(),n)},this.initNfc=function(e,n){e=o.a.DataMap(e),a.a.initNfc(e.toString(),n)},this.closeIpuApp=function(e){a.a.closeIpuApp(e)},this.openIpuApp=function(e,n,t){a.a.openIpuApp(e,n,t)},this.openNativeApp=function(e,n){a.a.openNativeApp(e,n)}},s=t(4),c=t(0),l=new function(){var e;this.isApp=function(){return!1},this.isAndroid=function(){return a.a.isAndroid()},this.isIOS=function(){return a.a.isIOS()},this.closeApp=function(){window.opener=null,window.open("","_self"),window.close()},this.dataRequest=function(e,n){return n=n?o.a.DataMap(n):"",new Promise((t,a)=>{s.a.ajax.post(e,n,e=>{t(Object(c.a)(e))},e=>{a(e)})})},this.openUrl=function(e,n){s.a.redirect.toUrl(e)},this.closeUrl=function(e){a.a.closeUrl(e)},this.openH5=function(e,n,t,r){a.a.openH5(e)},this.closeH5=function(e){history.back()},this.openPage=function(e,n,t){var a,r=s.a.ServerPath;r+="?action="+e,n&&(a={data:n}),this.savePostParam(n),s.a.redirect.postPage(r,a)},this.openTemplate=function(e,n,t){n=n?o.a.DataMap(n):"";var a=s.a.ServerPath;a+="?action="+e;var r=null;n&&(r={data:n,isContext:!0}),this.savePostParam(n),s.a.redirect.postPage(a,r)},this.getTemplate=function(e,n){return new Promise((t,a)=>{s.a.ajax.html(e,n,e=>{t(e)},e=>{a(e)},null,!0)})},this.getPage=function(e,n){return new Promise((t,a)=>{s.a.ajax.html(e,n,e=>{t(e)},e=>{a(e)})})},this.back=function(){history.go(-1)},this.savePostParam=function(e){this.setMemoryCache("_page_param",e)},this.getPostParam=function(){return this.getMemoryCache("_page_param").then(e=>Object(c.a)(e))},this.loadingStart=function(e,n){s.a.browser.loadingStart(e,n)},this.loadingStop=function(){s.a.browser.loadingStop()},this.confirm=function(e,n,t,a){f.confirm(e,n,t,a)},this.tip=function(e,n){f.tip(e,n)},this.alert=function(e,n,t){f.alert(e,n,t)},this.setMemoryCache=function(e,n){1===arguments.length&&(e=o.a.DataMap(e)),s.a.browser.setMemoryCache(e,n)},this.getMemoryCache=function(e,n){return new Promise(t=>{s.a.browser.getMemoryCache(e=>{t(e)},e,n)})},this.removeMemoryCache=function(e){s.a.browser.removeMemoryCache(e)},this.clearMemoryCache=function(){s.a.browser.clearMemoryCache()},this.downloadImg=function(e,n){return new Promise(t=>{s.a.browser.downloadImg(e=>{t(e)},e,n)})},this.getSysInfo=function(e,n){return new Promise(t=>{s.a.browser.getSysInfo(e=>{t(e)},e,n)})},this.openUrlWithPlug=function(e){return new Promise((n,t)=>{s.a.browser.openUrlWithPlug(e,e=>{n(e)},e=>{t(e)})})},this.setOfflineCache=function(e,n){1===arguments.length&&(e=o.a.DataMap(e)),s.a.browser.setOfflineCache(e,n)},this.getOfflineCache=function(e,n){return new Promise(t=>{s.a.browser.getOfflineCache(e=>{t(e)},e,n)})},this.removeOfflineCache=function(e){s.a.browser.removeOfflineCache(e)},this.clearOfflineCache=function(){s.a.browser.clearOfflineCache()};var n,t=!1;this.openWindow=function(n,a){return this.savePostParam(a),a&&(a={data:a}),new Promise(r=>{var o=s.a.redirect.buildUrl(n,null,!0);s.a.redirect.openPostWindow(n+new Date,o,a),e=e=>{r(e)},t=!0})},this.closeWindow=function(n){"string"!=typeof n&&(n=n.toString()),t&&e?(n&&e(n),t=!1):window.opener&&(window.opener.closeWindow(n),window.close())},window.closeWindow=this.closeWindow,window.dialogFlag=!1,this.openDialog=function(e,t){var a=this;return new Promise((r,o)=>{if(window.opener&&window.opener.dialogFlag){throw alert("存在已打开的窗口"),"存在已打开的窗口"}t&&(t={data:t}),a.savePostParam(t);var i=s.a.redirect.buildUrl(e,null,!0);s.a.redirect.openPostWindow(e,i,t),n=e=>{r(e)},window.dialogFlag=!0})},this.closeDialog=function(e){window.opener?(window.opener.closeDialog(e),window.close()):n&&(window.dialogFlag=!1,e&&n(e))},window.closeDialog=this.closeDialog,this.openSlidingMenu=function(e,n,t){alert("浏览器不实现")},this.closeSlidingMenu=function(e){alert("浏览器不实现")},this.execSQL=function(e,n,t,a,r){alert("浏览器不实现")},this.insert=function(e,n,t){alert("浏览器不实现")},this.delete=function(e,n,t,a){alert("浏览器不实现")},this.update=function(e,n,t,a,r){alert("浏览器不实现")},this.select=function(e,n,t,a,r,o,i){alert("浏览器不实现")},this.selectFirst=function(e,n,t,a,r){alert("浏览器不实现")},this.openNative=function(e,n){alert("浏览器不实现")},this.setScreenLock=function(e){alert("浏览器不实现手势锁功能")},this.getScreenLockState=function(){alert("浏览器不实现获取手势锁状态功能")},this.screenUnlock=function(e){alert("浏览器不实现解锁功能")},this.openKeyboard=function(e,n){alert("浏览器不实现打开小键盘")},this.initNfc=function(e,n){alert("浏览器不支持NFC功能")},this.closeIpuApp=function(e){l.tip("关闭子应用...")},this.openIpuApp=function(e,n,t){a.a.openIpuApp(e,n,t)},this.openNativeApp=function(e,n){a.a.openNativeApp(e,n)}},u=l,f=a.a.isApp()?i:u,p=new function(){this.openUrlWithPlug=function(e){return f.openUrlWithPlug(e)},this.openPage=function(e,n,t){n=n?new o.a.DataMap(n):new o.a.DataMap,t=t||function(e,n){f.loadingStop(),-100!=n?n?f.alert("错误编码:["+n+"]\n错误信息:"+e):f.alert("错误信息:"+e):f.closeIpuApp("SESSION_TIMEOUT")},p.get([Constant.SESSION_ID,Constant.STAFF_ID]).then(a=>{var r,i,s;"string"==typeof a&&(a=new o.a.DataMap(a)),a.get(Constant.SESSION_ID)&&n.put(Constant.SESSION_ID,a.get(Constant.SESSION_ID)),a.get(Constant.STAFF_ID)&&n.put(Constant.STAFF_ID,a.get(Constant.STAFF_ID)),r=e,i=n,s=t,f.openPage(r,i,(function(e){"string"==typeof e&&(e=new o.a.DataMap(e));var n=e.get("X_RESULTCODE"),t=e.get("X_RESULTINFO");s(t,n)}))})},this.openWindow=function(e,n){return f.openWindow(e,n)},this.getPostParam=function(){return f.getPostParam()},this.getPage=function(e,n){return n=n?new o.a.DataMap(n):new o.a.DataMap,new Promise((t,a)=>{p.get([Constant.SESSION_ID,Constant.STAFF_ID]).then(r=>{var i,s,c,l;"string"==typeof r&&(r=new o.a.DataMap(r)),r.get(Constant.SESSION_ID)&&n.put(Constant.SESSION_ID,r.get(Constant.SESSION_ID)),r.get(Constant.STAFF_ID)&&n.put(Constant.STAFF_ID,r.get(Constant.STAFF_ID)),i=e,s=n,c=e=>{t(e)},l=e=>{a(e)},f.getPage(i,s).then(c,e=>{"string"==typeof e&&(e=new o.a.DataMap(e)),l(JSON.parse(e.toString()))})})})},this.openMainSubAppPage=function(e,n){this.openAppPage("200001",e,n)},this.openCoreAppPage=function(e,n){this.openAppPage("300038",e,n)},this.openLocalH5=function(e,n,t,a){const r=location.href.lastIndexOf("/",location.href.indexOf(".html")),o=location.href.substring(0,r+1)+e;f.openH5(o,n,t,a)},this.openAppPage=function(e,n,t){const r=t?o.a.DataMap(t).toString():"";return new Promise((t,i)=>{f.savePostParam(r),a.a.openIpuApp(new o.a.DataMap({EXT_PARAM:r,APP_ID:e,MENU_PAGE_ACTION:n}),e=>{t(e)},e=>{i(e)})})},this.getLocationInfo=function(e){a.a.location().then(n=>{a.a.loadingStop();var t=new o.a.DataMap(n);e.put("GPRS_Y",t.get("Longitude")),e.put("GPRS_X",t.get("Latitude")),a.a.isIOS()?e.put("GPRS_ADDRESS",t.get("LocationDesc")):a.a.isAndroid()&&e.put("GPRS_ADDRESS",t.get("ADDRESS"))},e=>{a.a.loadingStop(),a.a.tip(e)})},this.closeApp=function(){confirm("确定要退出应用程序吗?")&&f.closeApp()},this.logoutAccount=function(){confirm("确定要注销该工号吗?")&&(p.remove(Constant.SESSION_ID),WadeMobile.clearBackStack(),f.openTemplate("Home"))},this.put=function(n,t){e(n)&&f.setMemoryCache(n,t)},this.get=function(e,t){return n(e)?f.getMemoryCache(e,t):Promise.reject("参数类型异常")},this.remove=function(e){n(e)&&f.removeMemoryCache(e)},this.clear=function(){f.clearMemoryCache()},this.putLocal=function(n,t){e(n)&&f.setOfflineCache(n,t)},this.getLocal=function(e,t){return n(e)?f.getOfflineCache(e,t):Promise.reject("参数类型异常")},this.removeLocal=function(e){n(e)&&f.removeOfflineCache(e)},this.clearLocal=function(){f.clearOfflineCache()},this.loadingStart=function(e){e||(e="正在加载..."),this.loadingStop(),$("body").append('<div class="mask" id="loading-mask"><div class="mask-loading-content">'+e+"</div></div>")},this.loadingStop=function(){try{$("#loading-mask").remove()}catch(e){}};function e(e){return!(!e||"string"!=typeof e&&!r.a.tool.isDataMap(e))||(f.alert(e+"参数类型异常"),!1)}function n(e){return!(!e||"string"!=typeof e&&!r.a.tool.isArray(e))||(f.alert(e+"参数类型异常"),!1)}this.execSQL=function(e,n){return f.execSQL("display",e,n)}};window.Constant={OPEN_PAGE_KEY:"OPEN_PAGE_KEY",STAFF_ID:"STAFF_ID",SESSION_ID:"SESSION_ID",X_RECORDNUM:"X_RECORDNUM",X_RESULTCODE:"X_RESULTCODE",X_RESULTINFO:"X_RESULTINFO",X_RESULTCAUSE:"X_RESULTCAUSE",TRACE_ID:"traceId",RESP_DATA:"respData"};var d=p;const h=a.a,m=f,g=d,b=o.a}])}));