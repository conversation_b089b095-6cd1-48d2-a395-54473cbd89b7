import browserTool from './browser-toolkit';
import <PERSON> from "./jcl";
import mobile from './mobile';
//wadeMobile中单独定义了pc端的openIpuApp实现，此处引入wadeMobile是为了将该方法提到Mobile中
import WadeMobile from "./wadeMobile";
import {safeTrans} from './util';

var PAGE_PARAM_KEY = "_page_param";

var Mobile = new function() {
    /******************系统功能**********************/
    /*判断是否App*/
    this.isApp = function(){
        return false;
    };
    this.isAndroid = function () {
        return WadeMobile.isAndroid();
    };
    this.isIOS = function () {
        return WadeMobile.isIOS();
    };
    /*关闭应用 */
    this.closeApp = function() {
        window.opener=null;
        window.open('','_self');
        window.close();
    };
    /******************数据请求**********************/
    /*调用服务*/
    this.dataRequest = function(action, param) {
        param = param ? Wade.DataMap(param) : "";
        return new Promise((resolve, reject) => {
            browserTool.ajax.post(action, param, (data) => {
                resolve(safeTrans(data));
            }, (err) => {
                reject(err);
            });
        })
    };
    /******************页面跳转**********************/
    /*页面跳转,url为跳转目标*/
    this.openUrl = function (url, err) {
        browserTool.redirect.toUrl(url);
    };
    this.closeUrl = function (result) {
        WadeMobile.closeUrl(result);
    };
    // this.openH5 = function (url, {title="", buttons=[], style=['#1e96fa' ,'#000000', '#ff0000'], uas=[], cookies=[], hideTopBar=false, refreshFlag=false}, callback) {
    //     WadeMobile.openH5(url);
    // };
    // this.closeH5 = function (param) {
    //     history.back();
    // };
    this.openH5 = function (url, cookies, callback, err) {
        WadeMobile.openH5(url);
    };
    this.closeH5 = function (param) {
        history.back();
    };
    /*页面跳转,param为打开页面时调用接口的参数*/
    this.openPage = function(pageName, param, err) {
        var url = browserTool.ServerPath;
        url += "?action=" + pageName;
        var params;
        if (param) {
            params = {data:param};
        }
        this.savePostParam(param);
        browserTool.redirect.postPage(url, params);
        /*
         * get方式不适用了
         * var url = browserTool.redirect.buildUrl(pageName,
         * param.toString()); browserTool.redirect.toUrl(url);
         */
    };

    /*页面跳转,param为打开页面的映射数据*/
    this.openTemplate = function(pageName, param, err) {
        param = param ? Wade.DataMap(param) : "";
        var url = browserTool.ServerPath;
        url += "?action=" + pageName;
        var params = null;
        if (param) {
            params = {data:param,isContext:true};
        }
        this.savePostParam(param);
        browserTool.redirect.postPage(url, params);
    };
    /*将模板转换成html源码*/
    this.getTemplate = function(action, param) {
        return new Promise((resolve, reject) => {
            browserTool.ajax.html(action, param, (data) => {
                resolve(data);
            }, (err) => {
                reject(err);
            }, null, true);
        })
    };
    /*将Page转换成html源码*/
    this.getPage = function(action, param) {
        return new Promise((resolve, reject) => {
            browserTool.ajax.html(action, param, (data) => {
                resolve(data);
            }, (err) => {
                reject(err);
            });
        })
    };
    /*回退到前一个界面*/
    this.back = function(){
        history.go(-1);
    };
    /** 保存post到界面的参数 */
    this.savePostParam  = function (param) {
        this.setMemoryCache(PAGE_PARAM_KEY, param);
    };
    /** 获取post到界面的参数 */
    this.getPostParam = function () {
        return this.getMemoryCache(PAGE_PARAM_KEY).then((value) => {
            return safeTrans(value);
        });
    };
    /******************基础UI**********************/
    /*打开loading对话框*/
    this.loadingStart = function(message,title){
        browserTool.browser.loadingStart(message,title);
    };
    /*关闭加载中对话框*/
    this.loadingStop = function(){
        browserTool.browser.loadingStop();
    };

    /* 确认提示框 */
    this.confirm = function(title, msg, btn, callback){
        mobile.confirm(title, msg, btn, callback);
    };
    /*弹出提示气泡*/
    this.tip = function(msg,callback){
        mobile.tip(msg,callback);
    };
    this.alert = function(msg,callback,title){
        mobile.alert(msg,callback,title);
    };
    /******************内存缓存**********************/
    this.setMemoryCache = function(key, value){
        if (arguments.length === 1) {
            key = Wade.DataMap(key);
        }
        browserTool.browser.setMemoryCache(key, value);
    };
    this.getMemoryCache = function(key, value){
        return new Promise((resolve) => {
            browserTool.browser.getMemoryCache((data) => {
                resolve(data);
            },key, value);
        })
    };
    this.removeMemoryCache = function(key){
        browserTool.browser.removeMemoryCache(key);
    };
    this.clearMemoryCache = function(){
        browserTool.browser.clearMemoryCache();
    };
    this.downloadImg = function (data,version) {
        return new Promise((resolve) => {
            browserTool.browser.downloadImg((data) => {
                resolve(data);
            },data,version);
        })
    };
    this.getSysInfo = function (data,version) {
        return new Promise((resolve) => {
            browserTool.browser.getSysInfo((data) => {
                resolve(data);
            },data,version);
        })
    };
    this.openUrlWithPlug = function (url) {
        return new Promise((resolve, reject) => {
            browserTool.browser.openUrlWithPlug(url,(data) => {
                resolve(data);
            },(err) => {
                reject(err);
            });
        })
    }
    /******************离线缓存**********************/
    this.setOfflineCache = function(key, value){
        if (arguments.length === 1) {
            key = Wade.DataMap(key);
        }
        browserTool.browser.setOfflineCache(key, value);
    };
    this.getOfflineCache = function(key, value){
        return new Promise((resolve) => {
            browserTool.browser.getOfflineCache((data) => {
                resolve(data);
            }, key, value);
        })
    };
    this.removeOfflineCache = function(key){
        browserTool.browser.removeOfflineCache(key);
    };
    this.clearOfflineCache = function(){
        browserTool.browser.clearOfflineCache();
    };
    /******************扩展UI**********************/
    var windowCallback;//关闭窗口时的回调函数
    var windowFlag = false;//关闭窗口的标识
    /*打开窗口*/
    this.openWindow = function(pageAction, param) {

        this.savePostParam(param);
        if(param){
            param = {data:param} //转换json格式
        }
        return new Promise((resolve) => {
            var url = browserTool.redirect.buildUrl(pageAction, null, true);
            browserTool.redirect.openPostWindow(pageAction + new Date(), url, param);
            windowCallback = (data) => {
                resolve(data);
            };
            windowFlag = true;
        })
    };
    /*关闭窗口*/
    this.closeWindow = function(result) {
        if(typeof(result) != "string"){
            result = result.toString();
        }
        if (windowFlag&&windowCallback) {//windowFlag标识可以防止递归关闭所有窗口
            if(result){//返回值为空不执行回调
                windowCallback(result);
            }
            windowFlag = false;
        } else if(window.opener){
            window.opener.closeWindow(result);
            window.close();
        }
    };
    window.closeWindow = this.closeWindow;//让方法全局化,提供给window.opener调用

    var dialogCallback;//关闭对话框时的回调函数
    window.dialogFlag = false;//用于控制不能多次打开对话框
    /*打开对话框*/
    this.openDialog = function(pageAction, param) {
        var _this = this;
        return new Promise((resolve, reject) => {
            if(window.opener&&window.opener.dialogFlag){
                var err = "存在已打开的窗口";
                alert(err);
                throw err;
            }
            if(param){
                param = {data:param} //转换json格式
            }
            _this.savePostParam(param);
            var url = browserTool.redirect.buildUrl(pageAction, null, true);
            browserTool.redirect.openPostWindow(pageAction, url, param);
            dialogCallback = (data) => {
                resolve(data);
            };
            window.dialogFlag = true;
        })
    };
    /*关闭对话框*/
    this.closeDialog = function(result) {
        if (window.opener) {
            window.opener.closeDialog(result);
            window.close();
        } else if(dialogCallback) {
            window.dialogFlag = false;
            if(result){//返回值为空不执行回调
                dialogCallback(result);
            }
        }
    };
    window.closeDialog = this.closeDialog;//让方法全局化,提供给window.opener调用

    /*打开侧滑菜单*/
    this.openSlidingMenu = function(action,param,type){//type:left|right
        alert("浏览器不实现");
    }
    /*关闭侧滑菜单*/
    this.closeSlidingMenu = function(result){
        alert("浏览器不实现");
    }
    /******************本地数据库操作**********************/
    this.execSQL = function(dbName,sql,bindArgs,limit,offset){
        alert("浏览器不实现");
    };
    this.insert = function(dbName,table,datas){
        alert("浏览器不实现");
    };
    this.delete = function(dbName,table,condSQL,conds){
        alert("浏览器不实现");
    };
    this.update = function(dbName,table,datas,condSQL,conds){
        alert("浏览器不实现");
    };
    this.select = function(dbName,table,columns,condSQL,conds,limit,offset){
        alert("浏览器不实现");
    };
    //查询第一行数据,效率高
    this.selectFirst = function(dbName,table,columns,condSQL,conds){
        alert("浏览器不实现");
    };
    this.openNative = function(data,err){
        alert("浏览器不实现");
    }
    // 设置手势锁
    this.setScreenLock = function(dataParam) {
        alert("浏览器不实现手势锁功能");
    }
    // 获取手势锁状态
    this.getScreenLockState = function() {
        alert("浏览器不实现获取手势锁状态功能");
    }
    // 解锁
    this.screenUnlock = function(forgetPageAction){
        alert("浏览器不实现解锁功能");
    }
    // 打开小键盘
    this.openKeyboard = function(value,err){
        alert("浏览器不实现打开小键盘");
    }
    // 初始化NFC
    this.initNfc = function(data,err){
        alert("浏览器不支持NFC功能");
    }
    this.closeIpuApp = function(data){
        //window.close();
        Mobile.tip("关闭子应用...");
    }
    this.openIpuApp = function (param, callback, err) {
        WadeMobile.openIpuApp(param, callback, err);
    }
    this.openNativeApp = function (param, err) {
        WadeMobile.openNativeApp(param, err);
    }
}
export default Mobile;