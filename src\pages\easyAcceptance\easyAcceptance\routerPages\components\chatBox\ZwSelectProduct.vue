<template>
  <div >
      <div class="selectProduct" id="selectProduct">
        <div class="box" v-for="(item,index) in zwInfoList" :key="index" >
          <div class="tag" :style="{ backgroundColor: item.color }">{{item.title}}</div>
           <div class="cell1" v-if="item.SHORT_NAME" v-html="item.SHORT_NAME"></div>
          <div class="cell2"  v-if="item.RENT"   v-html="item.RENT"></div>
          <div class="cell ellipsis" v-if="!item.SHORT_NAME&&!item.RENT"><van-popover placement="top" v-model="item.showPopover" trigger="click">
            <van-grid
                square
                clickable
                :border="false"
                column-num="1"
                style="width: 120px; "
            >
              <van-grid-item
                  style="font-weight: bold"
                  :text="item.iptvName"
                  @click="item.showPopover = false"
              />
            </van-grid>
            <template #reference>
              {{item.iptvName.length>30?item.iptvName.slice(0,30)+'...':item.iptvName}}
            </template>
          </van-popover></div>
          <van-button class="btn" @click="goodClick(item,$event)" >{{item.itemTip}}</van-button>
        </div>
      </div>
    <div v-if="showGoodName">您当前选择的商品是：{{showGoodName}}</div>

  </div>
</template>
<script>
import {mapActions, mapMutations, mapState} from "vuex";

export default {
  name:  "ZwSelectProduct",
  data(){
    return {
      radio: '',
      colors:['rgb(129,211,248)','rgb(251,6,6)','#71bc8c'],
      selectedGooIds: [],
      choosedIPTV:"",
      iptvInvalid:[],
      iptvInfoCheckList:[],
      zwInfoList: [
      ],
      showGoodName:'',
    }
  },
  
  mounted() {
    this.zwOrderData.zwGoodData.zwList=[];
    this.zwOrderData.zwGoodData.commodityChooseYWUlList=[];
    this.setZwOrderData(this.zwOrderData);
    this.zwInfoList=[];
    let colorLength=this.colors.length
    console.log(this.zwReProductList,"ZwReProductList")
    if(this.zwReProductList.length>0){
      for(let i=0;i<this.zwReProductList.length;i++){
        let title=''
        if(this.zwReProductList[i].catName=='WIFI6'){
          title='WIFI6'
        }
        else{
          if(this.isEmpty(this.zwReProductList[i].catName)){
            if(this.zwReProductList[i].iptvName.indexOf("WIFI6")!=-1){
              title='WIFI6'
            }else{
              title='FTTR'
            }
          }else{
            if(this.zwReProductList[i].catName.indexOf(",")!=-1){
              let c=this.zwReProductList[i].catName.split(",")
              title=c[c.length-1]
            }else{
              title=this.zwReProductList[i].catName
            }
          }
        }
        let iptvInfo={
          title:title,
          ancestors:this.zwReProductList[i].ancestors,
          SHORT_NAME:this.zwReProductList[i].SHORT_NAME,
          commodityCode:this.zwReProductList[i].commodityCode,
          commType:this.zwReProductList[i].commType,
          iptvName:this.zwReProductList[i].iptvName,
          RENT:this.zwReProductList[i].RENT,
          itemTip:'立即购买',
          color:this.colors[i%colorLength],
          showPopover:false
        }
          this.zwInfoList.push(iptvInfo)
      }
    }
  },
  computed: {
    ...mapState([
      'jzfkOrderData',
      'shbmMsgInfo',
      'iptvCacheList',
      'zwReProductList',
      'iptvCacheList',
       'zwOrderData'
    ])
  },
  methods:{
    ...mapMutations([
      'setFlowStep',
      'setRobotWorking',
      'setZwOrderData',
        'setZwOrderData'
    ]),
    ...mapActions(['updateChatList']),
    isEmpty(value) {
      let flag = false
      if (value == '' || value == 'undefined' || value == undefined || value == null || value == 'null') {
        flag = true
      }
      return flag
    },
    zwClickFee(zw){
      let data = {
        commId: zw.commodityCode,
        onlineMonths:this.zwOrderData.checkNumberData.onlineMonths,
        ancestors:zw.ancestors
      }
      this.$http.post('/zhzwZsd/qryProduct', data).then(res => {
        if (res.respCode == "0000") {
          // 接收返回参数
          let orderPrice=this.zwOrderData.orderPrice;
          orderPrice+=parseInt(res.respData.goodsPrice);
          this.zwOrderData.orderPrice=orderPrice;
          this.zwOrderData.autoDeduct=res.respData.autoDeduct;
          this.setZwOrderData(this.zwOrderData)
        }
      else{
          this.$toast(res.resMsg);
        }
      }).catch(e => {
       this.$toast(e)
      })
    },
    goodClick(item,e){
      this.showGoodName = item.iptvName
      const $this = $(e.target);
      if ($this.hasClass("btn")) {
        $this.addClass("active-btn")
        $this.parent().addClass("red-border")
        $this.removeClass("btn")
        $this.siblings().removeClass("active-btn")
        $this.siblings().removeClass("red-border")
        // 如果未选中，则添加
        item.itemTip='已选择'
        this.zwOrderData.zwGoodData.zwList=[];
        this.selectedGooIds.push(item.commodityCode);
        let zw={
          commodityCode:item.commodityCode,
          zwName:item.iptvName,
          commType:item.commType
        }
        this.zwOrderData.zwGoodData.zwList.push(zw);
        this.setZwOrderData(this.zwOrderData);
        this.zwClickFee(item)
        let data = {
          inputType: "1",
          type: '1',
          textInput: "zwGoodSubmit",
          notifyFlag: '',
          taskName:'智能组网甩单'
        }
        this.$emit('newChatApi', data);
      } else {
        $this.parent().removeClass("red-border")
        $this.removeClass("active-btn")
        $this.addClass("btn");
        item.itemTip='立即购买'
        this.selectedGooIds = this.selectedGooIds.filter(i => i !== item.commodityCode);
        this.zwClickFee(item)
      }
    }
  }
}
</script>
<style scoped lang="scss">
.red-border{
  border: rgb(221,248,255) 2px solid;
}
.selectProduct {
  display: flex;
  flex-wrap: nowrap; /* 确保子元素不换行 */
  overflow-x: scroll;  /* 启用水平滚动 */
  -webkit-overflow-scrolling: touch; /* 改善移动设备上滚动的体验 */
  .box {
    position: relative;
    border-radius: 10px;
    margin: 0 10px 10px 0;
    flex: 0 0 calc(24%); /* 使用 calc() 考虑 margin */
    max-width: 120px; /* 同样考虑 margin */
    background: linear-gradient(to bottom right, #fff, #EDF0FF);
    padding: 0 0 10px 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .tag {
      color: #fff;
      border-radius: 10px 0 10px 0;
      padding: 0 6px;
      font-size: 11px;
      margin-left: -3px;
      width: 45%;
      text-align: center;
    }
    .cell {
      margin: 6px 0;
      width: 85px;
      text-align: center;
      font-weight: bold;
      font-size: 13px;
      line-height: 15px;
     }
    .cell1 {
      margin: 6px 0;
      width: 95px;
      text-align: center;
      font-weight: bold;
      font-size: 13px;
      line-height: 15px;
     }
    .cell2 {
      margin-bottom: 6px;
      width: 100%;
      text-align: center;
      font-size: 11px;
      line-height: 16px;
     }
    .btn {
      border: 2px solid #4494E6;
      border-radius: 10px;
      color: #4494E6;
      height: 25px;
      font-size: 11px;
      padding: 5px 8px;
      background: none;
      margin-left: 17%;
      align-self: flex-start;
     }
    .active-btn {
      border: 2px solid #4494E6;
      border-radius: 10px;
      color: #fff;
      background: #4494E6!important;
      height: 25px;
      font-size: 11px;
      padding: 5px 8px;
      background: none;
      margin-left: 17%;
      align-self: flex-start;
     }
  }
  /* WebKit 浏览器滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  ::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

  /* Firefox 移动端浏览器 */
  .scrollable-element {
    scrollbar-width: auto;
    scrollbar-color: #888 #f1f1f1;
  }

  /* 元素样式 */
  .scrollable-element {
    width: 300px;
    height: 200px;
    overflow: auto;
    border: 1px solid #ccc;
    padding: 10px;
    -webkit-overflow-scrolling: touch;
  }
  @-webkit-keyframes showScrollbar {
    from {
      opacity: 1;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes showScrollbar {
    from {
      opacity: 1;
    }
    to {
      opacity: 1;
    }
  }
}
</style>