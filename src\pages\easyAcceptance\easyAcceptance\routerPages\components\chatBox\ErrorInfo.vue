<template>
    <div>
        <div class="part" style="margin-bottom: 0;">
            <p class="mb5">{{ params.title }} 
                <span v-show="!showDetail && params.hasDetail">（点击 
                    <span class="blue underline" @click="showDetail = true">查看详情</span>）
                </span>
            </p>
            <p>
                <span v-if="params.tip" class="mt5">{{ params.tip }}</span>
                <span v-show="params.tryAgainBtn">点击 
                    <span class="underline" :class="{'disabled': tryAgainDisabled, 'blue': !tryAgainDisabled}" @click="tryAgain">重试</span>
                </span>
            </p>
            <p v-show="showDetail" class="error-detail">{{ params.errorName }}：{{ params.errorDetail }}
                <span class="blue underline ml10" @click="showDetail = false">收起</span>
            </p>
        </div>
    </div>
</template>

<script>
import {mapActions, mapMutations} from 'vuex';

export default {
    name: 'ErrorInfo',
    props: {
        params: {
            title: '',
            tip: '',
            errorName: '',
            errorDetail: '',
            hasDetail: true,
            tryAgainBtn: false,
            moduleName: '',
            params: {}
        }
    },
    data() {
        return {
            showDetail: false,
            tryAgainDisabled: false
        }
    },
    async created() {},
    methods: {
      ...mapActions([
        'updateChatList'
      ]),
        // 重试
        tryAgain() {
            if (!this.tryAgainDisabled) {
                this.tryAgainDisabled = true
                this.updateChatList({
                    sender: '1',
                    type: 'module',
                    moduleName: this.params.moduleName,
                    moduleLevel: 1,
                    params: this.params.params,
                    show: false
                })
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.error-detail {
    color: #666666;
    font-size: 13px;
    margin-top: 5px;
    border-top: 1px solid #ececec;
    padding-top: 5px;
}

.underline.disabled {
    color: #999999;
}
</style>
