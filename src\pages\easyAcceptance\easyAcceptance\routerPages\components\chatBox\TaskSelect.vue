<template>
    <div class="TaskSelect">
        <div class="header">请选择您要继续办理的业务</div>
        <div class="content">
            <van-radio-group v-model="taskId">
              <van-cell-group v-for="(task, i) in runTimeTaskList_" :key="i">
                <van-cell :title="task.task_name" clickable @click="taskId = task.task_id">
                  <template #right-icon>
                    <van-radio :name="task.task_id" />
                  </template>
                </van-cell>
              </van-cell-group>
            </van-radio-group>
        </div>
        <div class="footer">
            <van-button block size="small" type="info" :loading="loading" :disabled="!taskId" @click="confirmFn">确定</van-button>
        </div>
    </div>
</template>

<script>
import { alertError } from '@/assets/bizComponents/funcComponent.js'
import {mapState, mapMutations, mapActions} from 'vuex'
import * as _ from 'lodash';
export default {
    name: 'TaskSelect',
    props: {
        params: {
            type: Object,
            default() {
                return {
                }
            }
        }
    },
    data() {
        return {
            taskId: '',
            loading: false,
            runTimeTaskList_: [],
        }
    },
    computed: {
        ...mapState([
            'agentSessionId',
            'runTimeTaskList',
            'flowStep',
            'chatList',
            'curTask',
        ])
    },
    mounted() {
        this.runTimeTaskList_ = _.cloneDeep(this.runTimeTaskList)
    },
    methods: {
        ...mapMutations([
            'setCurTask',
        ]),
      ...mapActions(['updateChatList']),
        confirmFn() {
            if (this.loading) return
            const task = this.runTimeTaskList_.find(v => v.task_id === this.taskId) || {}
            const { tool_list = [], tool_code = '' } = task
            this.loading = true
            this.updateTask(task.task_id, '01').then(res => {
                if (res.respCode === '0000') {
                    this.setCurTask({
                        taskId: task.task_id,
                        taskName: task.task_name,
                        taskStatus: '01',
                        toolCode: (tool_list[0] || {}).tool_code || tool_code || '',
                    })
                    this.renderFn(task)
                } else {
                    this.$toast.fail(res.respMsg)
                }
                this.loading = false
            }).catch(e => {
                alertError({
                    title: '出错了！',
                    message: e,
                    confirmButtonText: '报告错误'
                });
                this.loading = false
            });
        },
        // 更新任务状态
        updateTask(taskId, status) {
            const params = {
                agentSessionId: this.agentSessionId,
                taskId: taskId || this.curTask?.taskId || '',
                taskStatus: status
            }
            if (!params.taskId) return
            var qs = require('qs');
            return this.$http.post('/smartFlowChat/updateTaskStatus', params)
        },
        // 渲染对话列表
        renderFn(task) {
            this.updateChatList({
                sender: 'divideLine',
                text: `以下为${task.task_name}`,
                show: true
            })
            if (task.task_status === '00') {
                // 获取该任务的输入文字
                let sender0 = (this.chatList || []).slice().reverse().find(v => v.sender === '0' && v.moduleName != 'ConfirmBox' && v.moduleName != 'TaskSelect' && v.moduleName != 'ErrorInfo' && v.taskId == this.curTask.taskId) || null
                if (!sender0) {
                    this.sendDefaultMsg()
                } else {
                    const text = sender0?.params?.text || sender0?.content || ''
                    this.$emit('newChatApi', {
                        inputType: '0', // 0-文本 1-动作
                        type: '1', // 1-文字输入,2-语音输入
                        textInput: text, // 文字输入内容
                    })
                }
            } else {
                // 取任务组件信息
                let params = (this.chatList || []).slice().reverse().find(v => v.sender === '1' && v.moduleName != 'ConfirmBox' && v.moduleName != 'TaskSelect' && v.moduleName != 'ErrorInfo' && v.taskId == this.curTask.taskId) || null
                if (params) {
                    this.updateChatList(params)
                } else {
                    // 取不到组件信息则渲染：请重新输入信息
                    this.sendDefaultMsg()
                }
            }
        },
        // 发送默认信息
        sendDefaultMsg() {
            this.updateChatList({
                sender: '1',
                type: 'module',
                moduleName: 'TextResponse',
                moduleLevel: 1,
                params: {
                  text: '请重新输入信息'
                },
                show: true
            })
        },
    },
    beforeDestroy() {
        
    }
};
</script>

<style lang="scss" scoped>
    .TaskSelect {
        .header {
            margin-bottom: 10px;
        }
        .content {
            max-height: 216px;
            overflow: auto;
        }
    }
</style>
