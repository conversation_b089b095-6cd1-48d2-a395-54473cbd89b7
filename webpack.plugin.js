/* eslint-env node */
const path = require('path');

module.exports = {
    entry: './src/assets/js/beePlugin/index.js',
    output: {
        path: path.resolve(__dirname, './src/assets/js/'),
        filename: 'bee-plugin.js',
        libraryTarget: 'umd',
        library: 'beePlugin'
    },
    module: {
        rules: [
            {
                test: require.resolve('zepto'),
                loader: 'exports-loader?window.Zepto!script-loader'
            }
        ]
    }
};
