import WadeMobile from './wadeMobile';
import clientTool from './client-toolkit';
import mobile from './mobile';
import <PERSON> from './jcl';

var PAGE_PARAM_KEY = "_page_param";

var Mobile = new function () {
    /******************系统功能**********************/
    /*判断是否App*/
    this.isApp = function () {
        return WadeMobile.isApp();
    };
    this.isAndroid = function () {
        return WadeMobile.isAndroid();
    };
    this.isIOS = function () {
        return WadeMobile.isIOS();
    };
    /*关闭应用*/
    this.closeApp = function () {
        WadeMobile.close(false);
    };
    /******************数据请求**********************/
    /*调用服务*/
    this.dataRequest = function (action, param) {
        param = param ? Wade.DataMap(param) : "";
        return WadeMobile.dataRequest(action, param.toString(), null, null, null);
    };
    /*调用指定服务端地址服务*/
    this.dataRequestWithHost = function (url, action, param) {
        param = param ? Wade.DataMap(param) : "";
        return WadeMobile.dataRequestWithHost(url, action, param.toString(), null, null, null);
    };
    /******************页面跳转**********************/
    /*页面跳转,url为跳转目标*/
    this.loadUrl = function (url, err) {
        WadeMobile.loadUrl(url, err);
    };
    /*新开一个activity,页面跳转,url为跳转目标*/
    this.openUrl = function (url, callback, title, buttons, styles, err) {
        WadeMobile.openUrl(url, callback, title, buttons, styles, err);
    };
    this.closeUrl = function (result) {
        WadeMobile.closeUrl(result);
    };
    // this.openH5 = function (url, {title="", buttons=[], style=['#1e96fa' ,'#000000', '#ff0000'], uas=[], cookies=[], hideTopBar=false, refreshFlag=false}, callback) {
    //     WadeMobile.openPushInUrl(url, [title, buttons, style, uas, cookies, hideTopBar, refreshFlag], null, null, callback)
    // };
    // this.closeH5 = function (param) {
    //     WadeMobile.closePushInUrl(param);
    // };
    this.openH5 = WadeMobile.openH5;
    this.closeH5 = WadeMobile.closeH5;
    /*页面跳转,param为打开页面时调用接口的参数*/
    this.openPage = function (pageAction, param, err) {
        param = param ? Wade.DataMap(param) : "";
        this.savePostParam(param);
        WadeMobile.openPage(pageAction, param.toString(), err);
    };
    this.loadPage = function (pageAction, param, err) {
        param = param ? Wade.DataMap(param) : "";
        WadeMobile.loadPage(pageAction, param.toString(), err);
    };
    /*页面跳转,param为打开页面的映射数据*/
    this.openTemplate = function (pageAction, param, err) {
        param = param ? Wade.DataMap(param) : "";
        this.savePostParam(param);
        WadeMobile.openTemplate(pageAction, param.toString(), err);
    };
    this.loadTemplate = function (pageAction, param, err) {
        param = param ? Wade.DataMap(param) : "";
        WadeMobile.loadTemplate(pageAction, param.toString(), err);
    };
    /*将模板转换成html源码*/
    this.getTemplate = function (action, param) {
        param = param ? Wade.DataMap(param) : "";
        if (typeof(param) != "string") {
            param = param.toString();
        }
        return WadeMobile.getTemplate(action, param);
    };
    /*将Page转换成html源码*/
    this.getPage = function (action, param) {
        param = param ? Wade.DataMap(param) : "";
        if (typeof(param) != "string") {
            param = param.toString();
        }
        return WadeMobile.getPage(action, param);
    };
    /*回退到前一个界面*/
    this.back = function (tag, err) {
        WadeMobile.back(tag, err);
    };
    /*带回调的回退*/
    this.backWithCallback = function (data, tag, err) {
        WadeMobile.backWithCallback(data, tag, err);
    };
    /** 保存post到界面的参数 */
    this.savePostParam  = function (param) {
        this.setMemoryCache(PAGE_PARAM_KEY, param ? param.toString() : "");
    };
    /** 获取post到界面的参数 */
    this.getPostParam = function () {
        return this.getMemoryCache(PAGE_PARAM_KEY).then(value => {
            return value ? JSON.parse(value) : value;
        })
    };
    /******************基础UI**********************/
    /*打开loading对话框*/
    this.loadingStart = function (message, title) {
        WadeMobile.loadingStart(message, title);
    };
    /*关闭加载中对话框*/
    this.loadingStop = function () {
        WadeMobile.loadingStop();
    };

    /* 确认提示框 */
    this.confirm = function(title, msg, btn, callback){
        mobile.confirm(title, msg, btn, callback);
    };
    /*弹出提示气泡*/
    this.tip = function(msg,callback){
        mobile.tip(msg,callback);
    };
    this.alert = function(msg,callback,title){
        mobile.alert(msg,callback,title);
    };

    /******************内存缓存**********************/
    this.setMemoryCache = function (key, value) {
        if (arguments.length === 1) {
            key = Wade.DataMap(key);
        }
        if (clientTool.tool.isDataMap(key)) {
            WadeMobile.setMemoryCache(key.map);
        } else {
            WadeMobile.setMemoryCache(key, value);
        }
    };
    this.getMemoryCache = function (key, defValue) {
        return WadeMobile.getMemoryCache(key, defValue);
    };
    this.removeMemoryCache = function (key) {
        WadeMobile.removeMemoryCache(key);
    };
    this.clearMemoryCache = function () {
        WadeMobile.clearMemoryCache();
    };
    this.downloadImg = function (data) {
        return WadeMobile.downloadImg(data);
    };
    this.getSysInfo = function (key) {
        return WadeMobile.getSysInfo(key);
    };
    this.openUrlWithPlug = function (url) {
        return WadeMobile.openUrlWithPlug(url);
    }
    /******************离线缓存**********************/
    this.setOfflineCache = function (key, value) {
        if (arguments.length === 1) {
            key = Wade.DataMap(key);
        }
        if (clientTool.tool.isDataMap(key)) {
            WadeMobile.setOfflineCache(key.map);
        } else {
            WadeMobile.setOfflineCache(key, value);
        }
    };
    this.getOfflineCache = function (key, value) {
        return WadeMobile.getOfflineCache(key, value);
    };
    this.removeOfflineCache = function (key) {
        WadeMobile.removeOfflineCache(key);
    };
    this.clearOfflineCache = function () {
        WadeMobile.clearOfflineCache();
    };
    /******************扩展UI**********************/
    this.openDialog = function (pageAction, param, width, height) {
        param = param ? Wade.DataMap(param) : "";
        width = width ? width : 0.5;//默认0.5
        height = height ? height : 0.5;
        this.savePostParam(param);
        return WadeMobile.openDialog(pageAction, param.toString(), width, height);
    };
    this.closeDialog = function (result) {
        WadeMobile.closeDialog(result);
    };
    this.openWindow = function (pageAction, param) {
        param = param ? Wade.DataMap(param) : "";
        this.savePostParam(param);
        return WadeMobile.openWindow(pageAction, param.toString());
    };
    this.closeWindow = function (result) {
        if (typeof(result) == "undefined" || result == null) {
            WadeMobile.closeWindow();
            return;
        }
        result = Wade.DataMap(result).toString();
        WadeMobile.closeWindow(result);
    };
    /*打开侧滑菜单*/
    this.openSlidingMenu = function (action, param, type) {//type:left|right
        type = type ? type : "left";
        param = param ? param : "";
        let leftMargin = type === "left" ? 0 : 1;
        return WadeMobile.openSlidingMenu(action, param, 0.5, 1, leftMargin, 0);
    }
    /*关闭侧滑菜单*/
    this.closeSlidingMenu = function (result) {
        WadeMobile.closeSlidingMenu(result);
    }
    /******************本地数据库操作**********************/
    this.execSQL = function (dbName, sql, bindArgs, limit, offset) {
        return WadeMobile.execSQL(dbName, sql, bindArgs, limit, offset);
    };
    this.insert = function (dbName, table, datas) {
        return WadeMobile.insert(dbName, table, datas);
    };
    this.delete = function (dbName, table, condSQL, conds) {
        return WadeMobile.delete(dbName, table, condSQL, conds);
    };
    this.update = function (dbName, table, datas, condSQL, conds) {
        return  WadeMobile.update(dbName, table, datas, condSQL, conds);
    };
    this.select = function (dbName, table, columns, condSQL, conds, limit, offset) {
        return  WadeMobile.select(dbName, table, columns, condSQL, conds, limit, offset);
    };
    //查询第一行数据,效率高
    this.selectFirst = function (dbName, table, columns, condSQL, conds) {
        return WadeMobile.selectFirst(dbName, table, columns, condSQL, conds);
    };
    // 设置手势锁
    this.setScreenLock = function (dataParam) {
        dataParam = Wade.DataMap(dataParam);
        return WadeMobile.setScreenLock(dataParam.toString());
    }
    // 获取手势锁状态
    this.getScreenLockState = function () {
        return WadeMobile.getScreenLockState();
    }
    // 解锁
    this.screenUnlock = function (forgetPageAction) {
        return WadeMobile.screenUnlock(forgetPageAction);
    }
    // 打开小键盘
    this.openKeyboard = function (funcName, options, err) {
        WadeMobile.openKeyboard(funcName, options, err);
    }
    this.openNative = function (data, err) {
        data = Wade.DataMap(data);
        WadeMobile.openNative(data.toString(), err);
    }
    // 初始化NFC
    this.initNfc = function (data, err) {
        data = Wade.DataMap(data);
        WadeMobile.initNfc(data.toString(), err);
    }
    this.closeIpuApp = function (pageAction) {
        WadeMobile.closeIpuApp(pageAction);
    }
    this.openIpuApp = function (param, callback, err) {
        WadeMobile.openIpuApp(param, callback, err);
    }
    this.openNativeApp = function (param, err) {
        WadeMobile.openNativeApp(param, err);
    }
};

export default Mobile;