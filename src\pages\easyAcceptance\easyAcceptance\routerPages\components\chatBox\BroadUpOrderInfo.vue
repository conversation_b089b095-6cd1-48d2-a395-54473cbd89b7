<template>
  <div class="card-verification" style="background-color: white">
    <div class="subpreview-box" v-for="(item,index1) in showList" :key="index1" @click="item.showAll=!item.showAll">
      <div :style="{background: item.showAll ? '#E5F0FF':'#EDF7FF'}" class="total-title">
        <span class="custom-label-name">{{ item.name }}</span>
        <van-icon :name="item.showAll ? 'arrow-up':'arrow'" />
      </div>
      <div v-for="(item1,index) in item.itemList" :key="index" style="padding: 0 10px;">
        <div v-show="item.showAll" style="display:flex">
          <span class="custom-title flex-shink">{{ item1.text }}</span>
          <span class="custom-title">{{item1.value}}</span>
        </div>
      </div>
    </div>
    <div class="submit-container">
      <van-button class="sub" @click="preSubmit()" block>提交</van-button>
    </div>
  </div>
</template>
<script>
import WadeMobile from "rk-native-plugin";
import errorTips from '@/assets/bizComponents/errorTips/errorTips.vue';
import {copyText} from "../../../assets/js/func";
import {mapActions, mapMutations, mapState} from "vuex";
import {decrypted} from "../../../../../../assets/js/AESUtil";
export default {
  name: "BroadUpOrderInfo",
  data() {
    return {
      imageList: [
        require('../../../images/arrow.png'),
        require('../../../images/add.png')
      ],
      showList: [
      
      ],
   
    }
  },
  components: {
    errorTips
  },
  computed: {
    ...mapState([
      'chatList',
      'sessionId',
      'staffId',
      'flowStep',
      'num',
      'shbmMsgInfo',
      'jzfkOrderData',
      'activeModuleIndex',
      'loginPhoneNumber',
      'instanceId',
        'broadUpOrderData'
    ])
  },
  mounted(){
    this.setRespTipArrQry([]);
    this.setBlockShow(false);
    this.checkOrderDetail()
    console.log('tgybyhbhbh')
    console.log(this.broadUpOrderData.broadUpGoodData.commodityChooseYWUlList)
    console.log(this.broadUpOrderData.broadUpGoodData.broadUpList)
    let zwString='';
  
    if(this.broadUpOrderData.broadUpGoodData.broadUpList.length>0){
      for(let i=0;i<this.broadUpOrderData.broadUpGoodData.broadUpList.length-1;i++){
        zwString+=this.broadUpOrderData.broadUpGoodData.broadUpList[i].commodityName+",";
      }
      zwString+=this.broadUpOrderData.broadUpGoodData.broadUpList[this.broadUpOrderData.broadUpGoodData.broadUpList.length-1].commodityName;
    }
    this.showList=[
      {
        name: '客户信息',
        showAll: true,
        itemList:[
          {text:'宽带号码：',value:this.broadUpOrderData.checkNumberData.serialNumber},
          {text:'客户名称：',value:this.broadUpOrderData.checkNumberData.showCustName}
        ]
      },{
        name: '商品信息',
        showAll: true,
        itemList:[
          {text:'定制商品：',value:zwString}]
      },{
        name: '装机信息',
        showAll: false,
        itemList:[
          {text:'装机地址：',value:this.broadUpOrderData.checkNumberData.showInstallAddrTm}
        ]
      },
      {
        name: '费用信息',
        showAll: true,
        itemList:[
          {text:'商品费用：',value:this.broadUpOrderData.orderPrice+"元"},
        ]
      }
    ]
  },
  methods: {
    ...mapMutations([
      'setFlowStep',
      'setRobotWorking',
      'setBroadUpOrderData',
        'setRespTipArrQry',
        'setBlockShow'
    ]),
    ...mapActions(['updateChatList']),
    isEmpty(value) {
      let flag = false
      if (value == '' || value == 'undefined' || value == undefined || value == null || value == 'null') {
        flag = true
      }
      return flag
    },

    uniqueByProperty(arr, prop) {
      const uniqueMap = new Map();
      return arr.reduce((acc, current) => {
        const key = current[prop];
        if (!uniqueMap.has(key)) {
          uniqueMap.set(key, true);
          acc.push(current);
        }
        return acc;
      }, []);
    },
    checkOrderDetail(){
   WadeMobile.getSysInfo("PLATFORM").then((info) => {
      this.broadUpOrderData.platfForm=info;
      this.setBroadUpOrderData(this.broadUpOrderData);
      console.log("PLATFORM：：" + info);
    }, (err) => {
      console.log("PLATFORM"+"失败：" + err);
    });
   },
    preSubmit(){
      
      if(this.broadUpOrderData.broadUpGoodData.broadUpList.length==0){
        this.$toast('请选择定制商品')
        return;
      }
      this.$emit('startLoading', '')
      let zwInfoList={};
      if(this.broadUpOrderData.broadUpGoodData.broadUpList.length>0){
        let zw={
          commodityCode:this.broadUpOrderData.broadUpGoodData.broadUpList[0].commodityCode,
          commodityName:this.broadUpOrderData.broadUpGoodData.broadUpList[0].commodityName,
        }
        zwInfoList=zw;
      }
      let  stdContact=this.broadUpOrderData.timeData.contactName;
      if(this.isEmpty(stdContact)){
        stdContact=this.broadUpOrderData.checkNumberData.custName;
      }
      if(stdContact==this.broadUpOrderData.checkNumberData.showCustName){
        stdContact=this.broadUpOrderData.checkNumberData.custName;
      }
      let stdContactPhone=this.broadUpOrderData.timeData.contactPhone;
      let communityAddrInfo={
        exchCode:this.broadUpOrderData.checkNumberData.showExchCode,
        addressName:this.broadUpOrderData.checkNumberData.showInstallAddr,
      addressCode:this.broadUpOrderData.checkNumberData.showAddrCode,
      stdContact:stdContact,
       stdContactPhone:stdContactPhone,
        stdBookDay:this.broadUpOrderData.timeData.stdBookDay,
        stdBookTime:this.broadUpOrderData.timeData.stdBookTime
      }
      let rhRelationMemberMsg={
        userTypeFlag: this.broadUpOrderData.checkNumberData.isRHUserTag,
        kdProductId:this.broadUpOrderData.checkNumberData.kdProductId ,
        kdProductName:this.broadUpOrderData.checkNumberData.kdProductName
      }
      if("0"==this.broadUpOrderData.checkNumberData.isRHUserTag){
        rhRelationMemberMsg={
          userTypeFlag: this.broadUpOrderData.checkNumberData.isRHUserTag
        }
      }
      let req={
        changeCommodityInfo:zwInfoList,
        communityAddrInfo:communityAddrInfo,
        serviceNumber:decrypted(this.broadUpOrderData.checkNumberData.kdNumberJm),
        choiceValue:this.broadUpOrderData.choiceValue,
        rhRelationMemberMsg:rhRelationMemberMsg,
        orderPrice:this.broadUpOrderData.orderPrice,
        isBookingTimeFlagNew:this.broadUpOrderData.checkNumberData.isBookingTimeFlagNew,
        remark:"备注:"+this.broadUpOrderData.remark+" 预约安装时间:"+this.broadUpOrderData.timeData.stdBookDay+" "+this.broadUpOrderData.timeData.stdBookTime
      }
      
      this.$http.post('/broadbandUpSpeed/preSubmit',req).then((res)=>{
        this.$emit('endLoading', '')
        if (res.respCode === '0000') {
          this.broadUpOrderData.orderId=res.respData.orderId;
          this.updateChatList({
            sender: '1',
            type: 'module',
            moduleName: 'TextResponse',
            moduleLevel: 1,
            params: {
              text: '订单提交成功!订单号'+res.respData.orderId
            },
            show: true
          })
          this.setBroadUpOrderData(this.broadUpOrderData);
          let data = {
            inputType: "1",
            type: '1',
            textInput: "broadUpOrderInfoSubmit",
            notifyFlag: '',
            taskName:'宽带提速智能甩单'
          }
          this.$emit('newChatApi', data);
        }
        else{
          this.updateChatList({
            sender: '1',
            type: 'module',
            moduleName: 'TextResponse',
            moduleLevel: 1,
            params: {
              text: res.respMsg
            },
            show: true
          })
        }      })
    }
  }
}
</script>
<style lang="scss">
.card-verification {
  width:100%;
  margin: auto;
  .card-group {
    background-color: white;
    margin: 0px 10px;
    padding:0px 5px;
    .tip {
      .van-cell__value {
        color: black;
        text-align: right;
      }}

    .card-list {
      .van-cell__title {
        font-size: 13px;
        color: #666666;
      }
      .van-cell__value {
        color: black;
        text-align: right;
        font-size: 13px;
      }
      .van-cell {
        color: black !important;
        padding: 0px 16px;
      }
      .van-cell::after {
        color: black !important;
        border-bottom: 0;

      }
    }
    .van-hairline--top-bottom::after, .van-hairline-unset--top-bottom::after {
      border-width: 0 0;
    }
  }
  .card {
    .van-cell__title {
      font-size: 13px;
      color: #000000;
    }
    .checkButton {
      width: 70px;
      height: 30px;
      background: #0081FF;
      border-radius: 4px;
    }
    .checkFont {
      font-size: 13px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 14px;
    }
  }
  .check{
    .van-cell__title {
      font-size: 13px;
      color: #333333;
    }
  }
}

</style>

<style lang="scss" scoped>
.card-verification {
  background-color: rgb(57,159,254);
  .subpreview-box{
    border-radius: 10px;
    margin-bottom: 10px;
    box-shadow: 0 2px 1px 0 #eee;
    .total-title{
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 5px 10px;
      width: 93%;
      border-radius: 10px 10px 0 0;
      margin-bottom: 10px;
      .custom-label-name{
        font-size: 15px;
        line-height: 30px;
        color: #263A5F;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
      }
    }
    .custom-title{
      font-family: PingFangSC-Regular;
      font-size: 14px;
      line-height: 26px;
      color: #263A5F;
    }
    .flex-shink{
      flex-shrink: 0;
    }
  }
  .desc {
    color: black;
    font-size: 13px;
    line-height: 20px;
  }
  .margin-two {
    bottom: 10px;
    left: 15px;
    right: 15px;
    z-index: 2;
    position: absolute;
  }
  .van-buttons {
    border-radius: 4px;
    height: 44px;
    font-size: 13px;
    color: #FFFFFF;
    background-color: #0081FF;
  }
  .font {
    color:#333333;
  }
  .submit-container{
    .sub{
      border: 2px solid rgba(73,124,246,1);
      border-radius: 5px;
      color: rgba(73,124,246,1);
      margin-top: 20px;
    }
  }
}

</style>