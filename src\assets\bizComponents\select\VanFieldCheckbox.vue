<template>
  <div class="dh-field">
    <div class="van-hairline--bottom">
      <van-field
          v-model="resultLabel"
          v-bind="$attrs"
          readonly
          label="请选择超清商品"
          :disabled="$attrs.disabled"
          :is-link="$attrs.disabled === undefined"
          error-message-align='left'
          input-align="left"
          class="dh-cell"
          label-width="86%"
          :rules="[{ required: $attrs.required, message: '请选择' + $attrs.label }]"
          @click="showPopu($attrs.disabled)"
      />
      <van-popup v-model="show" position="bottom" class="" >
        <van-field style="margin-left: 5%;width: 90%;border-radius: 10px;padding: 6px;" v-if="isSearch" v-model="searchVal" input-align="left" placeholder="请输入搜索内容" @input="search"/>
<!--        <div class="van-picker__toolbar">-->
<!--          <button type="button" class="van-picker__cancel" @click="cancel">取消</button>-->
<!--          <div class="van-ellipsis van-picker__title">{{$attrs.label}}</div>-->
<!--          <button type="button" class="van-picker__confirm" @click="onConfirm">确认</button>-->
<!--        </div>-->
        <div class="popup-list">
        
          <van-cell title="全选" style="color: #263A5F;background: none;">
            <template #right-icon>
              <van-checkbox v-model="checkedAll" name="all" @click="toggleAll"/>
            </template>
          </van-cell>
          <van-checkbox-group ref="checkboxGroup" v-model="checkboxValue">
            <van-cell-group>
              <van-cell
                  v-for="(item, index) in columnsData"
                  :key="item[option.value]"
                  :title="item[option.label]"
                  clickable
                  style="color: #263A5F;background: none;"
                  @click="handleCellClick(item, index)"
              >
                <template #right-icon>
                  <van-checkbox @click="toggle(item,index)" style="margin-left: 30px;" ref="checkboxes" :name="item.value" />
                </template>
              </van-cell>
            </van-cell-group>
          </van-checkbox-group>
        </div>
        <button type="button" class="van-picker__confirm" @click="onConfirm">确认</button>
      </van-popup>
    </div>
  </div>
</template>

<script>
import {mapState} from "vuex";

export default {
  name: 'VanFieldCheckbox',
  model: {
    prop: 'selectValue'
  },
  props: {
    columns: {
      type: Array,
      default: function () {
        return []
      }
    },
    selectValue: {
      type: Array,
      default: function () {
        return []
      }
    },
    option: {
      type: Object,
      default: function () {
        return { label: 'label', value: 'value' }
      }
    },
    // 是否支持搜索
    isSearch: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    resultLabel: {
      get () {
        const res = this.columns.filter(item => {
          return this.resultValue.indexOf(item[this.option.value]) > -1
        })
        const resLabel = res.map(item => {
          return item[this.option.label]
        })
        return resLabel.join(',')
      },
      set () {

      }
    },
    ...mapState([
      'jzfkOrderData',
      'iptvOrderData'
    ])
  },
  data () {
    return {
      show: false,
      searchVal: '',
      columnsData: JSON.parse(JSON.stringify(this.columns)),
      checkboxValue: JSON.parse(JSON.stringify(this.selectValue)),
      checkedAll: false,
      resultValue: JSON.parse(JSON.stringify(this.selectValue)),
      label:'',
    }
  },
 
  mounted() {

  },
  methods: {
    // 搜索
    search (val) {
      if (val) {
        this.columnsData = this.columnsData.filter(item => {
          return item[this.option.label].indexOf(val) > -1
        })
      } else {
        this.columnsData = JSON.parse(JSON.stringify(this.columns))
      }
    },
    getData (val) {
        const res = this.columnsData.filter(item => {
          return val.indexOf(item[this.option.value]) > -1
        })
      return res;
    },
   
    
    
    onConfirm () {
      this.resultValue = this.checkboxValue
      this.show = !this.show
      this.$emit('confirm', this.resultValue, this.getData(this.resultValue))
    },
    change (val) {
      
     console.log(val)
      console.log("change")
      let c=this.$emit('checkBookedNotChooseNew',val[val.length-1]);
      console.log(c,"====是否可选")
      if(c){
        this.$emit('change', val, this.getData(this.resultValue))

      }
      
    },
    cancel () {
      this.show = !this.show
      this.$emit('cancel', this.resultValue)
    },
    handleCellClick(item,index){
      const checkbox = this.$refs.checkboxes[index];
      if (checkbox) {
        checkbox.$el.click();
      }
    },
    toggle (item,index) {
      if(this.$refs.checkboxes[index].checked){
        let data = {
          
            "iptvQryCommId": item.value,
            "iptvInvalidList": JSON.stringify(this.iptvOrderData.checkNumberData.iptvInvalid),
            "iptvInfoCheckList":JSON.stringify(this.iptvOrderData.checkNumberData.iptvInfoCheckList)
        }
        this.$http.post('/iptvReceive/checkBookedNotChoose', data).then(res => {
          if (res.respCode !== "0000") {
            this.$toast({
              message:res.respMsg
            })
            this.$refs.checkboxes[index].checked = false
            this.onConfirm();
          }
          this.$emit('checkBookedNotChooseNew')
        }).catch(e => {
          this.$dialog.alert({
            title:"提示：",
            message:e
          })
        })
      }
    },
    toggleAll (all) {
      this.$refs.checkboxGroup.toggleAll(this.checkedAll)
    },
    showPopu (disabled) {
      this.columnsData = JSON.parse(JSON.stringify(this.columns))
      this.checkboxValue = JSON.parse(JSON.stringify(this.selectValue))
      this.resultValue = JSON.parse(JSON.stringify(this.selectValue))
      if (disabled !== undefined && disabled !== false) {
        return false
      } else {
        this.show = !this.show
      }
    }
  },
  watch: {
    selectValue: function (newVal) {
      this.resultValue = newVal
    },
    resultValue (val) {
      this.searchVal = ''
      this.columnsData = JSON.parse(JSON.stringify(this.columns))
      this.$emit('input', val)
    },
    columnsData: {
      handler (val) {
        if (val.length && val.length === this.checkboxValue.length) {
          this.checkedAll = true
        } else {
          this.checkedAll = false
        }
      },
      immediate: true
    },
    checkboxValue: {
      handler (val) {
        if (val.length && val.length === this.columnsData.length) {
          this.checkedAll = true
        } else {
          this.checkedAll = false
        }
      },
      immediate: true
    }
  }
}
</script>

<style lang="less" scoped>
.dh-field {
  padding: 0;
  background:#fff;
   .van-hairline--bottom{
    padding: 10px;
     .dh-cell.van-cell {
       background: #EDF7FF;
       border-radius: 8px;
       padding: 8px;
     }
     .dh-cell.van-cell--required::before {
       left: -8px;
     }
     .van-popup {
       padding: 20px 0;
       border-radius: 20px 20px 0 0;
       background-image: linear-gradient(179deg, #E5F0FF 0%, #FFFFFF 36%);
       box-shadow: 0px -4px 14px 4px rgba(0,0,0,0.09);
       .popup-list{
         max-height:264px;
         overflow-y:auto;
         padding: 5px 15px;
       }
       .van-picker__confirm{
         width: 90%;
         border: 2px solid rgba(73, 124, 246, 1);
         border-radius: 8px;
         color: rgba(73, 124, 246, 1);
         padding: 10px 0;
         margin-left: 4%;
         margin-top: 20px;
       }
     }
   }
}
</style>
<style scoped>
.dh-field {
  .van-hairline--bottom{
    .van-field__label{
      color: #263A5F;
      font-weight: bold;
      font-size: 15px;
    }
    .van-cell__right-icon{
      color: #263A5F;
      font-weight: bold;
    }
    .van-popup {
      .popup-list{
        .van-cell{
          color: #263A5F;
          background: none;
        }
        .van-field__control {
          text-align: left;
        }

        .van-field__control::-webkit-input-placeholder {
          text-align: center;
          direction: ltr;
        }

        .van-field__control::-moz-placeholder {
          text-align: center;
          direction: ltr;
        }

        .van-field__control:-ms-input-placeholder {
          text-align: center;
          direction: ltr;
        }

        .van-field__control::-ms-input-placeholder {
          text-align: center;
          direction: ltr;
        }
      }
    }
  }
}
</style>