<template>
    <div class="error-tips">
        <van-dialog    title="提示"
                       v-model="showDialog"
                       @close="handleClose">
            <div class="error-style">
                <span><span style="color:#0081FF;">问题描述:</span>{{errorTip}}</span><br/>
                <span><span style="color:#0081FF;">解决办法:</span>{{suggestion}}</span><br/>
                <span><span style="color:#0081FF;">问题详情：</span>{{orgErrorInfo}}</span>
            </div>
        </van-dialog>
    </div>

</template>

<script>
    export default {
        name: "error-tips",
        // 接受父组件传递的值
        props:{
                errorTip: {
                    type: String,
                    required: true,
                },
                suggestion: {
                    type: String,
                    required: true,
                },
                orgErrorInfo: {
                    type: String,
                    required: true,
                },
                isShow: {
                    type: Boolean,
                    required: true,
                    default:false
                },
        },
        data () {
            return {
                // 控制弹出框显示隐藏
                showDialog:false

            };
        },
        components: {},

        computed: {},

        mounted () {},

        methods: {
            // 弹出框关闭后触发
            handleClose(){
                // 子组件调用父组件方法，并传递参数
                this.$emit('changeShow','false')
            },
        },
        watch:{
            // 监听 addOrUpdateVisible 改变
            isShow: function (newVal, oldVal) {
                // console.log(newVal);
                this.showDialog = newVal;
            },

        }
    }
</script>

<style lang='scss' scoped>
    .error-tips{
        .error-style{
            width:auto;
            height: auto;
            padding:10px 10px;
            font-size: 16px;
            line-height: 30px;
            color: #999999;
            margin-left: 15px;
        }
    }
</style>