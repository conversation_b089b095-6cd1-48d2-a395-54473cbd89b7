<template>
  <div>
   <div v-if="respTipArrQry.length>0">
     <div class="responseTip">
       <div :class="item.codeValue==='filter'?'box1':'box'"  v-if="!(item.codeValue==='submit'||item.codeValue==='yes'||item.codeValue==='no'||item.codeValue==='refuse'||checkIfContains(item.codeValue, 'GoPage'))" v-for="(item,index) in respTipArrQry" :key="index" @click="respTipClick(item)">{{item.codeDesc}}</div>
     </div>
     <div class="responseTip2" :style="respTipArrQry[0].codeDesc.length>5&&(!isEmpty(respTipArrQry[1].codeDesc))&&respTipArrQry[1].codeDesc.length>5?'width:85%':'width:35%'" >
       <div class="box" v-if="item.codeValue==='submit'||item.codeValue==='yes'||item.codeValue==='no'||item.codeValue==='refuse'||checkIfContains(item.codeValue, 'GoPage')" v-for="(item,index) in respTipArrQry" :key="index" @click="respTipClick(item)" >{{item.codeDesc}}</div>
     </div>
   </div>
    <div v-else></div>
  </div>
</template>
<script>
    import {mapMutations, mapState} from "vuex";
    import WadeMobile from "rk-native-plugin";
    import {Wade} from "rk-web-utils";

export default {
  name: "responseTip",
  data(){
    return {
      respTipArr: []}
  },
  mounted(){
    this.respTipArr=this.respTipArrQry
  },
  computed: {
    ...mapState([
      'respTipArrQry'
    ])
  },
  methods:{
      ...mapMutations(["updateChatList", "setNeedWait","setBlockShow","setRespTipArrQry"]),
      isEmpty(value) {
      let flag = false
      if (value == '' || value == 'undefined' || value == undefined || value == null || value == 'null') {
        flag = true
      }
      return flag
    },
      checkIfContains(str, sub) {
          return str.includes(sub);
      },
    respTipClick(item){
      if('filter'==item.codeValue){
      }else if('needBigModel'==item.codeValue){
          let data = {
              textInput: item.codeDesc,
              inputType: "0",
              type: '1'
          }
          this.$emit('userInput', data)
      }else if('submitGoPageRH'==item.codeValue){
          this.setBlockShow(false);
          this.setRespTipArrQry([]);
          this.updateChatList({
              sender: '0',
              type: 'text',
              content: "确定",
              show: true
          })
           WadeMobile.openH5('https://wxxapp.chinaunicom.cn:10070/touch_sub_front/openAccountForMix.html?intlAccptFlag=1', null, (result) => {
            console.log(result);
          });

      }else if('submitGoPageYW'==item.codeValue){
          this.setBlockShow(false);
          this.setRespTipArrQry([]);
          this.updateChatList({
              sender: '0',
              type: 'text',
              content: "确定",
              show: true
          })
          WadeMobile.openH5('https://wxxapp.chinaunicom.cn:10070/touch_sub_front/newBusinessMobile.html?intlAccptFlag=1', null, (result) => {
            console.log(result);
          });
      }else if('submitGoPageFK'==item.codeValue){
          this.setBlockShow(false);
          this.setRespTipArrQry([]);
          this.updateChatList({
              sender: '0',
              type: 'text',
              content: "确定",
              show: true
          })
          WadeMobile.openIpuApp(new Wade.DataMap({
            APP_ID: "300038",
            MENU_PAGE_ACTION: "addOtherCard",
            MENU_WELCOME_PAGE: "welcome/welcome.htm",
            EXT_PARAM: JSON.stringify({
              intlAccptFlag:"1"
            })
          }));
      }else if(item.codeValue=='refuseGoPage'){
          this.setBlockShow(false);
          this.setRespTipArrQry([]);
          this.updateChatList({
              sender: '0',
              type: 'text',
              content: "取消",
              show: true
          })
          this.updateChatList({
              sender: "1",
              type: "module",
              moduleName: "TextResponse",
              moduleLevel: 1,
              params: {
                  text: "好的，请您继续受理别的智能业务",
              },
              show: true,
          });
      }
      else{
        let data = {
          textInput: item.codeDesc,
          textInputCode: item.codeValue,
          inputType: "1",
          type: '1'
        }
        this.$emit('userInputForLabel', data)
      }

    }
  }
}

</script>
<style scoped lang="scss">
.responseTip{
  width: 70%;
  position: fixed;
  left: 10px;
  bottom: 65px;
  display: flex;
  flex-wrap: nowrap;
  overflow-x: scroll;
  z-index:1112;
  -webkit-overflow-scrolling: touch; /* 开启 iOS 设备的弹性滚动效果 */
  /* 隐藏滚动条（WebKit 内核浏览器，适用于大多数移动端） */
  -ms-overflow-style: none; /* 针对 IE 和 Edge */
  scrollbar-width: none; /* 针对 Firefox */
  .box1{
    padding: 4px 0px;
    margin-right: 1px;
    white-space: nowrap;
    font-size: 14px;
    line-height: 22px;
    height: 22px;
  }
  .box{
    border: 1px solid rgb(68,142,254);
    background: #fff;
    border-radius: 10px;
    padding: 4px 12px;
    margin-right: 10px;
    color: #263A5F;
    cursor: pointer;
    white-space: nowrap;
    font-size: 14px;
    line-height: 22px;
    height: 22px;
  }
}
.responseTip2{
  width: 30%;
  position: fixed;
  right: 10px;
  bottom: 65px;
  display: flex;
  flex-wrap: nowrap;
  justify-content: flex-end;
  overflow-x: hidden;
  z-index:1112;
  .box {
    border: 1px solid rgb(68,142,254);
    background: #fff;
    border-radius: 10px;
    padding: 4px 12px;
    margin-left: 10px;
    color: #263A5F;
    cursor: pointer;
    white-space: nowrap;
    font-size: 14px;
    line-height: 22px;
    height: 22px;
  }
}

.scrollable-x-compatible::-webkit-scrollbar {
  height: 0;
}
.scrollable-x::-webkit-scrollbar {
  display: none;
}
</style>