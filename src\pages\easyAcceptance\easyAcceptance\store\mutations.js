import { CUST_TYPE, HANDLING_TYPE } from '../assets/js/constants';

export default {
    /**
     * 设置sessionId
     */
    setSessionId: (state, payload) => {
        state.sessionId = payload;
    },
    /**
     * 设置登录工号
     */
    setStaffId: (state, payload) => {
        state.staffId = payload;
        // state.staffId = '********' // todo-wq
    },
    /**
     * 设置鉴权信息
     */
    setLoginInfo: (state, payload) => {
        state.loginInfo = payload
    },
    setShowOrderList: (state, payload) => {
        state.showOrderList = payload
    },
    setOrderInfoLists: (state, payload) => {
        state.orderInfoLists = payload
    },
    setFormatedDate: (state, payload) => {
        state.formattedDate = payload
    },
    setOutCallMonetOrderData: (state, payload) => {
        state.outCallMonetOrderData = payload
    },
    setFusionUpFttrOrderData: (state, payload) => {
        state.fusionUpFttrOrderData = payload
    },
    setFusionUpFttrReYwProductList: (state, payload) => {
        state.fusionUpFttrReYwProductList = payload
    },
    // 单移网
    setSingleModeNetworkOrderData: (state, payload) => {
        state.singleModeNetworkOrderData = payload;
    }, // 以网商品
    setYwSingleModeNetworkList: (state, payload) => {
        state.singleModeNetworkYwProductList = payload;
    }, // 合约商品
    setSingleModeNetworkList: (state, payload) => {
        state.singleModeNetworkProductList = payload;
    },
    setWslProductLimitList: (state, payload) => {
        state.wslProductLimitList = payload
    },
    setYskZwCacheList: (state, payload) => {
        state.yskZwCacheList = payload
    },
    setBroadUpReProductList: (state, payload) => {
        state.broadUpReProductList = payload
    },
    setYskKdfCacheList: (state, payload) => {
        state.yskKdfCacheList = payload
    },
    setYskYwfCacheList: (state, payload) => {
        state.yskYwfCacheList = payload
    },

    setZwOrderPrice: (state, payload) => {
        state.zwOrderPrice = payload
    },
    setKdOrderPrice: (state, payload) => {
        state.kdOrderPrice = payload
    },
    setIptvOrderPrice: (state, payload) => {
        state.iptvOrderPrice = payload
    },
    
    setYskCustCacheList: (state, payload) => {
        state.yskYwfCacheList = payload
    },
    setYskIptvCacheList: (state, payload) => {
        state.yskIptvCacheList = payload
    },
    setBroadUpOrderData: (state, payload) => {
        state.broadUpOrderData = payload
    },
    setReqDatas: (state, payload) => {
        state.reqDatas = payload
    },
    setOrderInfoDetail: (state, payload) => {
        state.orderInfoDetail = payload
    },
    /**
     * 设置登录手机号
     */
    setLoginPhoneNumber: (state, payload) => {
        state.loginPhoneNumber = payload;
    },
    /**
     * 更新对话列表
     */
    updateChatList: (state, payload) => {
        // 需要用户进行处理的模块
        if (payload.sender === '1' && payload.type === 'module' && payload.moduleLevel === 1 ) {
            // 将之前所有处理模块置为disabled
            state.chatList.forEach(item => {
                if (item.sender === '1' && state.noDisableModules.indexOf(item.moduleName) === -1 ) {
                    item.disabled = true // 用来控制遮罩层
                }
            })
            if (state.noDisableModules.indexOf(payload.moduleName) === -1) {
                // 用来控制用户输入“提交”或者“确认”时，调用哪个模块对应的方法
                payload.moduleId = state.needProcessModuleLength // 给这些模块设置一个id
                state.activeModuleIndex = state.needProcessModuleLength // 当前正在处理的模块Id值等于最后一个添加的模块
                ++ state.needProcessModuleLength // 模块列表长度+1
                payload.disabled = false // 用来控制遮罩层
            }
        }
        payload.taskId = state.curTask?.taskId || ''
        state.chatList.push(payload);
    },
    setFusionUpFttrReProductList: (state, payload) => {
        state.fusionUpFttrReProductList = payload
    },
    /**
     * 自由回溯
     */
    backToAnyStep: (state, stepInfo) => {
        return new Promise(resolve => {
            const stepList = stepInfo.stepList
            // let arrIndex = 0
            // if (stepInfo.stepIndex !== 0) {
            //     arrIndex = state.chatList.findIndex(item => {
            //         return item.moduleName === stepInfo.name
            //     })
            // }
            // let newChatList = state.chatList.slice(0, arrIndex)
            // state.chatList = newChatList
            // state.chatList.forEach(item => {
            //     if (item.sender === '1' && item.type === 'module' && item.moduleName !== 'TextResponse' && item.moduleLevel === 1 ) {
            //         state.needProcessModuleLength ++
            //     }
            // })
            state.kdOrderPrice=stepInfo.kdOrderPrice
            state.iptvOrderPrice=stepInfo.iptvOrderPrice
            state.zwOrderPrice=stepInfo.zwOrderPrice
            state.jzfkOrderData=stepInfo.jzfkOrderData
            state.iptvOrderData=stepInfo.iptvOrderData
            state.zwOrderData=stepInfo.zwOrderData
            state.broadUpOrderData=stepInfo.broadUpOrderData
            state.iptvCacheList=stepInfo.iptvCacheList
            state.zwCacheList=stepInfo.zwCacheList
            state.isFour=stepInfo.isFour
            state.broadUpReProductList = stepInfo.broadUpReProductList
            state.fusionUpFttrReProductList=stepInfo.fusionUpFttrReProductList
            state.fusionUpFttrOrderData=stepInfo.fusionUpFttrOrderData
            state.fusionUpFttrReYwProductList=stepInfo.fusionUpFttrReYwProductList
            state.flowStep = stepInfo.stepIndex
            state.respTipArrQry = stepInfo.respTipArrQry
            state.remark = ''
            state.reqDatas = stepInfo.reqDatas
            state.yskZwCacheList = stepInfo.yskZwCacheList
            state.yskKdfCacheList = stepInfo.yskKdfCacheList
            state.yskYwfCacheList = stepInfo.yskYwfCacheList
            state.yskCustCacheList = stepInfo.yskCustCacheList
            state.yskIptvCacheList = stepInfo.yskIptvCacheList
            state.zwOrderPrice = stepInfo.zwOrderPrice
            state.iptvOrderPrice = stepInfo.iptvOrderPrice
            state.kdOrderPrice = stepInfo.kdOrderPrice
            state.blockShow = stepInfo.blockShow
            state.cacheQryPara = stepInfo.cacheQryPara
            state.outCallMonetOrderData = stepInfo.outCallMonetOrderData
            state.iptvReYwProductList = stepInfo.iptvReYwProductList
            state.iptvReProductList = stepInfo.iptvReProductList
            // 回溯到二宽及其之前的节点
            if (stepInfo.stepIndex <= stepList.indexOf('addSecondBroadband')) {
                state.secondBroadbandInfo = {}
            }
            // 回溯到选择成员商品及其之前的节点
            if (stepInfo.stepIndex <= stepList.indexOf('changeChildPackage')) {
                state.commonChildren = {}
            }
            // 回溯到虚拟用户及其之前的节点
            if (stepInfo.stepIndex <= stepList.indexOf('userSelect')) {
                state.userSelectInfo = {}
            }
            // 回溯到融合定制商品及其之前的节点
            if (stepInfo.stepIndex <= stepList.indexOf('mixRecommendation')) {
                state.commonProduct = {}
                state.iData = {
                    fee: "", 
                    flow: "", 
                    downLoad: "", 
                    voice: "" 
                }
            }
            // 回溯到客户认证及其之前的节点
            if (stepInfo.stepIndex <= stepList.indexOf('custAuth')) {
                state.userAmount = ''
                state.certTimes = ''
                state.custInfo = {}
            }
            // 回溯到号码校验节点
            if (stepInfo.stepIndex <= stepList.indexOf('numCheck')) {
                state.numberInfo = {}
            }
            // 回到会话开始节点
            if (stepInfo.stepIndex === 0) {
                state.tradeId = ''
                state.paperlessFlag = false
                state.savedFeeList = []
                state.busiOrderId = ''
            }
            // 清空对话
            if (stepInfo.clearChat) {
                state.activeModuleIndex = 0
                state.chatList = []
                state.needProcessModuleLength = 0
            }
            resolve()
        })
    },
    /**
     * 设置号码校验信息
     */
    setNumberInfo: (state, payload) => {
        state.numberInfo = payload;
    },
    /**
    /**
     * 设置虚拟用户信息
     */
    setUserSelectInfo: (state, payload) => {
        state.userSelectInfo = payload;
    },
    /**
     * 设置业务流水号
     */
    setTradeId: (state, payload) => {
        state.tradeId = payload;
          // TODO: 测试代码，生产环境的测试单号
        //   state.tradeId = '2024061401120419';
    },
    /**
     * 设置省分权限编码
     */
    setStaffDataCodes: (state, payload) => {
        state.staffDataCodes = payload;
    },
    /**
     * 设置备注
     */
    setRemark: (state, payload) => {
        state.remark = payload;
    },
    /**
     * 设置一证五户数
     */
    setUserAmount: (state, payload) => {
        state.userAmount = payload;
    },
    /**
     * 设置一证五次数
     */
    setCertTimes: (state, payload) => {
        state.certTimes = payload;
    },
    /**
     * 设置客户认证数据
     */
    setCustInfo: (state, payload) => {
        state.custInfo = payload;
    },
    /**
     * 设置是否完成无纸化签名标识
     */
    setPaperlessFlag: (state, payload) => {
        state.paperlessFlag = payload;
    },
    /**
     * 设置费用明细
     */
    setSavedFeeList: (state, payload) => {
        state.savedFeeList = payload;
    },
    /**
     * 设置工号类型
     */
    setStaffKindCode: (state, payload) => {
        state.staffKindCode = payload;
    },
    /**
     * 设置流程步骤
     * backFlag: 是否回溯标志，true-是；false-否
     */
    setFlowStep: (state, payload, backFlag) => {
        if (backFlag) {
            state.savedFeeList = [] // 清空费用信息
        }
        state.flowStep = payload;
    },
    /**
     * 设置支付流水号
     */
    setBusiOrderId: (state, payload) => {
        state.busiOrderId = payload;
    },
    /**
     * 用来控制机器人正在输出文字或者正在加载内容时，不允许用户输入文字或语音
     */
    setRobotWorking: (state, payload) => {
        state.robotWorking = payload;
    },
    setJzfkOrderData: (state, payload) => {
        state.jzfkOrderData = payload;
    },
    setIptvCacheList:(state, payload) => {
        state.iptvCacheList = payload;
    },
    setIptvOrderData: (state, payload) => {
        state.iptvOrderData = payload;
    },
    setRespTipArrQry: (state, payload) => {
        state.respTipArrQry = payload;
    },
    setIptvReProductList:(state, payload) => {
        state.iptvReProductList = payload;
    },
    setIptvReYwProductList: (state, payload) => {
        state.iptvReYwProductList = payload;
    },

    setZwReProductList:(state, payload) => {
        state.zwReProductList = payload;
    },
    setZwReYwProductList: (state, payload) => {
        state.zwReYwProductList = payload;
    },
    setZwCacheList:(state, payload) => {
        state.zwCacheList = payload;
    },
    setZwOrderData: (state, payload) => {
        state.zwOrderData = payload;
    },
    setMqBookFlagData: (state, payload) => {
        state.mqBookFlagData = payload;
    },
    setCacheQryPara: (state, payload) => {
        state.cacheQryPara = payload;
    },
    setBlockShow: (state, payload) => {
        state.blockShow = payload;
    },
    setIsFour: (state, payload) => {
        state.isFour = payload;
    },
    showLoading(state) {
        state.chatLoading = true
    },
    // 隐藏loading的方法
    hideLoading(state) {
        state.chatLoading = false
    },
    
    /**
     * 设置客户认证mock开关
     */
    setCustAuthMockSwitch: (state, payload) => {
        state.custAuthMockSwitch = payload;
    },
    // 添加 custInfoList 中读证后数据
    updateCustInfoList: (state, payload) => {
        // 相同客户，进行覆盖
        let index = state.custInfoList.findIndex(item => item?.custPicInfo?.cardNo === payload?.custPicInfo?.cardNo);
        if (index > -1) {
            state.custInfoList.splice(index, 1, payload);
        } else {
            state.custInfoList.push(payload);
        }
        // 装维实名信息（简单实名认证）
        if (payload.realTag === '2') {
            let custInfoList = [{
                'certType': payload.certType || '02',
                'psptAuthority': '',
                'groupId': '',
                'contactPerson': payload.contactPerson,
                'certId': payload.certNum,
                'custName': payload.customerName,
                'custPhone': payload.contactPhone,
                'certAddress': payload.certAddress,
                'certExpireDate': '20501231',
                'groupNumber': '',
                'groupUserId': '',
                'photoInfoList': [{
                    'photoUrl': '',
                    'photoType': ''
                }],
                'groupExtNum': '',
                'custKind': '1',
                'groupNumberName': '',
                'contactPhone': payload.contactPhone,
                'issuesNumber': '',
                'realTag': '2',
                'custInfoList': payload.custInfoList,
                'custPicInfo': payload.custPicInfo
            }];
            state.custInfoList = [...custInfoList];
        }
    },
    // 三户校验
    setUserInfo(state, data) {
        state.userInfo = data;
    },
    setCommonProduct(state, payload){
        state.commonProduct = payload
    },
    setIData(state,payload){
        state.iData = payload
    },
    setCommonChildren(state, payload){
        state.commonChildren = payload
    },
    setSecondBroadbandInfo(state, payload){
        state.secondBroadbandInfo = payload
    },
    deleteLast(state, payload) {
        state.chatList.pop();
    },
    setLastShow(state, payload) {
        state.chatList[state.chatList.length - 1].show = false;
    },
    // 缓存热销商品中点击的“融合变更”商品信息
    setRecommendedMixProduct(state, payload) {
        state.recommendedMixProduct = payload;
    },
    // 流程引擎相关新增字段
    setAgentSessionId(state, payload){
        state.agentSessionId = payload
    },
    // 设置运行时任务列表
    setRunTimeTaskList(state, payload){
        state.runTimeTaskList = payload
    },
    // 缓存最新chatApi入参
    setChatApiParam(state, payload){
        state.chatApiParam = payload
    },
    // 缓存当前任务信息
    setCurTask(state, paload){
        state.curTask = paload
    },
    setShbmMsgInfo(state, payload){
        state.shbmMsgInfo = payload
    },
    setNeedWait(state, payload){
        state.needWait = payload
    }
};