<template>
  <div class="selectDate">
    <van-cell style="margin-left: 5px" title="预约日期" :value="date" @click="show = true" :icon="require('@/assets/images/reservationDate.png')" />
    <van-calendar v-model="show" @confirm="onConfirm" color="#497CF6" />
    <div  class="time-list">
      <div class="init" :class="item.flag === 1 ? 'bidden' : (item.flag === 0 && radio === (index + 1) ? 'active' : '')"   v-for="(item,index) in timeList"
           :key="index" @click="changTime(item,index)">
        {{item.value}}
      </div>
    </div>
    <div class="submit-container">
      <van-button class="sub" @click="submit()" block>确定</van-button>
    </div>
  </div>
</template>

<script>
export default {
  name: "selectDate",
  data(){
    return {
      date: this.formatDate(new Date()),
      show: false,
      timeList: [
        {value:'上午(9:00-12:00)',flag:0},
        {value:'下午(12:00-18:00)',flag:0},
        {value:'晚上(18:00-20:00)',flag:0}
      ],
      radio: ''
    }
  },
  methods:{
    onConfirm(date) {
      this.show = false;
      this.date = this.formatDate(date);
    },
    formatDate(val) {
      var date = new Date(val);
      var year = date.getFullYear();
      var month = Number(date.getMonth()) + 1;
      month = month >= 10 ? month : "0" + month;
      var day = date.getDate();
      day = day >= 10 ? day : "0" + day;
      return year + "-" + month + "-" + day;
    },
    changTime(item,index){
      this.radio=(index+1)
      if(item.flag==1){
        this.$toast('当前时间不可预约，请换一个')
      }
    },
    submit(){

    }
  }
}
</script>

<style scoped lang="scss">
.time-list{
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  .init{
    background-color: white;
    border: 1.5px solid rgba(203, 203, 203, 1);
    border-radius: 8px;
    padding: 6px 0;
    height:fit-content;
    line-height: 30px;
    font-size: 14px;
    margin-right: 10px;
    margin-top: 10px;
    width: 45%;
    text-align: center;
  }
  .active{
    background: #EDF7FF;
    border: 1px solid rgba(80,148,245,1);
  }
  .bidden{
    background: rgba(245,248,250,1) !important;
    color:rgba(76,80,82,0.3);
  }
  .gren{
    background: #EDF7FF;
    border: 1px solid green;
  }
}
</style>
<style lang="scss">
.selectDate {
  .van-cell,.van-field__label,.van-field__control{
    color: #263A5F;
    .van-cell__value{
      color: #263A5F;
    }
  }
  .submit-container{
    .sub{
      border: 2px solid rgba(73,124,246,1);
      border-radius: 5px;
      color: rgba(73,124,246,1);
      margin-top: 20px;
      width: 100%;
      height: 45px;
      font-size: 16px;
    }
  }
}
</style>