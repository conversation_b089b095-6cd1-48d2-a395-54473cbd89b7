<template>
  <div >
    <div class="selectProduct">
      <div class="box" :style="{border:item.isActive ? '2px solid #3498db':''}" v-for="(item,index) in commodityYWUlList" :key="index"  @click="goodClick1(item)" >
        <div class="tag" :style="{ backgroundColor: item.color }">{{item.title}}</div>
        <div class="cell" v-if="!item.SHORT_NAME&&!item.RENT" />
        <div class="cell1">{{item.SHORT_NAME}}</div>
        <div class="cell2">{{item.RENT}}</div>
        <div class="cell1 ellipsis" v-if="!item.SHORT_NAME&&!item.RENT">{{item.ywGoodName.length>20?item.ywGoodName.slice(0,20)+'...':item.ywGoodName}}</div>
        <div class="success-box" v-if="item.isActive">
          <div class="success"></div>
        </div>
      </div>
    </div>
    <div v-if="showGoodName">您当前选择的商品是：{{showGoodName}}</div>
  </div>
</template>
<script>
import {mapActions, mapMutations, mapState} from "vuex";
export default {
  name:  "YskYwSelectProduct",
  data(){
    return {
      isYwfDiscnt:-1,
      ywfuInfoNoNeedIsShow:true,
      radio: '',
      colors:['rgb(129,211,248)','rgb(251,6,6)','#71bc8c'],
      commodityChooseYWUlList: [],
      choosedIPTV:"",
      commodityYWUlList: [],
      zwReYwProductList:[],
      showGoodName:'',
      zuWithYwListNew:[],
      zwReYwProductListNew:[],
      yskYwfCacheListNew:[]
    }},
  mounted() {
    this.outCallMonetOrderData.goodData.commodityChooseYWUlList=[];
    this.setOutCallMonetOrderData(this.outCallMonetOrderData)
    this.$emit('startLoading', '')
    this.$http.post('/outCallMonetGBroad/getSomeYwfjGodds', {}).then(res => {
      this.$emit('endLoading', '')
      if("0000"==res.respData.code){
        this.zwReYwProductList=res.respData.ywFreeGoodsListResult
        console.log("res.respData.ywFreeGoodsListResult",this.zwReYwProductList.length)
        let colorLength=this.colors.length
        if(this.outCallMonetOrderData.zuWithYwList.length>0){
          for(let i=0;i<this.outCallMonetOrderData.zuWithYwList.length;i++){
            let iptvInfo={}
            iptvInfo={
              title:'组网优惠包',
              ancestors:this.outCallMonetOrderData.zuWithYwList[i].ancestors,
              SHORT_NAME:this.outCallMonetOrderData.zuWithYwList[i].SHORT_NAME,
              ywGoodId:this.outCallMonetOrderData.zuWithYwList[i].commId,
              ywGoodType:this.outCallMonetOrderData.zuWithYwList[i].commType,
              ywGoodName:this.outCallMonetOrderData.zuWithYwList[i].commName,
              RENT:this.outCallMonetOrderData.zuWithYwList[i].DETAIL,
              isActive:false,
              color:this.colors[i%colorLength]}
            this.zuWithYwListNew.push(iptvInfo)
          }}
        if(this.zwReYwProductList.length>0){
          for(let i=0;i<this.zwReYwProductList.length;i++){
            let iptvInfo={}
            iptvInfo={
              title:'优惠包',
              ancestors:this.zwReYwProductList[i].ancestors,
              SHORT_NAME:this.zwReYwProductList[i].SHORT_NAME,
              ywGoodId:this.zwReYwProductList[i].commId,
              ywGoodType:this.zwReYwProductList[i].commType,
              ywGoodName:this.zwReYwProductList[i].commName,
              RENT:this.zwReYwProductList[i].DETAIL,
              isActive:false,
              color:this.colors[i%colorLength]}
            this.zwReYwProductListNew.push(iptvInfo)
          }}
        
        if(this.yskYwfCacheList.length>0){
          for(let i=0;i<this.yskYwfCacheList.length;i++){
            let iptvInfo={}
            iptvInfo={
              title:'超清优惠包',
              ancestors:this.yskYwfCacheList[i].ancestors,
              SHORT_NAME:this.yskYwfCacheList[i].SHORT_NAME,
              ywGoodId:this.yskYwfCacheList[i].commId,
              ywGoodType:this.yskYwfCacheList[i].commType,
              ywGoodName:this.yskYwfCacheList[i].commName,
              RENT:this.yskYwfCacheList[i].DETAIL,
              isActive:false,
              color:this.colors[i%colorLength]}
            this.yskYwfCacheListNew.push(iptvInfo)
          }}
        this.zwReYwProductList=this.unionById(this.zuWithYwListNew, this.zwReYwProductListNew,'ywGoodId')
        this.zwReYwProductList=this.unionById(this.yskYwfCacheListNew, this.zwReYwProductList,'ywGoodId')
        this.commodityYWUlList=this.zwReYwProductList
      }
    })
  },
  computed: {
    ...mapState([
      'jzfkOrderData',
      'shbmMsgInfo',
      'outCallMonetOrderData',
      'yskYwfCacheList',
      'iptvCacheList', 
      'outCallMonetOrderData'])
  },
  methods:{
    ...mapMutations([
      'setFlowStep',
      'setRobotWorking',
      'setOutCallMonetOrderData',
        'setYskYwfCacheList'
    ]),
    ...mapActions(['updateChatList']),
    ywfNoNeed(){
      this.outCallMonetOrderData.goodData.commodityChooseYWUlList=[];
      this.outCallMonetOrderData.goodData.kdfList=[];
      this.setOutCallMonetOrderData(this.outCallMonetOrderData);
      if(this.commodityChooseYWUlList.length>0){
        for(let i in this.commodityChooseYWUlList){
          let id=this.commodityChooseYWUlList[i].ywGoodId
          $('#'+'YWF'+id).parent().removeClass('active')
        }
      }
      let c=[{ywGoodId:'0',ywGoodName:'不需要'}];
      this.outCallMonetOrderData.goodData.commodityChooseYWUlList=c;
      this.setOutCallMonetOrderData(this.outCallMonetOrderData);
      this.commodityChooseYWUlList=[];
      this.isYwfDiscnt=-1;
    },
    commodityYWUlClick(item,e) {
      // console.log(this.pageZwPrice)
      const $this = $(e.target);
      // console.log(item)
      if($this.parent().hasClass("active")){
        $this.parent().removeClass('active');
        this.commodityChooseYWUlList = this.commodityChooseYWUlList.filter(i=>i!==item);
        this.outCallMonetOrderData.goodData.commodityChooseYWUlList=this.commodityChooseYWUlList;
        this.setOutCallMonetOrderData(this.outCallMonetOrderData);
        console.log("this.outCallMonetOrderData.goodData.commodityChooseYWUlList")
        console.log(this.outCallMonetOrderData.goodData.commodityChooseYWUlList)

        if(this.commodityChooseYWUlList.length>0){
          this.isYwfDiscnt=0;
        }
        else{
          this.isYwfDiscnt=-1;
          this.ywfNoNeed();
          return;
        }
        this.orderPrice=0;
        this.orderPrice = this.pageZwPrice+this.ziFeiPrice+this.pageYwfuPrice;
      }else{
        this.isYwfDiscnt=0;
        $this.parent().addClass("active");
        this.commodityChooseYWUlList.push(item);
        this.outCallMonetOrderData.goodData.commodityChooseYWUlList=this.commodityChooseYWUlList;
        this.setOutCallMonetOrderData(this.outCallMonetOrderData)
        console.log("this.outCallMonetOrderData.goodData.commodityChooseYWUlList")
        console.log(this.outCallMonetOrderData.goodData.commodityChooseYWUlList)
        this.orderPrice=0;
        this.orderPrice = this.pageZwPrice+this.ziFeiPrice+this.pageYwfuPrice;
      }
      this.feeDesc = this.orderPrice > 0 ? "施工中收费" : " 无须支付";
    },
    goodClick1(item){
      this.showGoodName="";
      item.isActive=!item.isActive
      if (item.isActive) {
        let flag=false;
        if(this.outCallMonetOrderData.custWithYwList.length>0){
          for(let i=0;i<this.outCallMonetOrderData.custWithYwList.length;i++){
            if(item.ywGoodId==this.outCallMonetOrderData.custWithYwList[i].commId){
              flag=true;
              break;
            }
          }
        }
     if(!flag){
       // 如果未选中，则添加
       this.commodityChooseYWUlList.push(item);
       for(let i=0;i<this.commodityChooseYWUlList.length-1;i++){
         this.showGoodName+=this.commodityChooseYWUlList[i].ywGoodName+","
       }
       this.showGoodName+=this.commodityChooseYWUlList[this.commodityChooseYWUlList.length-1].ywGoodName
       this.outCallMonetOrderData.goodData.commodityChooseYWUlList=this.commodityChooseYWUlList;
       this.setOutCallMonetOrderData(this.outCallMonetOrderData)
     }else{
       item.isActive=!item.isActive
       this.$toast('您所选择的定制商品已包含该移网附加优惠商品，不能再次选择')
     }
      } else {
        this.commodityChooseYWUlList = this.commodityChooseYWUlList.filter(i=>i!==item);
        if(this.commodityChooseYWUlList.length>0){
          for(let i=0;i<this.commodityChooseYWUlList.length-1;i++){
            this.showGoodName+=this.commodityChooseYWUlList[i].ywGoodName+","
          }
          this.showGoodName+=this.commodityChooseYWUlList[this.commodityChooseYWUlList.length-1].ywGoodName
        }else{
          this.showGoodName="";
        }
        this.outCallMonetOrderData.goodData.commodityChooseYWUlList=this.commodityChooseYWUlList;
        this.setOutCallMonetOrderData(this.outCallMonetOrderData);
      }
    },
    unionById(array1, array2, idKey) {
      // console.log(array1.length+"array1.length")
      // console.log(array2.length+"array2.length")

      if(array1.length==0&&array2.length==0){
        return [];
      }
      else if(array1.length==0){
        return array2;
      }
      else if(array2.length==0){
        return array1;
      }
      else{
        return array1.concat(
            array2.reduce((acc, item) => {
              const existingItem = array1.find(el => el[idKey] === item[idKey]);
              if (!existingItem) {
                acc.push(item);
              }
              return acc;
            }, [])
        );
      }
    },
    //定制商品自带的移网附加商品不能再次选择
    isExtistInCollection(arry,obj){
      return arry.some(item => item.commId === obj.ywGoodId);
    },
    goodClick(item,e){
      const $this = $(e.target);
      if ($this.hasClass("btn")) {
        $this.addClass("active-btn")
        $this.parent().addClass("red-border")
        $this.removeClass("btn")
        // 如果未选中，则添加
        item.itemTip='已选择'
        this.commodityChooseYWUlList.push(item);
        this.outCallMonetOrderData.goodData.commodityChooseYWUlList=this.commodityChooseYWUlList;
        this.setOutCallMonetOrderData(this.outCallMonetOrderData)
      } else {
        $this.parent().removeClass("red-border")
        $this.removeClass("active-btn")
        $this.addClass("btn");
        item.itemTip='立即购买'
        this.commodityChooseYWUlList = this.commodityChooseYWUlList.filter(i=>i!==item);
        this.outCallMonetOrderData.goodData.commodityChooseYWUlList=this.commodityChooseYWUlList;
        this.setOutCallMonetOrderData(this.outCallMonetOrderData);
      }
    }
  }
}
</script>
<style scoped lang="scss">
.red-border{
  border: rgb(221,248,253) 2px solid;
}
.selectProduct {
  display: flex;
  flex-wrap: nowrap; /* 确保子元素不换行 */
  overflow-x: scroll; /* 启用水平滚动 */
  -webkit-overflow-scrolling: touch; /* 改善移动设备上滚动的体验 */
  .box {
    position: relative;
    border-radius: 10px;
    margin: 0 10px 10px 0;
    flex: 0 0 calc(22.6%); /* 使用 calc() 考虑 margin */
    max-width: calc(22.6%); /* 同样考虑 margin */
    background: linear-gradient(to bottom right, #fff, #EDF0FF);
    padding: 0 10px 10px 10px;
    height: 200px;

    .tag {
      color: #fff;
      border-radius: 10px 0 10px 0;
      padding: 0 6px;
      font-size: 9px;
      margin-left: -10px;
      width: 75%;
      text-align: center;
    }
    .cell {
      margin: 10px 0;
      height: 45px;
      width: 100%;
      text-align: center;
      font-weight: bold;
      font-size: 14px;
      line-height: 18px
    }

    .cell1 {
      position: absolute;
      top: 50px;
      margin: 10px 0;
      height: 45px;
      width: 81%;
      text-align: center;
      font-weight: bold;
      font-size: 14px;
      line-height: 18px
    }

    .cell2 {
      position: absolute;
      top: 120px;
      margin-bottom: 10px;
      width: 81%;
      text-align: center;
      font-size: 13px;
      line-height: 19px
    }

    .btn {
      position: absolute;
      bottom: 5px;
      border: 2px solid #4494E6;
      border-radius: 10px;
      color: #4494E6;
      height: 25px;
      font-size: 11px;
      padding: 5px 8px;
      background: none;
    }

    .active-btn {
      position: absolute;
      bottom: 5px;
      border: 2px solid #4494E6;
      border-radius: 10px;
      color: #fff;
      background: #4494E6 !important;
      height: 25px;
      font-size: 11px;
      padding: 5px 8px;
      background: none;
      margin-left: 10px;
    }
    .success-box{
      position: absolute;
      bottom: 0;
      right: 0;
      .success {
        position: relative;
        width: 25px;
        height: 25px;
        background-image: url('@/assets/images/choosePhoto.png');
        background-position: center center;
        background-repeat: no-repeat;
        background-size: cover;
        clip-path: polygon(0 100%, 100% 0, 100% 100%);
        border-bottom-right-radius: 8px;
      }

    }

  }
}
</style>