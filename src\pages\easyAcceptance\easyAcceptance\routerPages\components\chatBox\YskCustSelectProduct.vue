<template>
  <div >
      <div class="selectProduct" id="selectProduct">
        <div class="box" v-for="(item,index) in custInfoList" :key="index" >
          <img v-if="item.isHot == '1'" src="@/assets/images/flame.png" alt="" class="box-img">
         <div>
           <div class="tag" :style="{ backgroundColor: item.color }">{{item.title}}</div>
           <span class="tag1" style="color:red" v-show="isEmpty(item.hotGoods)">{{item.hotGoods}}</span>
         </div>
          <div class="cell" v-if="!item.SHORT_NAME&&!item.RENT" />
          <div class="cell1">{{item.SHORT_NAME}}</div>
          <div class="cell2">{{item.RENT}}</div>
          <div class="cell1 ellipsis" v-if="!item.SHORT_NAME&&!item.RENT"><van-popover placement="top" v-model="item.showPopover" trigger="click">
            <van-grid
                square
                clickable
                :border="false"
                column-num="1"
                style="width: 120px; "
            >
              <van-grid-item
                  style="font-weight: bold"
                  :text="item.iptvName"
                  @click="item.showPopover = false"
              />
            </van-grid>
            <template #reference>
              {{item.iptvName.length>30?item.iptvName.slice(0,30)+'...':item.iptvName}}
            </template>
          </van-popover></div>
          <van-button class="btn" @click="goodClick(item,$event)"  :id="'custom'+item.commodityCode">{{item.itemTip}}</van-button>
        </div>
      </div>
    <div v-if="showGoodName">您当前选择的商品是：{{showGoodName}}</div>
    <div v-if="showGoodName">  资费说明：营业费{{ziFeiPrice}}元和调测费{{tiaocePrice}}元</div>
  </div>
</template>
<script>
import {mapActions, mapMutations, mapState} from "vuex";
export default {
  name:  "YskCustSelectProduct",
  data(){
    return {
      radio: '',
      colors:['rgb(129,211,248)','rgb(251,6,6)','#71bc8c'],
      selectedGooIds: [],
      choosedIPTV:"",
      iptvInvalid:[],
      iptvInfoCheckList:[],
      custInfoList: [
      ],
      ziFeiPrice:0,
      tiaocePrice:0,
      showGoodName:'',
    }
  },
  
  mounted() {
    this.selectedGooIds=[];
    this.setKdOrderPrice(0)
    this.outCallMonetOrderData.goodData.custList=[];
    this.outCallMonetOrderData.goodData.commodityChooseYWUlList=[];
    this.outCallMonetOrderData.goodData.iptvList=[];
    this.outCallMonetOrderData.goodData.kdfList=[];
    this.outCallMonetOrderData.goodData.zwList=[];
    this.setOutCallMonetOrderData(this.outCallMonetOrderData);
    this.custInfoList=[];
    let colorLength=this.colors.length
    console.log(this.wslProductLimitList,"ZwReProductList")
    if(this.wslProductLimitList.length>0){
      for(let i=0;i<this.wslProductLimitList.length;i++){
        let title=''
        if(this.wslProductLimitList[i].limitTag=='ZG_YSK_TQ_DZ_JM'){
          title='减免'
        }
        else{
          title='不减免'
        }
        let iptvInfo={
          title:title,
          ancestors:this.wslProductLimitList[i].ancestors,
          SHORT_NAME:this.wslProductLimitList[i].SHORT_NAME,
          commodityCode:this.wslProductLimitList[i].idB,
          commType:this.wslProductLimitList[i].goodsType,
          iptvName:this.wslProductLimitList[i].goodsName,
          RENT:this.wslProductLimitList[i].RENT,
          itemTip:'立即购买',
          hotGoods:this.wslProductLimitList[i].hotGoods,
          color:this.colors[i%colorLength],
          showPopover:false,
          isHot:this.wslProductLimitList[i].isHot
        }
          this.custInfoList.push(iptvInfo)
      }
    }
  },
  computed: {
    ...mapState([
      'jzfkOrderData',
      'shbmMsgInfo',
      'iptvCacheList',
      'zwReProductList',
      'iptvCacheList',
       'outCallMonetOrderData',
        'wslProductLimitList',
        'kdOrderPrice'
    ])
  },
  methods:{
    ...mapMutations([
      'setFlowStep',
      'setRobotWorking',
      'setOutCallMonetOrderData',
        'setKdOrderPrice'
    ]),
    ...mapActions(['updateChatList']),
    isEmpty(value) {
      let flag = false
      if (value == '' || value == 'undefined' || value == undefined || value == null || value == 'null') {
        flag = true
      }
      return flag
    },
    custClickFee(zw){
      let data = {
        commodityCode: zw.commodityCode,
        showOpenDate:this.outCallMonetOrderData.checkNumberData.showJson.showOpenDate}
      this.$http.post('/outCallMonetGBroad/qryKuanDaiFee', data).then(res => {
        if (res.respCode == "0000") {
          // 接收返回参数
          let orderPrice=this.kdOrderPrice;
          this.tiaocePrice=res.respData.kdCommPrice;
          this.ziFeiPrice=res.respData.kdBusiPrice;
          orderPrice=orderPrice+parseInt(res.respData.kdCommPrice)+parseInt(res.respData.kdBusiPrice);
          this.outCallMonetOrderData.custWithYwList=res.respData.custWithYwList
          this.outCallMonetOrderData.b2iCommTag=res.respData.b2iCommTag
          this.setKdOrderPrice(orderPrice);
          this.setOutCallMonetOrderData(this.outCallMonetOrderData)
          let data = {
            inputType: "1",
            type: '1',
            textInput: "custGoodSubmit",
            notifyFlag: '',
            taskName:'智能移送宽甩单'
          }
          this.$emit('newChatApi', data);
        }
      else{
          $('#'+'custom'+zw.commodityCode).parent().removeClass("red-border")
          $('#'+'custom'+zw.commodityCode).removeClass("active-btn")
          $('#'+'custom'+zw.commodityCode).addClass("btn");
          zw.itemTip='立即购买'
          this.updateChatList({
            sender: '1',
            type: 'module',
            moduleName: 'TextResponse',
            moduleLevel: 1,
            params: {
              text: res.respMsg
            },
            show: true
          })
          let data = {
            inputType: "1",
            type: '1',
            textInput: "custGoodReChoose",
            notifyFlag: '',
            taskName:'智能移送宽甩单'
          }
          this.$emit('newChatApi', data);
        }
      }).catch(e => {
       this.$toast(e)
      })
    },
    goodClick(item,e){
      this.showGoodName = item.iptvName
      const $this = $(e.target);
      if ($this.hasClass("btn")) {
        $this.addClass("active-btn")
        $this.parent().addClass("red-border")
        $this.removeClass("btn")
        $this.siblings().removeClass("active-btn")
        $this.siblings().removeClass("red-border")
        // 如果未选中，则添加
        item.itemTip='已选择'
        this.outCallMonetOrderData.goodData.custList=[];
        this.selectedGooIds.push(item.commodityCode);
        let zw={
          commodityCode:item.commodityCode,
          commodityName:item.iptvName,
          commType:item.commType}
        this.outCallMonetOrderData.goodData.custList.push(zw);
        this.setOutCallMonetOrderData(this.outCallMonetOrderData);
        this.custClickFee(item)
      } else {
        $this.parent().removeClass("red-border")
        $this.removeClass("active-btn")
        $this.addClass("btn");
        item.itemTip='立即购买'
        this.selectedGooIds = this.selectedGooIds.filter(i => i !== item.commodityCode);
        this.custClickFee(item)
      }
    }
  }
}
</script>
<style scoped lang="scss">
.red-border{
  border: rgb(221,248,255) 2px solid;}
.selectProduct{
  display: flex;
  flex-wrap: nowrap; /* 确保子元素不换行 */
  overflow-x: scroll;  /* 启用水平滚动 */
  -webkit-overflow-scrolling: touch; /* 改善移动设备上滚动的体验 */
  .box {
    position: relative;
    border-radius: 10px;
    margin: 0 10px 10px 0;
    flex: 0 0 calc(22.6%); /* 使用 calc() 考虑 margin */
    max-width: calc(22.6%); /* 同样考虑 margin */
    background: linear-gradient(to bottom right, #fff, #EDF0FF);
    padding: 0 10px 10px 10px;
    height: 200px;
    .box-img{
      position: absolute;
      width: 22px;
      right: 0px;
      top: 0px;
    }
    .tag{
      float:left ;
      color: #fff;
      border-radius: 10px 0 10px 0;
      padding: 0 6px;
      font-size: 11px;
      margin-left: -10px;
      width: 60%;
      text-align: center;
    }
    
    .tag1{
      float:right ;
      color: red;
      font-weight: bold;
      border-radius: 10px 0 10px 0;
      padding: 0 6px;
      font-size: 11px;
      margin-left: -10px;
      width: 15%;
      text-align: center;
    }
    .cell{
      clear: both;
      margin: 10px 0;
      height: 45px;
      width: 100%;
      text-align: center;
      font-weight: bold;
      font-size: 14px;
      line-height: 18px
    }
    .cell1{
      position: absolute;
      top: 35px;
      margin: 10px 0;
      height: 45px;
      width: 81%;
      text-align: center;
      font-weight: bold;
      font-size: 14px;
      line-height: 18px
    }
    .cell2{
      position: absolute;
      top: 132px;
      margin-bottom: 10px;
      width: 81%;
      text-align: center;
      font-size: 13px;
      line-height: 19px
    }
    .btn{
      position: absolute;
      bottom: 5px;
      border: 2px solid #4494E6;
      border-radius: 10px;
      color: #4494E6;
      height: 25px;
      font-size: 11px;
      padding: 5px 8px;
      background: none;
    }
    .active-btn{
      position: absolute;
      bottom: 5px;
      border: 2px solid #4494E6;
      border-radius: 10px;
      color: #fff;
      background: #4494E6!important;
      height: 25px;
      font-size: 11px;
      padding: 5px 8px;
      background: none;
      margin-left: 10px;
    }
  }

  /* WebKit 浏览器滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  ::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

  /* Firefox 移动端浏览器 */
  .scrollable-element {
    scrollbar-width: auto;
    scrollbar-color: #888 #f1f1f1;
  }

  /* 元素样式 */
  .scrollable-element {
    width: 300px;
    height: 200px;
    overflow: auto;
    border: 1px solid #ccc;
    padding: 10px;
    -webkit-overflow-scrolling: touch;
  }
  @-webkit-keyframes showScrollbar {
    from {
      opacity: 1;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes showScrollbar {
    from {
      opacity: 1;
    }
    to {
      opacity: 1;
    }
  }
}
</style>