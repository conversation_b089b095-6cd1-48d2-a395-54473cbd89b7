<template>
<div>
  <van-button style="background-color: #77cbff" @click="openurl('1')">商品受理-移网</van-button>
  <van-button style="background-color: #77cbff"  @click="openurl('2')">代客下单</van-button>
  <van-button style="background-color: #77cbff" @click="openurl('3')">快速缴费</van-button>
</div>
</template>
<script>
import WadeMobile from "rk-native-plugin";
import {Wade} from "rk-web-utils";
export default {
    name: 'urlStart',
    props:{
      title: {
        type: String,
        default: '',
      },
      values:{
        type: Array,
        default: () => []
      }
    },
    computed: {
      other:function(){
        return this.values.length > 1 ? this.values.filter((_,i) => i > 0) : []
      }
    },
  methods: {
    openurl(type){
      if('1'==type){
        WadeMobile.openH5('https://wxxapp.chinaunicom.cn:10070/touch_sub_front/newBusinessMobile.html', null, (result) => {
          console.log(result);
        });
      }
      else if('2'==type){
        WadeMobile.openUrl('https://bbdigital.10010.com/udbh/udbh/transponder/valetPlaceAnOrder', undefined, 'valetPlaceAnOrder');
      }
      else if('3'==type){
        WadeMobile.openIpuApp(new Wade.DataMap({
          APP_ID: "300038",
          MENU_PAGE_ACTION: "PointPay",
          MENU_WELCOME_PAGE: "welcome/welcome.htm",
        }));
      }
    }
  }
}
</script>