<template>
    <div>
        <!-- <div class="navaBar">
            <span class="leftJ"><van-icon name="arrow-left" size="20px" @click="goBack" /></span>
            <span class="navaTitle">同客户验证</span>
        </div> -->
        <div class="main">
            <van-field
                readonly
                clickable
                name="picker"
                :value="value"
                placeholder="请选择验证类型"
                @click="showPicker = true"
            />
            <van-popup v-model="showPicker" position="bottom">
                <van-picker
                    show-toolbar
                    :columns="columns"
                    value-key="text"
                    @confirm="onConfirm"
                    @cancel="showPicker = false"
                />
            </van-popup>
            <van-field v-model="valueNum" type="digit" :placeholder="placeholder" />

            <div v-if="showCode" class="showCode">
                <van-field
                    v-model="valueCode"
                    center
                    clearable
                    placeholder="请输入短信验证码"
                >
                    <template #button>
                        <van-button
                            size="small"
                            type="info"
                            @click="verificationCode"
                            style="width: 120px;"
                            :disabled="isDisabled"
                        >
                            <span v-if="countdown">{{ codeText }}</span>
                            <span v-if="!countdown">{{ count + "s" }}</span>
                        </van-button>
                    </template>
                </van-field>
            </div>

            <van-sticky :offset-top="'92vh'" style="width: 100%;">
                <van-button
                    type="info"
                    @click="requestVerification"
                    style="width: 320px;"
                >
                    验证
                </van-button>
            </van-sticky>
        </div>
    </div>
</template>

<script>
import { Common } from 'rk-web-utils';
import { alertError } from '@/assets/bizComponents/funcComponent.js';
import Bus from '@/assets/bizComponents/PaperlessSign/bus';
export default {
    data() {
        return {
            countdown: true,
            isDisabled: false,
            codeLoading: false,
            timer: null,
            count: '',
            codeText: '发送验证码',
            showCode: false,
            valuetype: '',
            valueNum: '',
            pageLoading: false,
            psptId: '',
            placeholder: '请选择验证方式',
            valueCode: '', //验证码
            value: '',
            showPicker: false,
            columnsCode: null,
            sameCustomerAuthFlag: true,
            columns: [
                { text: '手机号码验证', value: '1' },
                { text: '固话号码验证', value: '4' },
                { text: '宽带账号验证', value: '3' }
            ]
        };
    },
    methods: {
        goBack() {
            this.$store.commit('setSameCustomerAuthFlag', this.sameCustomerAuthFlag);
            let obj = {
                sameCustomerAuthFlag: this.sameCustomerAuthFlag
            };
            Bus.$emit('sameCustomerAuthFlag', obj);
            Bus.$emit('sameCustomerAuthFlag', this.sameCustomerAuthFlag);
            // eslint-disable-next-line no-console
            this.$router.back();
        },
        onConfirm(value) {
            this.value = value.text;
            this.columnsCode = value.value;
            this.showPicker = false;
            this.valueNum = '';
            if (this.columnsCode == '1') {
                this.placeholder = '请输入手机号';
                this.showCode = true;
            } else if (this.columnsCode == '4') {
                this.placeholder = '请输入固话号码';
                this.showCode = false;
            } else if (this.columnsCode == '3') {
                this.placeholder = '请输入宽带账号';
                this.showCode = false;
            }
        },
        verificationCode() {
            const TIME_COUNT = 60;
            let myreg = /^[1][3,4,5,7,8,9][0-9]{9}$/;
            if (this.valueNum == '') {
                this.$toast('请输入手机号');
                return false;
            }
            if (!myreg.test(this.valueNum)) {
                this.$toast('手机号码格式不对');
                return;
            }
            if (!this.timer) {
                this.count = TIME_COUNT;
                this.countdown = false;
                this.codeText = '重新发送';
                this.timer = setInterval(() => {
                    this.isDisabled = false;
                    if (this.count > 0 && this.count <= TIME_COUNT) {
                        this.count--;
                        this.isDisabled = true;
                    } else {
                        this.countdown = true;
                        clearInterval(this.timer);
                        this.timer = null;
                    }
                }, 1000);
            }
            Common.loadingStart('获取验证码...');
            this.$http
                .get(`/sms/sendMsg?serialNumber=${this.valueNum}&busiType=simpleBroad`)
                .then(res => {
                    // eslint-disable-next-line no-console
                    Common.loadingStop();
                    if (res.respCode == '0000') {
                        this.isDisabled = false;
                        alertError({
                            title: '发送短信验证码',
                            message: res.respMsg + ' traceId:[' + res.traceId + ']'
                        });
                    } else {
                        this.$toast(res.respMsg);
                    }
                })
                .catch(() => {
                    // this.$toast();
                    Common.loadingStop();
                });
        },
        requestVerification() {
            let params = {
                psptId: this.psptId,
                serialNumberType: this.columnsCode,
                tradeTypeCode: '0340',
                serviceClassCode: '0040',
                serialNumber: this.valueNum
            };
            //获取身份证id
            if (this.psptId == '') {
                this.$toast('未能获取身份证号，请重新读取身份证！');
                return;
            }
            if (this.value == '') {
                this.$toast('请选择验证方式！');
                return;
            }
            if (this.columnsCode == '1') {
                if (this.valueNum == '') {
                    this.$toast('请输入手机号!');
                    return;
                }
                if (this.valueCode == '') {
                    this.$toast('请输入验证码！');
                    return;
                }
                let myreg = /^[1][3,4,5,7,8,9][0-9]{9}$/;
                if (!myreg.test(this.valueNum)) {
                    this.$toast('手机号码格式不对');
                    return;
                }
            }
            if (this.valueNum == '' && this.columnsCode == '3') {
                this.$toast('请输入宽带账号');
                return;
            }
            if (this.valueNum == '' && this.columnsCode == '4') {
                this.$toast('请输入固话号码');
                return;
            }
            if (this.columnsCode == '1') {
                (params.tradeTypeCode = '0120'), (params.serviceClassCode = '0050');
            }
            Common.loadingStart('正在同客户验证,请稍等……', '等待');
            this.$http
                .get('/sms/msmVerify?serialNumber=' + this.valueNum + '&verifyCode=' + this.valueCode.trim())
                .then(result => {
                    if (result.respCode == '0000') {
                        Common.loadingStop();
                        this.$toast('短信验证成功');
                        this.$http
                            .post('/SameCustomerVerification/checkCustomer', params)
                            .then(res => {
                                Common.loadingStop();
                                if (res.respCode == '0000') {
                                    // this.$toast(res.respMsg);
                                    this.$store.commit('setSameCustomerAuthFlag', this.sameCustomerAuthFlag);
                                    this.goBack();
                                }
                            })
                            .catch(() => {
                                Common.loadingStop();
                            });
                        // this.requestVerification()
                    } else {
                        Common.loadingStop();
                        this.$toast(result.respMsg);
                    }
                })
                .catch(() => {
                    Common.loadingStop();
                    this.$toast('短信验证失败');
                });
      
        }
    },
    mounted() {
        this.psptId = this.$route.query.psptId;
    }
};
</script>

<style lang="scss" scoped>
/deep/ .van-sticky {
    display: flex;
    justify-content: center;
}

.main {
    margin-top: 30px;
}

.navaBar {
    height: 60px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #c0c0c0;
}

.navaTitle {
    font-weight: 600;
    font-size: 20px;
    margin-left: 26%;
}

.leftJ {
    margin-left: 3%;
    margin-top: 8px;
}

.showCode {
    display: flex;
    align-items: center;
}
</style>
