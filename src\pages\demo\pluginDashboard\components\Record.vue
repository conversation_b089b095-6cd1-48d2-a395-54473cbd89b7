<template>
    <div>
        <van-button
            @click="start"
            type="primary"
        >
          开始录音
        </van-button>
        <van-button
            @click="end"
            type="primary"
        >
          结束录音
        </van-button>
        <van-cell
            title="录音路径"
            :value="recordAddress"
            size="large"
        />
        <div>需先点击“开始录音”，再点击“结束录音”才会有录音路径</div>
    </div>
</template>

<script>
import WadeMobile from 'rk-native-plugin';

export default {
    name: 'Record',
    data() {
      return {
          recordAddress: ''
      };
    },
    methods: {
        start() {
            WadeMobile.audioRecord(false,"wav").then(() => {console.log("调用成功");});
        },
        end() {
            WadeMobile.audioRecordEnd().then((path) => {
              this.recordAddress = path;
              console.log(path)
            });
        }
    }
};
</script>

<style scoped>

</style>
