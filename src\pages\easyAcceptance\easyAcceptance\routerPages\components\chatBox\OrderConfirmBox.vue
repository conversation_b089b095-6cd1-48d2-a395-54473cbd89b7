<template>
    <div>
      <div class="card-group" v-for="(item,index) in resp" :key="index" :style="{marginBottom:index!==resp.length-1?'10px':'0'}">
        <van-cell-group class="tip">
          <van-cell  class="desc"  style="color:#263A5F;padding:5px 0">
               <div style="float: left">
                  <span  style="font-weight: bold;color:#263A5F;" >{{item.senceName}}<span style="font-size: 14px !important;font-weight: lighter "> ({{item.orderState}})</span></span>
               </div>
                <div  v-if="'1'==item.orderCancelTag" style="float: right">
                  <van-icon :name="require('@/pages/easyAcceptance/easyAcceptance/images/tuidan.png')" />
                  <span  style="color:#497CF6 !important;" @click="cancelOrder(item)">撤单</span>
              </div>
            <div v-else style="float: right"></div>
          </van-cell>
        </van-cell-group>
        <van-cell-group class="tip1">
          <van-cell class="desc">
            <template #title>
              <van-icon :name="require('@/pages/easyAcceptance/easyAcceptance/images/name.png')" />
              <span class="custom-title" style="color:#263A5F;">{{item.custName+item.serialNumber}}</span>
            </template>
          </van-cell>
        </van-cell-group>
        <van-cell-group class="card-list" style="display: flex;justify-content: space-between;">
          <van-cell style="padding-right: 0;background-color: #E9F3FC;">
            <template #title>
              <van-icon :name="require('@/pages/easyAcceptance/easyAcceptance/images/time.png')" />
              <span class="custom-title">{{item.orderTime}}</span>
            </template>
          </van-cell>
          <van-cell style="padding: 0;background-color: #E9F3FC;width: 90%;">
            <template #title>
              <van-icon :name="require('@/pages/easyAcceptance/easyAcceptance/images/orderId.png')" />
              <span class="custom-title">{{item.orderId}}</span>
            </template>
          </van-cell>
        </van-cell-group>
        <van-cell-group class="card-list" style="display: flex;justify-content: space-between;">
          <van-cell style="padding-right: 0;background-color: #E9F3FC;">
            <template #title>
              <van-icon :name="require('@/pages/easyAcceptance/easyAcceptance/images/remark.png')" />
              <span class="custom-title"><b>备注：</b>{{item.remarkByQry}}</span>
            </template>
          </van-cell>
        </van-cell-group>
<!--        <van-cell-group class="card-list" style="display: flex;justify-content: space-between;">-->
<!--          <van-cell style="padding-right: 0;background-color: #E9F3FC;">-->
<!--            <van-button size="small" style="background-image: linear-gradient(to bottom right, #DAE9FF,#DAE9FF, #DAE9FF);" @click="cancelOrder(item)">退单</van-button>-->
<!--          </van-cell>-->
<!--        </van-cell-group>-->
      </div>
      <van-popup class="popup1" v-model="popupShow1" round position="bottom" :style="{ height: '50%' }">
        <div class="title">请选择一级撤单原因</div>
        <van-radio-group style="padding: 10px;" v-model="classReason">
          <van-cell-group>
            <van-cell v-for="item in classReasonArr" :title="item.codeDesc" clickable @click="onSelect1(item)" style="text-align: center">
<!--              <template #right-icon>-->
<!--                <van-radio :name="item.codeDesc" :icon-size="0" />-->
<!--              </template>-->
        
            </van-cell>
          </van-cell-group>
        </van-radio-group>
<!--        <div style="width: 50%;height: 100px;"></div>-->
<!--        <div class="submit-container" style="padding: 0 15px;position: fixed;bottom: 10px;width: 92%;">-->
<!--          <van-button class="sub" block @click="sureChioce(1)">确定</van-button>-->
<!--        </div>-->
      </van-popup>
      <van-popup class="popup1" v-model="popupShow2" round position="bottom" :style="{ height: '50%' }">
        <div class="title">请选择二级撤单原因</div>
        <van-radio-group style="padding: 10px;" v-model="reasonDetail">
          <van-cell-group>
            <van-cell v-for="item in reasonDetailArr" :title="item.codeDesc" clickable @click="onSelect2(item)" style="text-align: center">
<!--              <template #right-icon>-->
<!--                <van-radio :name="item.codeDesc" />-->
<!--              </template>-->
            </van-cell>
          </van-cell-group>
        </van-radio-group>
<!--        <div style="width: 50%;height: 100px;"></div>-->
<!--        <div class="submit-container" style="padding: 0 15px;position: fixed;bottom: 10px;width: 92%;">-->
<!--          <van-button class="sub" block @click="sureChioce(2)">确定</van-button>-->
<!--        </div>-->
      </van-popup>


      <!--        <div style="width: 100%;margin: auto;clear: both">-->
<!--          <van-button style="width: 90px;height: 20px;border-radius:7px;font-size: 12px;margin-left: 10px;float: left;margin-bottom: 5px;border: 2px solid gray;color:gray;" :loading="loading" :disabled="disabled" :icon="select2 ? 'success' : ''" v-if="!hiddenCancelButton"    @click="cancelFn">{{ params.cancelText || '取消' }}</van-button>-->
<!--          <van-button  style="width: 90px;height: 20px;border-radius:7px;font-size: 12px;margin-left: 8px;float: left;margin-bottom: 5px;border: 2px solid rgba(73,124,246,1);color: rgba(73,124,246,1);" :loading="loading" :disabled="disabled" :icon="select1 ? 'success' : ''" v-if="!hiddenConfirmButton"   @click="confirmFn">{{ params.confirmText || '确定' }}</van-button>-->
<!--        </div>-->
<!--      <OrderInfoList :showOrderList="showOrderList" @closeListPopup="closeListPopup()" ></OrderInfoList>-->
    </div>
</template>

<script>
import {mapActions, mapMutations, mapState} from 'vuex'
import OrderInfoList from "./OrderInfoList.vue";
import OrderTrack from "./orderTrack.vue";

export default {
    name: 'OrderConfirmBox',
  components: {
    OrderInfoList,
    OrderTrack,
  },
    props: {
        
        params: {
            type: Object,
            default() {
                return {
                    msg: '客户正在进行融合变更业务，是否切换到查询客户资料',
                    confirm: null,
                    cancel: null,
                }
            }
        }
    },
  
    data() {
        return {
          respReasonObj:{},
          classReasonArr:[],
          classReason:"",
          reasonDetailArr:[],
          reasonDetail:"",
          popupShow1:false,
          popupShow2:false,
          popListShow: false,
          
          loading: false,
            disabled: false,
            select1: false,
            select2: false,
          imageList: [
            require('../../../images/arrow.png'),
            require('../../../images/add.png')
          ],
          resp:[
           
          ]
        }
    },

  computed: {
    ...mapState([
      'showOrderList',
        'mqBookFlagData',
        'orderInfoLists',
        'reqDatas'
    ]),
        hiddenCancelButton() {
            return !!this.params?.hiddenCancelButton
        },
        hiddenConfirmButton() {
            return !!this.params?.hiddenConfirmButton
        },
    },
  mounted() {
    this.setReqDatas({});
    this.getReason();
  },
  created() {
      this.setBlockShow(true);
       this.mqBookFlagData.bookAddrTimeFlag="1";
       this.setMqBookFlagData(this.mqBookFlagData);
       this.resp=[];
    for(let i=0;i<this.orderInfoLists.length;i++){
      this.resp.push(this.orderInfoLists[i])
      this.resp[i].custName=this.resp[i].custName+'-'
      if(this.isEmpty( this.resp[i].remarkByQry)){
        this.resp[i].remarkByQry="无"
      }
    }
    },
    methods: {
      ...mapMutations([
        'setFlowStep',
        'setShowOrderList',
        'setRobotWorking',
        'deleteLast',
          'setMqBookFlagData',
          'setOrderInfoLists',
          'setBlockShow',
          'setReqDatas'
      ]),
      ...mapActions(['updateChatList']),
      sureChioce(type){
        if(type==1){
          this.popupShow1=false;
          this.popupShow2=true;
        }else if(type==2){
          this.popupShow2=false;
          let data = {
            inputType: "1",
            type: '1',
            textInput: "orderCancelAction",
            notifyFlag: '',
            taskName:'智能订单查询'
          }
          this.$emit('newChatApi', data);
        }
      },
      cancelOrder(item){
        this.classReasonArr=[];
        this.classReasonArr=this.respReasonObj.reasonArr
        this.reqDatas.orderId=item.orderId
        this.reqDatas.senceNameCode=item.senceNameCode
        this.setReqDatas( this.reqDatas);
        this.popupShow1=true;
      },
      onSelect1(item){
        this.reasonDetailArr=[];
        this.classReason=item.codeDesc
        this.reqDatas.reason=item.codeValue
        this.reasonDetailArr=this.respReasonObj[item.codeValue]
        this.setReqDatas( this.reqDatas);
        this.popupShow1=false;
        this.popupShow2=true;
      },
      onSelect2(item){
        this.reasonDetail=item.codeDesc
        this.reqDatas.serviceReason=item.codeDesc
        this.reqDatas.reasonCode=item.codeValue
        this.setReqDatas( this.reqDatas);
        this.popupShow2=false;
        let data = {
          inputType: "1",
          type: '1',
          textInput: "orderCancelAction",
          notifyFlag: '',
          taskName:'智能订单查询'
        }
        this.$emit('newChatApi', data);
      },
      
        confirmFn() {
          this.mqBookFlagData.bookAddrTimeFlag="0";
          this.setMqBookFlagData(this.mqBookFlagData);
            if (this.params?.confirm && (typeof this.params?.confirm === 'function')) {
                this.disabled = true
                this.select1 = true
                this.params.confirm()
            } else {
                console.log('缺少confirm参数或confirm参数不是函数');
            }
            
          let data = {
            inputType: "1",
            type: '1',
            textInput: "success",
            notifyFlag: '',
            taskName:'智能订单查询'
          }
          this.$emit('newChatApi', data);
        },
      getReason(){
        this.$http.post('/mpComm/getReason', {}).then(res => {
          if (res.respCode == "0000") {
            this.respReasonObj=res.respData
          }
          else{
            this.$toast(res.resMsg);
          }
        }).catch(e => {
          this.$toast(e)
        })
      },
      isEmpty(value) {
        let flag = false
        if (value == '' || value == 'undefined' || value == undefined || value == null || value == 'null') {
          flag = true
        }
        return flag
      },
      closeListPopup(){
        this.setShowOrderList(false);
        this.mqBookFlagData.bookAddrTimeFlag="1";
        this.setMqBookFlagData(this.mqBookFlagData);
        console.log(this.mqBookFlagData.bookAddrTimeFlag,"this.mqBookFlagData")
      },
      
        cancelFn() {
          this.mqBookFlagData.bookAddrTimeFlag="0";
          this.setMqBookFlagData(this.mqBookFlagData);

          if (this.params?.cancel && (typeof this.params?.cancel === 'function')) {
                this.disabled = true
                this.select2 = true
                this.parasetFlowStepms.cancel()
            } else {
                console.log('缺少cancel参数或cancel参数不是函数');
            }
          // this.popListShow=true;
          let data = {
            inputType: "1",
            type: '1',
            textInput: "fail",
            notifyFlag: '',
            taskName:'智能订单查询'
          }
          this.$emit('newChatApi', data);
          // this.setShowOrderList(true);

        },
    },
    beforeDestroy() {
        
    }
};
</script>


<style lang="scss" scoped>
.popup1{
  .title{
    text-align: center;
    font-size: 16px;
    color: #263A5F;
    font-weight: 700;
    width: 96%;
    padding: 0 0 10px 10px;
    border-bottom: 1px solid rgba(213,221,234,1);
  }
}
.desc {
  color: black ;
  font-size:14px ;
  line-height: 20px;
  font-weight: bolder ;
  background-color: #E9F3FC;
}
.content {
}
.footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 30px 0;
    //::v-deep .van-button--small {
        //padding-left: 26px;
        //padding-right: 26px;
    //}
}

.van-button__icon {
    margin-top: 5px;
}
.sub{
  border: 2px solid rgba(73,124,246,1);
  border-radius: 5px;
  color: rgba(73,124,246,1);
  //margin-top: 20px;
}
.img-title{
  width: 50px;
  margin-left: 10px;
  margin-top: 10px;
}
.card-group {
  background-color: #E9F3FC;
  padding: 10px;
  border-radius: 8px;
  .tip1 {
    clear: both;
    .van-cell {
      color: black !important;
      padding: 0;
      margin:7px 0px;
    }
    .van-cell__value {
      color: black;
    }}
  .tip {
    .van-cell__value {
      font-weight: normal !important;
      color: #666666;
      font-size: 13px !important;
      text-align: right;
    }
    .van-cell__title {
      width: 70%;
    }
  }
  .card-list {
    display: inline-block;
    width: 100%;
    .van-cell__title {
      font-size: 13px;
      color: #666666;
      width: 60%;
    }
    .van-cell__value {
      color: black;
      text-align: right;
      font-size: 13px;
    }
    .van-cell {
      color: black !important;
      padding: 0;
    }
    .van-cell::after {
      color: black !important;
      border-bottom: 0;

    }
  }
  .van-hairline--top-bottom::after, .van-hairline-unset--top-bottom::after {
    border-width: 0 0;
  }
}
.card {
  .van-cell__title {
    font-size: 13px;
    color: #000000;
  }
  .checkButton {
    width: 70px;
    height: 30px;
    background: #0081FF;
    border-radius: 4px;
  }
  .checkFont {
    font-size: 13px;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 14px;
  }
}
.check{
  .van-cell__title {
    font-size: 13px;
    color: #333333;
  }
}

</style>