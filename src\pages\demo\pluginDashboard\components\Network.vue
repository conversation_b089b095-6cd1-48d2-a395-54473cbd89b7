<template>
    <van-button
        @click="callServer"
        type="primary"
    >
        网络请求
    </van-button>
</template>
<script>
export default {
    data() {
        return {

        };
    },
    methods: {
        callServer() {
            this.$http.post('/queryOrder', {
                name: 'qq',
                age: 20
            }).then((res) => {
                // eslint-disable-next-line no-console
                console.log(res);
            }).catch((e) => {
                // eslint-disable-next-line no-console
                console.log(e);
            });
        },
    }
};
</script>
<style>

</style>
