<template>
    <div :class="{ 'terminal-type mg-b-10': showThisPopup }">
        <van-popup v-model="showThisPopup" style="max-height: 80%;" position="bottom" class="new-popup">
            <!--头部标题-->
            <van-icon class="image van-icon-rux-guanbi" @click="closeThisPopup" />
            <div class="terminal-way-popup-title border-buttom-1 pd-tb-15 terminal-way-popup">
                <van-row type="flex" justify="space-between" class="terminal-type-row">
                    <van-col span="23" class="t-title">
                        <span>{{ title }}</span><span class="font-gray font-size-13">{{ secondTitle }}</span>
                    </van-col>
                </van-row>
            </div>
            <div class="terminal-way-popup new-content">
                <!--内容-->
                <van-picker
                    class="picker"
                    :show-toolbar="false"
                    :columns="options"
                    :default-index="defaultIndex"
                    @change="changePicker"
                />
                <!--确定按钮-->
                <div class="popup-button new-ui">
                    <div class="my_confirm" @click="submit">
                        <span class="my-button__text">确定</span>
                    </div>
                </div>
            </div>
        </van-popup>
    </div>
</template>

<script>
export default {
    name: 'SelectPopup',
    components: {},
    model: {
        prop: 'showPopup'
    },
    props: {
        showPopup: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: '选择商品'
        },
        secondTitle: {
            type: String,
            default: ''
        },
        options: {
            type: Array,
            default: () => []
        },
        defaultIndex: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            showThisPopup: false,
            selectValue: this.options[this.defaultIndex]
        };
    },
    watch: {
        showThisPopup(newValue) {
            this.$emit('input', newValue);
        },
        showPopup(newValue) {
            this.showThisPopup = newValue;
        }
    },
    mounted() { },
    methods: {
        closeThisPopup() {
            this.showThisPopup = false;
            this.$emit('close');
        },
        submit() {
            this.showThisPopup = false;
            if (this.selectValue == undefined) {
                this.selectValue = this.options[this.defaultIndex];
            }
            this.$emit('select', this.selectValue);
        },
        changePicker(picker, item) {
            this.selectValue = item;
        }
    }
};
</script>

<style scoped lang="scss">
$size: 16px;
$weight: 400px;
$color: #ffffff;

@mixin font-PingFangSC-Medium($size: $size, $weight: $weight, $color: $color) {
    /* 字体风格,提供默认参数值 */
    font-size: $size;
    font-weight: $weight;
    color: $color;
    font-family: PingFangSC-Medium, PingFang SC;
}

.bc-white {
    background-color: #ffffff;
}

.pd-tb-18 {
    padding: 18px 0;
}

.pd-tb-15 {
    padding: 15px 0;
}

.border-buttom-1 {
    position: relative;
}

.border-buttom-1::after {
    content: '';
    display: block;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: #e5e5e5;
    transform: scaleY(0.5);
}

.font-gray {
    color: #999999;
}

.transform-90 {
    transform: rotate(90deg);
}

.text-right {
    text-align: right;
}

.image {
    position: absolute;
    right: 0;
    top: 0;
    width: 20px;
    height: 20px;
    font-size: initial;
    padding: 15px;
    z-index: 1;
}

.formItem-required::before {
    position: relative;
    left: 0;
    color: #f65f5d;
    font-size: 0.37333rem;
    content: '*';
    padding-right: 3px;
}

.terminal-type {
    @extend .bc-white;

    padding: 0 15px;
}

.terminal-type .terminal-way-popup-title {
    line-height: 22px;
    padding-right: 40px;

    @include font-PingFangSC-Medium(18px, 600, #000000);
}

.terminal-way-popup {
    padding-right: 20px;
    padding-left: 20px;
    position: relative;

    .popup-button {
        .my_confirm {
            height: 44px;
            line-height: 44px;
            background: #0081ff;
            border-radius: 4px;
            display: flex; //flex布局
            justify-content: center; //使子项目水平居中
            align-items: center; //使子项目垂直居中

            .my-button__text {
                line-height: 23px;
                letter-spacing: 1px;

                @include font-PingFangSC-Medium(16px, 600, #FFFFFF);
            }
        }
    }
}

.close-box {
    text-align: right;
    position: relative;

    .van-button {
        width: 1.8rem;
    }
}

.terminal-type-row {
    align-items: center;
}

.new-ui {
    .t-title {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .new-popup {
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .popup-button {
            width: 100%;
            left: 0;
            background: #ffffff;
            box-sizing: border-box;
        }

        .new-product-msg {
            position: absolute;
            right: 0;
            top: 1.7rem;
            background: linear-gradient(to right, #6cc1ff, #4d78ff);
            color: #ffffff;
            padding: 0.13rem 0.3rem 0.2rem 0.5rem;
            border-radius: 0.5rem 0 0 0.5rem;
            font-size: 0.34rem;
            z-index: 12;
        }
    }

    .new-content {
        padding-top: 0.1rem;
        position: relative;
        overflow-y: auto;
        padding-bottom: 46px;
    }

    .image {
        padding: 0.5rem 0.4rem;
        font-size: 0.4rem;
    }

    .terminal-way-popup .popup-button .my_confirm {
        flex: 1;
        border-radius: 0.1rem;
    }
}
</style>
