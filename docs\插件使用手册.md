## 插件使用手册

### 插件安装方法

1. 模板工程使用方法
    源码版本基于es6模块开发，需要工程（例如掌上通）自身支持webpack等打包构建工具。
    将beePlugin目录加入工程代码，通过es6的import语法引入beePlugin目录下的index.js即可使用所有插件。
    模板工程已内置了插件，通过es6的import语法从assets/js/beePlugin目录下引入即可。
    WadeMobile中包含了拍照、人脸识别、蓝牙读卡器等pc端无法模拟实现的插件.
    Mobile中包含应用跳转、读写缓存、调用服务等pc端和真机均有实现的插件。
    Common是对WadeMobile和Mobile基于具体业务的二次封装，主要提供了Common.callServer方法，用于调用后台接口（仅IPU可用）
   ```javascript
   import {Mobile, WadeMobile, Common} from "../../../assets/js/beePlugin";
   WadeMobile.getPhoto("base64").then((img) => {
       console.log("照片的base64编码：" + img);
   }, (err) => {
       console.log(err);
   });
   ```
```
    
2. 仅使用插件，不使用模板工程
    我们提供了插件的打包压缩版本bee-plugin.js，该版本已将插件打包为一个js文件，直接引入html就可以使用，不需要前端构建工具的处理。
    在官网下载bee-plugin.js文件后，将bee-plugin.js引入到项目工程中，通过beePlugin全局变量可访问到所有插件。
   ```html
   <script src="./bee-plugin.js"></script>
   <script type="text/javascript">
       beePlugin.WadeMobile.getPhoto("base64").then((img) => {
           console.log("照片的base64编码：" + img);
       }, (err) => {
           console.log(err);
       });
       beePlugin.Mobile.getMemoryCache(["key2", "key3"]).then((value) => {
           console.log(value.key2);
           console.log(value.key3);
       });
   </script>
```

   备注：插件采用了es6中的Promise语法实现异步编程，如果项目本身不支持Promise语法需引入[polyfills-promise.js](https://cdn.jsdelivr.net/npm/promise-polyfill@8/dist/polyfill.min.js)来兼容Promise。


### 基础插件使用说明

1. 判断是否是app

   * 功能

     判断运行环境是否是安卓、IOS手机，若是返回true，否则（pc环境）返回false。

   * 定义

     ```javascript
     Mobile.isApp()
     ```

   * 参数

     无

   * 返回值

     boolean

   * 示例

     ```javascript
     let isApp = Mobile.isApp();
     if(isApp) {
         //app环境下执行
     }else {
         //pc环境下执行
     }
     ```

2. 判断是否是安卓

   * 功能

     判断是否是安卓手机

   * 定义

     ```javascript
     Mobile.isAndroid()
     ```

   * 参数

     无

   * 返回值

     boolean

   * 示例

     ```javascript
     if(Mobile.isAndroid()) {
         console.log("当前设备为安卓手机");
     }
     ```

3. 判断是否是IOS手机

   * 功能

     判断是否是苹果手机

   * 定义

     ```javascript
     Mobile.isIOS()
     ```

   * 参数

     无

   * 返回值

     boolean

   * 示例

     ```javascript
     if(Mobile.isIOS()) {
         console.log("当前设备为苹果手机");
     }
     ```

4. 退出App

   * 功能

     退出App

   * 定义

     ```javascript
     Mobile.closeApp()
     ```

   * 参数

     无

   * 返回值

     无

   * 示例

     ```javascript
     Mobile.closeApp();
     ```

5. 打开H5子应用

   * 功能

     打开第三方Web应用。

   * 定义

     ```javascript
     Mobile.openUrl(url, callback, title, buttons, styles, err)
     ```

   * 参数

     | 参数名   | 参数类型 | 参数描述          | 备注 |
     | -------- | -------- | ----------------- | ---- |
     | url      | string   | 第三方web应用地址 | 必填 |
     | callback | function | 成功回调          | 可选 |
     | title    | string   | 标题              | 可选 |
     | buttons  | Array    |                   | 可选 |
     | styles   | Array    |                   | 可选 |
     | err      | function | 错误回调          | 可选 |

   * 返回值

     无

   * 示例

     ```javascript
     Mobile.openUrl("http://localhost:8020/mainapp/mobile?action=Index");
     ```

   * 备注

     pc端回调无效。

6. 关闭H5子应用

   * 功能

     关闭H5子应用。

   * 定义

     ```javascript
     Mobile.closeUrl(result)
     ```

   * 参数

     | 参数名 | 参数类型 | 参数描述                           | 备注 |
     | ------ | -------- | ---------------------------------- | ---- |
     | result | string   | 传递给Mobile.openUrl成功回调的参数 | 可选 |

   * 返回值

     无

   * 示例

     ```javascript
     Mobile.closeUrl("processResult");
     ```

   * 备注

     暂时无人用到，功能存在疑问。

7. 打开IPU子应用

   * 功能

     打开IPU子应用。

   * 定义

     ```javascript
     Mobile.openIpuApp(param, callback, err)
     ```

   * 参数

     | 参数名   | 参数类型 | 参数描述              | 备注 |
     | -------- | -------- | --------------------- | ---- |
     | param    | object   | 要打开的Ipu应用的信息 | 必传 |
     | callback | function | 成功回调函数          | 可选 |
     | err      | function | 错误回调函数          | 可选 |

   * 返回值

     无

   * 示例

     ```javascript
     let param = {
         APP_ID: "200001",
         MENU_PAGE_ACTION: "orderQuery",
         MENU_WELCOME_PAGE: "welcome/welcome.htm",
         EXT_PARAM: JSON.stringify({
             "EXT_1": "RT0760004"
         })
     };
     Mobile.openIpuApp(param, function(str) {
         //打开的子应用关闭时触发
     });
     ```

   * 备注

     1. 示例中的param参数必须包含"APP_ID"、"MENU_PAGE_ACTION"属性；
     2. "EXT_PARAM"的值必须是JSON字符串；
     3. pc端开发时callback、err回调无效，仅在真机运行时生效。
     4. 只能适用于打开IPU子应用
     5. 与8相关

8. 关闭IPU子应用

   * 功能

     关闭IPU子应用，并触发Mobile.openIpuApp的回调。

   * 定义

     ```javascript
     Mobile.closeIpuApp(result)
     ```

   * 参数

     | 参数名 | 参数类型 | 参数描述                                  | 备注 |
     | ------ | -------- | ----------------------------------------- | ---- |
     | result | string   | 传递给Mobile.openIpuApp成功回调函数的参数 | 可选 |

   * 返回值

     无

   * 示例

     ```javascript
     Mobile.closeIpuApp("SESSION_TIMEOUT");
     ```

   * 备注

     1. result参数仅在真机上运行时生效，pc端开发时无效。
     2. 与7相关

9. 打开原生子应用

   * 功能

     打开原生应用。

   * 定义

     ```javascript
     Mobile.openNativeApp(param, err)
     ```

   * 参数

     | 参数名 | 参数类型 | 参数描述                 | 备注 |
     | ------ | -------- | ------------------------ | ---- |
     | param  | object   | 打开的原生应用的参数信息 | 必传 |
     | err    | function | 错误回调函数             | 可选 |

   * 返回值

     无

   * 示例

     ```javascript
     //安卓端的param
     let param = {
         MENU_ANDROID_MAIN_CLASS: "",
         ANDROID_TYPE: "",
         EXTRA_PARAMS: ""
     }
     //IOS端的param
     let param2 = {
         MENU_IOS_MAIN_CLASS: "",
         EXTRA_PARAMS: ""
     }
     Mobile.openNativeApp(param);
     ```

   * 备注

     无关闭方法，因为已经跳转到其他app上面了。

10. 打开指定的模板页面（仅IPU可用）

   * 功能

     打开指定的模板页面

   * 定义

     ```javascript
     Mobile.openTemplate(pageAction, [param], err)
     ```

   * 参数

     | 参数名     | 参数类型 | 参数描述                     | 备注                                             |
     | ---------- | -------- | ---------------------------- | ------------------------------------------------ |
     | pageAction | string   | 解析模板页面对应的pageAction | 必传，对应配置文件server-page.xml中的action.name |
     | param      | object   | 解析模板页面需要的渲染数据   | 可选，缺省时模板页面无需渲染数据                 |
     | err        | function | 错误回调函数                 |                                                  |

   * 返回值

     无

   * 示例

     ```javascript
     Mobile.openTemplate("QrCode", {
         envType: "envType"
     });
     ```

11. 设置回调监听器（仅IPU可用）

    * 功能

      为Mobile.openTemplate设置回调监听器函数

    * 定义

      ```javascript
      WadeMobile.setBackCallListener(callback)
      ```

    * 参数

      | 参数名   | 参数类型 | 参数描述           | 备注 |
      | -------- | -------- | ------------------ | ---- |
      | callback | function | 需要设置的回调函数 | 必传 |

    * 返回值

      无

    * 示例

      ```javascript
      WadeMobile.setBackCallListener(function(e) {
          //Mobile.openTemplate打开的页面通过Mobile.backWithCallback关闭时触发
          //需要通过e.data获取传递的参数
          //获取的参数为字符串形式
          let param = e.data;
      });
      Mobile.openTemplate("QrCode", {
          envType: "envType"
      });
      ```

    * 备注

      1. Mobile.openTemplate没有成功回调函数，需要配合WadeMobile.setBackCallListener来监听所打开页面通过Mobile.backWithCallback关闭时触发的回调，并获得返回的参数。
      2. 与10相关

12. 回退上一级视图（仅IPU可用）

    * 功能

      回退上一级视图

    * 定义

      ```javascript
      Mobile.back(tag)
      ```

    * 参数

      | 参数名 | 参数类型 | 参数描述                                                     | 备注 |
      | ------ | -------- | ------------------------------------------------------------ | ---- |
      | tag    | string   | 回退到指定界面，tag对应配置文件server-page.xml中的action.name | 可选 |

    * 返回值

      无

    * 示例

      ```javascript
      Mobile.back();//回退到上一级界面
      Mobile.back("Index");//回退到指定的界面，首页
      ```

    * 备注

      ​	对应浏览器中的history.go(-1)语法，但是移动端上的实现是完全不一样的，因为移动端上是多视图的，而浏览器严格来说是单视图的。并且back中的参数tag可以实现回退到指定的界面。

13. 回退视图层级（仅IPU可用）

    * 功能

      回退视图层级，并触发回调事件

    * 定义

      ```javascript
      Mobile.backWithCallback(data, tag, err)
      ```

    * 参数

      | 参数名 | 参数类型 | 参数描述                                                     | 备注 |
      | ------ | -------- | ------------------------------------------------------------ | ---- |
      | data   | string   | 传递给WadeMobile.setBackCallListener成功回调的参数           | 可选 |
      | tag    | string   | 回退到指定界面，tag对应配置文件server-page.xml中的action.name | 可选 |
      | err    | function | 错误回调函数                                                 | 可选 |

    * 返回值

      无

    * 示例

      ```javascript
      let param = {reloadMenu: "test"};
      //将对象转为字符串
      param = JSON.stringify(param);
      Mobile.backWithCallback(param);
      ```

14. 获取openTemplate传递的参数（仅IPU可用）

    * 功能

      获取Mobile.openTemplate传递的参数

    * 定义

      ```javascript
      Mobile.getPostParam()
      ```

    * 参数

      无

    * 返回值

      Promise

    * 示例

      ```javascript
      //页面1
      Mobile.openTemplate("QrCode", {
          envType: "envType"
      });
      //QrCode页面
      Mobile.getPostParam().then((data) => {
          console.log(data);
      }, (err) => {
          alert(err);
      });
      ```

15. 调用服务（仅IPU可用）

    * 功能

      调用后端服务

    * 定义

      ```javascript
      Mobile.dataRequest(action, param)
      ```

    * 参数

      | 参数名 | 参数类型 | 参数描述                         | 备注 |
      | ------ | -------- | -------------------------------- | ---- |
      | action | string   | 服务端用@WServerData注册的Action | 必填 |
      | param  | object   | 调用服务的参数                   | 可选 |

    * 返回值

      Promise

    * 示例

      ```javascript
      Mobile.dataRequest("PayForKd.qryNofeeNorealTradeByNum", {
          serialNumber: '13124324'
      }).then((result) => {
          console.log(result);
      }, (err) => {
          console.log(err);
      });
      ```
      
    * 备注
      IPU框架下推荐使用Common.callServer,该方法是对Mobile.dataRequest基于业务的二次封装，已在请求体中加入了SESSION_ID等参数，省份也可根据自己业务需求对该方法进行修改，方法定义如下：
        Common.callServer(action, param, timeout)
      action和param参数以及返回值与Mobile.dataRequest插件含义相同，timeout为调用接口的最大超时时间，参数类型为整数，默认为30，表示30秒超时
16. 设置内存缓存

    * 功能

      将一个键值对保存到缓存中，应用关闭时将失去此记录。

    * 定义

      ```javascript
      Mobile.setMemoryCache(key, value)
      或
      Mobile.setMemoryCache(map)
      ```
      
    * 参数

      | 参数名 | 参数类型 | 参数描述 | 备注 |
      | ------ | -------- | -------- | ---- |
      | key    | string   | 键名     | 必填 |
      | value  | string   | 保存的值 | 必填 |
      或
      
      | 参数名 | 参数类型 | 参数描述 | 备注 |
      | ------ | -------- | -------- | ---- |
      | map    | object   | 键值对对象     | 必填 |

    * 返回值

      无

    * 示例

      ```javascript
      Mobile.setMemoryCache("key1", "value1");
      Mobile.setMemoryCache({
          "key2": "value2",
          "key3": "value3"
      })
      ```

17. 获取内存缓存

    * 功能

      从缓存中读取指定键名的值。

    * 定义

      ```javascript
      Mobile.getMemoryCache(key)
      ```
      
    * 参数

      | 参数名 | 参数类型 | 参数描述 | 备注 |
      | ------ | -------- | -------- | ---- |
      | key    | string或string数组   | 键名     | 必填 |
      
    * 返回值

      Promise

    * 示例

      ```javascript
      Mobile.getMemoryCache("key1").then((value) => {
          console.log(value);
      });
      Mobile.getMemoryCache(["key2", "key3"]).then((value) => {
          console.log(value.key2);
          console.log(value.key3);
      });
      ```

18. 删除内存缓存

    * 功能

      删除缓存中指定键值对。

    * 定义

      ```javascript
      Mobile.removeMemoryCache(key)
      ```

    * 参数

      | 参数名 | 参数类型 | 参数描述         | 备注 |
      | ------ | -------- | ---------------- | ---- |
      | key    | string   | 指定要删除的键名 | 必填 |

    * 返回值

      无

    * 示例

      ```javascript
      Mobile.removeMemoryCache("key1");
      ```

19. 清空内存缓存

    * 功能

      清空缓存中所有的键值对。

    * 定义

      ```javascript
      Mobile.clearMemoryCache()
      ```

    * 参数

      无

    * 返回值

      无

    * 示例

      ```javascript
      Mobile.clearMemoryCache();
      ```

20. 设置离线文件缓存

    * 功能

      ​	添加一个键值对到一个离线缓存文件中，退出应用后，数据仍然存在。（此文件仅本应用程序可见，但内容仍然以明文方式保存）

    * 定义

      ```javascript
      Mobile.setOfflineCache(key, value)
      ```
      
    * 参数

      | 参数名 | 参数类型 | 参数描述 | 备注 |
      | ------ | -------- | -------- | ---- |
      | key    | string   | 键名     | 必填 |
      | value  | string   | 保存的值 | 必填 |

    * 返回值

      无

    * 示例

      ```javascript
      Mobile.setOfflineCache("key1", "value1");
      ```

21. 获取离线文件缓存

    * 功能

      获取离线缓存文件中指定键名的值。

    * 定义

      ```javascript
      Mobile.getOfflineCache(key)
      ```
      
    * 参数

      | 参数名 | 参数类型 | 参数描述 | 备注 |
      | ------ | -------- | -------- | ---- |
      | key    | string   | 键名     | 必填 |
      
    * 返回值

      Promise

    * 示例

      ```javascript
      Mobile.getOfflineCache("key1").then((value) => {
          console.log(value);
      });
      ```

22. 删除离线文件缓存

    * 功能

      删除离线缓存文件中指定的键值对。

    * 定义

      ```javascript
      Mobile.removeOfflineCache(key)
      ```

    * 参数

      | 参数名 | 参数类型 | 参数描述         | 备注 |
      | ------ | -------- | ---------------- | ---- |
      | key    | string   | 指定要删除的键名 | 必填 |

    * 返回值

      无

    * 示例

      ```javascript
      Mobile.removeOfflineCache("key1");
      ```

23. 清空离线文件缓存

    * 功能

      清空离线缓存文件中所有的键值对。

    * 定义

      ```javascript
      Mobile.clearOfflineCache()
      ```

    * 参数

      无

    * 返回值

      无

    * 示例

      ```javascript
      Mobile.clearOfflineCache();
      ```

### 业务插件使用说明

1. 获取系统信息

   * 功能

     获取系统相关信息

   * 定义

     ```javascript
     WadeMobile.getSysInfo(key)
     ```

   * 参数

     | 参数名 | 参数类型 | 参数描述           | 备注                 |
     | ------ | -------- | ------------------ | -------------------- |
     | key    | string   | 指定需要获取的信息 | 必传，可选值参见备注 |

   * 返回值

     Promise，完成函数的入参为成功获取到的设备信息，拒绝函数的入参为异常信息

   * 示例

     ```javascript
     WadeMobile.getSysInfo("IMEI").then((info) => {
     	console.log("获取imei信息：" + info);
     }, (err) => {
     	console.log("获取imei信息失败：" + err);
     });
     ```

   * 备注

     IMEI：获取移动设备国际身份码。 

     IMSI：获取国际移动用户识别码，区别移动用户的标志，储存在SIM卡中。 

     MODEL：获取手机型号。 

     UUID：通用唯一识别码，软件唯一标识。 

     MANUFACTURER：获取制造商信息。

     BRAND：获取手机品牌。 

     SDKVERSION：获取SDK版本。 

     OSVERSION：获取操作系统版本。 

     SIMNUMBER：SIM卡的序号 

     PLATFORM：获取平台类型。Android或者IOS 

     TIMEZONEID：国际时区 

     PRODUCTNAME：产品名称

2. 获取网络信息

   * 功能

     获取网络相关信息

   * 定义

     ```javascript
     WadeMobile.getNetInfo(key)
     ```

   * 参数

     | 参数名 | 参数类型 | 参数描述           | 备注                 |
     | ------ | -------- | ------------------ | -------------------- |
     | key    | string   | 指定需要获取的信息 | 必传，可选值参见备注 |

   * 返回值

     Promise，完成函数的入参为成功获取到的网络信息，拒绝函数的入参为异常信息

   * 示例

     ```javascript
     WadeMobile.getNetInfo("MAC").then((info) => {
         console.log("移动设备的MAC地址为：" + info);
     }, (err) => {
         console.log("获取MAC地址失败：" + err);
     });
     ```

   * 备注

     IP：获取移动设备的IPV4地址。

     IPV4：获取移动设备的IPV4地址。

     IPV6：获取移动设备的IPV6地址。

     MAC：获取移动设备的MAC地址。

3. 拍照（安卓用getPhoto2，ios用getPhoto）

   * 功能

     调用手机的拍照功能，返回照片的路径或相片的base64编码。

   * 定义

     ```javascript
     WadeMobile.getPhoto2(type)
     ```

   * 参数

     | 参数名 | 参数类型 | 参数描述                                                     | 备注               |
     | ------ | -------- | ------------------------------------------------------------ | ------------------ |
     | type   | string   | 返回的相片的类型<br />可选值包括<br />"base64"：返回相片的base64编码 <br />"path"：返回相片的路径 | 可选，默认为"path" |

   * 返回值

     Promise

   * 示例

     ```javascript
     WadeMobile.getPhoto2("base64").then((img) => {
         console.log("照片的base64编码：" + img);
     }, (err) => {
         console.log(err);
     });
     ```
    * 备注
    getPhoto已废弃

4. 调用自定义相机

   * 功能

     调用自定义相机，引导拍摄人脸，身份证正反面照片，返回相片路径或者base64编码。

   * 定义

     ```javascript
     WadeMobile.getIdentifyPhoto(frameType,resultType)
     ```

   * 参数

     | 参数名     | 参数类型 | 参数描述                                                     | 备注               |
     | ---------- | -------- | ------------------------------------------------------------ | ------------------ |
     | frameType  | string   | 拍摄类型，可选值包括<br />"idCardFront"：身份证正面<br />"idCardBack"：身份证反面<br />"noBorder"：无框拍照<br />"face"：人脸拍照 | 可选，默认为"face" |
     | resultType | string   | 返回的相片的类型<br />可选值包括<br />"base64"：返回相片的base64编码<br />"path"：返回相片的路径 | 可选，默认为"path" |

   * 返回值

     Promise

   * 示例

     ```javascript
     WadeMobile.getIdentifyPhoto("idCardFront", "base64").then((img) => {
         console.log(img);
     }, (err) => {
         console.log(err);
     });
     ```

5. 选择图库照片

   * 功能

     调用手机自带的图库类应用，选择一张照片后返回路径或base64编码。

   * 定义

     ```javascript
     WadeMobile.getPicture(type)
     ```

   * 参数

     | 参数名 | 参数类型 | 参数描述                                                     | 备注               |
     | ------ | -------- | ------------------------------------------------------------ | ------------------ |
     | type   | string   | 返回的相片的类型<br />可选值包括<br />"base64"：返回相片的base64编码<br />"path"：返回相片的路径 | 可选，默认为"path" |

   * 返回值

     Promise

   * 示例

     ```javascript
     WadeMobile.getPicture("base64").then((img) => {
         console.log("照片的base64编码：" + img);
     }, (err) => {
         console.log(err);
     });
     ```

6. 获取客户端资源版本号

   * 功能

     获取客户端资源版本号

   * 定义

     ```javascript
     WadeMobile.getClientResourceVersion()
     ```

   * 参数

     无

   * 返回值

     Promise

   * 示例

     ```javascript
     WadeMobile.getClientResourceVersion().then((version) => {
         console.log("客户端版本号:" + version);
     });
     ```

7. 手机响铃功能

   * 功能

     调用手机的响铃功能

   * 定义

     ```javascript
     WadeMobile.beep(count)
     ```

   * 参数

     | 参数名 | 参数类型 | 参数描述 | 备注        |
     | ------ | -------- | -------- | ----------- |
     | count  | int      | 响铃次数 | 可选，默认1 |

   * 返回值

     无

   * 示例

     ```javascript
     WadeMobile.beep(3);
     ```

8. 手机震动功能

   * 功能

     调用手机的震动功能

   * 定义

     ```javascript
     WadeMobile.shock(duration)
     ```

   * 参数

     | 参数名   | 参数类型 | 参数描述     | 备注           |
     | -------- | -------- | ------------ | -------------- |
     | duration | int      | 震动持续时间 | 可选，单位毫秒 |

   * 返回值

     无

   * 示例

     ```javascript
     WadeMobile.shock(2000);
     ```

9. 打电话

   * 功能

     调用手机拨打电话。

   * 定义

     ```javascript
     WadeMobile.call(num, autoCall)
     ```

   * 参数

     | 参数名   | 参数类型 | 参数描述     | 备注                                                         |
     | -------- | -------- | ------------ | ------------------------------------------------------------ |
     | num      | string   | 电话号码     | 必传                                                         |
     | autoCall | boolean  | 是否自动拨打 | 可选，默认false<br />true：直接拨打电话<br />false：跳转到拨打电话界面 |

   * 返回值

     无

   * 示例

     ```javascript
     WadeMobile.call("13711112222");//跳转到电话界面
     WadeMobile.call("13711112222", true);//直接拨打
     ```

10. 发短信

    * 功能

      调用手机发送短信功能。

    * 定义

      ```javascript
      WadeMobile.sms(num, msg, autoSms)
      ```

    * 参数

      | 参数名  | 参数类型 | 参数描述           | 备注                                                         |
      | ------- | -------- | ------------------ | ------------------------------------------------------------ |
      | num     | string   | 电话号码           | 必传                                                         |
      | msg     | string   | 短信内容           | 必传                                                         |
      | autoSms | boolean  | 是否跳转到短信页面 | 可选，默认false<br />true：自动发送短信<br />false：跳转至短信界面 |

    * 返回值

      无

    * 示例

      ```javascript
      WadeMobile.sms("10010", "余额查询");//跳转到短信界面
      WadeMobile.sms("10010", "余额查询", true);//直接发送，不跳转到短信界面
      ```

11. 获取定位

    * 功能

      获取当前位置的信息

    * 定义

      ```javascript
      WadeMobile.location()
      ```

    * 参数

      无

    * 返回值

      Promise，完成函数的入参为获取到的对象形式的定位信息，出错时执行拒绝函数。

    * 示例

      ```javascript
      WadeMobile.location().then((info) => {
          if(WadeMobile.isAndroid()) {
              console.log("省份：" + info.PROVINCE);
          }else if(WadeMobile.isIOS()) {
              console.log("省份：" + info.Province);
          }
          
      }, (err) => {
          console.log(err);
      });
      ```
      
    * 备注

      ```javascript
      //安卓和ios返回的字段有区别，需要判断所属平台后再取值
      //安卓返回的定位信息格式如下
      {
          ACCURACY: "",
          ADCODE: "",
          ADDRESS: "",
          ALTITUDE: "",
          BEARING: "",
          BUILDINGID: "",
          CITY: "",
          CITYCODE: "",
          COUNTRY: "",
          DISTRICT: "",
          FLOOR: "",
          GETPOINAME: "",
          GPSSATELLITES: "",
          GPSSTATUS: "",
          ISWIFIABLE: "",
          LOCATIONDETAIL: "",
          LOCATIONTYPE: "",
          Latitude: "",
          Longitude: "",
          POSITIONINGTIME: "",
          POSITIONINGTIMEFORMAT: "",
          PROVIDER: "",
          PROVINCE: "",
          RESULTFLAG: "",
          SATELLITES: "",
          SPEED: "",
          STREET: "",
          STREETNUM: ""
      }
      //ios返回的定位信息格式如下
      {
          Accuracy: "",
          AdCode: "",
          Altitude: "",
          City: "",
          CityCode: "",
          District: "",
          Latitude: "",
          LocationDesc: "",
          Longitude: "",
          Provider: "",
          Province: "",
          Time: ""
      }
      ```

12. 扫描二维码或条形码

    * 功能

      扫描二维码或条形码，返回识别的值。

    * 定义

      ```javascript
      WadeMobile.scanQrCode()
      ```

    * 参数

      无

    * 返回值

      Promise

    * 示例

      ```javascript
      WadeMobile.scanQrCode().then((info) => {
          console.log("二维码或条形码的信息为：" + info);
      });
      ```

13. 蓝牙分享功能

    * 功能

      开启蓝牙分享功能。注意，此蓝牙分享功能只能分享应用本身。

    * 定义

      ```javascript
      WadeMObile.shareByBluetooth(err)
      ```

    * 参数

      | 参数名 | 参数类型 | 参数描述 | 备注 |
      | ------ | -------- | -------- | ---- |
      | err    | function | 错误回调 | 可选 |

    * 返回值

      无

    * 示例

      ```javascript
      WadeMobile.shareByBluetooth();
      ```

14. 用浏览器打开指定网址

    * 功能

      调用手机默认浏览器打开指定的url

    * 定义

      ```javascript
      WadeMobile.openBrowser(url,err)
      ```

    * 参数

      | 参数名 | 参数类型 | 参数描述          | 备注 |
      | ------ | -------- | ----------------- | ---- |
      | url    | string   | 要打开的网页的url | 必传 |
      | err    | function | 错误回调函数      | 可选 |

    * 返回值

      无

    * 示例

      ```javascript
      WadeMobile.openBrowser("http://www.10010.com");
      ```

15. 录音

    * 功能

      调用原生的录音界面进行录音。

    * 定义

      ```javascript
      WadeMobile.audioRecord(auto)
      ```

    * 参数

      | 参数名 | 参数类型 | 参数描述     | 备注                                                     |
      | ------ | -------- | ------------ | -------------------------------------------------------- |
      | auto   | boolean  | 是否自动录音 | 可选，默认false<br />false：按住录音<br />true：自动录音 |

    * 返回值

      Promise，完成函数的入参为录音文件存放路径，出错时执行拒绝函数。

    * 示例

      ```javascript
      WadeMobile.audioRecord().then((path) => {
          console.log(path);
      }, (err) => {
          console.log("出错了" + err);
      });
      ```

    * 备注

      示例中的path：字符串类型，返回录音文件存放的路径。

16. 播放音频

    * 功能

      播放语音文件

    * 定义

      ```javascript
      WadeMobile.audioPlay(audioPath,hasRipple,err)
      ```

    * 参数

      | 参数名    | 参数类型 | 参数描述                 | 备注                                                  |
      | --------- | -------- | ------------------------ | ----------------------------------------------------- |
      | audioPath | string   | 将要播放的语音文件的路径 | 必填                                                  |
      | hasRipple | boolean  | 将要播放的语音文件的效果 | 可选，默认true<br />true：弹出波纹<br />false：无效果 |
      | err       | function | 错误回调                 | 可选                                                  |

    * 返回值

      无

    * 示例

      ```javascript
      WadeMobile.audioPlay("/storage/emulated/0/Sounds/004.m4a");
      ```

17. 批量文件上传

    * 功能

      文件上传，支持批量文件上传。

    * 定义

      ```javascript
      WadeMobile.uploadWithServlet(filePath,dataAction,param)
      ```

    * 参数

      | 参数名     | 参数类型 | 参数描述               | 备注 |
      | ---------- | -------- | ---------------------- | ---- |
      | filePath   | string   | 文件路径（相对于沙盒） | 必传 |
      | dataAction | string   | 服务端servlet名称      | 必传 |
      | param      | object   | 参数                   | 必传 |

    * 返回值

      Promise

    * 示例

      ```javascript
      let params = {
          "UPLOAD_PATH": "photo",//图片上传的指定相对路径，如果没有设置，默认值temp
          "FILE_NAME": "my.png",//图片默认的名字，如果没有设置，取原图片的名字
          "SESSION_ID": new Date().getTime()//服务端SESSION_ID不能为空，服务端配置verify="false"并不校验
      }
      WadeMobile.uploadWithServlet("/my123.png", "UploadDownloadBean.upload", params).then((result) => {
          console.log(result);
      }, (err) => {
          console.log(err);
      });
      ```

    * 备注

      1. 文件路径upload_path，可以是多个文件（数组），也可以是单个文件（字符串）；
      2. 返回信息result，由服务端拼凑结果。

18. 文件下载

    * 功能

      文件下载，单个文件下载。

    * 定义

      ```javascript
      WadeMobile.downloadWithServlet(savePath, dataAction, param)
      ```

    * 参数

      | 参数名     | 参数类型 | 参数描述                                            | 备注 |
      | ---------- | -------- | --------------------------------------------------- | ---- |
      | savePath   | string   | 文件保存路径（相对于沙盒）                          | 必传 |
      | dataAction | string   | 服务端servlet名称                                   | 必传 |
      | param      | object   | 参数（必须包含FILE_PATH，即文件在服务端的相对位置） | 必传 |

    * 返回值

      Promise

    * 示例

      ```javascript
      let savePath = '/my123.png';
      let params = {
          "FILE_PATH": "photo/my.png"//下载文件的相对路径
      };
      WadeMobile.downloadWithServlet(savePath, "UploadDownloadBean.download", params).then((result) => {
          console.log(result);
      }, (err) => {
          console.log(err);
      });
      ```

    * 备注

      返回信息result，由服务端拼凑结果，应返回文件在客户端的绝对路径。

19. 录制视频

    * 功能

      调用原生的视频功能进行录制。

    * 定义

      ```javascript
      WadeMobile.recordVideo(compressRatio,timeLimit)
      ```

    * 参数

      | 参数名        | 参数类型 | 参数描述     | 备注                                             |
      | ------------- | -------- | ------------ | ------------------------------------------------ |
      | compressRatio | double   | 视频压缩比   | 可选，默认1<br />0.1表示压缩比是10:1             |
      | timeLimit     | int      | 录制时长限制 | 可选，单位秒，默认30<br />超过时长则自动结束录制 |

    * 返回值

      Promise，完成函数的入参为视频文件存放路径，出错时执行拒绝函数。

    * 示例

      ```javascript
      WadeMobile.recordVideo().then((path) => {
          console.log("视频录制完成，存放路径为：" + path);
      }, (err) => {
          console.log(err);
      });
      ```

20. 保存图片到相册

    * 功能

      保存图片到相册

    * 定义

      ```javascript
      WadeMobile.saveImageToAlbum(url)
      ```

    * 参数

      | 参数名 | 参数类型 | 参数描述                             | 备注 |
      | ------ | -------- | ------------------------------------ | ---- |
      | url    | string   | 图片的url，支持file://开头的本地文件、http(s) | 必传 |

    * 返回值

      Promise，完成函数的入参为保存完成后的执行结果。

    * 示例

      ```javascript
      WadeMobile.saveImageToAlbum($("#pic").attr("src")).then((res) => {
          console.log(res);
      });
      ```

    * 备注

      传入的url为绝对路径，不可以是相对路径。
    
21. 活体识别

    * 功能

      调用活体识别插件，返回活体照片

    * 定义

      ```javascript
      WadeMobile.faceAuthentication(param)
      ```

    * 参数

      | 参数名 | 参数类型 | 参数描述                                                     | 备注 |
      | ------ | -------- | ------------------------------------------------------------ | ---- |
      | param  | array    | isTD：是否走天盾校验，0是不校验，1是校验<br />imgCode：暗水印图片Code<br />cameraType：0是后置摄像头，1是前置摄像头 | 必传 |

    * 返回值

      Promise

    * 示例

      ```javascript
      WadeMobile.faceAuthentication([0, 0, 1]).then((result) => {
          if (result.retCode === '0') {
              const originImg = "data:image/jpg;base64," + result.img;
              const originImgBest = "data:image/jpg;base64," + result.img_best;
          } else {
              console.log("活体认证照片采集失败，请重新尝试人脸识别");
          }
      }).catch((e) => {
          console.log(e);
      })
      ```

    * 备注

      ```javascript
      //返回的promise完成函数入参如下
      {
          retCode：0成功 其余不成功
          img：全景图
          img_best：质量图
      }
      ```

22. 蓝牙连接

    * 功能

      获取蓝牙设备信息

    * 定义

      ```javascript
      WadeMobile.deviceList()
      ```

    * 参数

      无

    * 返回值

      Promise

    * 示例

      ```javascript
      WadeMobile.deviceList().then((result) => {
          console.log("蓝牙设备名称",result.btName);
          console.log("蓝牙设备地址",result.address);
      }).catch((e) => {
          console.log(e);
      })
      ```

23. 读取身份证

    * 功能

      读取身份证信息

    * 定义

      ```javascript
      WadeMobile.readIDCard(param)
      ```

    * 参数

      | 参数名 | 参数类型 | 参数描述                                                     | 备注 |
      | ------ | -------- | ------------------------------------------------------------ | ---- |
      | param  | array    | btName：读卡器名<br />address：读卡器的蓝牙地址<br />type：默认为“1”<br />readerIP：读卡器厂商服务器IP地址 | 必传 |

    * 返回值

      Promise

    * 示例

      ```javascript
      WadeMobile.readIDCard([btName, address, '1', readerIP]).then((result) => {
          let IDCardInfo = {};
          if (WadeMobile.isAndroid()) {
              IDCardInfo = {
                  name: result.name,
                  period: result.effectDate + '-' + result.expireDate,
                  sex: result.sex,
                  address: result.address,
                  avatar: "data:image/png;base64," + result.avatar,
                  cardNo: result.cardNo
              }
          }else if (WadeMobile.isIOS()) {
              IDCardInfo = {
                  name: result.Name,
                  period: result.effectDate + '-' + result.expireDate,
                  sex: result.Sex,
                  address: result.Address,
                  avatar: "data:image/png;base64," + result.avatar,
                  cardNo: result.CardNo
              }
          }
      }).catch((e) => {
          console.log(e);
      })
      ```

    * 备注

      ```javascript
      //readerIP数据格式
      {
          "SYD":"",//三元达地址
          "SYDP":"",//三元达图片解码地址
          "SYD_SE":"",//已废弃，可固定传1
          "SR":"",//森锐地址
          "ST":"",//信通地址
          "DC":"",//正宏地址
          "KT":"",//卡尔地址
          "KAER_ACCOUT":"",//卡尔账号
          "KAER_PASSWORD":"",//卡尔密码
          "AZT":"",//安政通地址
          "BM":"",//青海地址
          "Emini":""//易迷你地址
      }
      //Android返回数据格式
      {
          "name":"",//姓名
          "sex":"",//性别
          "ethnicity":"",//
          "birth":"",//生日
          "address":"",//地址
          "cardNo":"",//身份证号码
          "avatar":"",//
          "authority":"",//
          "period":"",//
          "expireDate":"",//
          "effectDate":"",//
          "englishName":"",//英文名字
          "dn":"",//
          "nationCode":"",//
          "procId":"",//
          "photoSignFields":"",//
          "contentSign":"",//内容签名
          "photoSign":"",//照片签名
          "timestamp":"",//
          "contentSignFields":"",//
          "genderCode":"",//
          "idType":"",//
          "signCount":"",//
          "passNum":"",//
      }
      //ios返回数据格式
      {
          "Name":"",//姓名
          "Sex":"",//性别
          "Nation":"",//
          "Born":"",//生日
          "Address":"",//地址
          "CardNo":"",//身份证号码
          "avatar":"",//照片
          "IssuedAt":"",//
          "ExpiredDate":"",//
          "EffectedDate":"",//
          "englishName":"",//英文名字
          "dn":"",//
          "nationCode":"",//
          "procId":"",//
          "photoSignFields":"",//
          "contentSign":"",//内容签名
          "photoSign":"",//照片签名
          "timestamp":"",//
          "contentSignFields":"",//
          "genderCode":"",//
          "idType":"",//
          "signCount":"",//
          "passNum":"",//
      }
      
      
      ```

24. 读取SIM卡

    * 功能

      读取SIM卡信息

    * 定义

      ```javascript
      WadeMobile.readCard(params);
      ```

    * 参数

      | 参数名 | 参数类型 | 参数描述                                         | 备注 |
      | ------ | -------- | ------------------------------------------------ | ---- |
      | param  | array    | btName：蓝牙名称<br />bluetoothAddress：蓝牙地址 | 必传 |

    * 返回值

      Promise

    * 示例

      ```javascript
      WadeMobile.readCard([btName, bluetoothAddress]).then((result) => {
          const retCode = result.retCode;
          const iccid = result.ICCID;
      }).catch((e) => {
          console.log(e);
      })
      ```

    * 备注

      ```javascript
      //返回的promise完成函数入参如下
      {
          retCode：0，表示白卡可进行写卡；2，表示成卡或已被写卡
          ICCID：SIM卡的ICCID
      }
      ```

25. 写SIM卡

    * 功能

      写SIM卡

    * 定义

      ```javascript
      WadeMobile.writeCard(param)
      ```

    * 参数

      | 参数名 | 参数类型 | 参数描述                                                     | 备注 |
      | ------ | -------- | ------------------------------------------------------------ | ---- |
      | param  | array    | btName:蓝牙名称<br />bluetoothAddress:蓝牙地址<br />imsi:写入的imsi号<br />ICCID:SIM卡的ICCID<br />serialNumber:写入的手机号<br />scriptSeq:写卡脚本<br />msgCenterNumber:短信中心号 | 必传 |

    * 返回值

      Promise

    * 示例

      ```javascript
      WadeMobile.writeCard([btName, bluetoothAddress, imsi, ICCID, serialNumber, scriptSeq, msgCenterNumber]).then((result) => {
          if(result.retCode === '0') {
              console.log("写卡成功");
          }else {
              console.log("写卡失败")
          }
      })
      ```

    * 备注

      retCode：0，写卡成功；非0，写卡失败

26. 分享

    * 功能

      分享至微信好友或朋友圈

    * 定义

      ```javascript
      WadeMobile.jShare(param)
      ```

    * 参数

      | 参数名 | 参数类型 | 参数描述                                                     | 备注 |
      | ------ | -------- | ------------------------------------------------------------ | ---- |
      | param  | array    | imgUrl：图片url地址<br />platform：平台，1为微信好友，2为微信朋友圈<br />url：分享的链接地址（非必选）<br />title：分享链接的title（非必选）<br />text：分享链接的内容（非必选） | 必传 |

    * 返回值

      无

    * 示例

      ```javascript
      WadeMobile.jShare([imgUrl, 1, location.href, document.title, '中国联通软件研究院统一APP框架子应用模板工程']);
      ```
