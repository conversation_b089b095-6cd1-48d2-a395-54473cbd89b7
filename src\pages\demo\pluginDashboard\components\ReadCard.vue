<template>
    <div>
        <van-button
            @click="readCard"
            type="primary"
        >
            读取SIM卡
        </van-button>
        <van-cell
            title="ICCID"
            :value="iccid"
            size="large"
        />
        <van-cell
            title="SIM卡状态"
            :value="status"
            size="large"
        />
        <van-button
            @click="writeCard"
            type="primary"
        >
            写SIM卡
        </van-button>
    </div>
</template>

<script>
import { mapState } from 'vuex';
import WadeMobile from 'rk-native-plugin';

export default {
    name: 'ReadCard',
    data() {
        return {
            iccid: '',
            retCode: null
        };
    },
    computed: {
        ...mapState(['btName', 'address']),
        status() {
            if (this.retCode === '0') {
                return '白卡，可进行写卡';
            } else if (this.retCode === '2') {
                return '成卡或已被写卡';
            } else {
                return '';
            }
        }
    },

    methods: {
        readCard() {
            WadeMobile.readCard([this.btName, this.address]).then((result) => {
                this.retCode = result.retCode;
                this.iccid = result.ICCID;
            }).catch(() => {
                this.iccid = '';
                this.retCode = null;
            });
        },
        writeCard() {
            <PERSON>Mobile.writeCard([this.btName, this.address, '***************', this.iccid, '17854114882', '!A0A40000023F00,S,,9FXX!A0A40000027F10,S,,9FXX!A0A40000026F42,S,,9FXX!A0DC010428FFFFFFFFFFFFFFFFFFFFFFFFFDFFFFFFFFFFFFFFFFFFFFFFFF0891683110109505F0FFFFFFFFFFFF,S,,9000', '13010171500']).then(result => {
                // eslint-disable-next-line no-console
                console.log(result);
            }).catch(e => {
                // eslint-disable-next-line no-console
                console.log(e);
            });
        }
    }
};
</script>

<style scoped>

</style>
