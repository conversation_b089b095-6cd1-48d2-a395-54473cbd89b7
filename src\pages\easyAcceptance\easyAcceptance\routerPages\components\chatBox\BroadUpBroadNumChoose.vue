<template>
      <div>
        <h1 class="bh_title" id="ywFreeGoods">宽带号码清单</h1>
        <div class="one-key-open-account">
          <div class="form-wrap">
            <div class="card-style" >
              <div class="contract-wrap" >
                <div class="made-list">
                  <div class="list-item"  v-for="(item, index) in numberList"  :class="{'active': selectedNumber === item.number}"
                       :key="index" @click="chooseNumber(item)"   v-if="numberList!==null&& Object.keys(numberList).length>=1" >
                    <div>
                      {{ `${item.number.substr(0, 3)} ${item.number.substr(3, 4)} ${item.number.substr(7, 4)}` }}
                    </div>
                    <div>{{item.address}}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="submit-container">
            <van-button class="sub" @click="confirm()" block :disabled="isClick">确定</van-button>
          </div>
        </div>
      </div>
</template>


<script>

import {mapActions, mapMutations, mapState} from "vuex";

    export default {
        name: 'BroadUpBroadNumChoose',
        data() {
            return {
              isClick:false,
                proChecked: false,
                numberList: [],
                selectedNumber: '',
                audio: ''
            }
        },
        computed: {
            ...mapState([
                'jzfkOrderData',
                'isFour',
                'broadUpOrderData'
            ])
        },

        mounted() {
          
          this.$emit('startLoading', '')
            let broadNumberByYw=this.broadUpOrderData.broadNumberByYw;
          this.numberList=[];
          console.log(broadNumberByYw)

          if(broadNumberByYw.length<=0){
              this.$toast('您没有宽带号码')
            }else{
              for(let i=0;i<broadNumberByYw.length;i++){
                this.numberList.push({
                  number: broadNumberByYw[i].kdSerialNumber,
                  isNice: false,
                  address: broadNumberByYw[i].kdAddress
                })
              }
              this.selectedNumber = this.numberList[0].number;
              this.chooseNumber( this.numberList[0]);
            }
          // this.setIsFour(1);
          this.$emit('endLoading', '')
            console.log(this.isFour + "==============this.isFour，初始化")
        },
        methods: {
            ...mapMutations([
                'setFlowStep',
                'setRobotWorking',
                'setNum',
                'setJzfkOrderData',
                'setIsFour',
                'setBroadUpOrderData'
                
            ]),
          ...mapActions(['updateChatList','mobileRecommendationSubmit']),

            chooseNumber(item) {
                console.log(item)
                this.selectedNumber = item.number
                this.broadUpOrderData.selectedNumber = this.selectedNumber;
                this.setBroadUpOrderData(this.broadUpOrderData);
            },
            confirm() {
                if (!this.selectedNumber) {
                    this.$toast('请先选择号码~');
                    return;
                }
                this.broadUpOrderData.selectedNumber = this.selectedNumber;
                this.isClick=true;
              let req = {
                serialNumber: this.selectedNumber,
                phoneType: "1"
              }
              this.$http.post('/broadbandUpSpeed/checkNumber', req).then(res => {
               if("0000"==res.respCode){
                 this.broadUpOrderData.checkNumberData=res.respData
                 this.broadUpOrderData.timeData.contactName=res.respData.custName
                 this.setBroadUpOrderData(this.broadUpOrderData);
                 let data = {
                   inputType: "1",
                   type: '1',
                   textInput: "broadUpChooseBroadNumSubmit",
                   notifyFlag: '',
                   taskName:'宽带提速智能甩单'
                 }
                 this.$emit('newChatApi', data);
               }
               else{
                 this.broadUpOrderData.selectedNumber ="";
                 this.setBroadUpOrderData(this.broadUpOrderData);
                 this.updateChatList({
                   sender: '1',
                   type: 'module',
                   moduleName: 'TextResponse',
                   moduleLevel: 1,
                   params: {
                     text: res.respMsg
                   },
                   show: true
                 })
                
                 let data = {
                   inputType: "1",
                   type: '1',
                   textInput: "broadUpChooseBroadNumSubmit",
                   notifyFlag: '',
                   taskName:'宽带提速智能甩单'
                 }
                 this.$emit('newChatApi', data);
               }
              }).catch((e) => {
                this.$dialog({
                  title: '出错了！',
                  message: e,
                  confirmButtonText: '报告错误'
                });

              });
            
            }
        }
    };
</script>

<style lang="scss" scoped>
.submit-container{
  .sub{
    border: 2px solid rgba(73,124,246,1);
    border-radius: 5px;
    color: rgba(73,124,246,1);
    margin-top: 20px;
  }
}

.one-key-open-account{
  overflow: hidden;
  .content {
    min-height: 50px;
  }

  .demo {
    width: 150px;
    height: 100px;
    background-color: #646566;
  }

  .card-style{
    margin: 0;
    //padding-bottom: 15px;
    width: 100%;
    height: auto;
    background: #FFFFFF;
    border-radius: 8px;
    border: 1px solid #FFFFFF;
  }
  .form-wrap {
    padding: 0;
    background: #F5F5F5;
    .contract-wrap {
      margin: 0;
      /*border-bottom: 1px solid #F7F7F7;*/
      p {
        padding: 10px 0;
        height: 34px;
        line-height: 34px;
        font-size: 14px;
        color: #333;
      }
      .desc{
        color: #ee0a24;
        font-size: 12px;

      }
      .made-list {
        //flex-direction: row;
        //justify-content: space-between; // 或使用其他值如 'flex-start', 'center' 等
        //flex-wrap: wrap;
        
        .list-item {
          width: 95%;
          padding: 5px 10px;
          box-sizing: border-box;
          height:fit-content;
          line-height: 25px;
          font-size: 14px;
          margin: 5px 5px;
          background: #F5F8FA;
          border: 1.5px dashed rgba(203, 203, 203, 1);
          border-radius: 8px;
        }
        .active {
          background: #EDF7FF;
          border: 1px solid rgba(80,148,245,1);
        }
      }
    }
    .border {
      background: #FFFFFF;
      border-radius: 4px;
      border: 1px solid #CACACA;
    }
    .blueBorder {
      background: rgba(0, 129, 255, 0.05);
      border-radius: 12px;
      border: 1px solid #0081FF;
      width: 300px;
      margin-left: 20px;
    }
    .notBlueBorder {
      border-radius: 12px;
      width: 300px;
      margin-left: 20px;
    }
    .made-name{
      color: #323233;
      font-size: 0.37333rem;
      line-height: 0.64rem;
    }
    .made-van {
      /*padding: 5px 0px;*/
      /*background: #c8e3fe;*/
      /*border-radius: 12px;*/
      /*font-size: 14px;*/
      align-content: center;
      width: 300px;
      text-align: center;
    }
  }}
</style>