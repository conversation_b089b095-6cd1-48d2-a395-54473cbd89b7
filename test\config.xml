{"env": {"MOUNT_POINT": "vers=4,minorversion=0,rsize=1048576,wsize=1048576,hard,timeo=600,retrans=2,noresvport,port=18107 **************:/"}, "labels": {"SPIDER_NET_VPC_NAME": "", "SPIDER_NET_SUBNET": "spidernet", "SPIDER_NET_IP_AUTO_ASSIGN": "true", "SPIDER_NET_IP": "", "SPIDER_NET_SECURITY_GROUP": "spidernetgroup-vpc11318jlzwt", "SPIDER_NET_VPC": "vpc11318jlzwt", "SPIDER_NET_SUBNET_NAME": ""}, "id": "/jlzwtaih5", "backoffFactor": 1.15, "backoffSeconds": 1, "container": {"portMappings": [], "type": "DOCKER", "volumes": [], "docker": {"parameters": [], "image": "harbor.dcos.xixian.unicom.local/jlzwt/jlzwtaih5-war:20210528135737", "forcePullImage": true, "privileged": true}}, "cpus": 0.1, "disk": 0, "instances": 1, "maxLaunchDelaySeconds": 3600, "mem": 2048, "gpus": 0, "networks": [{"mode": "container", "name": "spidernetgroup-vpc11318jlzwt"}], "requirePorts": false, "upgradeStrategy": {"maximumOverCapacity": 0, "minimumHealthCapacity": 0}, "killSelection": "YOUNGEST_FIRST", "unreachableStrategy": {"inactiveAfterSeconds": 0, "expungeAfterSeconds": 0}, "healthChecks": [], "fetch": [], "constraints": []}