<template>
  <div class="card-verification" style="background-color: white">
    <div class="card-group" >
      <van-cell-group class="tip">
        <van-cell title="主卡信息"  class="desc"  style=" background-color: white;padding:5px 0px"/>
        <!--        <div style="height: 2px;background-color:rgb(247,247,247)" ></div>-->
      </van-cell-group>
      <van-cell-group class="card-list" >
        <van-cell title="主卡号码:" :value=resp.serialNumber  style=" background: linear-gradient(to right, rgba(230,244,255,0.8), rgb(230,244,255)); "/>
      </van-cell-group>
    </div>
    <div class="card-group">
      <van-cell-group  class="tip">
        <van-cell title="副卡信息" class="desc" right-icon="success"  is-link style=" background-color: white;padding:5px 0px" />
      </van-cell-group>
      <van-cell-group class="card-list"  >
        <div style="padding: 0px 10px;background: linear-gradient(to right, rgba(230,244,255,0.8), rgb(230,244,255));" >
          <van-cell  title="副卡商品名称" :value=resp.goodName  style="background: linear-gradient(to right, rgba(230,244,255,0.8), rgb(230,244,255));margin-bottom: 10px;" />
          <van-cell  title="副卡客户信姓名:" :value=resp.custName  style=" background: linear-gradient(to right, rgba(230,244,255,0.8), rgb(230,244,255));" />
          <van-cell  title="副卡号码:" :value="resp.openPhone" style=" background: linear-gradient(to right, rgba(230,244,255,0.8), rgb(230,244,255));" />
        </div>
      </van-cell-group>
    </div>
    <div class="submit-container">
      <van-button style="background-color: rgb(52,137,252);color: white" @click="preSubmit()" block>提交</van-button>
    </div>


  </div>
</template>

<script>
import WadeMobile from "rk-native-plugin";
import errorTips from '@/assets/bizComponents/errorTips/errorTips.vue';
import {copyText} from "../../../assets/js/func";
import {mapActions, mapMutations, mapState} from "vuex";
export default {
  name: "OrderInfo",
  data() {
    return {
      imageList: [
        require('../../../images/arrow.png'),
        require('../../../images/add.png')
      ],
      resp:{
        openPhone:'',
        serialNumber:'',
        menuName:'',
        openCustName:'',
        openPsptId:'',
        goodId:'',
        goodName:'',
        certCode:'',
        custName:'',
        addr:'',
        preFee:'0元/月',
        communiFee:'0元',
        protocol:'0月'
      }
    }
  },
  components: {
    errorTips
  },
  computed: {
    ...mapState([
      'chatList',
      'sessionId',
      'staffId',
      'flowStep',
      'num',
      'shbmMsgInfo',
      'jzfkOrderData',
      'activeModuleIndex',
      'loginPhoneNumber',
      'instanceId'
    ])
  },
  mounted(){
    this.resp={
      openPhone:this.jzfkOrderData.numberChooseData.selectedNumber,
      serialNumber:this.jzfkOrderData.mainNumberCheckData.mainNumber,
      menuName:this.jzfkOrderData.mainNumberCheckData.custInfo.productName,
      openCustName:this.jzfkOrderData.mainNumberCheckData.custInfo.custName,
      goodId:this.jzfkOrderData.goodsSelectData.filter(obj => obj.roleId =="-1")[0].commId,
      goodName:this.jzfkOrderData.goodsSelectData.filter(obj => obj.roleId =="-1")[0].commName,
      custName:this.maskName(this.jzfkOrderData.custCheckData.custInfo.CUSTOMER_NAME),
      addr:this.maskAddr(this.jzfkOrderData.custCheckData.custInfo.CERT_ADDRESS),
      preFee:'0元/月',
      communiFee:'0元',
      protocol:'0月',
      size:this.jzfkOrderData.mainNumberCheckData.custInfo.deputyCardNum
    }
    this.checkOrderDetail();
  },

  methods: {
    ...mapMutations([
      'setFlowStep',
      'setRobotWorking',
      'setJzfkOrderData'
    ]),
    ...mapActions(['updateChatList']),
    async copyId(){
      const textToCopy = document.getElementById('goodId').textContent;
      try{
        // navigator clipboard 需要https等安全上下文
        if (navigator.clipboard && window.isSecureContext) {
          // navigator clipboard 向剪贴板写文本
          await navigator.clipboard.writeText(textToCopy);
        } else {
          // 创建text area
          let textArea = document.createElement("textarea");
          textArea.value = textToCopy;
          // 使text area不在viewport，同时设置不可见
          textArea.style.position = "absolute";
          textArea.style.opacity = 0;
          textArea.style.left = "-999999px";
          textArea.style.top = "-999999px";
          document.body.appendChild(textArea);
          textArea.focus();
          textArea.select();
          new Promise((res, rej) => {
            // 执行复制命令并移除文本框
            document.execCommand('copy') ? res() : rej();
            textArea.remove();
          });
        }
        // this.$message.success('内容已复制到剪贴板');
        alert("内容已复制到剪贴板")
      }catch(err){
        // this.$message.error('复制到剪贴板时发生错误')
        alert("复制到剪贴板时发生错误")

      }
    },
    maskAddr(addr) {
      let halfLength=4;
      if(addr.length/2==0){
        halfLength=addr.length/2;
      }
      else{
        halfLength=addr.length/2+1;
      }
      return addr.substring(0, halfLength) + '*'.repeat(addr.length/2);
    },

    maskPspt(pspt) {
      return pspt.replace(/^(.{6})(.*)(.{2})$/, '$1*********$3');
    },
    maskName(name) {
      return name.substring(0, 1) + '*'.repeat(name.length - 1);
    },

    // 点击查看订单详情
    checkOrderDetail() {
      WadeMobile.getNetInfo("IP").then((info) => {
        console.log("获取移动设备的IPV4地址：" + info);
        this.jzfkOrderData.preSubmitShowData.ip=info;
      }, (err) => {
        console.log("获取移动设备的IPV4地址：" + err);
      });
      WadeMobile.getNetInfo("MAC").then((info) => {
        console.log("获取移动设备的MAC地址：" + info);
        this.jzfkOrderData.preSubmitShowData.mac=info;
      }, (err) => {
        console.log("获取移动设备的MAC地址：" + err);
      });
      WadeMobile.getSysInfo("IMEI").then((info) => {
        console.log("获取imei信息：：" + info);
        this.jzfkOrderData.preSubmitShowData.imei=info;
      }, (err) => {
        console.log("IMEI失败：" + err);
      });
      WadeMobile.getSysInfo("MODEL").then((info) => {
        this.jzfkOrderData.preSubmitShowData.model=info;
        console.log("MODEL：：" + info);
      }, (err) => {
        console.log("MODEL"+"失败：" + err);
      });
      WadeMobile.getSysInfo("PRODUCTNAME").then((info) => {
        this.jzfkOrderData.preSubmitShowData.productName=info;
        console.log("PRODUCTNAME：：" + info);
      }, (err) => {
        console.log("PRODUCTNAME"+"失败：" + err);
      });
      WadeMobile.getSysInfo("UUID").then((info) => {
        this.jzfkOrderData.preSubmitShowData.uuid=info;
        console.log("UUID：：" + info);
      }, (err) => {
        console.log("UUID"+"失败：" + err);
      });
      WadeMobile.getSysInfo("BRAND").then((info) => {
        this.jzfkOrderData.preSubmitShowData.brand=info;
        console.log("BRAND：：" + info);
      }, (err) => {
        console.log("BRAND"+"失败：" + err);
      });
      WadeMobile.getSysInfo("SDKVERSION").then((info) => {
        this.jzfkOrderData.preSubmitShowData.sdkversion=info;
        console.log("SDKVERSION：：" + info);
      }, (err) => {
        console.log("SDKVERSION"+"失败：" + err);
      });
      WadeMobile.getSysInfo("OSVERSION").then((info) => {
        this.jzfkOrderData.preSubmitShowData.osversion=info;
        console.log("OSVERSION：：" + info);
      }, (err) => {
        console.log("OSVERSION"+"失败：" + err);
      });
      WadeMobile.getSysInfo("PLATFORM").then((info) => {
        this.jzfkOrderData.preSubmitShowData.platfForm=info;
        console.log("PLATFORM：：" + info);
      }, (err) => {
        console.log("PLATFORM"+"失败：" + err);
      });
      WadeMobile.location(2).then((info) => {
        console.log("====location(2)====");
        console.log(info);
        // WadeMobile.location()插件安卓和ios返回的地址信息字段名上有区别，需要判断设备类型再取值
        let locationInfo={};
        if (WadeMobile.isAndroid()) {
          locationInfo = {
            gpsType: info.gprsType,
            gpsName: "百度",
            province: info.PROVINCE,
            city: info.CITY,
            address: info.ADDRESS,
            longitude: info.Longitude,
            latitude: info.Latitude
          }
        }
        else if (WadeMobile.isIOS()) {
          locationInfo = {
            gpsType: info.gprsType,
            gpsName: "百度",
            province: info.Province,
            city: info.City,
            address: info.LocationDesc,
            longitude: info.Longitude,
            latitude: info.Latitude
          }
        }
        this.jzfkOrderData.preSubmitShowData.locationInfo=locationInfo;
        this.setJzfkOrderData(this.jzfkOrderData)
      }).catch(e => {
        console.log(e);
      });

    },
    deleteCacheData(){
      this.$http.post('/contineBreak/deleteData',{operatorId:this.shbmMsgInfo.operatorId}).then(res=>{
        return new Promise((resolve, reject) => {
          return resolve();
        })
      })
    },
    jump(orderId) {
      this.deleteCacheData();
      WadeMobile.openH5('https://wxxapp.chinaunicom.cn:10070/touch_sub_front/pay.html?orderId='+orderId+'&toProduce=1&aiAcceptFlag=1#/commonPay', null, (result) => {
        console.log(result);
        this.$emit('startLoading', '')
        let tip = ''
        result=JSON.parse(result);
        if(!this.isEmpty(result)){
          if(!this.isEmpty(result.isPayResult)&&'1'==result.isPayResult){
            if(!this.isEmpty(result.isProduceResult)&&'1'==result.isProduceResult){
              tip='您的业务订单:'+orderId+'，已受理完成';
              this.updateChatList({
                sender: '1',
                type: 'module',
                moduleName: 'TextResponse',
                moduleLevel: 1,
                params: {text:tip},
                show: true
              })

              tip='我是上海联通智能受理小助手，具备自然语言理解和业务受理能力，请描述您需要办理的业务。<br>前支持的业务范围:<br>1、加装副卡';
              this.updateChatList({
                sender: '1',
                type: 'module',
                moduleName: 'startChat',
                moduleLevel: 1,
                params: {text:tip},
                show: true
              })
              this.jzfkOrderData= {
                mainNumberCheckData:{mainNumber:'',brandCode:'',custInfo: {}},
                smsCodeCheckData:{},
                goodsSelectData:[],
                numberChooseData:{params:{},numberList:[]},
                custCheckData:{custInfo:{}},
                preSubmitShowData:{locationInfo:{}}
              }
              this.setJzfkOrderData(this.jzfkOrderData);
              this.$emit('endLoading', '')
            }
            else{
              tip='您的业务订单:'+orderId+',因受理中途异常退出，请至“新-待生产”菜单继续处理。';
              this.updateChatList({
                sender: '1',
                type: 'module',
                moduleName: 'TextResponse',
                moduleLevel: 1,
                params: {text:tip},
                show: true
              })
              this.$emit('endLoading', '')
            }
          }     else{
            tip='您的业务订单:'+orderId+',因受理中途异常退出，请至“待支付”菜单继续处理。';
            this.updateChatList({
              sender: '1',
              type: 'module',
              moduleName: 'TextResponse',
              moduleLevel: 1,
              params: {text:tip},
              show: true
            })
            this.$emit('endLoading', '')
          }
        }
        else{
          tip='您的业务订单:'+orderId+',因受理中途异常退出，请至“待支付”或“新-待生产”菜单继续处理。';
          this.updateChatList({
            sender: '1',
            type: 'module',
            moduleName: 'TextResponse',
            moduleLevel: 1,
            params: {text:tip},
            show: true
          })
          this.$emit('endLoading', '')
        }
        // eslint-disable-next-line no-unused-vars
      });
    },
    isEmpty(value) {
      let flag = false
      if (value == '' || value == 'undefined' || value == undefined || value == null || value == 'null') {
        flag = true
      }
      return flag
    },

    uniqueByProperty(arr, prop) {
      const uniqueMap = new Map();
      return arr.reduce((acc, current) => {
        const key = current[prop];
        if (!uniqueMap.has(key)) {
          uniqueMap.set(key, true);
          acc.push(current);
        }
        return acc;
      }, []);
    },
    preSubmit(){
      const uniqueObjects = this.uniqueByProperty(this.jzfkOrderData.goodsSelectData, 'roleId');
      let req={
        orderId:this.shbmMsgInfo.orderId,
        mainNumberInfo:{mainSerialNumber:this.jzfkOrderData.mainNumberCheckData.mainNumber},
        goodsInfo:uniqueObjects,
        subsNumberInfo:{viceNumber:this.jzfkOrderData.numberChooseData.selectedNumber},
        custInfo:this.jzfkOrderData.custCheckData,
        clientInfo:this.jzfkOrderData.preSubmitShowData,
        mainNumberCheckData:this.jzfkOrderData.mainNumberCheckData.custInfo
      }
      // console.log( this.jzfkOrderData.mainNumberCheckData.custInfo)
      // console.log(JSON.stringify(req))
      this.$http.post('/shjzfkOrderInfo/preSubmit',req).then((res)=>{
        if (res.respCode === '0000') {
          this.jzfkOrderData.preSubmitShowData.orderId=res.respData.orderId
          this.setJzfkOrderData(this.jzfkOrderData);
          this.jump(res.respData.orderId);
        }
        else{
          this.updateChatList({
            sender: '1',
            type: 'module',
            moduleName: 'TextResponse',
            moduleLevel: 1,
            params: {
              text: res.respMsg
            },
            show: true
          })
        }      })
    }
  }
}
</script>
<style lang="scss">
.card-verification {
  width:100%;
  margin: auto;
  .card-group {
    background-color: white;
    margin: 0px 10px;
    padding:0px 5px;
    .tip {
      .van-cell__value {
        color: black;
        text-align: right;
      }}

    .card-list {
      .van-cell__title {
        font-size: 13px;
        color: #666666;
      }
      .van-cell__value {
        color: black;
        text-align: right;
        font-size: 13px;
      }
      .van-cell {
        color: black !important;
        padding: 0px 16px;
      }
      .van-cell::after {
        color: black !important;
        border-bottom: 0;

      }
    }
    .van-hairline--top-bottom::after, .van-hairline-unset--top-bottom::after {
      border-width: 0 0;
    }
  }
  .card {
    .van-cell__title {
      font-size: 13px;
      color: #000000;
    }
    .checkButton {
      width: 70px;
      height: 30px;
      background: #0081FF;
      border-radius: 4px;
    }
    .checkFont {
      font-size: 13px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 14px;
    }
  }
  .check{
    .van-cell__title {
      font-size: 13px;
      color: #333333;
    }
  }
}

</style>

<style lang="scss" scoped>
.card-verification {
  background-color: rgb(57,159,254);
  .desc {
    color: black;
    font-size: 13px;
    line-height: 20px;
  }
  .margin-two {
    bottom: 10px;
    left: 15px;
    right: 15px;
    z-index: 2;
    position: absolute;
  }
  .van-buttons {
    border-radius: 4px;
    height: 44px;
    font-size: 13px;
    color: #FFFFFF;
    background-color: #0081FF;
  }
  .font {
    color:#333333;
  }

}

</style>