user root;
worker_processes auto;
pid /run/nginx.pid;
include /etc/nginx/modules-enabled/*.conf;

events {
        worker_connections 1024;
        # multi_accept on;
}

http {
        
        ##
        # 基础配置段
        ##
        
        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;
        # server_tokens off;
        # server_names_hash_bucket_size 64;
        # server_name_in_redirect off;
        include /etc/nginx/mime.types;
        default_type application/octet-stream;
        client_max_body_size 128m;
        
        ##
        # SSL 配置段
        ##
        
        # ssl_protocols TLSv1 TLSv1.1 TLSv1.2; # Dropping SSLv3, ref: POODLE
        # ssl_prefer_server_ciphers on;
        # ssl_session_cache    shared:SSL:10m;  
        # ssl_session_timeout 5m;
        # ssl_ciphers	ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4:!DH:!DHE; 
        # ssl_certificate /etc/nginx/key/10010sh-2020.cer;
        # ssl_certificate_key /etc/nginx/key/10010sh-2020.key;
        
        ##
        # 日志 配置段
        ##
        
        log_format my_json_log '{"@timestamp":"$time_iso8601",'
                     '"remote_addr":"$remote_addr",'
                     '"xff":"$http_x_forwarded_for",'
                     '"size":$body_bytes_sent,'
                     '"req_time":$request_time,'
                     '"host":"$host",'
                     '"method":"$request_method",'
                     '"request":"$request",'
                     '"uri":"$uri",'
                     '"referer":"$http_referer",'
                     '"agent":"$http_user_agent",'
                     '"up_addr": "$upstream_addr",'
                     '"up_host": "$upstream_http_host",'
                     '"up_resp_time": "$upstream_response_time",'
                     '"status":"$status"}';

        ##
        # 压缩 配置段
        ##
        
        #gzip on;
        # gzip_vary on;
        # gzip_proxied any;
        # gzip_comp_level 6;
        # gzip_buffers 16 8k;
        # gzip_http_version 1.1;
        # gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
        
        ##
        # 虚拟主机 配置段
        ##
        
        server {
            # listen       443 ssl;
            listen       8080;
            server_name  _;

            # 配置日志文件名称
            if ($time_iso8601 ~ "^(\d{4})-(\d{2})-(\d{2})") {
                set $year $1;
                set $month $2;
                set $day $3;
            }
            access_log /mnt/logs/shzwt-sit/front/front-access-$year-$month-$day.log my_json_log;
            
            #设置仅允许get post head options方法的调用
            if ($request_method !~ ^(GET|POST|HEAD|OPTIONS)$) {
                return 403;
            }
            
            #设置前端页面的路径地址
            location /shzwt/ {
                root   /usr/share/nginx/html/;
                try_files $uri $uri/ /shzwt/index.html;
                index  index.html index.htm;
            }
            
            #设置反向代理后端接口
            #设置version1.1解决代理网关后接口报426的错误。
            location ^~ /shzwt/api/ {
                proxy_pass_header Server;
                proxy_set_header Host $host:$server_port;
                proxy_redirect off;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#            #由于暂时没有网关，直接用后端地址代替网关地址。
#                proxy_pass http://istio-ingressgateway.istio-system.svc/;
                proxy_pass http://xx.xx.xx.xx:12621/shzwt/;
                proxy_http_version 1.1;
            }
            
            #将反向代理配置到cos地址
#            location /shzwt/cos/ {
#                proxy_pass http://;
#            }
            
            error_page   404              /404.html;
            error_page   500 502 503 504  /50x.html;
            location = /50x.html {
                root   /usr/share/nginx/html/;
            }
        
        }

}

