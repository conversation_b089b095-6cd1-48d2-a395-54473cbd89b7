<template>
  <div >
      <div class="selectProduct" id="selectProduct">
        <div class="box" :style="{border:item.isActive ? '2px solid #3498db':''}" v-for="(item,index) in iptvInfoList" :key="index"  @click="goodClick(item,$event)">
          <div class="tag" :style="{ backgroundColor: item.color }">{{item.title}}</div>
          <div class="cell1" v-if="item.SHORT_NAME" v-html="item.SHORT_NAME"></div>
          <div class="cell2"  v-if="item.RENT"   v-html="item.RENT"></div>
          <div class="cell ellipsis" v-if="!item.SHORT_NAME&&!item.RENT">{{item.iptvName.length>30?item.iptvName.slice(0,30)+'...':item.iptvName}}</div>
          <div class="cell3" v-if="item.goodPrice">调测费:</div>
          <div class="cell4" v-if="item.goodPrice">{{item.goodPrice}}元</div>
          <div class="success-box" v-if="item.isActive">
            <div class="success"></div>
          </div>
        </div>
      </div>


  </div>
</template>
<script>
import {mapActions, mapMutations, mapState} from "vuex";
export default {
  name:  "YskIptvProduct",
  data(){
    return {
      ziFeiPrice:0,
      pageIptvPrice:0,
      orderPriceNum:0,
      radio: '',
      colors:['rgb(129,211,248)','rgb(251,6,6)','#71bc8c'],
      selectedGooIds: [],
      choosedIPTV:"",
      iptvInvalid:[],
      iptvInfoCheckList:[],
      iptvInfoList1:[],
      yskIptvCacheList1:[],
      iptvInfoList: [
      ]
    }
  },
  mounted() {
    this.setIptvOrderPrice(0)
    this.outCallMonetOrderData.goodData.commodityChooseYWUlList=[];
    this.outCallMonetOrderData.goodData.iptvList=[];
    this.setOutCallMonetOrderData(this.outCallMonetOrderData);
    this.iptvInfoList=[];
    let colorLength=this.colors.length
    console.log(this.yskIptvCacheList.length)
    if(this.yskIptvCacheList.length>0){
      for(let i=0;i<this.yskIptvCacheList.length;i++){
        let price=0
        if(!this.isEmpty(this.yskIptvCacheList[i].GOOD_PRICE)){
          price=this.yskIptvCacheList[i].GOOD_PRICE
        }
        let title=''
        if(this.yskIptvCacheList[i].catName=='宽视界'){
          title='宽视界'
        }
        else{
          title='IPTV'
        }
        let exit=false;
        // for(let j=0;j<this.iptvOrderData.checkNumberData.iptvInvalid.length;j++){
        //   if(this.yskIptvCacheList[i].ancestors.indexOf(this.iptvOrderData.checkNumberData.iptvInvalid[j].iptvId) !== -1){
        //     exit=true
        //     break;
        //   }}
        let iptvInfo={
          title:title,
          ancestors:this.yskIptvCacheList[i].ancestors,
          SHORT_NAME:this.yskIptvCacheList[i].SHORT_NAME,
          commodityCode:this.yskIptvCacheList[i].commodityCode,
          commType:this.yskIptvCacheList[i].commType,
          iptvName:this.yskIptvCacheList[i].iptvName,
          RENT:this.yskIptvCacheList[i].RENT,
          color:this.colors[i%colorLength],
          isActive: false,
          // goodPrice: this.yskIptvCacheList[i].GOOD_PRICE
          goodPrice: price
        }
        if(!exit){
          this.iptvInfoList.push(iptvInfo)
        }
      }
    }
  },
  computed: {
    ...mapState([
      'jzfkOrderData',
      'shbmMsgInfo',
      'outCallMonetOrderData',
      'yskIptvCacheList',
        'outCallMonetOrderData',
        'iptvOrderPrice'
    ])
  },
  methods:{
    ...mapMutations([
      'setFlowStep',
      'setRobotWorking',
      'setOutCallMonetOrderData',
        'setIptvOrderPrice'
    ]),
    ...mapActions(['updateChatList']),
    isEmpty(value) {
      let flag = false
      if (value == '' || value == 'undefined' || value == undefined || value == null || value == 'null') {
        flag = true
      }
      return flag
    },
    iptvClickFee(iptv,sub){
      let data = {
        "iptvQryCommId": iptv.commodityCode,
        "iptvJmTag": "0"
      }
      this.$http.post('/iptvReceive/getIptvGoodsFeeByInterface', data).then(res => {
        if (res.respCode == "0000") {
          // 接收返回参数
          iptv.goodsJMPrice=res.respData.goodsJmPrice;
          iptv.goodsPrice=res.respData.goodsPrice;
          console.log(this.outCallMonetOrderData.goodData.iptvList,"console.log(outCallMonetOrderData.goodData.iptvList)")
          if(sub=='+'){
            this.outCallMonetOrderData.goodData.iptvList.push(iptv);
            this.setOutCallMonetOrderData(this.outCallMonetOrderData);
            console.log(this.outCallMonetOrderData.goodData.iptvList,"console.log(outCallMonetOrderData.goodData.iptvList)")
            this.ziFeiPrice=this.ziFeiPrice+parseInt(res.respData.goodsJmPrice);
            this.pageIptvPrice=this.pageIptvPrice+parseInt(res.respData.goodsPrice);
            this.orderPriceNum=this.ziFeiPrice+this.pageIptvPrice;}
          else{
            this.ziFeiPrice=this.ziFeiPrice-parseInt(res.respData.goodsJmPrice);
            this.pageIptvPrice=this.pageIptvPrice-parseInt(res.respData.goodsPrice);
            this.orderPriceNum=this.ziFeiPrice+this.pageIptvPrice;
            this.outCallMonetOrderData.goodData.iptvList=this.outCallMonetOrderData.goodData.iptvList.filter(i => i !== iptv)
            this.setOutCallMonetOrderData(this.outCallMonetOrderData);
          }
          this.setIptvOrderPrice( this.orderPriceNum);
          this.setOutCallMonetOrderData(this.outCallMonetOrderData)
        }
      else{
          this.$toast(res.resMsg);
        }
      }).catch(e => {
       this.$toast(e)
      })
    },
    goodClick1(item){
      item.isActive = !item.isActive
      if (item.isActive) {
        this.selectedGooIds.push(item.commodityCode);
        this.iptvClickFee(item,'+')
      } else {
        this.selectedGooIds = this.selectedGooIds.filter(i => i !== item.commodityCode);
        this.iptvClickFee(item,'-')
      }
    },
    goodClick(item){
      item.isActive = !item.isActive
      if (item.isActive) {
        this.selectedGooIds.push(item.commodityCode);
        this.iptvClickFee(item,'+')
      } else {
        this.selectedGooIds = this.selectedGooIds.filter(i => i !== item.commodityCode);
        this.iptvClickFee(item,'-')
      }
    }},
}
</script>
<style scoped lang="scss">
.red-border{
  border: rgb(221,248,255) 2px solid;}
.selectProduct{
  display: flex;
  flex-wrap: nowrap; /* 确保子元素不换行 */
  overflow-x: scroll;  /* 启用水平滚动 */
  -webkit-overflow-scrolling: touch; /* 改善移动设备上滚动的体验 */
  .box {
    position: relative;
    border-radius: 10px;
    margin: 0 10px 10px 0;
    flex: 0 0 calc(24%); /* 使用 calc() 考虑 margin */
    max-width: 120px; /* 同样考虑 margin */
    background: linear-gradient(to bottom right, #fff, #EDF0FF);
    padding: 0 0 10px 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .tag{
      color: #fff;
      border-radius: 10px 0 10px 0;
      padding: 0 6px;
      font-size: 11px;
      width: 45%;
      text-align: center;
    }
    .cell {
        margin: 6px 0;
        width: 85px;
        text-align: center;
        font-weight: bold;
        font-size: 13px;
      }
    .cell1 {
      margin: 6px 0;
      width: 95px;
      text-align: center;
      font-weight: bold;
      font-size: 13px;
      line-height: 15px;
    }
    .cell2 {
      margin-bottom: 6px;
      width: 100%;
      text-align: center;
      font-size: 11px;
      line-height: 16px;
    }
    .cell3{
      margin-bottom: 6px;
      width: 100%;
      text-align: center;
      font-size: 11px;
      line-height: 16px;
    }
    .cell4{
      margin-bottom: 6px;
      width: 100%;
      text-align: center;
      font-size: 11px;
      line-height: 16px;
    }
    .cell5{
      margin-bottom: 6px;
      width: 100%;
      text-align: center;
      font-size: 11px;
      line-height: 16px;
    }
    .btn{
      border: 2px solid #4494E6;
      border-radius: 10px;
      color: #4494E6;
      height: 25px;
      font-size: 11px;
      padding: 5px 8px;
      background: none;
    }
    .active-btn{
      border: 2px solid #4494E6;
      border-radius: 10px;
      color: #fff;
      background: #4494E6!important;
      height: 25px;
      font-size: 11px;
      padding: 5px 8px;
      background: none;
      margin-left: 10px;
    }
    .success-box{
      position: absolute;
      bottom: 0;
      right: 0;
      .success {
        position: relative;
        width: 25px;
        height: 25px;
        background-image: url('@/assets/images/choosePhoto.png');
        background-position: center center;
        background-repeat: no-repeat;
        background-size: cover;
        clip-path: polygon(0 100%, 100% 0, 100% 100%);
        border-bottom-right-radius: 8px;
      }

    }
  }
  /* WebKit 浏览器滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  ::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

  /* Firefox 移动端浏览器 */
  .scrollable-element {
    scrollbar-width: auto;
    scrollbar-color: #888 #f1f1f1;
  }

  /* 元素样式 */
  .scrollable-element {
    width: 300px;
    height: 200px;
    overflow: auto;
    border: 1px solid #ccc;
    padding: 10px;
    -webkit-overflow-scrolling: touch;
  }
  @-webkit-keyframes showScrollbar {
    from {
      opacity: 1;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes showScrollbar {
    from {
      opacity: 1;
    }
    to {
      opacity: 1;
    }
  }
}
</style>