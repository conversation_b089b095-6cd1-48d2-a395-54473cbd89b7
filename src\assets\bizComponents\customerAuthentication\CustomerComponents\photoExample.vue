<template>
    <div class="photo-example">
        <p class="title">
            示例
        </p>
        <div class="example">
            <div class="example-item">
                <img src="@/assets/images/icon-error1.png" alt="">
                <p>标准</p>
            </div>
            <div class="example-item">
                <img src="@/assets/images/icon-error2.png" alt="">
                <p>边缘缺失</p>
            </div>
            <div class="example-item">
                <img src="@/assets/images/icon-error3.png" alt="">
                <p>照片模糊</p>
            </div>
            <div class="example-item">
                <img src="@/assets/images/icon-error4.png" alt="">
                <p>闪光强烈</p>
            </div>
        </div>
    </div>
</template>

<script>
export default {
        
};
</script>

<style lang="scss" scoped>
.photo-example {
    margin-top: 12px;
    border-top: 1px solid #eeeeee;
    width: 100%;

    .title {
        margin: 0.4rem 0;
    }

    .example {
        display: flex;
        justify-content: space-between;
        align-items: center;

        &-item {
            img {
                width: 1.9rem;
                height: 1.5rem;
            }

            p {
                color: rgba(51, 51, 51, 1);
                font-size: 12px;
                text-align: center;
                margin-top: 0.1rem;
                font-weight: normal;
            }
        }
    }
}
</style>