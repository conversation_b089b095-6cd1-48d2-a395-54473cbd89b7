<template>
  <div class="card-verification" style="background-color: white">
    <div class="subpreview-box" v-for="(item,index1) in showList" :key="index1" @click="item.showAll=!item.showAll">
      <div :style="{background: item.showAll ? '#E5F0FF':'#EDF7FF'}" class="total-title">
        <span class="custom-label-name">{{ item.name }}</span>
        <van-icon :name="item.showAll ? 'arrow-up':'arrow'" />
      </div>
      <div v-for="(item1,index) in item.itemList" :key="index" style="padding: 0 10px;">
        <div v-show="item.showAll" style="display:flex">
          <span class="custom-title flex-shink">{{ item1.text }}</span>
          <span class="custom-title" >{{item1.value}}</span>
        </div>
      </div>
    </div>
    <div class="submit-container">
      <van-button class="sub" @click="preSubmit()" block>提交</van-button>
    </div>
  </div>
</template>

<script>
import WadeMobile from "rk-native-plugin";
import errorTips from '@/assets/bizComponents/errorTips/errorTips.vue';
import {copyText} from "../../../assets/js/func";
import {mapActions, mapMutations, mapState} from "vuex";
export default {
  name: "YskOrderInfo",
  data() {
    return {
      imageList: [
        require('../../../images/arrow.png'),
        require('../../../images/add.png')
      ],
      showList: [
      
      ],
   
    }
  },
  components: {
    errorTips
  },
  computed: {
    ...mapState([
      'chatList',
      'sessionId',
      'staffId',
      'flowStep',
      'num',
      'shbmMsgInfo',
      'jzfkOrderData',
      'activeModuleIndex',
      'loginPhoneNumber',
      'instanceId',
        'outCallMonetOrderData',
        'kdOrderPrice',
        'zwOrderPrice',
        'iptvOrderPrice'
    ])
  },
  mounted(){
    this.setRespTipArrQry([]);
    this.setBlockShow(false);
    this.checkOrderDetail()
    this.outCallMonetOrderData.orderPrice=this.kdOrderPrice+this.zwOrderPrice+this.iptvOrderPrice;
    console.log('tgybyhbhbh')
    this.setOutCallMonetOrderData(this.outCallMonetOrderData)
    let zwString='无';
    let ywfString='无';
    let custString='';
    let iptvString='无';
    let kdfString='无';
    if(this.outCallMonetOrderData.goodData.zwList.length>0){
      zwString=''
      for(let i=0;i<this.outCallMonetOrderData.goodData.zwList.length-1;i++){
        zwString+=this.outCallMonetOrderData.goodData.zwList[i].commodityName+",";
      }
      zwString+=this.outCallMonetOrderData.goodData.zwList[this.outCallMonetOrderData.goodData.zwList.length-1].commodityName;
    }
    
    if(this.outCallMonetOrderData.goodData.custList.length>0){
      custString=''
      for(let i=0;i<this.outCallMonetOrderData.goodData.custList.length-1;i++){
        custString+=this.outCallMonetOrderData.goodData.custList[i].commodityName+",";
      }
      custString+=this.outCallMonetOrderData.goodData.custList[this.outCallMonetOrderData.goodData.custList.length-1].commodityName;
    }
    
    if(this.outCallMonetOrderData.goodData.commodityChooseYWUlList.length>0){
      if(this.outCallMonetOrderData.goodData.commodityChooseYWUlList[0].ywGoodId!='0'){
        ywfString='';
        for(let i=0;i<this.outCallMonetOrderData.goodData.commodityChooseYWUlList.length-1;i++){
          ywfString+=this.outCallMonetOrderData.goodData.commodityChooseYWUlList[i].ywGoodName+",";
        }
        ywfString+=this.outCallMonetOrderData.goodData.commodityChooseYWUlList[this.outCallMonetOrderData.goodData.commodityChooseYWUlList.length-1].ywGoodName;
      }
    }
    if(this.outCallMonetOrderData.goodData.iptvList.length>0){
      iptvString=''
      for(let i=0;i<this.outCallMonetOrderData.goodData.iptvList.length-1;i++){
        iptvString+=this.outCallMonetOrderData.goodData.iptvList[i].iptvName+",";
      }
      iptvString+=this.outCallMonetOrderData.goodData.iptvList[this.outCallMonetOrderData.goodData.iptvList.length-1].iptvName;
    }
    if(this.outCallMonetOrderData.goodData.kdfList.length>0){
      kdfString='';
      for(let i=0;i<this.outCallMonetOrderData.goodData.kdfList.length-1;i++){
        kdfString+=this.outCallMonetOrderData.goodData.kdfList[i].commName+",";
      }
      kdfString+=this.outCallMonetOrderData.goodData.kdfList[this.outCallMonetOrderData.goodData.kdfList.length-1].commName;

    }
    if("1"==this.outCallMonetOrderData.singleSwitchParams.switchToOutCallFlag){
      this.showList=[
        {
          name: '客户信息',
          showAll: true,
          itemList:[
            {text:'用户号码：',value:this.outCallMonetOrderData.checkNumberData.serialNumber},
            {text:'客户名称：',value:this.outCallMonetOrderData.checkNumberData.showJson.showCustName}
          ]
        },
        {
          name: '商品信息',
          showAll: true,
          itemList:[
            {text:'移网商品：',value:''},
            {text:'    ',value:"    "+this.outCallMonetOrderData.singleSwitchParams.commName},
            {text:'融合商品：',value:''},
            {text:'    ',value:"    "+custString},
            {text:'超清商品：',value:''},
            {text:'    ',value:"    "+iptvString},
            {text:'组网商品：',value:''},
            {text:'    ',value:"    "+zwString},
            {text:'移网附加优惠商品：',value:''},
            {text:'    ',value:"    "+ywfString},
          ]
        },{
          name: '装机信息',
          showAll: false,
          itemList:[
            {text:'装机地址：',value:this.maskAddr(this.outCallMonetOrderData.installAddr.fullAddr)}
          ]
        },
        {
          name: '费用信息',
          showAll: true,
          itemList:[
            {text:'商品费用：',value:this.outCallMonetOrderData.orderPrice+"元"},
          ]
        }
      ]
    }
    else{
      this.showList=[
        {
          name: '客户信息',
          showAll: true,
          itemList:[
            {text:'用户号码：',value:this.outCallMonetOrderData.checkNumberData.serialNumber},
            {text:'客户名称：',value:this.outCallMonetOrderData.checkNumberData.showJson.showCustName}
          ]
        },
        {
          name: '商品信息',
          showAll: true,
          itemList:[
            {text:'融合商品：',value:''},
            {text:'    ',value:"    "+custString},
            {text:'超清商品：',value:''},
            {text:'    ',value:"    "+iptvString},
            {text:'组网商品：',value:''},
            {text:'    ',value:"    "+zwString},
            {text:'移网附加优惠商品：',value:''},
            {text:'    ',value:"    "+ywfString},
          ]
        },{
          name: '装机信息',
          showAll: false,
          itemList:[
            {text:'装机地址：',value:this.maskAddr(this.outCallMonetOrderData.installAddr.fullAddr)}
          ]
        },
        {
          name: '费用信息',
          showAll: true,
          itemList:[
            {text:'商品费用：',value:this.outCallMonetOrderData.orderPrice+"元"},
          ]
        }
      ]
    }

  },
  methods: {
    ...mapMutations([
      'setFlowStep',
      'setRobotWorking',
      'setOutCallMonetOrderData',
        'setRespTipArrQry',
        'setBlockShow'
    ]),
    ...mapActions(['updateChatList']),
    maskAddr(addr) {
      let halfLength=4;
      if(addr.length/2==0){
        halfLength=addr.length/2;
      }
      else{
        halfLength=addr.length/2+1;
      }
      return addr.substring(0, halfLength) + '*'.repeat(addr.length/2);
    },
    isEmpty(value) {
      let flag = false
      if (value == '' || value == 'undefined' || value == undefined || value == null || value == 'null') {
        flag = true
      }
      return flag
    },
    uniqueByProperty(arr, prop) {
      const uniqueMap = new Map();
      return arr.reduce((acc, current) => {
        const key = current[prop];
        if (!uniqueMap.has(key)) {
          uniqueMap.set(key, true);
          acc.push(current);
        }
        return acc;
      }, []);
    },
    checkOrderDetail(){
   WadeMobile.getSysInfo("PLATFORM").then((info) => {
      this.outCallMonetOrderData.platfForm=info;
      this.setOutCallMonetOrderData(this.outCallMonetOrderData);
      console.log("PLATFORM：：" + info);
    }, (err) => {
      console.log("PLATFORM"+"失败：" + err);
    });
   },
    preSubmit(){
      
      let zwInfoList={};
      if(this.outCallMonetOrderData.goodData.custList.length==0){
        this.$toast('请选择融合定制商品')
        return;
      }
      this.$emit('startLoading', '')
      if(this.outCallMonetOrderData.goodData.zwList.length>0){
        let zw={
          commodityCode:this.outCallMonetOrderData.goodData.zwList[0].commodityCode,
          commodityName:this.outCallMonetOrderData.goodData.zwList[0].commodityName,
        }
        zwInfoList=zw;
      }

      let custInfoList={};
      if(this.outCallMonetOrderData.goodData.custList.length>0){
        let zw={
          commodityCode:this.outCallMonetOrderData.goodData.custList[0].commodityCode,
          commodityName:this.outCallMonetOrderData.goodData.custList[0].commodityName,
        }
        custInfoList=zw;
      }
      
      
      let ywfGoodInfoList=[];
      if(this.outCallMonetOrderData.goodData.commodityChooseYWUlList.length==0){
        let ywf={
          ywfFlg:0,
          ywfName:'不需要',
          goodsPrice:0
        };
        ywfGoodInfoList.push(ywf);
      }
      if(this.outCallMonetOrderData.goodData.commodityChooseYWUlList.length>0){
        if(this.outCallMonetOrderData.goodData.commodityChooseYWUlList[0].ywGoodId!='0'){
          for(let i=0;i<this.outCallMonetOrderData.goodData.commodityChooseYWUlList.length;i++){
          let ywf={
            ywfFlg:this.outCallMonetOrderData.goodData.commodityChooseYWUlList[i].ywGoodId,
            ywfName:this.outCallMonetOrderData.goodData.commodityChooseYWUlList[i].ywGoodName,
            goodsPrice:0
          }
            ywfGoodInfoList.push(ywf);
          }
        }
        else{
            let ywf={
              ywfFlg:0,
              ywfName:this.outCallMonetOrderData.goodData.commodityChooseYWUlList[0].ywGoodName,
              goodsPrice:0
                };
                ywfGoodInfoList.push(ywf);
          
        }
      }
      let iptvInfoList=[];
      if(this.outCallMonetOrderData.goodData.iptvList.length>0) {
        for (let i = 0; i < this.outCallMonetOrderData.goodData.iptvList.length; i++) {
          let iptv = {
            iptvFlg: this.outCallMonetOrderData.goodData.iptvList[i].commodityCode,
            iptvName: this.outCallMonetOrderData.goodData.iptvList[i].iptvName,
            goodsPrice: this.outCallMonetOrderData.goodData.iptvList[i].goodsPrice,
            goodsJMPrice: this.outCallMonetOrderData.goodData.iptvList[i].goodsJMPrice,
            commType: this.outCallMonetOrderData.goodData.iptvList[i].commType
          }
          iptvInfoList.push(iptv)
        }
      }

        let kdfGoodInfoList=[];
        if(this.outCallMonetOrderData.goodData.kdfList.length==0){
          let ywf={
            commId:0,
            commName:'不需要',
            goodsPrice:0
          };
          kdfGoodInfoList.push(ywf);
        }
        if(this.outCallMonetOrderData.goodData.kdfList.length>0){
          if(this.outCallMonetOrderData.goodData.kdfList[0].commId!='0'){
            for(let i=0;i<this.outCallMonetOrderData.goodData.kdfList.length;i++){
              let ywf={
                kdfjFlg:this.outCallMonetOrderData.goodData.kdfList[i].commId,
                kdfjName:this.outCallMonetOrderData.goodData.kdfList[i].commName,
                goodsPrice:0
              }
              kdfGoodInfoList.push(ywf);
            }
          }
          else{
            let ywf={
              kdfjFlg:0,
              kdfjName:this.outCallMonetOrderData.goodData.kdfList[0].commName,
              goodsPrice:0
            };
            kdfGoodInfoList.push(ywf);

          }
        }
      let  stdContact=this.outCallMonetOrderData.timeData.contactName;
      if(this.isEmpty(stdContact)){
        stdContact=this.outCallMonetOrderData.checkNumberData.showJson.custName;
      }
      if(stdContact==this.outCallMonetOrderData.checkNumberData.showJson.showCustName){
        stdContact=this.outCallMonetOrderData.checkNumberData.showJson.custName;
      }
      let communityAddrInfo={
        exchCode:this.outCallMonetOrderData.installAddr.ziyuan.accessList.exchList.exchCode,
        addressName:this.outCallMonetOrderData.installAddr.fullAddr,
      addressCode:this.outCallMonetOrderData.installAddr.SEGM_ID,
      stdContact:stdContact,
       stdContactPhone:this.outCallMonetOrderData.timeData.contactPhone,
        stdBookDay:this.outCallMonetOrderData.timeData.stdBookDay,
        stdBookTime:this.outCallMonetOrderData.timeData.stdBookTime
      }
      let req={
        kdfjGoodInfoList:kdfGoodInfoList,
        ywfGoodInfoList:ywfGoodInfoList,
        iptvInfoList:iptvInfoList,
        zwCommodityInfo:zwInfoList,
        kdCommodityInfo:custInfoList,
        orderType:"03",
        communityAddrInfo:communityAddrInfo,
        serviceNumber:this.outCallMonetOrderData.checkNumberData.serialNumber,
        choiceValue:"1",
        orderPrice:this.outCallMonetOrderData.orderPrice,
        remark:"备注:",
        singleSwitchParams:this.outCallMonetOrderData.singleSwitchParams
      }
      this.$http.post('/outCallMonetGBroad/preSubmitYsk',req).then((res)=>{
        this.$emit('endLoading', '')
        if (res.respCode === '0000') {
          this.outCallMonetOrderData.orderId=res.respData.orderId;
          this.updateChatList({
            sender: '1',
            type: 'module',
            moduleName: 'TextResponse',
            moduleLevel: 1,
            params: {
              text: '订单提交成功！订单号'+res.respData.orderId
            },
            show: true
          })
          this.setOutCallMonetOrderData(this.outCallMonetOrderData);
          let data = {
            inputType: "1",
            type: '1',
            textInput: "zwOrderfoSubmit",
            notifyFlag: '',
            taskName:'智能移送宽甩单'
          }
          this.$emit('newChatApi', data);
        }
        else{
          this.updateChatList({
            sender: '1',
            type: 'module',
            moduleName: 'TextResponse',
            moduleLevel: 1,
            params: {
              text: res.respMsg
            },
            show: true
          })
        }
      })
    }
  }
}
</script>
<style lang="scss">
.card-verification {
  width:100%;
  margin: auto;
  .card-group {
    background-color: white;
    margin: 0px 10px;
    padding:0px 5px;
    .tip {
      .van-cell__value {
        color: black;
        text-align: right;
      }
    }
    .card-list {
      .van-cell__title {
        font-size: 13px;
        color: #666666;
      }
      .van-cell__value {
        color: black;
        text-align: right;
        font-size: 13px;
      }
      .van-cell {
        color: black !important;
        padding: 0px 16px;
      }
      .van-cell::after {
        color: black !important;
        border-bottom: 0;

      }
    }
    .van-hairline--top-bottom::after, .van-hairline-unset--top-bottom::after {
      border-width: 0 0;
    }
  }
  .card {
    .van-cell__title {
      font-size: 13px;
      color: #000000;
    }
    .checkButton {
      width: 70px;
      height: 30px;
      background: #0081FF;
      border-radius: 4px;
    }
    .checkFont {
      font-size: 13px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 14px;
    }
  }
  .check{
    .van-cell__title {
      font-size: 13px;
      color: #333333;
    }
  }
}
</style>

<style lang="scss" scoped>
.card-verification {
  background-color: rgb(57,159,254);
  .subpreview-box{
    border-radius: 10px;
    margin-bottom: 10px;
    box-shadow: 0 2px 1px 0 #eee;
    .total-title{
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 5px 10px;
      width: 93%;
      border-radius: 10px 10px 0 0;
      margin-bottom: 10px;
      .custom-label-name{
        font-size: 15px;
        line-height: 30px;
        color: #263A5F;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
      }
    }
    
    .custom-title{
      font-family: PingFangSC-Regular;
      font-size: 14px;
      line-height: 26px;
      color: #263A5F;
      padding-left: 14px;
    }
    .flex-shink{
      flex-shrink: 0;
    }
  }
  .desc {
    color: black;
    font-size: 13px;
    line-height: 20px;
  }
  .margin-two {
    bottom: 10px;
    left: 15px;
    right: 15px;
    z-index: 2;
    position: absolute;
  }
  .van-buttons {
    border-radius: 4px;
    height: 44px;
    font-size: 13px;
    color: #FFFFFF;
    background-color: #0081FF;
  }
  .font {
    color:#333333;
  }
  .submit-container{
    .sub{
      border: 2px solid rgba(73,124,246,1);
      border-radius: 5px;
      color: rgba(73,124,246,1);
      margin-top: 20px;
    }
  }
}

</style>