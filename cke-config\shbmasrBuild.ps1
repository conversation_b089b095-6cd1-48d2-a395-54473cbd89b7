# Docker打包脚本示例（控制台执行命令：.\ManageBuild.ps1）
# 1、确认dockerFile路径
# 2、确认镜像名称


$currentTime = Get-Date
$formattedTime = $currentTime.ToString("yyyyMMddHHmmss")
$imageType = "shbmasr"
$dockerfilePath = "C:\liangxf\shbmasr_front\cke-config\Dockerfile"
$imageName = $imageType + ":" + $formattedTime

Write-Host "--------------------------------"  $imageName  "--------------------------------"
Write-Host ">>>>>>>>>>>>>>>>>>Docker Start>>>>>>>>>>>>>>>>>"
# 登录Docker仓库
docker login ************* -u puying2-3 -p n5T@7aI4V848B
# 构建Docker镜像
docker build -t $imageName -f $dockerfilePath .
