<template>
  <div class="dh-field">
    <div class="van-hairline--bottom">
      <van-field
          v-model="resultLabel"
          v-bind="$attrs"
          readonly
          label="请选择组网商品"
          :disabled="$attrs.disabled"
          :is-link="$attrs.disabled === undefined"
          error-message-align='left'
          input-align="left"
          class="dh-cell"
          label-width="88%"
          :rules="[{ required: $attrs.required, message: '请选择' + $attrs.label }]"
          @click="showPopu($attrs.disabled)"
      />
      
      <van-popup v-model="show" position="bottom" class="" >
        <van-field style="margin-left: 5%;width: 90%;border-radius: 10px;padding: 10px;" v-if="isSearch" v-model="searchVal" input-align="left" placeholder="请输入搜索内容" @input="search"/>
        <!--        <div class="van-picker__toolbar">-->
<!--          <button type="button" class="van-picker__cancel" @click="cancel">取消</button>-->
<!--          <div class="van-ellipsis van-picker__title">{{$attrs.label}}</div>-->
<!--          <button type="button" class="van-picker__confirm" @click="onConfirm">确认</button>-->
<!--        </div>-->
        
        
        <div class="popup-list">
          <van-radio-group ref="checkboxGroup" v-model="checkboxValue" @change="change">
            <van-cell-group>
              <van-cell
                  v-for="(item, index) in columnsData"
                  :key="item[option.value]"
                  :title="item[option.label]"
                  clickable
                  @click="toggle(index)"
              >
                <template #right-icon>
                  <van-radio ref="checkboxes" :name="item[option.value]" />
                </template>
              </van-cell>
            </van-cell-group>
          </van-radio-group>
        </div>
        <button type="button" class="van-picker__confirm" @click="onConfirm">确认</button>

      </van-popup>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VanFieldCheckbox',
  model: {
    prop: 'selectValue'
  },
  props: {
    columns: {
      type: Array,
      default: function () {
        return []
      }
    },
    selectValue: {
      type: Array,
      default: function () {
        return []
      }
    },
    option: {
      type: Object,
      default: function () {
        return { label: 'label', value: 'value' }
      }
    },
    // 是否支持搜索
    isSearch: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    resultLabel: {
      get () {
        const res = this.columns.filter(item => {
          return this.resultValue.indexOf(item[this.option.value]) > -1
        })
        const resLabel = res.map(item => {
          return item[this.option.label]
        })
        return resLabel.join(',')
      },
      set () {

      }
    }
  },
  data () {
    return {
      show: false,
      searchVal: '',
      columnsData: JSON.parse(JSON.stringify(this.columns)),
      checkboxValue: JSON.parse(JSON.stringify(this.selectValue)),
      checkedAll: false,
      resultValue: JSON.parse(JSON.stringify(this.selectValue)),
      label:''
    }
  },
  methods: {
    // 搜索
    search (val) {
      if (val) {
        this.columnsData = this.columnsData.filter(item => {
          return item[this.option.label].indexOf(val) > -1
        })
      } else {
        this.columnsData = JSON.parse(JSON.stringify(this.columns))
      }
    },
    getData (val) {
      const res = this.columnsData.filter(item => {
        return val.indexOf(item[this.option.value]) > -1
      })
      return res
    },
    onConfirm () {
      this.resultValue = this.checkboxValue
      this.show = !this.show
      this.$emit('confirm', this.resultValue, this.getData(this.resultValue))
    },
    change (val) {
      
      this.$emit('checkBookedNotChooseNew')
      this.$emit('change', val, this.getData(this.resultValue))
    },
    cancel () {
      this.show = !this.show
      this.$emit('cancel', this.resultValue)
    },
    toggle (index) {
      this.$refs.checkboxes[index].toggle()
    },
    toggleAll (all) {
      this.$refs.checkboxGroup.toggleAll(this.checkedAll)
    },
    showPopu (disabled) {
      this.columnsData = JSON.parse(JSON.stringify(this.columns))
      this.checkboxValue = JSON.parse(JSON.stringify(this.selectValue))
      this.resultValue = JSON.parse(JSON.stringify(this.selectValue))
      if (disabled !== undefined && disabled !== false) {
        return false
      } else {
        this.show = !this.show
      }
    }
  },
  watch: {
    selectValue: function (newVal) {
      this.resultValue = newVal
    },
    resultValue (val) {
      this.searchVal = ''
      this.columnsData = JSON.parse(JSON.stringify(this.columns))
      this.$emit('input', val)
    },
    columnsData: {
      handler (val) {
        if (val.length && val.length === this.checkboxValue.length) {
          this.checkedAll = true
        } else {
          this.checkedAll = false
        }
      },
      immediate: true
    },
    checkboxValue: {
      handler (val) {
        if (val.length && val.length === this.columnsData.length) {
          this.checkedAll = true
        } else {
          this.checkedAll = false
        }
      },
      immediate: true
    }
  }
}
</script>

<style lang="less" scoped>
.dh-field {
  padding: 0;
  background:#fff;
  .van-hairline--bottom{
    padding: 10px;
    .dh-cell.van-cell {
      background: #EDF7FF;
      border-radius: 8px;
      padding: 8px;
    }
    .dh-cell.van-cell--required::before {
      left: -8px;
    }
    .van-popup {
      padding: 20px 0;
      border-radius: 20px 20px 0 0;
      background-image: linear-gradient(179deg, #E5F0FF 0%, #FFFFFF 36%);
      box-shadow: 0px -4px 14px 4px rgba(0,0,0,0.09);
      .popup-list{
        max-height:264px;
        overflow-y:auto;
        padding: 0 15px;
      }
      .van-picker__confirm{
        width: 90%;
        border: 2px solid rgba(73, 124, 246, 1);
        border-radius: 8px;
        color: rgba(73, 124, 246, 1);
        padding: 10px 0;
        margin-left: 4%;
        margin-top: 20px;
      }
    }
  }
}
</style>
<style scoped>
.dh-field {
  .van-hairline--bottom{
    .van-field__label{
      color: #263A5F;
      font-weight: bold;
      font-size: 15px;
    }
    .van-cell__right-icon{
      color: #263A5F;
      font-weight: bold;
    }
    .van-popup {
      .popup-list{
        .van-cell{
          color: #263A5F;
          background: none;
        }
        .van-field__control {
          text-align: left;
        }

        .van-field__control::-webkit-input-placeholder {
          text-align: center;
          direction: ltr;
        }

        .van-field__control::-moz-placeholder {
          text-align: center;
          direction: ltr;
        }

        .van-field__control:-ms-input-placeholder {
          text-align: center;
          direction: ltr;
        }

        .van-field__control::-ms-input-placeholder {
          text-align: center;
          direction: ltr;
        }
      }
    }
  }
}
</style>