<template>
  <div >
      <div class="selectProduct" id="selectProduct">
        <div class="box" :style="{border:item.isActive ? '2px solid #3498db':''}" v-for="(item,index) in iptvInfoList" :key="index"  @click="goodClick(item,$event)">
          <div class="tag" :style="{ backgroundColor: item.color }">{{item.title}}</div>
          <div class="cell1" v-if="item.SHORT_NAME" v-html="item.SHORT_NAME"></div>
          <div class="cell2"  v-if="item.RENT"   v-html="item.RENT"></div>
          <div class="cell ellipsis" v-if="!item.SHORT_NAME&&!item.RENT">{{item.iptvName.length>30?item.iptvName.slice(0,30)+'...':item.iptvName}}</div>
          <div class="cell3" v-if="item.goodPrice">调测费:</div>
          <div class="cell4" v-if="item.goodPrice">{{item.goodPrice}}元</div>
          <div class="cell5" v-show="!isEmpty(item.SPECIAL_NAME)">{{item.SPECIAL_NAME}}</div>
          <div class="success-box" v-if="item.isActive">
            <div class="success"></div>
          </div>
        </div>
      </div>
  </div>
</template>
<script>
import {mapActions, mapMutations, mapState} from "vuex";
export default {
  name:  "IptvSelectProduct",
  data(){
    return {
      ziFeiPrice:0,
      pageIptvPrice:0,
      orderPriceNum:0,
      radio: '',
      colors:['rgb(129,211,248)','rgb(251,6,6)','#71bc8c'],
      selectedGooIds: [],
      choosedIPTV:"",
      iptvInvalid:[],
      iptvInfoCheckList:[],
      iptvInfoList: [
      ]
    }
  },
  mounted() {
    this.iptvOrderData.iptvGoodData.iptvList=[];
    this.iptvOrderData.iptvGoodData.commodityChooseYWUlList=[];
    this.setIptvOrderData(this.iptvOrderData);
    this.iptvInfoList=[];
    console.log(this.iptvOrderData.checkNumberData.iptvInvalid,'this.iptvOrderData.checkNumberData.iptvInvalid')
    let colorLength=this.colors.length
    if(this.iptvReProductList.length>0){
      for(let i=0;i<this.iptvReProductList.length;i++){
        let price=0
        if(!this.isEmpty(this.iptvReProductList[i].GOOD_PRICE)){
          price=this.iptvReProductList[i].GOOD_PRICE
        }
        let title=''
        if(this.iptvReProductList[i].catName=='宽视界'){
          title='宽视界'
        }
        else{
          title='IPTV'
        }
        let exit=false;
        for(let j=0;j<this.iptvOrderData.checkNumberData.iptvInvalid.length;j++){
          if(this.isEmpty(this.iptvReProductList[i].ancestors)){
           break;
          }
          if(this.iptvReProductList[i].ancestors.indexOf(this.iptvOrderData.checkNumberData.iptvInvalid[j].iptvId) !== -1){
            exit=true
            break;
          }}
        let iptvInfo={
          title:title,
          ancestors:this.iptvReProductList[i].ancestors,
          SHORT_NAME:this.iptvReProductList[i].SHORT_NAME,
          commodityCode:this.iptvReProductList[i].commodityCode,
          SPECIAL_NAME:this.iptvReProductList[i].SPECIAL_NAME,
          commType:this.iptvReProductList[i].commType,
          iptvName:this.iptvReProductList[i].iptvName,
          RENT:this.iptvReProductList[i].RENT,
          color:this.colors[i%colorLength],
          isActive: false,
          // goodPrice: this.iptvReProductList[i].GOOD_PRICE
          goodPrice: price
        }
        if(!exit){
          this.iptvInfoList.push(iptvInfo)
        }
      }
    }
  },
  computed: {
    ...mapState([
      'jzfkOrderData',
      'shbmMsgInfo',
      'iptvOrderData',
      'iptvCacheList',
      'iptvReProductList',
      'iptvCacheList',
        'iptvOrderData'
    ])
  },
  methods:{
    ...mapMutations([
      'setFlowStep',
      'setRobotWorking',
      'setIptvOrderData'
    ]),
    ...mapActions(['updateChatList']),
    isEmpty(value) {
      let flag = false
      if (value == '' || value == 'undefined' || value == undefined || value == null || value == 'null') {
        flag = true
      }
      return flag
    },
    iptvClickFee(iptv,sub){
      let data = {
        "iptvQryCommId": iptv.commodityCode,
        "iptvJmTag": "0"
      }
      this.$http.post('/iptvReceive/getIptvGoodsFeeByInterface', data).then(res => {
        if (res.respCode == "0000") {
          // 接收返回参数
          iptv.goodsJMPrice=res.respData.goodsJmPrice;
          iptv.goodsPrice=res.respData.goodsPrice;
          console.log(this.iptvOrderData.iptvGoodData.iptvList,"console.log(iptvOrderData.iptvGoodData.iptvList)")
          if(sub=='+'){
            this.iptvOrderData.iptvGoodData.iptvList.push(iptv);
            this.setIptvOrderData(this.iptvOrderData);
            console.log(this.iptvOrderData.iptvGoodData.iptvList,"console.log(iptvOrderData.iptvGoodData.iptvList)")
            this.ziFeiPrice=this.ziFeiPrice+parseInt(res.respData.goodsJmPrice);
            this.pageIptvPrice=this.pageIptvPrice+parseInt(res.respData.goodsPrice);
            this.orderPriceNum=this.ziFeiPrice+this.pageIptvPrice;}
          else{
            this.ziFeiPrice=this.ziFeiPrice-parseInt(res.respData.goodsJmPrice);
            this.pageIptvPrice=this.pageIptvPrice-parseInt(res.respData.goodsPrice);
            this.orderPriceNum=this.ziFeiPrice+this.pageIptvPrice;
            this.iptvOrderData.iptvGoodData.iptvList=this.iptvOrderData.iptvGoodData.iptvList.filter(i => i !== iptv)
            this.setIptvOrderData(this.iptvOrderData);
          }
          this.iptvOrderData.orderPrice=this.orderPriceNum;
          this.setIptvOrderData(this.iptvOrderData)
        }
      else{
          this.$toast(res.resMsg);
        }
      }).catch(e => {
       this.$toast(e)
      })
    },
    goodClick(item){
      item.isActive = !item.isActive
      if (item.isActive) {
        this.selectedGooIds.push(item.commodityCode);
        this.iptvClickFee(item,'+')
      } else {
        this.selectedGooIds = this.selectedGooIds.filter(i => i !== item.commodityCode);
        this.iptvClickFee(item,'-')
      }
    }}
}
</script>
<style scoped lang="scss">
.red-border{
  border: rgb(221,248,255) 2px solid;}
.selectProduct{
  display: flex;
  flex-wrap: nowrap; /* 确保子元素不换行 */
  overflow-x: scroll;  /* 启用水平滚动 */
  -webkit-overflow-scrolling: touch; /* 改善移动设备上滚动的体验 */
  .box {
    position: relative;
    border-radius: 10px;
    margin: 0 10px 10px 0;
    flex: 0 0 calc(24%); /* 使用 calc() 考虑 margin */
    max-width: 120px; /* 同样考虑 margin */
    background: linear-gradient(to bottom right, #fff, #EDF0FF);
    padding: 0 0 10px 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .tag{
      color: #fff;
      border-radius: 10px 0 10px 0;
      padding: 0 6px;
      font-size: 11px;
      width: 45%;
      text-align: center;
    }
    .cell {
      margin: 6px 0;
      width: 85px;
      text-align: center;
      font-weight: bold;
      font-size: 13px;
    }
    .cell1 {
      margin: 6px 0;
      width: 95px;
      text-align: center;
      font-weight: bold;
      font-size: 13px;
      line-height: 15px;
    }
    .cell2 {
      margin-bottom: 6px;
      width: 100%;
      text-align: center;
      font-size: 11px;
      line-height: 16px;
    }
    .cell3{
      margin-bottom: 6px;
      width: 100%;
      text-align: center;
      font-size: 11px;
      line-height: 16px;
    }
    .cell4{
      margin-bottom: 6px;
      width: 100%;
      text-align: center;
      font-size: 11px;
      line-height: 16px;
    }
    .cell5{
      margin-bottom: 6px;
      width: 100%;
      text-align: center;
      font-size: 11px;
      line-height: 16px;
    }
    .btn{
      border: 2px solid #4494E6;
      border-radius: 10px;
      color: #4494E6;
      height: 25px;
      font-size: 11px;
      padding: 5px 8px;
      background: none;
    }
    .active-btn{
      border: 2px solid #4494E6;
      border-radius: 10px;
      color: #fff;
      background: #4494E6!important;
      height: 25px;
      font-size: 11px;
      padding: 5px 8px;
      background: none;
      margin-left: 10px;
    }
    .success-box{
      position: absolute;
      bottom: 0;
      right: 0;
      .success {
        position: relative;
        width: 25px;
        height: 25px;
        background-image: url('@/assets/images/choosePhoto.png');
        background-position: center center;
        background-repeat: no-repeat;
        background-size: cover;
        clip-path: polygon(0 100%, 100% 0, 100% 100%);
        border-bottom-right-radius: 8px;
      }

    }
  }
  /* WebKit 浏览器滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  ::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

  /* Firefox 移动端浏览器 */
  .scrollable-element {
    scrollbar-width: auto;
    scrollbar-color: #888 #f1f1f1;
  }

  /* 元素样式 */
  .scrollable-element {
    width: 300px;
    height: 200px;
    overflow: auto;
    border: 1px solid #ccc;
    padding: 10px;
    -webkit-overflow-scrolling: touch;
  }
  @-webkit-keyframes showScrollbar {
    from {
      opacity: 1;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes showScrollbar {
    from {
      opacity: 1;
    }
    to {
      opacity: 1;
    }
  }
}
</style>