apiVersion: apps/v1
kind: Deployment
metadata:
  name: shbmasr-front-pre
  namespace: shbmasr-pre
  labels:
    app: shbmasr-front-pre
spec:
  replicas: 1
  selector:
    matchLabels:
      app: shbmasr-front-pre
  template:
    metadata:
      name: shbmasr-front-pre
      labels:
        app: shbmasr-front-pre
    spec:
      containers:
        - name: shbmasr-front-pre
          image: 'harbor.dcos.xixian.unicom.local/shzwt/shbmasr-front-pre'
          ports:
            - containerPort: 80
          imagePullPolicy: IfNotPresent
          volumeMounts:
            - name: shbmasr-front-pre
              mountPath: /mnt
      volumes:
        - name: shbmasr-front-pre
          hostPath:
            path: /var/lib/docker/log/shbm/shbmasr-front-pre
      restartPolicy: Always