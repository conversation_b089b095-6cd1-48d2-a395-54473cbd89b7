import "@babel/polyfill";
// import 'vant/lib/index.less';
import "vant/lib/index.css";
import 'vant/lib/icon/local.css';
import '@/assets/css/reset.scss';
import '@/assets/js/flexible.js';
//iconfont多色图标
import "@/assets/font/iconfont/iconfont.js";
//iconfont单色字体图标
import "@/assets/font/iconfont/iconfont.css";
function enableConsole() {
    import (/* webpackChunkName: "vconsole" */'./vconsole.min.js').then(module => {
        const VConsole = module.default;
        new VConsole()
    })
}

if (ENV === 'pre') {
    enableConsole();
}

