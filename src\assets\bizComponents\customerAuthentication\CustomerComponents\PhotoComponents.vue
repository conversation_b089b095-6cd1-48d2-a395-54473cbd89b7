<template>
    <div :class="{'photo-box':copyCardCustInfoFrontSwitch}">
        <div class="box-new-ui" v-if="copyCardCustInfoFrontSwitch">
            <div class="faceImgBox" @click="faceRecognition()" v-if="switchParam.facePicFlag != 'true'">
                <img src="@/assets/images/icon-face-img.png" class="icon-img" v-if="faceImgResult">
                <svg
                    t="1675909200254"
                    class="icon"
                    v-if="!faceImgResult"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="7152"
                    width="30"
                    height="30"
                ><path d="M767.158857 877.714286h-115.858286a25.526857 25.526857 0 0 1 0-51.017143h115.858286a59.611429 59.611429 0 0 0 59.538286-59.538286V660.845714a25.526857 25.526857 0 0 1 51.017143 0v106.313143A110.701714 110.701714 0 0 1 767.158857 877.714286z m85.028572-476.269715a25.526857 25.526857 0 0 1-25.490286-25.526857V256.841143a59.611429 59.611429 0 0 0-59.538286-59.538286h-115.858286a25.526857 25.526857 0 0 1 0-51.017143h115.858286A110.665143 110.665143 0 0 1 877.714286 256.841143v119.076571a25.490286 25.490286 0 0 1-25.526857 25.526857z m-680.374858 0A25.526857 25.526857 0 0 1 146.285714 375.917714V256.841143A110.665143 110.665143 0 0 1 256.841143 146.285714h114.834286a25.526857 25.526857 0 0 1 0 51.017143h-114.834286a59.611429 59.611429 0 0 0-59.538286 59.538286v119.076571a25.490286 25.490286 0 0 1-25.490286 25.526857zM371.675429 877.714286h-114.834286A110.665143 110.665143 0 0 1 146.285714 767.158857V660.845714a25.526857 25.526857 0 0 1 51.017143 0v106.313143c0 32.804571 26.697143 59.538286 59.538286 59.538286h114.834286a25.526857 25.526857 0 0 1 0 51.017143zM512 716.105143c-96.182857 0-174.336-91.538286-174.336-204.105143 0-112.566857 78.189714-204.105143 174.336-204.105143 96.182857 0 174.336 91.538286 174.336 204.105143 0 112.566857-78.262857 204.105143-174.336 204.105143z m0-357.193143c-68.022857 0-123.318857 68.681143-123.318857 153.088S443.977143 665.088 512 665.088c68.022857 0 123.318857-68.681143 123.318857-153.088S580.022857 358.912 512 358.912z" fill="#666666" p-id="7153" /><path d="M770.962286 552.630857H253.074286c-14.08 0-25.490286-9.106286-25.490286-20.297143 0-11.227429 11.410286-20.333714 25.490286-20.333714H770.925714c14.08 0 25.490286 9.142857 25.490286 20.333714 0 11.190857-11.410286 20.297143-25.490286 20.297143z" fill="#666666" p-id="7154" /></svg>
                <div class="faceImgTips">
                    <p class="name">
                        人脸识别
                    </p>
                    <p class="tips">
                        请打开摄像头，按要求操作
                    </p>
                </div>
                <van-button type="info" size="small" round>
                    {{ faceImgResult ? '重新识别' : '去完成' }}
                </van-button>
            </div>
            <div v-if="faceImgResult">
                <img :src="faceImgResult" class="img-style" style="width: 40%;">
            </div>
        </div>
        <div class="box-new-ui">
            <div class="faceImgBox flex-warp">
                <img src="@/assets/images/icon-idCard.png" class="icon-img">
                <div class="faceImgTips">
                    <p class="name">
                        证件上传
                    </p>
                    <p class="tips">
                        请确保证件上所有信息清晰可见
                    </p>
                </div>
                <div class="take-photo">
                    <div @click="takePhotos('idCardFront', frontImg)">
                        <div v-if="frontImg">
                            <img :src="frontImg" class="img-style">
                        </div>
                        <div v-else>
                            <img src="@/assets/images/icon-photo-bg.png" class="img-style">
                        </div>
                        <p class="text" v-if="!['10','35'].includes(switchParam.chooseCertType)">
                            身份证正面
                        </p>
                    </div>
                    <div @click="takePhotos('idCardBack', backImg)">
                        <div v-if="backImg">
                            <img :src="backImg" class="img-style">
                        </div>
                        <div v-else>
                            <img src="@/assets/images/icon-photo-bg.png" class="img-style">
                        </div>
                        <p class="text" v-if="!['10','35'].includes(switchParam.chooseCertType)">
                            身份证反面
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <van-image-preview v-model="showImg.show" :images="showImg.images" :show-index="false" class="my-img-preview">
            <template #cover>
                <van-button type="info" @click="showImg.func == 'takePhotos' ? takePhotos(showImg.photoType) : (showImg.func =='takeGroupPhotos' ? takeGroupPhotos(showImg.photoType) : faceRecognition())">
                    重拍
                </van-button>
            </template>
        </van-image-preview>
        <van-action-sheet
            v-model="showSheet"
            cancel-text="取消"
            close-on-click-action
            :actions="sheetActions"
            @select="onSelectSheet"
        />
    </div>
</template>

<script>
/* eslint-disable vue/no-mutating-props, no-unused-vars */
import { Mobile, Common } from 'rk-web-utils';
import WadeMobile from 'rk-native-plugin';
import { alertError, location } from '@/assets/bizComponents/funcComponent.js';
import util from './customerUtil';
import { Dialog } from 'vant';
import Bus from '@/assets/bizComponents/PaperlessSign/bus';
import SameCustomerAuthentication from './SameCustomerAuthentication';
import CentralInfoCollected from './CentralInfoCollected';
import axios from 'axios';
import { base64ToFile } from '@/assets/js/utils';
// import photoExample from './photoExample';
import { aes } from '@/assets/js/utils.js';
import { mapState } from 'vuex';
import { ATTACH_0, ATTACH_1, ATTACH_2 } from  '@/assets/js/mock'

export default {
    props: {
        switchParam: { required: true, type: Object, default: () => {} },
        // eslint-disable-next-line vue/prop-name-casing
        IDCardInfo: { required: true, type: Object, default: () => {} },
        customerCheck: { required: true, type: String, default: '' },
        sessionId: { required: true, type: String, default: '' },
        passportCanDo: { type: Boolean, default: false },
        authModeType: { type: String, default: '' },
        showAuthenticationType: { type: String, default: '' },
        copyCardCustInfoFrontSwitch: { type: Boolean, default: false },
        selectedReadOrNotType: { type: String, default: '1' },
    },
    components: {
        // eslint-disable-next-line vue/no-unused-components
        SameCustomerAuthentication,
        // eslint-disable-next-line vue/no-unused-components
        CentralInfoCollected,
        // photoExample
    },
    mounted() {
        let isApp = Mobile.isApp();
        if (isApp) {
            Mobile.getMemoryCache('staffInfo').then(result => {
                this.staffInfo =  JSON.parse(result);
            });
        } else {
            let staffInfoData = JSON.parse(sessionStorage.getItem('staffInfo'));
            this.staffInfo = staffInfoData;
        }
        // this.$router.addRoutes([
        //     {
        //         path: '/SameCustomerAuthentication',
        //         name: 'SameCustomerAuthentication',
        //         component: SameCustomerAuthentication,
        //         meta: {
        //             topBar: {
        //                 title: '同客户校验',
        //                 back: true
        //             }
        //         }
        //     },
        //     {
        //         path: '/CentralInfoCollected',
        //         name: 'CentralInfoCollected',
        //         component: CentralInfoCollected,
        //         meta: {
        //             topBar: {
        //                 title: '人工审核信息收集',
        //                 back: true
        //             }
        //         }
        //     }

        // ]);
        // this.sameCustomerAuthentication()
        // this.enterCentralAudit()
        this.getIdentityBackAuthSwitch();
        this.getCacheAndBlackSwitchDetail();
    },
    data() {
        return {
            faceImgResult: '', //人脸识别活体照
            imgCode: '',
            faceProcId: '', //天盾流水号
            realFaceImg: '', //活体第二张（best）
            customerImg: '',
            frontImg: '',
            backImg: '',
            cityName: '',
            watermark: '', //水印
            customerWaterMark: '',
            pic6Num: 0,
            pic7Num: 0,
            checkNum: 0,
            faceMixContrast: '66',
            needRealNameFlag: false, //需要人工稽核,暂时不去这个字段，需要再加
            sameCustomerAuthFlag: false, //同客户校检，为true降低阈值到省份最低阈值（默认66）,需求暂时关闭同客户验证，目前都取false
            custPicParamInfo: {},
            picDataa1Img: '',
            translationImg: '',
            greementPhoto1: '',
            greementPhoto2: '',
            videoPath: '',
            riskNoticeImg: '',
            picGD: '',
            picSZ: '',
            pic29: '',
            pic30: '',
            pic43: '',
            picVImg: '',
            greementPhoto1Img: '',
            greementPhoto2Img: '',
            handPhotoImg: '', // 手持二维码照片 - picU
            tdImg: '',
            staffInfo: {},
            groupPicImg1: '',
            groupPicImg2: '',
            groupPicImg3: '',
            passPortFrontImg: '', //护照正面
            passPortBackImg: '', //护照反面
            showImg: {
                show: false,
                photoType: '',
                images: [],
                func: ''
            },
            oneCardImg: '', //一证通查照片
            showSheet: false,
            sheetActions: [{ name: '拍照' }, { name: '相册' }],
            isOpenUnBlacklistComparison: false,
            // 照片压缩开关
            compressPhotoByPhotoTypeSwitch: [],
            // compressPhoto方法的压缩次数
            compressTimes: 10,
            tipImg: null,
            pic59: '', // 金融告知书
            pic60: '', //金融礼品
            readTypeShowSwitch: false,
        };
    },
    computed: {
        ...mapState([
            'cryptKey',
            'switchData',
            'staffId',
            'custAuthMockSwitch'
        ]),
    },
    created() {
        // this.readTypeShowSwitch = this.switchData.readTypeShowSwitch == '1' ? true:false
        Bus.$on('sameCustomerAuthFlag', newCustomer => {
            util.customer.info = { ...newCustomer };
        });
        this.sameCustomerAuthFlagObject = { ...util.customer.info };
        this.sameCustomerAuthFlag = this.sameCustomerAuthFlagObject.sameCustomerAuthFlag;
        if (this.sameCustomerAuthFlag) {
            this.$toast('同客户验证成功，请在此页重新人脸识别！');
        }
    },
    watch: {
        switchParam(param) {
            if (param.isMoreWaterMark == '1') {
                this.getCityName();
            }
            //判断是否为自动化测试工号，如果是则关闭活体、客户现场照、以及身份证照正反面
            if (!util.isEmpty(param.isAutomicTestStaff) && 'true' == param.isAutomicTestStaff) {
                param.facePicFlag = 'true';
                param.custPicFlag = 'true';
                param.idPicFlag = 'true';
            }
        },
        // copyCardCustInfoFrontSwitch: {
        //     handler() {
        //         this.$nextTick(()=>{
        //             if (this.copyCardCustInfoFrontSwitch && document.getElementsByClassName('photo-box')[0]) {
        //                 document.getElementsByClassName('photo-box')[0].getElementsByClassName('photo-box-live')[0].getElementsByClassName('img-style').forEach(ele=>{
        //                     ele.src = require('@/assets/images/icon-photo-bg2.png');
        //                 });
        //             }
        //         });
        //     },
        //     immediate: true
        // }
    },
    methods: {
        getCacheAndBlackSwitchDetail() {
            Mobile.getMemoryCache('staffInfo').then(res => {
                let staffInfo = JSON.parse(res);
                let provinceCode = staffInfo ? staffInfo.PROVINCE_CODE : '';
                if (staffInfo && staffInfo.rightInfo) {
                    let rightInfo = staffInfo.rightInfo;
                    for (let i = 0; i < rightInfo.length; i++) {
                        if (rightInfo[i].dataId === 'UnBlacklistComparison') {
                            this.isOpenUnBlacklistComparison = true;
                            break;
                        }
                    }
                }
            });
        },
        // 删除照片按钮
        delectPhoto(name) {
            this[name] = '';
        },
        getCityName() {
            location().then(info => {
                if (WadeMobile.isAndroid()) {
                    this.cityName = info.CITY;
                } else if (WadeMobile.isIOS()) {
                    this.cityName = info.City;
                }
            }).catch(e => {
                this.$toast('调用获取地市插件异常:' + e);
            });
        },
        cleanImgInfo() {
            this.customerImg = '';
            this.faceImgResult = '';
            this.frontImg = '';
            this.backImg = '';
            this.$emit('update:customerCheck', '0');
            this.custPicParamInfo = {};
            this.picDataa1Img = '';
            this.greementPhoto1 = '';
            this.greementPhoto2 = '';
            this.videoPath = '';
            this.riskNoticeImg = '';
            this.handPhotoImg = '';
            this.picSZ = '';
            this.picGD = '';
            this.picVImg = '';
            this.oneCardImg = '';
        },
        faceRecognition(imgUrl) {
            if (imgUrl) {
                this.showImg.show = true;
                this.showImg.images = [imgUrl];
                this.showImg.photoType = '';
                this.showImg.func = 'faceRecognition';
            } else {
                this.showImg.show = false;
                if (this.switchParam.chooseCertType == '08' && !this.IDCardInfo.avatar) {
                    this.$toast('请先上传证件信息！');
                    return;
                }
                if (!this.IDCardInfo.name) {
                    this.$toast('请先读取身份证信息！');
                } 
                // else if (this.customerCheck == '0') {
                //     this.$toast('客户资料校验未通过！');
                // } 
                else {
                    var paramsArray = [0, 0, 1];//默认为前置摄像头
                    // 客户认证mock开关打开，使用mock读取证件，接口传mock数据
                    if (this.custAuthMockSwitch) {
                        const result = {
                            "img": ATTACH_0,
                            "retCode": "0",
                            "img_best": ATTACH_0
                        }
                        if (result.retCode === '0') {
                            this.faceAuthenticationCallBack(result);
                        } else {
                            this.faceImgResult = '';
                            this.$toast('实人认证照片采集失败，请重新尝试人脸识别');
                        }
                    } else {
                        WadeMobile.faceAuthentication(paramsArray).then(result => {
                            if (result.retCode === '0') {
                                this.faceAuthenticationCallBack(result);
                            } else {
                                this.faceImgResult = '';
                                this.$toast('实人认证照片采集失败，请重新尝试人脸识别');
                            }
                        }).catch(e => {
                            this.faceImgResult = '';
                            this.$toast('调用实人认证插件异常：' + e);
                        });
                    }
                    
                }
            }
        },
        faceAuthenticationCallBack(face) {//人脸回调获取处理后的照片
            let faceImg = 'data:image/jpeg;base64,' + face.img;
            this.faceImgResult = faceImg; //未进行活体校验前的展示
            if (util.isEmpty(face.img_best)) {//活体第二张照片必须要有,没有不往下走
                this.$toast('实人认证照片采集失败，请重新尝试人脸识别');
                this.faceImgResult = '';
                return;
            }
            this.realFaceImg = face.img_best;
            let secondLivingFaceImg = 'data:image/jpeg;base64,' + face.img_best;
            let TDLivingFaceImg = face.img_best; //天盾实人认证人脸识别加固改造,现在先不走天盾，这个值暂时搁置
            if (this.switchParam.isAddCitymark == '1') {//上海水印开关
                this.watermark = '仅限办理联通业务使用' + '|' + util.getNowFormatDate() + '|' + this.staffId + '|' + this.cityName;
            } else if (this.switchParam.isDoubleWatermarkswitch == '1') {//西藏水印开关
                this.watermark = '仅限办理联通业务使用' + '|' + util.getNowFormatDate() + '  ' + this.staffId;
            } else if (this.switchParam.isAddStaffIdSwitch == '1') {//宁夏能人经理工号水印开关
                this.watermark = '仅限办理联通业务使用' + '|' + util.getNowFormatDate() + '|' + this.staffId + '|' + this.switchParam.waterMarkStaffId;
            } else {
                this.watermark = '仅限办理联通业务使用' + '|' + util.getNowFormatDate() + '|' + this.staffId;
            }
            let watermarkSplit = this.watermark.split('|');
            let livingImage = new Image();
            livingImage.src = faceImg;
            livingImage.onload = (() => {//这里先取到faceImg的值再取secondLivingFaceImg的值
                // faceImg = this.extractedWithText(livingImage, watermarkSplit, this.faceImg, 1);
                // faceImg = faceImg.replace(/data:image\/.*;base64,/, '');
                this.custPicParamInfo.pic5 = faceImg;
                this.custPicParamInfo.picC = secondLivingFaceImg;
                // this.livingBestImgOnload(secondLivingFaceImg, watermarkSplit, faceImg, TDLivingFaceImg);
            });
        },
        livingBestImgOnload(secondLivingFaceImg, watermarkSplit, faceImg, TDLivingFaceImg) {
            let livingBestImage = new Image();
            livingBestImage.src = secondLivingFaceImg;
            livingBestImage.onload = (() => {
                let secondLivingFaceImg1 = '';
                if (this.switchParam.isDoubleWatermarkswitch == '1') {
                    secondLivingFaceImg1 = this.extractedWithTextXizang(livingBestImage, watermarkSplit, secondLivingFaceImg, 2);
                } else {
                    secondLivingFaceImg1 = this.extractedWithText(livingBestImage, watermarkSplit, secondLivingFaceImg, 2);
                }
                secondLivingFaceImg1 = secondLivingFaceImg1.replace(/data:image\/.*;base64,/, '');
                if (this.$store.state.queryFaceSwitch) {
                    this.$http.post('touchService/call', {
                        serviceName: 'custPicInfoVerify.queryFaceDetect',
                        realFaceImg: this.realFaceImg
                    }).then(res => {
                        Dialog.confirm({
                            title: '提示',
                            message: res.respMsg,
                            showCancelButton: false
                        }).then(() => {
                            if (typeof res == 'object' && res.respCode == '0000') {
                                this.uploadLivingPic(faceImg, secondLivingFaceImg1, TDLivingFaceImg);
                            } else {
                                this.uploadLivingPic(faceImg, secondLivingFaceImg1, TDLivingFaceImg);
                            }
                        }).catch(() => {
                            this.uploadLivingPic(faceImg, secondLivingFaceImg1, TDLivingFaceImg);
                        });
                    });
                }
                this.uploadLivingPic(faceImg, secondLivingFaceImg1, TDLivingFaceImg);
            });
        },
        // 图片dom对象，水印信息字符串数组['仅限xx使用', 日期, staffid]，活体照片base64编码字符串，图片索引
        extractedWithText(livingImage, watermarkSplit, faceImg, imgIndex) {
            let width = livingImage.width;
            let height = livingImage.height;
            let baseFontSize = document.documentElement.style.fontSize;
            let fontSize = height / 30;
            fontSize = (fontSize * baseFontSize.substring(0, baseFontSize.length - 2) / 37.5).toFixed(0);
            let imageScale = 0.9;
            let canvas = document.createElement('canvas');
            canvas.width = width;
            canvas.height = height;
            let context = canvas.getContext('2d');
            context.drawImage(livingImage, 0, 0, width, height);
            context.font = fontSize + 'px microsoft yahei';
            if (1 == imgIndex) {
                context.fillStyle = 'rgba(255,0,0,0.4)';
            } else {
                context.fillStyle = 'rgba(255,255,255,0.8)';
            }
            let x = (width - (watermarkSplit[0].length + 1) * fontSize) / 2;
            context.fillText(watermarkSplit[0], x, height - 4 * fontSize);
            context.fillText(watermarkSplit[1], x, height - 3 * fontSize);
            context.fillText(watermarkSplit[2], x, height - 2 * fontSize);
            if (this.switchParam.isAddCitymark == '1' || this.switchParam.isAddStaffIdSwitch == '1') context.fillText(watermarkSplit[3], x, height - fontSize);
            // 活体照上方新增水印
            if (this.switchParam.isMoreWaterMark == '1') {
                context.fillText(watermarkSplit[0], x, 1 * fontSize);
                context.fillText(watermarkSplit[1], x, 2.5 * fontSize);
                context.fillText(watermarkSplit[2], x, 4 * fontSize);
                if (this.switchParam.isAddCitymark == '1' || this.switchParam.isAddStaffIdSwitch == '1') context.fillText(watermarkSplit[3], x, 5.5 * fontSize);
            }
            faceImg = canvas.toDataURL('image/jpeg', imageScale);
            let faceFlag = true;
            while (faceFlag) {
                if (faceImg.length / 1024 * 3 / 4 > 200) {
                    imageScale -= 0.05;
                    faceImg = canvas.toDataURL('image/jpeg', imageScale);
                } else {
                    faceFlag = false;
                }
            }
            return faceImg;
        },
        // 西藏水印需求，左上角+右下角水印，字体颜色黄蓝渐变
        extractedWithTextXizang(livingImage, watermarkSplit, faceImg, imgIndex) {
            let width = livingImage.width;
            let height = livingImage.height;
            // document.documentElement.style.fontSize的值为‘XXXpx’的字符串
            let baseFontSize = document.documentElement.style.fontSize;
            let fontSize = (width / 30).toFixed(0);
            let imageScale = 0.9;
            let canvas = document.createElement('canvas');
            canvas.width = width;
            canvas.height = height;
            let context = canvas.getContext('2d');
            context.drawImage(livingImage, 0, 0, width, height);
            context.font = fontSize + 'px microsoft yahei';
            // 水印颜色渐变
            // context.fillStyle = gradient;
            if (1 == imgIndex) {
                context.fillStyle = 'rgba(255,0,0,0.8)';
            } else {
                context.fillStyle = 'rgba(255,255,255,0.8)';
            }
            let x1 = (width - (watermarkSplit[0].length) * fontSize);
            // 第二行主要是英文字母+数字，所以字符数量/2
            let x2 = (width - (watermarkSplit[1].length) / 2 * fontSize);
            // 右下角水印
            context.fillText(watermarkSplit[0], x2, height - 1.7 * fontSize);
            context.fillText(watermarkSplit[1], x2, height - 0.7 * fontSize);
            // 左上角水印
            context.fillText(watermarkSplit[0], 0, fontSize);
            context.fillText(watermarkSplit[1], 0, 2 * fontSize);

            // 活体照上方新增水印
            if (this.switchParam.isMoreWaterMark == '1') {
                context.fillText(watermarkSplit[0], x2, 1 * fontSize);
                context.fillText(watermarkSplit[1], x2, 1.5 * fontSize);
            }
            faceImg = canvas.toDataURL('image/jpeg', imageScale);
            let faceFlag = true;
            while (faceFlag) {
                if (faceImg.length / 1024 * 3 / 4 > 200) {
                    imageScale -= 0.05;
                    faceImg = canvas.toDataURL('image/jpeg', imageScale);
                } else {
                    faceFlag = false;
                }
            }
            return faceImg;
        },
        async uploadLivingPic(faceImg, secondLivingFaceImg, TDLivingFaceImg) {
            // eslint-disable-next-line no-console
            // this.$toast.loading({
            //     message: '正在进行人脸比对,请稍等...',
            //     forbidClick: true,
            //     duration: 0
            // });
            // // let livingFaceKey = 'LIVING' + this.IDCardInfo.cardNo;
            // let avatar = this.IDCardInfo.avatar.replace(/data:image\/.*;base64,/, '');
            // const params = {
            //     faceimg: avatar, // 人体照片编码， base64字符串编码必传
            //     certName: this.IDCardInfo.name, // 身份证姓名必传
            //     certNum: this.IDCardInfo.cardNo, // 身份证编码，长度15或者18位必传
            //     baseimg: faceImg, // 二代证照片（芯片照）， base64字符串编码必传
            //     sessionId: this.sessionId // 必传
            // }
            // const paramsK = {
            //     cipherText: aes(true, JSON.stringify(params), this.cryptKey)
            // }
            // this.$http.post('/nx_easy/customChecknx/facecheck', paramsK).then(res => {
            //     Common.loadingStop();
            //     this.$toast.clear();
            //     res = JSON.parse(aes(false, res.respData, this.cryptKey)).respData;
            //     if (typeof res == 'object' && res.respCode == '0000') {
            //         this.$toast('人脸比对成功！');
                    this.faceImgResult = 'data:image/jpeg;base64,' + faceImg;
                    // 存储活体照和活体留存照
                    // this.custPicParamInfo.pic5 = livingFaceKey;
                    // this.custPicParamInfo.picC = 'LIVING_BEST' + this.IDCardInfo.cardNo;
                    this.custPicParamInfo.pic5 = this.faceImgResult;
                    this.custPicParamInfo.picC = 'data:image/jpeg;base64,' + secondLivingFaceImg
                    //人脸识别返回阈值
                    // this.custPicParamInfo.faceSimilarity = res.similarity;
                    this.$emit('custPicParamInfo', this.custPicParamInfo);
            //     } else {
            //         if (util.isEmpty(res) || util.isEmpty(res.similarity)) {
            //             this.custPicParamInfo.faceSimilarity = '0.00';
            //         } else {
            //             this.custPicParamInfo.faceSimilarity = res.similarity;
            //         }
            //         this.dealImg('10', res.respMsg);
            //     }
            // }).catch(e => {
            //     Common.loadingStop();
            //     this.$toast.clear();
            //     this.faceImgResult = '';
            //     alertError({
            //         title: '人脸识别校验失败！',
            //         message: e,
            //         confirmButtonText: '报告错误'
            //     });
            //     this.dealImg('10', '抱歉，人脸识别失败了');
            // });
        },
        takeGroupPhotos(photoType, imgUrl) {
            if (imgUrl) {
                this.showImg.show = true;
                this.showImg.images = [imgUrl];
                this.showImg.photoType = photoType;
                this.showImg.func = 'takeGroupPhotos';
            } else {
                this.showImg.show = false;
                if (['groupPic1', 'groupPic2', 'groupPic3'].includes(photoType)) {
                    this.getIdentifyPhoto(photoType, false);
                }
            }
        },
        async takePhotos(photoType, imgUrl) {
            if (imgUrl) {
                this.showImg.show = true;
                this.showImg.images = [imgUrl];
                this.showImg.photoType = photoType;
                this.showImg.func = 'takePhotos';
            } else {
                this.showImg.show = false;
                // if (!this.passportCanDo) {
                //     this.$toast('请先点击查询进行护照校验');
                //     return;
                // }
                if (!this.IDCardInfo.name) {
                    this.$toast('请先读取身份证信息');
                } else if (photoType == 'handPhotoImg') {
                    this.getIdentifyPhoto(photoType, true);
                } else if (!this.faceImgResult && !this.passportCanDo) {
                    this.$toast('请先进行人脸识别');
                } else {
                    if (photoType == 'face') {
                        if ('36' == this.switchParam.chooseCertType) {
                            this.getIdentifyPhoto(photoType, false);//护照边 现场照片无需人脸比对
                        } else {
                            this.getIdentifyPhoto(photoType, true);
                        }
                    } else if (photoType == 'idCardFront') {
                        // if (!this.customerImg && (this.switchParam.custPicFlag != 'true')) {
                        //     this.$toast('请先拍摄客户照片');
                        // } else {
                            this.pic6Num++;
                            this.getIdentifyPhoto(photoType, false);
                        // }
                        // this.pic6Num++;
                        // this.getIdentifyPhoto(photoType, false);
                    } else if (['picDataa1', 'translationCert', 'greementPhoto1', 'greementPhoto2', 'riskNotice', '0', '4', 'picV', 'passPortFront', 'passPortBack', '29', '30', 'oneCardImg', '43', '59', '60'].includes(photoType)) {
                        this.getIdentifyPhoto(photoType, false);
                    } else {
                        // if (!this.customerImg && (this.switchParam.custPicFlag != 'true')) {
                        //     this.$toast('请先拍摄客户照片');
                        // } else 
                        if (!this.frontImg) {
                            this.$toast('请先拍摄身份证正面照片');
                        } else {
                            this.pic7Num++;
                            this.getIdentifyPhoto(photoType, false);
                        }
                        // if (!this.frontImg && this.switchParam.idPicFlag != 'true') {
                        //     this.$toast('请先拍摄身份证正面照片');
                        // } else if (!this.customerImg && (this.switchParam.custPicFlag != 'true') && photoType != 'idCardBack') {
                        //     this.$toast('请先拍摄客户照片');
                        // } else {
                        //     this.pic7Num++;
                        //     this.getIdentifyPhoto(photoType, false);
                        // }
                    }
                }
            }
        },
        getIdentifyPhoto(photoType, needFace) {
            let frameType = photoType;
            if (['groupPic1', 'groupPic2', 'groupPic3', 'picDataa1', 'translationCert', 'greementPhoto1', 'greementPhoto2', 'riskNotice', '4', 'picV', 'passPortBack', 'passPortFront', '29', '30', 'oneCardImg', '43', '59', '60'].includes(frameType)) {
                frameType = 'noBorder';
            }
            // 护照首页照片参照身份证正面旋转
            if (['passPortFront'].includes(photoType)) {
                frameType = 'idCardFront';
            }
            // 客户认证mock开关打开，使用mock读取证件，接口传mock数据
            if (this.custAuthMockSwitch) {
                let img = ''
                if (frameType === 'idCardFront') {
                    img = ATTACH_1
                } else if (frameType === 'idCardBack') {
                    img = ATTACH_2
                }
                // if (img.length / 1024 * 3 / 4 < 50) {
                //     this.$toast('图片质量过低，请重新拍摄!');
                //     return;
                // } else 
                if (photoType == 'face') {
                    this.customerImg = img;
                } else if (photoType == 'idCardFront') {
                    this.frontImg = img;
                } else if (photoType == 'picDataa1') {
                    this.picDataa1Img = img;
                } else if (photoType == 'translationCert') {
                    this.translationImg = img;
                } else if (photoType == 'greementPhoto1') {
                    this.greementPhoto1Img = img;
                } else if (photoType == 'greementPhoto2') {
                    this.greementPhoto2Img = img;
                } else if (photoType === 'riskNotice') {
                    this.riskNoticeImg = img;
                } else if (photoType === 'handPhotoImg') {
                    this.handPhotoImg = img;
                    this.savePicUToRedis();
                } else if (photoType == '0') {
                    this.picGD = img;
                } else if (photoType == '4') {
                    this.picSZ = img;
                } else if (photoType === 'groupPic1') {
                    this.groupPicImg1 = img;
                } else if (photoType === 'groupPic2') {
                    this.groupPicImg2 = img;
                } else if (photoType === 'groupPic3') {
                    this.groupPicImg3 = img;
                } else if (photoType == 'picV') {
                    this.picVImg = img;
                } else if (photoType == 'passPortFront') {
                    this.passPortFrontImg = img;
                    /* 将芯片信息设置成护照正面 */
                    this.IDCardInfo.avatar = img;
                    if (['08', '36'].includes(this.switchParam.chooseCertType)) {
                        this.IDCardInfo.avatar = this.IDCardInfo.avatar.replace(/data:image\/.*;base64,/, '');
                    }
                } else if (photoType == 'passPortBack') {
                    this.passPortBackImg = img;
                } else if (photoType == '29') {
                    this.pic29 = img;
                } else if (photoType == '30') {
                    this.pic30 = img;
                } else if (photoType == 'oneCardImg') {
                    this.oneCardImg = img;
                } else if (photoType == '43') {
                    this.pic43 = img;
                } else if (photoType == '59') {
                    this.pic59 = img;
                } else if (photoType == '60') {
                    this.pic60 = img;
                } else {
                    this.backImg = img;
                }
                this.checkCustomerPhoto(img, needFace, photoType);
            } else {
                WadeMobile.getIdentifyPhoto(frameType, 'base64').then(img => {
                    // if (img.length / 1024 * 3 / 4 < 50) {
                    //     this.$toast('图片质量过低，请重新拍摄!');
                    //     return;
                    // } else 
                    if (photoType == 'face') {
                        this.customerImg = img;
                    } else if (photoType == 'idCardFront') {
                        this.frontImg = img;
                    } else if (photoType == 'picDataa1') {
                        this.picDataa1Img = img;
                    } else if (photoType == 'translationCert') {
                        this.translationImg = img;
                    } else if (photoType == 'greementPhoto1') {
                        this.greementPhoto1Img = img;
                    } else if (photoType == 'greementPhoto2') {
                        this.greementPhoto2Img = img;
                    } else if (photoType === 'riskNotice') {
                        this.riskNoticeImg = img;
                    } else if (photoType === 'handPhotoImg') {
                        this.handPhotoImg = img;
                        this.savePicUToRedis();
                    } else if (photoType == '0') {
                        this.picGD = img;
                    } else if (photoType == '4') {
                        this.picSZ = img;
                    } else if (photoType === 'groupPic1') {
                        this.groupPicImg1 = img;
                    } else if (photoType === 'groupPic2') {
                        this.groupPicImg2 = img;
                    } else if (photoType === 'groupPic3') {
                        this.groupPicImg3 = img;
                    } else if (photoType == 'picV') {
                        this.picVImg = img;
                    } else if (photoType == 'passPortFront') {
                        this.passPortFrontImg = img;
                        /* 将芯片信息设置成护照正面 */
                        this.IDCardInfo.avatar = img;
                        if (['08', '36'].includes(this.switchParam.chooseCertType)) {
                            this.IDCardInfo.avatar = this.IDCardInfo.avatar.replace(/data:image\/.*;base64,/, '');
                        }
                    } else if (photoType == 'passPortBack') {
                        this.passPortBackImg = img;
                    } else if (photoType == '29') {
                        this.pic29 = img;
                    } else if (photoType == '30') {
                        this.pic30 = img;
                    } else if (photoType == 'oneCardImg') {
                        this.oneCardImg = img;
                    } else if (photoType == '43') {
                        this.pic43 = img;
                    } else if (photoType == '59') {
                        this.pic59 = img;
                    } else if (photoType == '60') {
                        this.pic60 = img;
                    } else {
                        this.backImg = img;
                    }
                    this.checkCustomerPhoto(img, needFace, photoType);
                }).catch(e => {
                    this.$toast('调用人脸插件异常:' + e);
                });
            }
        },
        checkCustomerPhoto(imageData, needFace, photosType) {
            if (['picDataa1', 'translationCert', 'greementPhoto1', 'greementPhoto2', 'groupPic1', 'groupPic2', 'groupPic3', 'picV'].includes(photosType)) {
                this.dealGuanaiPhoto(imageData, needFace, photosType);
                return;
            }
            if (photosType === 'riskNotice') {
                this.dealRiskNoticePhoto(imageData, needFace, photosType);
                return;
            }
            // if (this.switchParam.isAddCitymark == '1') {
            //     this.customerWaterMark = '仅限办理联通业务使用' + '|' + util.getNowFormatDate() + '|' + this.staffId + '|' + this.cityName;
            // } else if (this.switchParam.isDoubleWatermarkswitch == '1') {
            //     this.customerWaterMark = '仅限办理联通业务使用' + '|' + util.getNowFormatDate() + '  ' + this.staffId;
            // } else if (this.switchParam.isAddStaffIdSwitch == '1') {//宁夏能人经理工号水印开关
            //     this.customerWaterMark = '仅限办理联通业务使用' + '|' + util.getNowFormatDate() + '|' + this.staffId + '|' + this.switchParam.waterMarkStaffId;
            // } else if (this.switchParam.gdClientProofPhotoSwitch == '1' || this.switchParam.gdClientSitePhotoSwitch == '1') {
            //     this.customerWaterMark = '仅限办理联通业务使用' + '|' + util.getNowFormatDate() + '|' + this.staffId;
            // } else if (this.switchParam.isAgent == '1') {
            //     this.customerWaterMark = '仅限办理联通业务使用' + '|' + util.getNowFormatDate() + '|' + this.staffId;
            // } else if (this.switchParam.showPhotoPosition == 'true' && this.switchParam.openFlag == '1' && this.switchParam.levelType == '1') {
            //     this.customerWaterMark = '仅限办理联通业务使用' + '|' + util.getNowFormatDate() + '|' + this.staffId;
            // } else {
            //     this.customerWaterMark = '仅限办理联通业务使用' + '|' + util.getNowFormatDate() + '|' + this.staffId;
            // }
            let img = new Image();
            img.src = imageData;
            img.onload = (async() => {
                let height = img.height;
                let width = img.width;
                let canvas = document.createElement('canvas');
                canvas.width = width;
                canvas.height = height;
                let ctx = canvas.getContext('2d');
                ctx.drawImage(img, 0, 0);
                //最短的边作为基长度
                let basePx = height;
                if (width > height) {
                    basePx = height;
                }
                //字体大小占边长20%
                let baseFontSize = document.documentElement.style.fontSize;
                let bodyFontSize = document.body.style.fontSize;
                let fontSize = basePx / 30;
                if (this.switchParam.isDoubleWatermarkswitch && this.switchParam.isDoubleWatermarkswitch == '1') {
                    fontSize = (width / 30).toFixed(0);
                }
                fontSize = (fontSize * baseFontSize.substring(0, baseFontSize.length - 2) / 37.5).toFixed(0);
                ctx.font = fontSize + 'px microsoft yahei';
                ctx.fillStyle = 'rgba(255,0,0,0.4)';
                //水印长度
                // let length = fontSize * this.customerWaterMark.length;

                if (needFace || (needFace == false && 'face' == photosType && '36' == this.switchParam.chooseCertType)) {//客户照片校验//护照边虽不人脸比对，但是照片正常添加水印
                    // if (this.customerWaterMark.indexOf('|') != -1) {
                    //     let watermarkSplit = this.customerWaterMark.split('|');
                    //     if (watermarkSplit.length >= 2) {
                    //         let max = watermarkSplit[0].length;
                    //         let x = (width - (max + 1) * fontSize) / 2;
                    //         // （西藏）左上+右下角水印
                    //         if (this.switchParam.isDoubleWatermarkswitch && this.switchParam.isDoubleWatermarkswitch == '1') {
                    //             // 水印文字（左上）
                    //             ctx.fillText(watermarkSplit[0], 0, 1 * fontSize);
                    //             ctx.fillText(watermarkSplit[1], 0, 2 * fontSize);
                    //             let x1 = (width - (watermarkSplit[0].length + 2) * fontSize);
                    //             // 第二行主要是英文字母+数字，所以字符数量/2
                    //             let x2 = (width - (watermarkSplit[1].length) / 2 * fontSize);
                    //             // 水印文字（右下）
                    //             ctx.fillText(watermarkSplit[0], x2, height - fontSize * 1.7);
                    //             ctx.fillText(watermarkSplit[1], x2, height - fontSize * 0.7);
                    //         } else {
                    //             ctx.fillText(watermarkSplit[0], x, height - fontSize * 4);
                    //             ctx.fillText(watermarkSplit[1], x, height - fontSize * 3);
                    //             ctx.fillText(watermarkSplit[2], x, height - fontSize * 2);
                    //         }
                    //         // (上海)地市名称水印
                    //         if ((this.switchParam.isAddCitymark && this.switchParam.isAddCitymark == '1')
                    //             || (this.switchParam.isAddStaffIdSwitch && this.switchParam.isAddStaffIdSwitch == '1')) {
                    //             ctx.fillText(watermarkSplit[3], x, height - fontSize * 1);
                    //         }
                    //         // 现场照上方新增水印
                    //         if (this.switchParam.isMoreWaterMark == '1') {
                    //             ctx.fillText(watermarkSplit[0], x, fontSize * 1);
                    //             ctx.fillText(watermarkSplit[1], x, fontSize * 2);
                    //             ctx.fillText(watermarkSplit[2], x, fontSize * 3);
                    //             // （上海）地市名称水印
                    //             if ((this.switchParam.isAddCitymark && this.switchParam.isAddCitymark == '1')
                    //                 || (this.switchParam.isAddStaffIdSwitch && this.switchParam.isAddStaffIdSwitch == '1')) {
                    //                 ctx.fillText(watermarkSplit[3], x, fontSize * 4);
                    //             }
                    //         }
                    //     }
                    // }
                    ctx.restore();
                } else {
                    if ('30' === photosType || '29' === photosType) {
                        // if (this.customerWaterMark.indexOf('|') != -1) {
                        //     let watermarkSplit = this.customerWaterMark.split('|');
                        //     let x = (width - (watermarkSplit[0].length + 1) * fontSize) / 2;
                        //     if ('29' === photosType) {
                        //         // let max = watermarkSplit[0].length;
                        //         // let x = (width - max * fontSize);
                        //         ctx.fillText(watermarkSplit[0], x, height - 2 * fontSize);
                        //         ctx.fillText(watermarkSplit[1], x, height - 3 * fontSize);
                        //         ctx.fillText(watermarkSplit[2], x, height - 4 * fontSize);
                        //     } else if ('30' === photosType) {
                        //         // let max = watermarkSplit[0].length;
                        //         // let x = (width - max * fontSize);
                        //         ctx.fillText(watermarkSplit[0], x, height - 2 * fontSize);
                        //         ctx.fillText(watermarkSplit[1], x, height - 3 * fontSize);
                        //         ctx.fillText(watermarkSplit[2], x, height - 4 * fontSize);
                        //     }
                        // }
                        ctx.restore();
                        let imageDataWithWaterMark = canvas.toDataURL('image/jpeg', 1.0);
                        let flag = true;
                        let quality = 0.99;
                        if (this.compressPhotoByPhotoTypeSwitch.includes(photosType)) {
                            imageDataWithWaterMark = await this.compressPhoto(imageDataWithWaterMark, 140, this.compressTimes);
                            imageDataWithWaterMark = 'data:image/jpeg;base64,' + imageDataWithWaterMark;
                        } else {
                            while (flag) {
                                if (imageDataWithWaterMark.length / 1024 * 3 / 4 > 140) {
                                    quality -= 0.01;
                                    imageDataWithWaterMark = canvas.toDataURL('image/jpeg', quality);
                                } else {
                                    flag = false;
                                }
                            }
                        }
                        // if (imageDataWithWaterMark.length / 1024 * 3 / 4 < 50) {
                        //     this.$toast('图片质量过低，请重新上传！');
                        //     return;
                        // }

                        if (typeof (imageDataWithWaterMark) == 'undefined' || '' === imageDataWithWaterMark || null == imageDataWithWaterMark) {
                            this.$toast('图片压缩失败，请重新上传！');
                            return;
                        } else {
                            //水印图片存到redis
                            let params = {};
                            let certNum = this.IDCardInfo.cardNo;
                            let sessionId = this.sessionId;
                            //默认存储2小时
                            // params.redisLimitTIME =  2 * 60 * 60;
                            params.photoInfoWithWaterMark =  imageDataWithWaterMark;
                            params.cardNo = certNum;
                            if ('29' === photosType) {
                                this.pic29 = imageDataWithWaterMark;
                                // params.redisKey = 'OCR_' + sessionId + '_' + certNum + '_' + '29';
                                params.businessScene = '29';
                                // this.custPicParamInfo.pic29 = 'OCR_' + sessionId + '_' + certNum + '_' + '29';
                            } else if ('30' === photosType) {
                                this.pic30 = imageDataWithWaterMark;
                                // params.redisKey = 'OCR_' + sessionId + '_' + certNum + '_' + '30';
                                params.businessScene = '30';
                                // this.custPicParamInfo.pic30 = 'OCR_' + sessionId + '_' + certNum + '_' + '30';
                            }
                            Common.loadingStart({
                                message: '正在处理照片请稍等……',
                                duration: 0
                            });
                            this.$http.post('redisAndCachController/saveOcrPic', params).then(res => {
                                Common.loadingStop();
                                if (res.respCode == '0000') {
                                    this.custPicParamInfo.pic29 = 'OCR_' + sessionId + '_' + certNum + '_' + '29';
                                    this.custPicParamInfo.pic30 = 'OCR_' + sessionId + '_' + certNum + '_' + '30';
                                    this.$emit('custPicParamInfo', this.custPicParamInfo);
                                    this.$toast('照片上传成功！');
                                }
                            }).catch(e => {
                                Common.loadingStop();
                                this.$toast('照片上传失败！');
                            });
                        }
                        return;
                    }
                    if ('43' === photosType) {
                        if (this.customerWaterMark.indexOf('|') != -1) {
                            let watermarkSplit = this.customerWaterMark.split('|');
                            if ('43' === photosType) {
                                let max = watermarkSplit[0].length;
                                let x = (width - max * fontSize);
                            }
                        }
                        ctx.restore();
                        let imageDataWithWaterMark = canvas.toDataURL('image/jpeg', 1.0);
                        let flag = true;
                        let quality = 0.99;
                        if (this.compressPhotoByPhotoTypeSwitch.includes(photosType)) {
                            imageDataWithWaterMark = await this.compressPhoto(imageDataWithWaterMark, 140, this.compressTimes);
                            imageDataWithWaterMark = 'data:image/jpeg;base64,' + imageDataWithWaterMark;
                        } else {
                            while (flag) {
                                if (imageDataWithWaterMark.length / 1024 * 3 / 4 > 140) {
                                    quality -= 0.01;
                                    imageDataWithWaterMark = canvas.toDataURL('image/jpeg', quality);
                                } else {
                                    flag = false;
                                }
                            }
                        }
                        // if (imageDataWithWaterMark.length / 1024 * 3 / 4 < 50) {
                        //     this.$toast('图片质量过低，请重新上传！');
                        //     return;
                        // }

                        if (typeof (imageDataWithWaterMark) == 'undefined' || '' === imageDataWithWaterMark || null == imageDataWithWaterMark) {
                            this.$toast('图片压缩失败，请重新上传！');
                            return;
                        } else {
                            //水印图片存到redis
                            let params = {};
                            let certNum = this.IDCardInfo.cardNo;
                            let sessionId = this.sessionId;
                            //默认存储2小时
                            // params.redisLimitTIME =  2 * 60 * 60;
                            params.photoInfoWithWaterMark =  imageDataWithWaterMark;
                            params.cardNo = certNum;
                            if ('43' === photosType) {
                                this.pic43 = imageDataWithWaterMark;
                                // params.businessScene = '01';
                                params.redisKey = 'TRIPCODE_' + sessionId + '_' + certNum + '_' + '01';
                                params.redisLimitTIME =  2 * 60 * 60;
                                params.redisValue =  imageDataWithWaterMark;
                            }
                            Common.loadingStart({
                                message: '正在处理照片请稍等……',
                                duration: 0
                            });
                            this.$http.post('redisAndCachController/saveToRedis', params).then(res => {
                                Common.loadingStop();
                                if (res.respCode == '0000') {
                                    this.custPicParamInfo.pic43 = 'TRIPCODE_' + sessionId + '_' + certNum + '_' + '01';
                                    this.$emit('custPicParamInfo', this.custPicParamInfo);
                                    this.$toast('照片上传成功！');
                                }
                            }).catch(e => {
                                Common.loadingStop();
                                this.$toast('照片上传失败！');
                            });
                        }
                        return;
                    }
                }
                let imageData = canvas.toDataURL('image/jpeg', 1.0);
                if (this.compressPhotoByPhotoTypeSwitch.includes(photosType)) {
                    imageData = await this.compressPhoto(imageData, 140, this.compressTimes);
                    imageData = 'data:image/jpeg;base64,' + imageData;
                } else {
                    let flag = true;
                    let quality = 0.99;
                    while (flag) {//进行图片压缩
                        if (imageData.length / 1024 * 3 / 4 > 140) {
                            quality -= 0.01;
                            imageData = canvas.toDataURL('image/jpeg', quality);
                        } else {
                            flag = false;
                        }
                    }
                }
                //原下沉逻辑是点击的不是身份证正反面才能才进行图品质量的判断，这里直接划掉
                // if (imageData.length / 1024 * 3 / 4 < 50) {
                //   this.$toast("图片质量过低，请重新上传！")
                //   return ''
                // }
                if (typeof (imageData) == 'undefined' || '' == imageData || null == imageData) {
                    return;
                } else {
                    //护照边无人脸识别时，无法通过后台保存，只能通过前端保存redis
                    if (needFace == false && 'face' == photosType && '36' == this.switchParam.chooseCertType) {
                        let imageDatastr = imageData.split('data:image/jpeg;base64,')[1];
                        let validateResult = await this.validateBlackAndHZB(imageDatastr);
                        if (!validateResult) {
                            return;
                        }
                        let params = {};
                        let certNum = this.IDCardInfo.cardNo;
                        let sessionId = this.sessionId;
                        //默认存储2小时
                        params.redisLimitTIME = 2 * 60 * 60;
                        params.redisValue = imageData.split('data:image/jpeg;base64,')[1];
                        params.redisKey = 'CUSTOMER_' + sessionId + '_' + certNum + (this.picRandNum || '');
                        this.customerImg = imageData;
                        this.custPicParamInfo.pic1 = 'CUSTOMER_' + sessionId + '_' + certNum + (this.picRandNum || '');
                        this.custPicParamInfo.custSimilarity = '0.00';
                        this.$emit('custPicParamInfo', this.custPicParamInfo);

                        Common.loadingStart({
                            message: '正在处理照片请稍等……',
                            duration: 0
                        });
                        this.$http.post('redisAndCachController/saveToRedis', params).then(res => {
                            Common.loadingStop();
                            if (res.respCode !== '0000') {
                                alertError({
                                    title: '照片上传失败！',
                                    message: res.respMsg,
                                    confirmButtonText: '报告错误'
                                });
                            }
                        }).catch(e => {
                            Common.loadingStop();
                            //保存失败，给与失败提示
                            alertError({
                                title: '照片上传失败！',
                                message: e,
                                confirmButtonText: '报告错误'
                            });
                        });
                        return;
                    }
                    else if (needFace) {
                        let imageDatastr = imageData.split('data:image/jpeg;base64,')[1];
                        let avatar = this.IDCardInfo.avatar.replace(/data:image\/.*;base64,/, '');
                        let keyPrefix = photosType === 'handPhotoImg' ? 'HANDPHOTO' : 'CUSTOMER';
                        let customerPicKey = keyPrefix + this.IDCardInfo.cardNo;
                        this.$toast.loading({
                            message: '正在进行人脸比对...',
                            forbidClick: true,
                            duration: 0
                        });
                        //46807 广东-商品受理-移网 支持港澳台证件开户
                        // let params = '';
                        // if (this.switchParam.chooseCertType == '10' || this.switchParam.chooseCertType == '35') {
                        //     avatar = await this.compressPhoto(avatar, 32);
                        //     params = {
                        //         idType: this.switchParam.chooseCertType == '10' ? '6' : '4',
                        //         certName: this.IDCardInfo.name,
                        //         certNum: this.IDCardInfo.cardNo,
                        //         imageType: photosType === 'handPhotoImg' ? '3' : '2', //客户照传2,手持二维码照传3
                        //         nation: this.IDCardInfo.ethnicity,
                        //         serviceName: 'faceRecognitionService.faceRecognition',
                        //         faceimg: avatar,
                        //         baseimg: null,
                        //         baseimgbest: null,
                        //         picRandNum: this.switchParam.picRandNum
                        //     };
                        // }
                        // else {
                        //     params = {
                        //         certName: this.IDCardInfo.name,
                        //         certNum: this.IDCardInfo.cardNo,
                        //         faceimg: avatar,
                        //         certType: '', //以前的逻辑取的值根本就没取到相应字段的，这里传空了
                        //         birthday: this.IDCardInfo.birthday,
                        //         effectDate: this.IDCardInfo.effectDate,
                        //         baseimg: imageDatastr,
                        //         similarity: '10', //固定传10
                        //         imageType: photosType === 'handPhotoImg' ? '3' : '2', //客户照传2,手持二维码照传3
                        //         serviceName: 'custPicInfoVerify.faceCheckEx',
                        //         sameCustomerAuthFlag: this.sameCustomerAuthFlag,
                        //         faceMixContrast: this.faceMixContrast,
                        //         picRandNum: this.switchParam.picRandNum
                        //     };
                        // }
                        const params = {
                            faceimg: avatar, // 人体照片编码， base64字符串编码必传
                            certName: this.IDCardInfo.name, // 身份证姓名必传
                            certNum: this.IDCardInfo.cardNo, // 身份证编码，长度15或者18位必传
                            baseimg: imageDatastr, // 二代证照片（芯片照）， base64字符串编码必传
                            sessionId: this.sessionId // 必传
                        }
                        const paramsK = {
                            cipherText: aes(true, JSON.stringify(params), this.cryptKey)
                        }
                        this.$http.post('/nx_easy/customChecknx/facecheck', paramsK).then(async res => {
                            Common.loadingStop();
                            this.$toast.clear();
                            res = JSON.parse(aes(false, res.respData, this.cryptKey)).respData;
                            if (typeof res == 'object' && res.respCode == '0000') {
                                this.$toast('人脸识别校验成功！');
                                if (photosType === 'handPhotoImg') { // 手持二维码照
                                    this.handPhotoImg = imageData;
                                    this.custPicParamInfo.picU = customerPicKey;
                                } else { // 客户照
                                    this.customerImg = imageData;
                                    // this.custPicParamInfo.pic1 = customerPicKey;
                                    this.custPicParamInfo.pic1 = imageData;
                                }
                                this.custPicParamInfo.custSimilarity = res.similarity;
                                this.$emit('custPicParamInfo', this.custPicParamInfo);
                            } else {
                                if (photosType === 'handPhotoImg') { // 手持二维码照
                                    this.handPhotoImg = imageData;
                                    this.custPicParamInfo.picU = '';
                                } else { // 客户照
                                    this.customerImg = imageData;
                                    this.custPicParamInfo.pic1 = '';
                                }
                                if (util.isEmpty(res) || util.isEmpty(res.similarity)) {
                                    this.custPicParamInfo.custSimilarity = '0.00';
                                } else {
                                    this.custPicParamInfo.custSimilarity = res.similarity;
                                }
                                this.$emit('custPicParamInfo', this.custPicParamInfo);
                                this.dealImg(photosType === 'handPhotoImg' ? '30' : '20');
                                alertError({
                                    title: '人脸识别校验失败！',
                                    message: res.respMsg,
                                    confirmButtonText: '报告错误'
                                });
                            }
                        }).catch(e => {
                            Common.loadingStop();
                            this.$toast.clear();
                            if (photosType === 'handPhotoImg') { // 手持二维码照
                                this.handPhotoImg = '';
                            } else { // 客户照
                                this.customerImg = '';
                            }
                            this.$toast('人脸识别异常：' + e);
                            this.dealImg(photosType === 'handPhotoImg' ? '30' : '20');
                        });
                    } else {
                        if (needFace == false) {
                            let businessScene = '';
                            let imageDatastr = imageData.split('data:image/jpeg;base64,')[1];
                            let ocrPicKey = '';
                            if (photosType == 'idCardFront' || photosType == 'passPortFront') {
                                businessScene = '01';
                                this.checkNum = this.pic6Num;
                                ocrPicKey = 'OCR' + this.IDCardInfo.cardNo + '01';
                            } else if (photosType == 'idCardBack' || photosType == 'passPortBack') {
                                businessScene = '02';
                                this.checkNum = this.pic7Num;
                                ocrPicKey = 'OCR' + this.IDCardInfo.cardNo + '02';
                            }
                            if (['10', '35'].includes(this.switchParam.chooseCertType) || (photosType == 'passPortFront' || photosType == 'passPortBack')) {
                                //护照不走ocr 对比 跟对比结果无关
                                this.$toast.clear();
                                this.$toast('照片解析成功！');
                                if (this.customerWaterMark.indexOf('|') != -1) {
                                    let watermarkSplit = this.customerWaterMark.split('|');
                                    if (watermarkSplit.length >= 2) {
                                        let max = watermarkSplit[0].length;
                                        //字体大小占边长20%
                                        let fontSize = width / 20;
                                        fontSize = (fontSize * baseFontSize.substring(0, baseFontSize.length - 2) / 37.5).toFixed(0);
                                        ctx.font = fontSize + 'px microsoft yahei';
                                        ctx.fillStyle = 'rgba(255,0,0,0.4)';
                                        ctx.translate(-width / 4, 0);
                                        if (this.switchParam.isDoubleWatermarkswitch && this.switchParam.isDoubleWatermarkswitch == '1') {
                                            fontSize = (width / 35).toFixed(0);
                                            ctx.font = fontSize + 'px microsoft yahei';
                                        } else {
                                            ctx.rotate(-45 * Math.PI / 180);
                                        }
                                        let x2 = (width - max * fontSize) / 18;
                                        let y2 = height - 3 * fontSize;
                                        // // （西藏）左上角水印 + 描边
                                        if (this.switchParam.isDoubleWatermarkswitch && this.switchParam.isDoubleWatermarkswitch == '1') {
                                            // var x1 = (width - (watermarkSplit[0].length + 1) * fontSize);
                                            let xOne = (width - (watermarkSplit[0].length + 2) * fontSize) + 10 * fontSize;
                                            // 第二行主要是英文字母+数字，所以字符数量/2
                                            let xTwo = (width - (watermarkSplit[1].length + 2) / 2 * fontSize) + 10 * fontSize;
                                            // 水印文字（左上）
                                            ctx.fillText(watermarkSplit[0], 8.7 * fontSize, 1 * fontSize);
                                            ctx.fillText(watermarkSplit[1], 8.7 * fontSize, 2 * fontSize);
                                            // 水印文字（右下）
                                            ctx.fillText(watermarkSplit[0], xTwo, height - fontSize * 1.5);
                                            ctx.fillText(watermarkSplit[1], xTwo, height - fontSize * 0.5);
                                        } else {
                                            ctx.fillText(watermarkSplit[2], x2, y2 + fontSize * 3);
                                            ctx.fillText(watermarkSplit[1], x2, y2 + fontSize * 2);
                                            ctx.fillText(watermarkSplit[0], x2, y2 + fontSize * 1);
                                            // (上海)地市名称水印
                                            if ((this.switchParam.isAddCitymark && this.switchParam.isAddCitymark == '1')
                                                || (this.switchParam.isAddStaffIdSwitch && this.switchParam.isAddStaffIdSwitch == '1')) {
                                                ctx.fillText(watermarkSplit[3], x2, y2 + fontSize * 4);
                                            }
                                        }
                                    }
                                }
                                ctx.restore();
                                let imageDataWithWaterMark = canvas.toDataURL('image/jpeg', 1.0);
                                let flag = true;
                                let quality = 0.99;
                                if (this.compressPhotoByPhotoTypeSwitch.includes(photosType)) {
                                    imageDataWithWaterMark = await this.compressPhoto(imageDataWithWaterMark, 140, this.compressTimes);
                                    imageDataWithWaterMark = 'data:image/jpeg;base64,' + imageDataWithWaterMark;
                                } else {
                                    while (flag) {
                                        if (imageDataWithWaterMark.length / 1024 * 3 / 4 > 140) {
                                            quality -= 0.01;
                                            imageDataWithWaterMark = canvas.toDataURL('image/jpeg', quality);
                                        } else {
                                            flag = false;
                                        }
                                    }
                                }
                                if (typeof (imageDataWithWaterMark) == 'undefined' || '' === imageDataWithWaterMark || null == imageDataWithWaterMark) {
                                    this.$toast('图片压缩失败，请重新上传！');
                                    return;
                                } else {
                                    //水印图片存到redis
                                    let params = {};
                                    let certNum = this.IDCardInfo.cardNo;
                                    //默认存储2小时
                                    // params.redisLimitTIME =  2 * 60 * 60;
                                    params.photoInfoWithWaterMark =  imageDataWithWaterMark;
                                    params.cardNo = certNum;
                                    params.businessScene = businessScene + this.switchParam.picRandNum;
                                    Common.loadingStart({
                                        message: '正在处理照片,请稍等……',
                                        duration: 0
                                    });
                                    this.$http.post('redisAndCachController/saveOcrPic', params).then(res => {
                                        Common.loadingStop();
                                        if (res.respCode == '0000') {
                                            if (photosType == 'passPortFront') {
                                                this.custPicParamInfo.pic6 = ocrPicKey;
                                                this.custPicParamInfo.avatar = ocrPicKey;
                                                this.$emit('custPicParamInfo', this.custPicParamInfo);
                                                this.passPortFrontImg = imageDataWithWaterMark;
                                                //现场照片转人工审单标志
                                                //this.backCheckIsToManVerify = "0"
                                            } else if (photosType == 'passPortBack') {
                                                this.custPicParamInfo.pic7 = ocrPicKey;
                                                this.$emit('custPicParamInfo', this.custPicParamInfo);
                                                this.passPortBackImg = imageDataWithWaterMark;
                                                //现场照片转人工审单标志
                                                //this.backCheckIsToManVerify = "0"
                                            } else if (photosType == 'idCardFront') {
                                                // this.custPicParamInfo.pic6 = ocrPicKey;
                                                this.custPicParamInfo.pic6 = imageDataWithWaterMark;
                                                this.$emit('custPicParamInfo', this.custPicParamInfo);
                                                this.frontImg = imageDataWithWaterMark;
                                                //现场照片转人工审单标志
                                                //this.frontCheckIsToManVerify = "0"
                                            } else if (photosType == 'idCardBack') {
                                                // this.custPicParamInfo.pic7 = ocrPicKey;
                                                this.custPicParamInfo.pic7 = imageDataWithWaterMark;
                                                this.$emit('custPicParamInfo', this.custPicParamInfo);
                                                this.backImg = imageDataWithWaterMark;
                                                //现场照片转人工审单标志
                                                //this.backCheckIsToManVerify = "0"
                                            }
                                            this.$toast('照片已上传成功！');
                                        } else {
                                            this.$toast('照片保存失败，请重新操作！');
                                        }
                                    }).catch(e => {
                                        Common.loadingStop();
                                        this.$toast('照片上传失败,请重新操作');
                                    });
                                }
                                return;
                            }
                            // this.$toast.loading({
                            //     message: '正在进行身份证照片解析,请稍等...',
                            //     forbidClick: true,
                            //     duration: 0
                            // });
                            // const params = {
                            //     photoInfo: imageDatastr, // 身份证照片base64字符串编码必传
                            //     sessionId: this.sessionId // 必传
                            // }
                            //todo_clj
                            // if(this.selectedReadOrNotType == '0'){
                            //     params.custName = this.IDCardInfo.name//姓名【新增】
                            //     params.custPsptId = this.IDCardInfo.cardNo//证件号码【新增】
                            //     params.businessScene = photosType == 'idCardFront' ? '01' : '02'//01头像 02国徽【新增】
                            // }
                            // console.log(params,this.selectedReadOrNotType,'提示')
                            // const paramsK = {
                            //     cipherText: aes(true, JSON.stringify(params), this.cryptKey)
                            // }
                            // this.$http.post('/nx_easy/customChecknx/ocrcheck', paramsK).then(async res => {
                                // Common.loadingStop();
                                // res = JSON.parse(aes(false, res.respData, this.cryptKey));
                                // console.log(res,'=========')
                                // if ('0000' == res.respCode) {
                                //     this.$toast.clear();
                                //     this.$toast('照片解析成功！');
                                    //水印 ==============================================start
                                    // 生成水印照片
                                    if (this.customerWaterMark.indexOf('|') != -1) {
                                        let watermarkSplit = this.customerWaterMark.split('|');
                                        if (watermarkSplit.length >= 2) {
                                            let max = watermarkSplit[0].length;
                                            //字体大小占边长20%
                                            let fontSize = width / 20;
                                            fontSize = (fontSize * baseFontSize.substring(0, baseFontSize.length - 2) / 37.5).toFixed(0);
                                            ctx.font = fontSize + 'px microsoft yahei';
                                            ctx.fillStyle = 'rgba(255,0,0,0.4)';
                                            ctx.translate(-width / 4, 0);
                                            if (this.switchParam.isDoubleWatermarkswitch && this.switchParam.isDoubleWatermarkswitch == '1') {
                                                fontSize = (width / 35).toFixed(0);
                                                ctx.font = fontSize + 'px microsoft yahei';
                                            } else {
                                                ctx.rotate(-45 * Math.PI / 180);
                                            }
                                            let x2 = (width - max * fontSize) / 18;
                                            let y2 = height - 3 * fontSize;
                                            // // （西藏）左上角水印 + 描边
                                            if (this.switchParam.isDoubleWatermarkswitch && this.switchParam.isDoubleWatermarkswitch == '1') {
                                                // var x1 = (width - (watermarkSplit[0].length + 1) * fontSize);
                                                let xOne = (width - (watermarkSplit[0].length + 2) * fontSize) + 10 * fontSize;
                                                // 第二行主要是英文字母+数字，所以字符数量/2
                                                let xTwo = (width - (watermarkSplit[1].length + 2) / 2 * fontSize) + 10 * fontSize;
                                                // 水印文字（左上）
                                                ctx.fillText(watermarkSplit[0], 8.7 * fontSize, 1 * fontSize);
                                                ctx.fillText(watermarkSplit[1], 8.7 * fontSize, 2 * fontSize);
                                                // 水印文字（右下）
                                                ctx.fillText(watermarkSplit[0], xTwo, height - fontSize * 1.5);
                                                ctx.fillText(watermarkSplit[1], xTwo, height - fontSize * 0.5);
                                            } else {
                                                ctx.fillText(watermarkSplit[2], x2, y2 + fontSize * 3);
                                                ctx.fillText(watermarkSplit[1], x2, y2 + fontSize * 2);
                                                ctx.fillText(watermarkSplit[0], x2, y2 + fontSize * 1);
                                                // (上海)地市名称水印
                                                if ((this.switchParam.isAddCitymark && this.switchParam.isAddCitymark == '1')
                                                        || (this.switchParam.isAddStaffIdSwitch && this.switchParam.isAddStaffIdSwitch == '1')) {
                                                    ctx.fillText(watermarkSplit[3], x2, y2 + fontSize * 4);
                                                }
                                            }
                                        }
                                    }
                                    ctx.restore();
                                    let imageDataWithWaterMark = canvas.toDataURL('image/jpeg', 1.0);
                                    let flag = true;
                                    let quality = 0.99;
                                    if (this.compressPhotoByPhotoTypeSwitch.includes(photosType)) {
                                        imageDataWithWaterMark = await this.compressPhoto(imageDataWithWaterMark, 140, this.compressTimes);
                                        imageDataWithWaterMark = 'data:image/jpeg;base64,' + imageDataWithWaterMark;
                                    } else {
                                        while (flag) {
                                            if (imageDataWithWaterMark.length / 1024 * 3 / 4 > 140) {
                                                quality -= 0.01;
                                                imageDataWithWaterMark = canvas.toDataURL('image/jpeg', quality);
                                            } else {
                                                flag = false;
                                            }
                                        }
                                    }
                                    if (photosType == 'idCardFront') {
                                        this.custPicParamInfo.pic6 = imageDataWithWaterMark;
                                        this.$emit('custPicParamInfo', this.custPicParamInfo);
                                        this.frontImg = imageDataWithWaterMark;
                                    } else if (photosType == 'idCardBack') {
                                        this.custPicParamInfo.pic7 = imageDataWithWaterMark;
                                        this.$emit('custPicParamInfo', this.custPicParamInfo);
                                        this.backImg = imageDataWithWaterMark;
                                    }
                                    // else if (photosType == 'passPortFront') {
                                    //     this.custPicParamInfo.pic6 = ocrPicKey;
                                    //     this.custPicParamInfo.avatar = ocrPicKey;
                                    //     this.$emit('custPicParamInfo', this.custPicParamInfo);
                                    //     this.passPortFrontImg = imageDataWithWaterMark;
                                    //     //现场照片转人工审单标志
                                    //     //this.backCheckIsToManVerify = "0"
                                    // } else if (photosType == 'passPortBack') {
                                    //     this.custPicParamInfo.pic7 = ocrPicKey;
                                    //     this.$emit('custPicParamInfo', this.custPicParamInfo);
                                    //     this.passPortBackImg = imageDataWithWaterMark;
                                    //     //现场照片转人工审单标志
                                    //     //this.backCheckIsToManVerify = "0"
                                    // }
                                    //存放水印照片接口
                                    // Common.loadingStart({
                                    //     message: '正在进行身份证照片上传,请稍等...',
                                    //     duration: 0
                                    // });
                                    // this.$http.post('redisAndCachController/saveOcrPic', {
                                    //     cardNo: this.IDCardInfo.cardNo,
                                    //     businessScene: businessScene + this.switchParam.picRandNum,
                                    //     photoInfoWithWaterMark: imageDataWithWaterMark
                                    // }).then(res => {
                                    //     Common.loadingStop();
                                    //     if (res.respCode == '0000') {
                                    //         this.$toast.clear();
                                    //         this.$toast('证件照上传成功！');
                                    //         //水印 =================================================end
                                    //         if (photosType == 'idCardFront') {
                                    //             // this.custPicParamInfo.pic6 = ocrPicKey;
                                    //             this.custPicParamInfo.pic6 = imageDataWithWaterMark;
                                    //             this.$emit('custPicParamInfo', this.custPicParamInfo);
                                    //             this.frontImg = imageDataWithWaterMark;
                                    //             //现场照片转人工审单标志
                                    //             //this.frontCheckIsToManVerify = "0"
                                    //         } else if (photosType == 'idCardBack') {
                                    //             // this.custPicParamInfo.pic7 = ocrPicKey;
                                    //             this.custPicParamInfo.pic7 = imageDataWithWaterMark;
                                    //             this.$emit('custPicParamInfo', this.custPicParamInfo);
                                    //             this.backImg = imageDataWithWaterMark;
                                    //             //现场照片转人工审单标志
                                    //             //this.backCheckIsToManVerify = "0"
                                    //         } else if (photosType == 'passPortFront') {
                                    //             this.custPicParamInfo.pic6 = ocrPicKey;
                                    //             this.custPicParamInfo.avatar = ocrPicKey;
                                    //             this.$emit('custPicParamInfo', this.custPicParamInfo);
                                    //             this.passPortFrontImg = imageDataWithWaterMark;
                                    //             //现场照片转人工审单标志
                                    //             //this.backCheckIsToManVerify = "0"
                                    //         } else if (photosType == 'passPortBack') {
                                    //             this.custPicParamInfo.pic7 = ocrPicKey;
                                    //             this.$emit('custPicParamInfo', this.custPicParamInfo);
                                    //             this.passPortBackImg = imageDataWithWaterMark;
                                    //             //现场照片转人工审单标志
                                    //             //this.backCheckIsToManVerify = "0"
                                    //         }
                                    //     } else {
                                    //         this.$toast('身份证照片保存失败，请重新操作！');
                                    //     }
                                    // });
                                // } else {
                                //     this.$toast.clear();
                                //     if (this.switchParam.forcedThroughSwitch == '1') {
                                //         if (photosType == 'idCardFront') {
                                //             alertError({
                                //                 title: '请拍摄身份证正面！',
                                //                 message: '身份证正面拍摄失败，请重试！',
                                //                 confirmButtonText: '关闭'
                                //             });
                                //             this.custPicParamInfo.pic6 = '';
                                //             this.frontImg = '';
                                //         } else if (photosType == 'idCardBack') {
                                //             alertError({
                                //                 title: '请拍摄身份证反面！',
                                //                 message: '身份证反面拍摄失败，请重试！',
                                //                 confirmButtonText: '关闭'
                                //             });
                                //             this.custPicParamInfo.pic7 = '';
                                //             this.backImg = '';
                                //         }
                                //     } else {
                                //         if ('0001' == res.respCode && this.checkNum >= 3) {
                                //             //水印 ==============================================start
                                //             // 生成水印照片
                                //             if (this.customerWaterMark.indexOf('|') != -1) {
                                //                 let watermarkSplit = this.customerWaterMark.split('|');
                                //                 if (watermarkSplit.length >= 2) {
                                //                     let max = watermarkSplit[0].length;
                                //                     //字体大小占边长20%
                                //                     let fontSize = width / 20;
                                //                     fontSize = (fontSize * baseFontSize.substring(0, baseFontSize.length - 2) / 37.5).toFixed(0);
                                //                     ctx.font = fontSize + 'px microsoft yahei';
                                //                     ctx.fillStyle = 'rgba(255,0,0,0.4)';
                                //                     ctx.translate(-width / 4, 0);
                                //                     if (this.switchParam.isDoubleWatermarkswitch && this.switchParam.isDoubleWatermarkswitch == '1') {
                                //                         fontSize = (width / 35).toFixed(0);
                                //                         ctx.font = fontSize + 'px microsoft yahei';
                                //                     } else {
                                //                         ctx.rotate(-45 * Math.PI / 180);
                                //                     }
                                //                     let x2 = (width - max * fontSize) / 18;
                                //                     let y2 = height - 3 * fontSize;
                                //                     // // （西藏）左上角水印 + 描边
                                //                     if (this.switchParam.isDoubleWatermarkswitch && this.switchParam.isDoubleWatermarkswitch == '1') {
                                //                         // var x1 = (width - (watermarkSplit[0].length + 1) * fontSize);
                                //                         let xOne = (width - (watermarkSplit[0].length + 2) * fontSize) + 10 * fontSize;
                                //                         // 第二行主要是英文字母+数字，所以字符数量/2
                                //                         let xTwo = (width - (watermarkSplit[1].length + 2) / 2 * fontSize) + 10 * fontSize;
                                //                         // 水印文字（左上）
                                //                         ctx.fillText(watermarkSplit[0], 8.7 * fontSize, 1 * fontSize);
                                //                         ctx.fillText(watermarkSplit[1], 8.7 * fontSize, 2 * fontSize);
                                //                         // 水印文字（右下）
                                //                         ctx.fillText(watermarkSplit[0], xTwo, height - fontSize * 1.5);
                                //                         ctx.fillText(watermarkSplit[1], xTwo, height - fontSize * 0.5);
                                //                     } else {
                                //                         ctx.fillText(watermarkSplit[2], x2, y2 + fontSize * 3);
                                //                         ctx.fillText(watermarkSplit[1], x2, y2 + fontSize * 2);
                                //                         ctx.fillText(watermarkSplit[0], x2, y2 + fontSize * 1);
                                //                         // (上海)地市名称水印
                                //                         if ((this.switchParam.isAddCitymark && this.switchParam.isAddCitymark == '1')
                                //                                 || (this.switchParam.isAddStaffIdSwitch && this.switchParam.isAddStaffIdSwitch == '1')) {
                                //                             ctx.fillText(watermarkSplit[3], x2, y2 + fontSize * 4);
                                //                         }
                                //                     }
                                //                 }
                                //             }
                                //             ctx.restore();
                                //             let imageDataWithWaterMark = canvas.toDataURL('image/jpeg', 1.0);
                                //             let flag = true;
                                //             let quality = 0.99;
                                //             if (this.compressPhotoByPhotoTypeSwitch.includes(photosType)) {
                                //                 imageDataWithWaterMark = await this.compressPhoto(imageDataWithWaterMark, 140, this.compressTimes);
                                //                 imageDataWithWaterMark = 'data:image/jpeg;base64,' + imageDataWithWaterMark;
                                //             } else {
                                //                 while (flag) {
                                //                     if (imageDataWithWaterMark.length / 1024 * 3 / 4 > 140) {
                                //                         quality -= 0.01;
                                //                         imageDataWithWaterMark = canvas.toDataURL('image/jpeg', quality);
                                //                     } else {
                                //                         flag = false;
                                //                     }
                                //                 }
                                //             }
                                //             if (photosType == 'idCardFront') {
                                //                 this.custPicParamInfo.pic6 = imageDataWithWaterMark;
                                //                 this.$emit('custPicParamInfo', this.custPicParamInfo);
                                //                 this.frontImg = imageDataWithWaterMark;
                                //             } else if (photosType == 'idCardBack') {
                                //                 this.custPicParamInfo.pic7 = imageDataWithWaterMark;
                                //                 this.$emit('custPicParamInfo', this.custPicParamInfo);
                                //                 this.backImg = imageDataWithWaterMark;
                                //             } 
                                //             // else if (photosType == 'passPortFront') {
                                //             //     this.custPicParamInfo.pic6 = ocrPicKey;
                                //             //     this.custPicParamInfo.avatar = ocrPicKey;
                                //             //     this.$emit('custPicParamInfo', this.custPicParamInfo);
                                //             //     this.passPortFrontImg = imageDataWithWaterMark;
                                //             //     //现场照片转人工审单标志
                                //             //     //this.backCheckIsToManVerify = "0"
                                //             // } else if (photosType == 'passPortBack') {
                                //             //     this.custPicParamInfo.pic7 = ocrPicKey;
                                //             //     this.$emit('custPicParamInfo', this.custPicParamInfo);
                                //             //     this.passPortBackImg = imageDataWithWaterMark;
                                //             //     //现场照片转人工审单标志
                                //             //     //this.backCheckIsToManVerify = "0"
                                //             // }
                                //             //存放水印照片接口
                                //             // Common.loadingStart({
                                //             //     message: '正在进行身份证照片上传,请稍等...',
                                //             //     duration: 0
                                //             // });
                                //             // this.$http.post('redisAndCachController/saveOcrPic', {
                                //             //     cardNo: this.IDCardInfo.cardNo,
                                //             //     businessScene: businessScene + this.switchParam.picRandNum,
                                //             //     photoInfoWithWaterMark: imageDataWithWaterMark
                                //             // }).then(res => {
                                //             //     Common.loadingStop();
                                //             //     if (res.respCode == '0000') {
                                //             //         this.$toast.clear();
                                //             //         this.$toast('证件照上传成功！');
                                //             //         //水印 =================================================end
                                //             //         if (photosType == 'idCardFront') {
                                //             //             // this.custPicParamInfo.pic6 = ocrPicKey;
                                //             //             this.custPicParamInfo.pic6 = imageDataWithWaterMark;
                                //             //             this.$emit('custPicParamInfo', this.custPicParamInfo);
                                //             //             this.frontImg = imageDataWithWaterMark;
                                //             //             //现场照片转人工审单标志
                                //             //             //this.frontCheckIsToManVerify = "0"
                                //             //         } else if (photosType == 'idCardBack') {
                                //             //             // this.custPicParamInfo.pic7 = ocrPicKey;
                                //             //             this.custPicParamInfo.pic7 = imageDataWithWaterMark;
                                //             //             this.$emit('custPicParamInfo', this.custPicParamInfo);
                                //             //             this.backImg = imageDataWithWaterMark;
                                //             //             //现场照片转人工审单标志
                                //             //             //this.backCheckIsToManVerify = "0"
                                //             //         } else if (photosType == 'passPortFront') {
                                //             //             this.custPicParamInfo.pic6 = ocrPicKey;
                                //             //             this.custPicParamInfo.avatar = ocrPicKey;
                                //             //             this.$emit('custPicParamInfo', this.custPicParamInfo);
                                //             //             this.passPortFrontImg = imageDataWithWaterMark;
                                //             //             //现场照片转人工审单标志
                                //             //             //this.backCheckIsToManVerify = "0"
                                //             //         } else if (photosType == 'passPortBack') {
                                //             //             this.custPicParamInfo.pic7 = ocrPicKey;
                                //             //             this.$emit('custPicParamInfo', this.custPicParamInfo);
                                //             //             this.passPortBackImg = imageDataWithWaterMark;
                                //             //             //现场照片转人工审单标志
                                //             //             //this.backCheckIsToManVerify = "0"
                                //             //         }
                                //             //     } else {
                                //             //         this.$toast('身份证照片保存失败，请重新操作！');
                                //             //     }
                                //             // });
                                //         } else {
                                //             if (photosType == 'idCardFront') {
                                //                 this.custPicParamInfo.pic6 = '';
                                //                 this.frontImg = '';
                                //                 alertError({
                                //                     title: '请拍摄身份证正面！',
                                //                     message: res.respMsg,
                                //                     confirmButtonText: '关闭'
                                //                 });
                                //             } else if (photosType == 'idCardBack') {
                                //                 this.custPicParamInfo.pic7 = '';
                                //                 this.backImg = '';
                                //                 alertError({
                                //                     title: '请拍摄身份证反面！',
                                //                     message: res.respMsg,
                                //                     confirmButtonText: '关闭'
                                //                 });
                                //             }
                                //         }
                                //     }
                                // }
                            // }).catch(e => {
                            //     Common.loadingStop();
                            //     this.$toast.clear();
                            //     if (photosType == 'idCardFront') {
                            //         this.custPicParamInfo.pic6 = '';
                            //         this.frontImg = '';
                            //         alertError({
                            //             title: '请拍摄身份证正面！',
                            //             message: e,
                            //             confirmButtonText: '关闭'
                            //         });
                            //     } else if (photosType == 'idCardBack') {
                            //         this.custPicParamInfo.pic7 = '';
                            //         this.backImg = '';
                            //         alertError({
                            //             title: '请拍摄身份证反面！',
                            //             message: e,
                            //             confirmButtonText: '关闭'
                            //         });
                            //     }
                            // });
                        }
                    }
                }
                return;
            });
        },
        async validateBlackAndHZB(imageData) {
            let result = true;
            return result;
        },
        dealGuanaiPhoto(imageData, needFace, photosType) {
            if (this.switchParam.isAddCitymark == '1') {
                this.customerWaterMark = '仅限办理联通业务使用' + '|' + util.getNowFormatDate() + '|' + this.staffId + '|' + this.cityName;
            } else if (this.switchParam.isDoubleWatermarkswitch == '1') {
                this.customerWaterMark = '仅限办理联通业务使用' + '|' + util.getNowFormatDate() + '  ' + this.staffId;
            } else if (this.switchParam.isAddStaffIdSwitch == '1') {//宁夏能人经理工号水印开关
                this.customerWaterMark = '仅限办理联通业务使用' + '|' + util.getNowFormatDate() + '|' + this.staffId + '|' + this.switchParam.waterMarkStaffId;
            } else {
                this.customerWaterMark = '仅限办理联通业务使用' + '|' + util.getNowFormatDate() + '|' + this.staffId;
            }
            let img = new Image();
            img.src = imageData;
            img.onload = (async() => {
                let height = img.height;
                let width = img.width;
                let canvas = document.createElement('canvas');
                canvas.width = width;
                canvas.height = height;
                let ctx = canvas.getContext('2d');
                ctx.drawImage(img, 0, 0);
                //最短的边作为基长度
                let basePx = height;
                if (width > height) {
                    basePx = height;
                }
                //字体大小占边长20%
                let baseFontSize = document.documentElement.style.fontSize;
                let bodyFontSize = document.body.style.fontSize;
                let fontSize = basePx / 30;
                if (this.switchParam.isDoubleWatermarkswitch && this.switchParam.isDoubleWatermarkswitch == '1') {
                    fontSize = (width / 30).toFixed(0);
                }
                fontSize = (fontSize * baseFontSize.substring(0, baseFontSize.length - 2) / 37.5).toFixed(0);
                ctx.font = fontSize + 'px microsoft yahei';
                ctx.fillStyle = 'rgba(255,0,0,0.4)';
                //水印长度
                let length = fontSize * this.customerWaterMark.length;
                if (this.customerWaterMark.indexOf('|') != -1) {
                    let watermarkSplit = this.customerWaterMark.split('|');
                    if (watermarkSplit.length >= 2) {
                        let max = watermarkSplit[0].length;
                        let x = (width - (max + 1) * fontSize) / 2;
                        // （西藏）左上+右下角水印
                        if (this.switchParam.isDoubleWatermarkswitch && this.switchParam.isDoubleWatermarkswitch == '1') {
                            // 水印文字（左上）
                            ctx.fillText(watermarkSplit[0], 0, 1 * fontSize);
                            ctx.fillText(watermarkSplit[1], 0, 2 * fontSize);
                            let x1 = (width - (watermarkSplit[0].length + 2) * fontSize);
                            // 第二行主要是英文字母+数字，所以字符数量/2
                            let x2 = (width - (watermarkSplit[1].length) / 2 * fontSize);
                            // 水印文字（右下）
                            ctx.fillText(watermarkSplit[0], x2, height - fontSize * 1.7);
                            ctx.fillText(watermarkSplit[1], x2, height - fontSize * 0.7);
                        } else {
                            ctx.fillText(watermarkSplit[0], x, height - fontSize * 4);
                            ctx.fillText(watermarkSplit[1], x, height - fontSize * 3);
                            ctx.fillText(watermarkSplit[2], x, height - fontSize * 2);
                        }
                        // (上海)地市名称水印
                        if ((this.switchParam.isAddCitymark && this.switchParam.isAddCitymark == '1')
                            || (this.switchParam.isAddStaffIdSwitch && this.switchParam.isAddStaffIdSwitch == '1')) {
                            ctx.fillText(watermarkSplit[3], x, height - fontSize * 1);
                        }
                        // 现场照上方新增水印
                        if (this.switchParam.isMoreWaterMark == '1') {
                            ctx.fillText(watermarkSplit[0], x, fontSize * 1);
                            ctx.fillText(watermarkSplit[1], x, fontSize * 2);
                            ctx.fillText(watermarkSplit[2], x, fontSize * 3);
                            // （上海）地市名称水印
                            if ((this.switchParam.isAddCitymark && this.switchParam.isAddCitymark == '1')
                                || (this.switchParam.isAddStaffIdSwitch && this.switchParam.isAddStaffIdSwitch == '1')) {
                                ctx.fillText(watermarkSplit[3], x, fontSize * 4);
                            }
                        }
                    }
                }
                ctx.restore();
                let imageDataWithWaterMark = canvas.toDataURL('image/jpeg', 1.0);
                let flag = true;
                let quality = 0.99;
                if (this.compressPhotoByPhotoTypeSwitch.includes(photosType)) {
                    imageDataWithWaterMark = await this.compressPhoto(imageDataWithWaterMark, 140, this.compressTimes);
                    imageDataWithWaterMark = 'data:image/jpeg;base64,' + imageDataWithWaterMark;
                } else {
                    while (flag) {//进行图片压缩
                        if (imageDataWithWaterMark.length / 1024 * 3 / 4 > 140) {
                            quality -= 0.01;
                            imageDataWithWaterMark = canvas.toDataURL('image/jpeg', quality);
                        } else {
                            flag = false;
                        }
                    }
                }
                // if (imageData.length / 1024 * 3 / 4 < 50) {
                //     this.$toast('图片质量过低，请重新上传！');
                //     return '';
                // }
                if (typeof (imageData) == 'undefined' || '' == imageData || null == imageData) {
                    return;
                } else {
                    let businessScene = '';
                    let imageDatastr = imageData.split('data:image/jpeg;base64,')[1];
                    let ocrPicKey = '';
                    if (['picDataa1', 'greementPhoto1', 'greementPhoto2', 'picV'].includes(photosType)) {
                        let businessSceneMap = {
                            picDataa1: '04',
                            greementPhoto1: '05',
                            greementPhoto2: '06',
                            picV: '03'
                        };
                        businessScene = businessSceneMap[photosType] + this.switchParam.picRandNum;
                        ocrPicKey = 'OCR' + this.IDCardInfo.cardNo + businessSceneMap[photosType];
                    }

                    if (['translationCert'].includes(photosType)) {
                        businessScene = 'HZB_03' + this.switchParam.picRandNum;
                        ocrPicKey = 'OCR' + this.IDCardInfo.cardNo + businessScene;
                    }

                    if (['groupPic1', 'groupPic2', 'groupPic3'].includes(photosType)) {
                        let businessSceneMap = {
                            groupPic1: 'BB',
                            groupPic2: 'CC',
                            groupPic3: 'DD'
                        };
                        businessScene = businessSceneMap[photosType];
                        ocrPicKey = 'OCR' + this.IDCardInfo.cardNo + businessSceneMap[photosType];
                    }
                    //存放照片接口
                    Common.loadingStart({
                        message: '正在进行照片上传,请稍等...',
                        duration: 0
                    });
                    this.$http.post('redisAndCachController/saveOcrPic', {
                        cardNo: this.IDCardInfo.cardNo,
                        businessScene: businessScene,
                        photoInfoWithWaterMark: imageDataWithWaterMark
                    }).then(res => {
                        if (res.respCode == '0000') {
                            this.$toast('照片上传成功！');
                            Common.loadingStop();
                            this.$toast.clear();
                            if (['picDataa1', 'translationCert', 'greementPhoto1', 'greementPhoto2', 'picV'].includes(photosType)) {
                                this.custPicParamInfo[photosType] = ocrPicKey;
                                this.$emit('custPicParamInfo', this.custPicParamInfo);
                            }
                            if (['groupPic1', 'groupPic2', 'groupPic3'].includes(photosType)) {
                                this.custPicParamInfo[photosType] = ocrPicKey;
                                this.$emit('custPicParamInfo', this.custPicParamInfo);
                            }
                            if (photosType == 'picV') {
                                this.picVImg = imageDataWithWaterMark;
                            } else {
                                this.picDataa1Img = imageDataWithWaterMark;
                            }
                        }
                    });
                }
                return;
            });
        },
        dealRiskNoticePhoto(imageData, needFace, photosType) {
            if (this.switchParam.isAddCitymark == '1') {
                this.customerWaterMark = '仅限办理联通业务使用' + '|' + util.getNowFormatDate() + '|' + this.staffId + '|' + this.cityName;
            } else if (this.switchParam.isDoubleWatermarkswitch == '1') {
                this.customerWaterMark = '仅限办理联通业务使用' + '|' + util.getNowFormatDate() + '  ' + this.staffId;
            } else if (this.switchParam.isAddStaffIdSwitch == '1') {//宁夏能人经理工号水印开关
                this.customerWaterMark = '仅限办理联通业务使用' + '|' + util.getNowFormatDate() + '|' + this.staffId + '|' + this.switchParam.waterMarkStaffId;
            } else {
                this.customerWaterMark = '仅限办理联通业务使用' + '|' + util.getNowFormatDate() + '|' + this.staffId;
            }
            let img = new Image();
            img.src = imageData;
            img.onload = (async() => {
                let height = img.height;
                let width = img.width;
                let canvas = document.createElement('canvas');
                canvas.width = width;
                canvas.height = height;
                let ctx = canvas.getContext('2d');
                ctx.drawImage(img, 0, 0);
                //最短的边作为基长度
                let basePx = height;
                if (width > height) {
                    basePx = height;
                }
                //字体大小占边长20%
                let baseFontSize = document.documentElement.style.fontSize;
                let bodyFontSize = document.body.style.fontSize;
                let fontSize = basePx / 30;
                if (this.switchParam.isDoubleWatermarkswitch && this.switchParam.isDoubleWatermarkswitch == '1') {
                    fontSize = (width / 30).toFixed(0);
                }
                fontSize = (fontSize * baseFontSize.substring(0, baseFontSize.length - 2) / 37.5).toFixed(0);
                ctx.font = fontSize + 'px microsoft yahei';
                ctx.fillStyle = 'rgba(255,0,0,0.4)';
                //水印长度
                let length = fontSize * this.customerWaterMark.length;
                if (this.customerWaterMark.indexOf('|') != -1) {
                    let watermarkSplit = this.customerWaterMark.split('|');
                    if (watermarkSplit.length >= 2) {
                        let max = watermarkSplit[0].length;
                        let x = (width - (max + 1) * fontSize) / 2;
                        // （西藏）左上+右下角水印
                        if (this.switchParam.isDoubleWatermarkswitch && this.switchParam.isDoubleWatermarkswitch == '1') {
                            // 水印文字（左上）
                            ctx.fillText(watermarkSplit[0], 0, 1 * fontSize);
                            ctx.fillText(watermarkSplit[1], 0, 2 * fontSize);
                            let x1 = (width - (watermarkSplit[0].length + 2) * fontSize);
                            // 第二行主要是英文字母+数字，所以字符数量/2
                            let x2 = (width - (watermarkSplit[1].length) / 2 * fontSize);
                            // 水印文字（右下）
                            ctx.fillText(watermarkSplit[0], x2, height - fontSize * 1.7);
                            ctx.fillText(watermarkSplit[1], x2, height - fontSize * 0.7);
                        } else {
                            ctx.fillText(watermarkSplit[0], x, height - fontSize * 4);
                            ctx.fillText(watermarkSplit[1], x, height - fontSize * 3);
                            ctx.fillText(watermarkSplit[2], x, height - fontSize * 2);
                        }
                        // (上海)地市名称水印
                        if ((this.switchParam.isAddCitymark && this.switchParam.isAddCitymark == '1')
                                || (this.switchParam.isAddStaffIdSwitch && this.switchParam.isAddStaffIdSwitch == '1')) {
                            ctx.fillText(watermarkSplit[3], x, height - fontSize * 1);
                        }
                        // 现场照上方新增水印
                        if (this.switchParam.isMoreWaterMark == '1') {
                            ctx.fillText(watermarkSplit[0], x, fontSize * 1);
                            ctx.fillText(watermarkSplit[1], x, fontSize * 2);
                            ctx.fillText(watermarkSplit[2], x, fontSize * 3);
                            // （上海）地市名称水印
                            if ((this.switchParam.isAddCitymark && this.switchParam.isAddCitymark == '1')
                                    || (this.switchParam.isAddStaffIdSwitch && this.switchParam.isAddStaffIdSwitch == '1')) {
                                ctx.fillText(watermarkSplit[3], x, fontSize * 4);
                            }
                        }
                    }
                }
                ctx.restore();
                let imageDataWithWaterMark = canvas.toDataURL('image/jpeg', 1.0);
                let flag = true;
                let quality = 0.99;
                if (this.compressPhotoByPhotoTypeSwitch.includes(photosType)) {
                    imageDataWithWaterMark = await this.compressPhoto(imageDataWithWaterMark, 140, this.compressTimes);
                    imageDataWithWaterMark = 'data:image/jpeg;base64,' + imageDataWithWaterMark;
                } else {
                    while (flag) {//进行图片压缩
                        if (imageDataWithWaterMark.length / 1024 * 3 / 4 > 140) {
                            quality -= 0.01;
                            imageDataWithWaterMark = canvas.toDataURL('image/jpeg', quality);
                        } else {
                            flag = false;
                        }
                    }
                }
                // if (imageData.length / 1024 * 3 / 4 < 50) {
                //     this.$toast('图片质量过低，请重新上传！');
                //     return '';
                // }
                if (typeof (imageData) == 'undefined' || '' == imageData || null == imageData) {
                    return;
                } else {
                    let businessScene = '07';
                    let ocrPicKey = 'OCR' + this.IDCardInfo.cardNo + '07';
                    //存放照片接口
                    Common.loadingStart({
                        message: '正在进行照片上传,请稍等...',
                        duration: 0
                    });
                    this.$http.post('redisAndCachController/saveOcrPic', {
                        cardNo: this.IDCardInfo.cardNo,
                        businessScene: businessScene + this.switchParam.picRandNum,
                        photoInfoWithWaterMark: imageDataWithWaterMark
                    }).then(res => {
                        if (res.respCode == '0000') {
                            this.$toast('照片上传成功！');
                            Common.loadingStop();
                            this.$toast.clear();
                            this.custPicParamInfo.picR = ocrPicKey;
                            this.$emit('custPicParamInfo', this.custPicParamInfo);
                            this.riskNoticeImg = imageDataWithWaterMark;
                        }
                    });
                }
                return;
            });
        },
        //中台介入 人脸识别失败
        enterCentralAudit(transId) {
            let params = {
                realFaceImg: this.switchParam.facePicFlag == 'true' ? this.customerImg : this.realFaceImg,
                customerImg: this.switchParam.custPicFlag == 'true' ? this.realFaceImg : this.customerImg,
                cardNo: this.IDCardInfo.cardNo,
                transId: transId,
                sceneType: this.switchParam.sceneType,
                facePicFlag: this.switchParam.facePicFlag,
                custPicFlag: this.switchParam.custPicFlag,
                cardInfo: this.IDCardInfo
            };
            this.$router.push({
                path: '/centralInfoCollected',
                query: params
            });
        },

        sameCustomerAuthentication(type, provinceCode) {
            if (this.switchParam.sameCustomerSwitch === '0' || this.needRealNameFlag) {
                return;
            }
            let mixParams = {
                keyCode: 'faceMixContrast',
                provinceCode: provinceCode
            };
            this.$http.post('/paramController/getSwitch', mixParams).then(res=>{
                this.faceMixContrast = res.respData;
                if (type == '10' && (this.custPicParamInfo.faceSimilarity < this.faceMixContrast)) {
                    return;
                }
                if (type == '20' && (this.custPicParamInfo.custSimilarity < this.faceMixContrast)) {
                    return;
                }
                let params = {
                    psptId: this.IDCardInfo.cardNo
                };
                Dialog.confirm({
                    title: '同客户验证',
                    message: '人脸识别失败，是否进行同客户验证？',
                    confirmButtonText: '是',
                    confirmButtonColor: '#0081ff'
                    //cancelButtonText: "否"
                }).then(() => {
                    this.$router.push({
                        path: '/sameCustomerAuthentication',
                        query: params
                    });
                    this.sameCustomerAuthFlag = true;
                }).catch(() => {

                });
            });
        },

        // 图片错误处理,判断条件都为(this.switchParam.sceneType == "01")现场开户，所以这个错误处理应该是为现场开户做的处理
        dealImg(type, error) { // type: '0'活体照片识别失败，‘1’客户照片识别失败
            if (error) {
                this.$toast('照片错误处理提示：' + error);
            }
            // this.switchParam.ZTCheckFlag = 'true';
            //是从现场开户来的,需要中台稽核，
            if (this.switchParam.sceneType == '01' && this.switchParam.ZTCheckFlag == 'true' && (type == '10' || type == '20' || type == '30')) { // 照片识别失败
                let realFaceImg = type == '10' ? this.realFaceImg : '';
                let customerImg = type == '20' ? this.customerImg : '';
                let handPhotoImg = type == '30' ? this.handPhotoImg : '';
                Common.loadingStart({
                    message: '请稍等...',
                    duration: 0
                });
                this.$http.post('/touchService/call', {
                    custName: this.IDCardInfo.name,
                    psptId: this.IDCardInfo.cardNo,
                    psptStartDate: this.IDCardInfo.effectDate,
                    psptEndDate: this.IDCardInfo.expireDate,
                    faceSimilarity: this.custPicParamInfo.faceSimilarity,
                    custSimilarity: this.custPicParamInfo.custSimilarity,
                    status: type,
                    menuName: '', //老的逻辑下沉就传空
                    sceneType: this.switchParam.sceneType,
                    recordedVideo: '', //老的逻辑下沉就传空
                    videoUrl: '', //老的逻辑下沉就传空
                    realFaceImg: realFaceImg,
                    customerImg: customerImg,
                    picU: handPhotoImg,
                    authModeType: this.authModeType,
                    serviceName: 'customerRealNameVerifyService.submitAuditInfo'
                }).then(res => {
                    Common.loadingStop();
                    this.$toast.clear();
                    if (res.respData && res.respData.respCode == '0') {
                        if (res.respData.data && res.respData.data.NEED_AUDIT == '02') {
                            this.needRealNameFlag = true;
                            let transId = res.respData.data.TRANS_ID;
                            if (util.isEmpty(transId)) {
                                this.$toast('留存流水号为空，请尝试重新认证');
                                return;
                            }
                        } else if (res.respData.data.NEED_AUDIT == '03') {
                            Common.loadingStop();
                            this.$toast.clear();
                        }
                    } else {
                        alertError({
                            title: '处理失败！',
                            message: '错误编码:[' + res.respCode + '],错误流水:[' + res.traceId + ']' + '错误信息:[' + res.respMsg + ']',
                            confirmButtonText: '报告错误'
                        });
                    }
                }).catch(e => {
                    Common.loadingStop();
                    this.$toast.clear();
                    this.$toast('处理错误图片异常：' + e);
                });
            } else if (this.switchParam.sceneType == '01' && this.switchParam.ZTCheckFlag == 'true' && (type == '11' || type == '21')) {
                //原下沉逻辑就只做了接口请求
                this.$http.post('/touchService/call', {
                    custName: this.IDCardInfo.name,
                    psptId: this.IDCardInfo.cardNo,
                    psptStartDate: this.IDCardInfo.effectDate,
                    psptEndDate: this.IDCardInfo.expireDate,
                    faceSimilarity: this.custPicParamInfo.faceSimilarity,
                    custSimilarity: this.custPicParamInfo.custSimilarity,
                    status: type,
                    menuName: '', //老的逻辑下沉就传空
                    sceneType: this.switchParam.sceneType,
                    recordedVideo: '', //老的逻辑下沉就传空
                    videoUrl: '', //老的逻辑下沉就传空
                    // eslint-disable-next-line no-undef
                    // realFaceImg: realFaceImg,
                    // eslint-disable-next-line no-undef
                    // customerImg: customerImg,
                    authModeType: this.authModeType,
                    serviceName: 'customerRealNameVerifyService.submitAuditInfo'
                }).then(res => {

                }).catch(e => {

                });
            }
        },
        takePromiseVideo() {
            if (!this.IDCardInfo.name) {
                this.$toast('请先读取身份证信息！');
                return;
            }
            // Common.loadingStart({
            //     message: '正在查询朗读内容,请稍等...',
            //     duration: 0
            // });
            let content = '仅限办理中国联通业务使用' + this.switchParam.date + '' + this.staffId;
            WadeMobile.recordVideoEx(1, 30, true, content, this.switchParam.riskNotice).then(path => {
                // 视频处理
                setTimeout(() => {
                    // Common.loadingStop();
                    Common.loadingStart('视频上传中,请耐心等待...');
                    WadeMobile.getFileBase64(path, 9999999).then(res => {
                        res.ossPath = 'datas/video/' + this.switchParam.provinceCode;
                        this.$http.post('/video/videoUploadByBase64', res).then(result => {
                            Common.loadingStop();
                            if ('0' === result.respCode) {
                                //视频上传成功
                                this.$toast.success('视频上传成功！');
                                this.videoPath = result.ossKey;
                                this.custPicParamInfo.promiseVideo = result.ossKey;
                                this.$emit('custPicParamInfo', this.custPicParamInfo);
                            } else {
                                alertError({
                                    title: '视频上传失败！',
                                    message: '视频上传失败，请退出重试！',
                                    confirmButtonText: '报告错误'
                                });
                                return;
                            }
                        }).catch(() => {});
                    });
                }, 500);
            });
        },
        saveTDtemToRedis() {
            this.$http.post('redisAndCachController/saveToRedis', {
                redisKey: 'TD_' + this.switchParam.sessionId + '_' + this.IDCardInfo.cardNo + this.switchParam.picRandNum,
                redisValue: this.tdImg,
                redisLimitTIME: 2 * 60 * 60//默认存储2小时
            }).then(() => {
            }).catch(e => {
                Common.loadingStop();
                this.$toast.clear();
                // 保存失败，给与失败提示，业务可以继续进行
                this.$toast(e + '水印信息保存失败');
            });
        },
        savePicUToRedis() {
            this.$http.post('redisAndCachController/saveToRedis', {
                redisKey: 'HANDPHOTO_' + this.switchParam.sessionId + '_' + this.IDCardInfo.cardNo + this.switchParam.picRandNum,
                redisValue: this.handPhotoImg,
                redisLimitTIME: 2 * 60 * 60//默认存储2小时
            }).then(() => {
            }).catch(e => {
                Common.loadingStop();
                this.$toast.clear();
                // 保存失败，给与失败提示，业务可以继续进行
                this.$toast(e + '存储信息失败,但不影响正常业务！');
            });
        },
        dealBaseImg(faceImgResult) {
            this.faceImgResult = faceImgResult;
            this.custPicParamInfo.pic5 = 'LIVING' + this.IDCardInfo.cardNo;
            this.custPicParamInfo.picC = 'LIVING_BEST' + this.IDCardInfo.cardNo;
            this.$emit('custPicParamInfo', this.custPicParamInfo);
        },
        dealCustomerImg(customerImg) {
            this.customerImg = customerImg;
            let customerPicKey = 'CUSTOMER' + this.IDCardInfo.cardNo;
            // this.custPicParamInfo.pic1 = customerPicKey;
            this.custPicParamInfo.pic1 = customerImg;
            this.$emit('custPicParamInfo', this.custPicParamInfo);
        },
        //获取后置摄像头开关
        getIdentityBackAuthSwitch() {
            Mobile.getMemoryCache('staffInfo').then(result => {
                let provinceCode = JSON.parse(result) ? JSON.parse(result).PROVINCE_CODE : '';
            });
        },
        onSelectSheet(action, index) {
            if (index === 0) {
                //拍照
                this.takePhotos('translationCert', this.translationImg);
            }
            else if (index === 1) {
                //相册
                if (!this.passportCanDo) {
                    this.$toast('请先点击查询进行护照校验');
                    return;
                }
                if (!this.IDCardInfo.name) {
                    this.$toast('请先读取身份证信息');
                } else if (!this.faceImgResult && !this.passportCanDo) {
                    this.$toast('请先进行人脸识别');
                } else {
                    this.openAlbum('translationCert', this.translationImg);
                }

            }
        },
        // 打开相册
        openAlbum(id, img) {
            let _this = this;
            Common.loadingStart({
                message: '正在进行照片解析,请稍等...',
                duration: 0
            });
            WadeMobile.getPicture2('base64').then(img => {
                Common.loadingStop();
                let image = 'data:image/png;base64,' + img;
                _this.handCardPhotoImg(id, image);
            }, err => {
                Common.loadingStop();
                alertError({
                    title: '调用原生相册插件出错！',
                    message: err,
                    confirmButtonText: '报告错误'
                });
            }).catch(err => {
                alertError({
                    title: '调用原生相册插件出错！',
                    message: err,
                    confirmButtonText: '报告错误'
                });
            });
        },
        handCardPhotoImg(id, imgStr) {
            var imageData = 'data:image/png;base64,' + imgStr;
            // if (imageData.length / 1024 * 3 / 4 < 50) {
            //     Mobile.alert('图片质量过低，请重新拍摄');
            //     return;
            // }
            this.translationImg = imgStr;
            this.checkCustomerPhoto(imgStr, false, id);
        },
        // 压缩图片
        compressPhoto(base64 = '', maxSize, compressTimes = 10) {
            base64 = base64.replace(/data:image\/.*;base64,/, '');
            return new Promise(resolve => {
                const photosize = this.imageSize(base64);
                if (photosize / 1024 <= maxSize) {
                    resolve(base64);
                } else {
                    // 创建 img 元素
                    const image = new Image();
                    image.onload = async() => {
                        // 创建 canvas 元素
                        const canvas = document.createElement('canvas');
                        canvas.width = image.width;
                        canvas.height = image.height;
                        // 绘制 canvas
                        canvas.getContext('2d').drawImage(image, 0, 0, image.width, image.height);
                        let canvasURL;
                        let L = true;
                        let quality = 0;
                        for (let i = 1; i <= compressTimes; i++) {
                            quality += L ? 1 / (2 * i) : -(1 / (2 * i));
                            canvasURL = canvas.toDataURL('image/jpeg', L ? (quality += 1 / (2 * i)) : (quality -= 1 / (2 * i)));
                            let canvasURLCopy = canvasURL.replace(/data:image\/.*;base64,/, '');
                            let size = this.imageSize(canvasURLCopy);
                            (size / 1024) > maxSize ? L = false : L = true;
                            if ((size / 1024) < maxSize && (size / 1024) > maxSize / 4 * 3) {
                                break;
                            }
                        }
                        canvasURL = canvasURL.replace(/data:image\/.*;base64,/, '');
                        resolve(canvasURL);
                    };
                    image.src = 'data:image/jpeg;base64,' + base64;
                }
            });
        },
        imageSize(base64Str) {
            const indexBase64 = base64Str.indexOf('=');
            if (indexBase64 > 0) {
                base64Str = base64Str.substring(0, indexBase64);
            }
            return base64Str.length;
        }
    }
};
</script>

<style lang="scss" scoped>
.img-grounp {
    display: flex;
    flex-wrap: wrap;
    width: 100%;

    .img-single {
        margin-top: 10px;
        padding: 4px 6px;
        flex-direction: row;

        &:first-child {
            padding-left: unset;
        }

        .img-style {
            height: 80px;
        }

        .text {
            margin-top: 5px;
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            color: rgb(116, 115, 115);
        }
    }

    .active {
        display: none;
    }
}

.my-img-preview {
    /deep/ .van-image-preview__cover {
        position: absolute;
        bottom: 0.5rem;
        top: unset;
        width: 100%;
        display: flex;
        justify-content: center;

        .van-button {
            width: 90%;
        }
    }
}

.photo-box {
    .box-new-ui {
        .faceImgBox {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #ffffff;
            border-radius: 0.2rem;
            padding: 5px 0;

            &.flex-warp {
                flex-wrap: wrap;
            }

            .icon-img {
                margin-left: 4px;
                width: 22px;
            }

            .faceImgTips {
                flex: 1;
                padding: 0 11px;

                .name {
                    font-size: 15px;
                    line-height: 24px;
                }

                .tips {
                    font-size: 13px;
                    color: #999999;
                    flex: 1 1 100%;
                    margin-top: 2px;
                    white-space: nowrap;
                    line-height: 20px;
                }
            }

            .van-button {
                height: auto;
                padding: 0.12rem 0.4rem;
                font-size: 0.35rem;
            }

            .take-photo {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 25px;
                position: relative;
                flex: 1 1 100%;

                &::after {
                    position: absolute;
                    content: ' ';
                    top: -12px;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background-color: #e5e5e5;
                    transform: scaleY(0.5);
                }

                .img-container {
                    width: 158px;
                    height: 87.5px;
                    overflow: hidden;
                    font-size: 0;
                    border-radius: 4px;
                }

                .img-container-other {
                    font-size: 0;
                }

                > div {
                    .img-style {
                        height: 78px;
                        width: 140px;
                    }

                    .text {
                        margin-top: 0.13333rem;
                        text-align: center;
                        font-size: 0.37333rem;
                        color: #333333;
                    }
                }
            }
        }
    }

    .img-grounp {
        background: #ffffff;
        padding: 0.3rem;
        box-sizing: border-box;
        margin-top: 0.3rem;
        border-radius: 0.2rem;
        justify-content: flex-start;

        .img-grounp-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            flex: 1 1 100%;
            position: relative;

            &::after {
                position: absolute;
                content: ' ';
                bottom: -12px;
                left: 0;
                right: 0;
                border-bottom: 1px solid #eeeeee;
            }

            .icon-img {
                width: 20px;
                margin-left: 9px;
            }

            .faceImgTips {
                flex: 1;
                padding: 0 0.5rem;
                padding-right: 12px;

                .name {
                    font-size: 15px;
                    line-height: 24px;
                }

                .tips {
                    font-size: 0.37rem;
                    color: #999999;
                    flex: 1 1 100%;
                    margin-top: 2px;
                    white-space: nowrap;
                    line-height: 20px;
                }
            }
        }

        .img-single {
            width: 110px;
            margin-top: 0;
            position: relative;
            padding: 0;

            > div {
                display: flex;
                justify-content: center;
            }

            &:first-child {
                padding-left: unset;
            }

            .img-style {
                height: 128px;

                &.max-width {
                    width: 100%;
                }
            }

            .text {
                font-weight: normal;
                color: #333333;
            }
        }
    }

    * {
        box-sizing: border-box;
    }
}

.photoFillet {
    border-radius: 4px;
}

.delectBtn {
    position: absolute;
    top: 0;
    right: 0;
}
</style>
