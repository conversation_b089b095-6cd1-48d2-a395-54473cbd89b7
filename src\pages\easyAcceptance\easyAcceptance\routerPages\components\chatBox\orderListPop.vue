<template>
  <div class="orderListPop">
    <van-popup
        v-model="show"
        position="bottom"
        closeable
        @close="$emit('closePopup')"
        :close-on-click-overlay="false"
        :style="{ width: '100%',height: '56%' }">
      <div class="box">
        <div class="one-title">我的订单</div>
        <div class="pop-box">
          <div class="title">按时间</div>
          <div>
            <div class="time-btn">
              <div class="time-checkradio" :class="timeActive===1?'activetime-checkradio':''" @click="chooseMounth(1)">1个月内</div>
              <div class="time-checkradio" :class="timeActive===3?'activetime-checkradio':''" @click="chooseMounth(3)">3个月内</div>
              <div class="time-checkradio" :class="timeActive===6?'activetime-checkradio':''" @click="chooseMounth(6)">6个月内</div>
            </div>
            <div class="time-input-box">
              <div @click="chooseDate(1)" class="input-time" :class="timeActive==='other'?'activetime-checkradio':''" :style="{color: startTime!=='起始时间'?'#263A5F':''}">{{ startTime }}</div>
              <span>-</span>
              <div @click="chooseDate(2)" class="input-time" :class="timeActive==='other'?'activetime-checkradio':''" :style="{color: endTime!=='结束时间'?'#263A5F':''}">{{ endTime }}</div>
            </div>
          </div>
        </div>
        <div class="pop-box" style="padding: 20px 0px 20px 6px">
          <div class="title" style="padding-left: 20px;">按订单状态</div>
          <van-radio-group style="width: 100%" v-model="radio" direction="horizontal">
            <van-radio name="orderOnTheWay"  class="baseRadio">
              <div class="inner-card" >
                <img :src="require('@/pages/easyAcceptance/easyAcceptance/images/photocard.png')"/>
                <span>未竣工</span>
              </div>
            </van-radio>
            <van-radio name="XX"  class="baseRadio">
              <div class="inner-card">
                <img :src="require('@/pages/easyAcceptance/easyAcceptance/images/ting.png')"/>
                <span>已退单</span>
              </div>
            </van-radio>
            <van-radio name="00"  class="baseRadio" >
              <div class="inner-card">
                <img :src="require('@/pages/easyAcceptance/easyAcceptance/images/super.png')"/>
                <span>已归档</span>
              </div>
              
            </van-radio>
          </van-radio-group>
        </div>
        
        <div class="btn-box">
          <van-button class="btn1" @click="reset()">重置</van-button>
          <van-button class="btn2" @click="submit()">确定</van-button>
        </div>
      </div>
    </van-popup>
    <van-calendar color="#497CF6" :min-date="minDate" :max-date="maxDate" v-model="showstart" @confirm="onConfirm"  :default-date="defaultDateS" />
    <van-calendar color="#497CF6" :min-date="minDate" :max-date="maxDate" v-model="showend" @confirm="onConfirm"  :default-date="defaultDateE" />
  </div>
</template>
<script>
import {mapMutations} from "vuex";
import moment from "moment";

export default {
  name: 'orderListPop',
  data(){
    return {
      show: false,
      radio: '',
      startTime: '',
      endTime: moment().format('yyyy-MM-DD'),
      showCalendar: false,
      timeActive: '',
      // 设置 minDate 为今天
      showstart:false,
      showend:false,
      defaultDateS:new Date(this.withInMonths(1)),
      defaultDateE:new Date(),
      minDate: new Date('2024-01-01'),
      maxDate: new Date()
    }
  },
  props:['popShow'],
  watch:{
    popShow(){
      this.show = this.popShow
    }
  },
  mounted(){
    this.startTime=this.withInMonths(1)
  },
  methods:{
    ...mapMutations([
      'setOrderInfoLists'
    ]),
    formatDate(date) {
      let currMonth = date.getMonth() + 1;
      if(currMonth < 10) currMonth = "0"+currMonth;
      let currDay = date.getDate();
      if(currDay < 10) currDay = "0"+currDay;
      return `${date.getFullYear()}-${currMonth}-${currDay}`;
    },
    submit(){
      
      
      console.log(this.endTime,"=========",this.radio,"=========",this.startTime,"=========")
      if(this.isEmpty(this.endTime)&&this.isEmpty(this.startTime)&&this.isEmpty(this.radio)){
        this.$toast('至少选择一个条件进行查询')
        return;
      }
      
      else if((this.isEmpty(this.endTime)&&!this.isEmpty(this.startTime))){
        this.$toast('请选择结束时间')
        return;
      }
     else if((this.isEmpty(this.startTime)&&!this.isEmpty(this.endTime))){
        this.$toast('请选择开始时间')
        return;
      }
     else{
        let req={};
        if("orderOnTheWay"==this.radio){
          req={
            orderStateDetect:this.radio,
            endDate:this.endTime,
            startDate:this.startTime,
          }
        }
        else{
          req={
            orderState:this.radio,
            endDate:this.endTime,
            startDate:this.startTime,
          }
        }
        this.$http.post('/Order/custOrderQuery',req).then((res)=>{
          if (res.respCode === '0000') {
            this.$emit('recivePopData', res.respData)
          }
          else{
            this.$toast('没有查到数据，返回结果为空')
            this.$emit('recivePopData', [])
          }
          this.show=false;
        })
      }

    },
    reset(){
      this.radio='',
      this.startTime= '',
      this.endTime='', 
      this.timeActive=''
    },
    onConfirm(date) {
      console.log(this.defaultDateS,this.defaultDateE)
      if(this.timeType===1){
        this.showstart=false
        this.startTime = this.formatDate(date);
      }else if(this.timeType===2){
        this.showend=false
        this.endTime = this.formatDate(date);
      }
    },
    isEmpty(value) {
      let flag = false
      if (value == "" || value == 'undefined' || value == undefined || value == null || value == 'null') {
        flag = true
      }
      return flag
    },
    withInMonths(n){
      // 获取当前日期
      if (n <= 0 || n > 11) {
        throw new Error("n should be a positive integer and less than or equal to 11");
      }
      const currentDate = new Date();
      let year = currentDate.getFullYear();
      let month = currentDate.getMonth();
      // Subtract n months from the current month
      month -= n;
      // Handle the case where month goes negative (e.g., January - 1 month should be December of the previous year)
      if (month < 0) {
        year -= Math.ceil(Math.abs(month) / 12); // Adjust year
        month += 12 * Math.ceil(Math.abs(month) / 12); // Normalize month to be within 0-11
      }
      // Create the new date object with the adjusted year and month
      const previousDate = new Date(year, month, currentDate.getDate());
      // Format the date as yyyy-mm-dd
      const formattedDate = previousDate.toISOString().split('T')[0];
      return formattedDate;
    },

    chooseMounth(time){
      this.startTime = this.withInMonths(time)
      this.defaultDateS = new Date(this.startTime)
      this.endTime = this.formatDate(new Date())
      this.defaultDateE = new Date(this.endTime)
      this.timeActive = time
    },
    chooseDate(type){
      if(1==type){
        this.showstart = true
      }
      if(2==type){
        this.showend = true
      }
      this.timeType = type;
      this.timeActive = 'other'
    }
  }
}
</script>

<style scoped lang="scss">
.orderListPop{
  .box{
    position: relative;
    width: 100%;
    height: 100%;
    .one-title{
      width: 100%;
      text-align: center;
      padding: 20px 0;
    }
    .pop-box{
      padding: 20px;
      border-bottom: 2px solid #F8F8F8;
      .title{
        font-weight: bold;
        font-size: 17px;
        margin-bottom: 20px;
      }
      .time-btn{
        display: flex;
        justify-content: space-evenly;
        width: 100%;
        .time-checkradio{
          width: 30%;
          background: #F5F5F5;
          border-radius: 10px;
          text-align: center;
          height: 35px;
          line-height: 35px;
        }
      }
      .time-input-box{
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        width: 100%;
        margin-top: 10px;
        .input-time{
          background: #F5F5F5;
          border-radius: 10px;
          height: 35px;
          line-height: 35px;
          width: 42%;
          text-align: center;
          color: #ADADAD;
        }
      }
      .activetime-checkradio{
        background: #eef3ff!important;
        color: #497CF6!important;
      }
      .baseRadio{
        display: flex;
        justify-content: space-between;
        flex-direction: row-reverse;
        align-items: flex-start;
        padding-top: 5px;
        width: 80px;
        height: 64px;
        font-size: 14px;
        background-size: cover; /* 背景图片覆盖整个元素 */
        background-repeat: no-repeat; /* 背景图片不重复 */
        background-position: center; /* 背景图片居中 */
        .inner-card{
          margin-left:15px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          flex-direction: column;
          img{
            width: 25px;
            height: 25px;
          }
          span{
            display: inline-block;
            font-size: 12px;
            margin-top: 5px;
          }
        }
      }
    }
    .btn-box{
      display: flex;
      position: absolute;
      bottom: 20px;
      width: 100%;
      justify-content: space-evenly;
      .btn1{
        background: #fff;
        border: 2px solid #000;
        color: #000;
        border-radius: 20px;
        width: 35%;
      }
      .btn2{
        background: #497CF6;
        border: 2px solid #fff;
        color: #fff;
        border-radius: 20px;
        width: 35%;
      }
    }
  }
}
</style>
<style>
.orderListPop .van-radio-group--horizontal {
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}
</style>