<template>
<div class="customerInfo">
<div v-for="(item,index) in infoArr" :key="index">
  <span style="padding-right: 10px">{{item.name}}：</span>
  <span>{{item.value}}</span>
</div>
</div>
</template>

<script>
export default {
  name: "customerInfo",
  data(){
    return {
      infoArr: [
        {name: '姓名',value: '张*丰'},
        {name: '手机',value: '15618008888'},
        {name: '宽带',value: '021-8888-6666'},
        {name: '地址',value: '上海市杨浦区荆州路***号***'}
      ]
    }
  },
  methods:{

  }
}
</script>

<style scoped lang="scss">

</style>