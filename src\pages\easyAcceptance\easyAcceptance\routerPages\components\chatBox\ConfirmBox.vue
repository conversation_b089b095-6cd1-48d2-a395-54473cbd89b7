<template>
    <div>
        <div class="content mt5" v-html="params.msg">
<!--            {{ params.msg }}-->
        </div>
        <div class="footer" :style="{ 'justify-content': (!hiddenConfirmButton && !hiddenCancelButton) ? 'space-between' : 'center' }">
            <van-button :loading="loading" :disabled="disabled" :icon="select1 ? 'success' : ''" v-if="!hiddenConfirmButton" size="small" type="info" @click="confirmFn">{{ params.confirmText || '确定' }}</van-button>
            <van-button :loading="loading" :disabled="disabled" :icon="select2 ? 'success' : ''" v-if="!hiddenCancelButton" size="small" plain type="info" class="ml10" @click="cancelFn">{{ params.cancelText || '取消' }}</van-button>
        </div>
    </div>
</template>

<script>
import { mapMutations } from 'vuex'

export default {
    name: 'ConfirmBox',
    props: {
        params: {
            type: Object,
            default() {
                return {
                    msg: '客户正在进行融合变更业务，是否切换到查询客户资料',
                    confirm: null,
                    cancel: null,
                }
            }
        }
    },
    data() {
        return {
            loading: false,
            disabled: false,
            select1: false,
            select2: false
        }
    },
    computed: {
        hiddenCancelButton() {
            return !!this.params?.hiddenCancelButton
        },
        hiddenConfirmButton() {
            return !!this.params?.hiddenConfirmButton
        },
    },
    async created() {
        
    },
    methods: {
        confirmFn() {
            if (this.params?.confirm && (typeof this.params?.confirm === 'function')) {
                this.disabled = true
                this.select1 = true
                this.params.confirm()
            } else {
                console.log('缺少confirm参数或confirm参数不是函数');
            }
        },
        cancelFn() {
            if (this.params?.cancel && (typeof this.params?.cancel === 'function')) {
                this.disabled = true
                this.select2 = true
                this.params.cancel()
            } else {
                console.log('缺少cancel参数或cancel参数不是函数');
            }
        },
    },
    beforeDestroy() {
        
    }
};
</script>

<style lang="scss" scoped>
.content {
}
.footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 30px 0;
    ::v-deep .van-button--small {
        padding-left: 26px;
        padding-right: 26px;
    }
}

.van-button__icon {
    margin-top: 5px;
}
</style>
