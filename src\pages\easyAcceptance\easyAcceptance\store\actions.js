import http from '@/assets/js/axios.js';
import {SWITCH_LIST, SWITCH_DETAIL_LIST} from '../assets/js/constants';
import {Dialog, Toast} from 'vant';
import {decrypted} from '@/assets/js/AESUtil.js';

import {alertError,getCurrentFormattedTime} from '@/assets/bizComponents/funcComponent.js'
import {safeTrans} from "../../../../assets/js/beePlugin/util";
import getters from "./getters";
import {commit} from "lodash/seq";
// import state from "./state";
// 异常处理
const errorHandle = (res, fn, errorFn) => {
    if (res.respCode === '0000') {
        if (fn) fn();
    } else {
        if (errorFn) errorFn();
        else Toast(res.respMsg);
    }
};

export default {
    /**
     * 查询全部开关以及系统权限
     * 仅value1，以 'true'/'false' 的形式返回，如：res.respData.demoSwitch = 'true'
     */
    // qryAllSwitch({ commit }) {
    //     return http.post('/paramController/qryAllSwitchAndSysRightBySwitchType', {
    //         checkListParam: SWITCH_LIST
    //     }).then(res => {
    //         if (res && res.respCode === '0000') {
    //             commit('updateSwitch', res.respData || {});
    //             return 'success';
    //         } else {
    //             Toast(res.respMsg || '开关查询出错');
    //             return 'failed';
    //         }
    //     }).catch(err => {
    //         Toast(err || '开关查询出错了');
    //         return err;
    //     });
    // },
    // /**
    //  * 查询全部开关以及系统权限的详情
    //  * 同时返回 value1 以及 value2
    //  */
    // qryAllSwitchDetail({ commit }) {
    //     return http.post('/paramController/qryAllSwitchDetailBySwitchType', {
    //         checkListParam: SWITCH_DETAIL_LIST
    //     }).then(res => {
    //         if (res && res.respCode === '0000') {
    //             commit('updateSwitchDetail', res.respData || {});
    //             return 'success';
    //         } else {
    //             Toast(res.respMsg || '开关详细查询出错');
    //             return 'failed';
    //         }
    //     }).catch(err => {
    //         Toast(err || '开关详细查询出错了');
    //         return err;
    //     });
    // },
    // /**
    //  * 获取缓存中常用的数据
    //  */
    // getCommonMemoryCache({ commit }) {
    //     Mobile.getMemoryCache(['staffInfo', 'SESSION_ID']).then(res => {
    //         const staffInfo = res ? JSON.parse(res?.staffInfo || '{}') : {};
    //         const provinceCode = staffInfo.PROVINCE_CODE;
    //         const sessionId = res?.SESSION_ID;

    //         commit('setStaffInfo', staffInfo);
    //         commit('setProvinceCode', provinceCode);
    //         commit('setSessionId', sessionId);
    //     }).catch(() => {});
    // },
    // // 宽带调测费
    // async qryCommTestFee({ commit }, param) {
    //     return await http.post('/QryProduct/qryCommTestFee', param).then(res => {
    //         // 设置调测费
    //         let testFee = {
    //             number: param.number,
    //             commId: param.testFeePolicyId, // 固网子商品
    //             feeMode: 0,
    //             feeTypeCode: 8236,
    //             feeTypeName: '调测费',
    //             fee: res.respData.testFee || res.respData.test_fee || '0', // 费用
    //             isStages: 0
    //         };
    //         commit('addFeeInfo', testFee);
    //         return res;
    //     }).catch(err => {
    //         return err;
    //     });
    // },
    // // 装维工号服务
    // async installWorkNum({ commit }, queryParam) {
    //     Toast.loading('认证中...');
    //     return await http.post('/developInfo/getDeveloperInfo', {
    //         sysUserId: queryParam
    //     }).then(res => {
    //         errorHandle(res, () => {
    //             if (res.respData[0]?.STAFF_QUALITY?.respCode === '0000') {
    //                 const ywStaffId = res.respData[0]?.STAFF_QUALITY?.respDesc;
    //                 commit('setState', { isRealWorkNum: true, ywStaffId: ywStaffId });
    //                 Toast('认证通过');
    //             } else {
    //                 commit('setState', { isRealWorkNum: false, ywStaffId: '' });
    //                 Toast(res.respData[0]?.STAFF_QUALITY?.respDesc);
    //             }
    //         }, () => {
    //             Toast('工号校验未通过');
    //         });
    //         return (res.respCode === '0000' && res.respData[0]?.STAFF_QUALITY?.respCode === '0000');
    //     }).catch(() => {
    //         Toast('糟糕，接口出错了，请您重试！');
    //     });
    // },
    // 主号码校验
    mainNumVerify({commit, getters,dispatch}, queryParam) {
        // Toast.loading({
        //     icon:''
        // });
        commit('showLoading');
        return new Promise((resolve, reject) => {
                let req = {
                    serialNumber: queryParam.mainNumber
                }
                http.post('/shjzfkNumberCheck/mainCardAuthentication', req).then(res => {
                    let mainNumVerifyFlag = 0
                    commit('hideLoading');
                    errorHandle(res, () => {
                        
                        // mapState.jzfkOrderData.mainNumberCheckData.mainNumber=queryParam.mainNumber
                        const jzfkOrderData=getters.getJzfkOrderData
                        jzfkOrderData.mainNumberCheckData.mainNumber=queryParam.mainNumber
                        jzfkOrderData.mainNumberCheckData.custInfo=res.respData.custInfo
                        commit('setJzfkOrderData',jzfkOrderData);
                        dispatch('saveCacheData')
                        console.log('主号码校验通过');
                        return resolve({flag: "1"});
                    }, () => {
                        const jzfkOrderData=getters.getJzfkOrderData
                        jzfkOrderData.mainNumberCheckData.mainNumber=''
                        jzfkOrderData.mainNumberCheckData.custInfo={}
                        commit('setJzfkOrderData',jzfkOrderData);
                        console.log('主号码校验不通过');
                        return resolve({
                            flag: "2",
                            respMsg: res.respMsg + "请重新输入主号码",
                        });
                    });
                }).catch((e) => {
                    alertError({
                        title: '出错了！',
                        message: e,
                        confirmButtonText: '报告错误'
                    });
                    return resolve({respMsg: e + "请稍后重试", flag: "2"});
                });
        })
    },
    // 发送验证码
    sendMsg({commit, getters}, queryParam) {
        commit('showLoading');
        return new Promise((resolve, reject) => {
            let req={
                serialNumber:getters.getJzfkOrderData.mainNumberCheckData.mainNumber
            }
            http.post('/shjzfkNumberCheck/wgmessage', req).then(res=>{
                commit('hideLoading');
            })
            return resolve({flag: "1"});
        })
    },

    FusUpYskTip({commit, getters}, queryParam) {
        commit('showLoading');
        return new Promise((resolve, reject) => {
            return resolve({flag: "1",autoDeduct:getters.getFusionUpFttrOrderData.autoDeduct});
        })
    },
    ////用户选择了免预存商品且网龄不满一年且为Fttr商品 报错让其重新选择
    zwUpTip({commit, getters}, queryParam) {
        commit('showLoading');
        return new Promise((resolve, reject) => {
            return resolve({flag: "1",autoDeduct:getters.getZwOrderData.autoDeduct});
        })
    },
    // 短信验证码校验
    msgCodeVerify({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        return new Promise((resolve, reject) => {
            let req={
                checkCode:queryParam.msgCode,
                serialNumber:getters.getJzfkOrderData.mainNumberCheckData.mainNumber
            }
            commit('hideLoading');
            // 本地测试用的
            // if('123456'==queryParam.msgCode){
            //     console.log('短信验证码通过');
            //     const jzfkOrderData=getters.getJzfkOrderData
            //     const curTask=getters.getCurTask
            //     jzfkOrderData.curTask.taskId=curTask.taskId
            //     jzfkOrderData.curTask.toolCode=curTask.toolCode
            //     jzfkOrderData.curTask.taskName=curTask.taskName
            //     const chatList=getters.getChatList
            //     jzfkOrderData.chatList=chatList
            //     commit('setJzfkOrderData',jzfkOrderData);
            //
            //     let req1 = {
            //         operatorId: getters.getShbmMsgInfo.operatorId,
            //         jzfkOrderData:JSON.stringify(getters.getJzfkOrderData)
            //     }
            //     http.post('/contineBreak/saveData',req1).then(res=>{
            //     });
            //     return resolve({flag: "1", msgCodeVerifyFlag: "1", msgCode: ""});
            // }
            // else{
            //     console.log('主号码校验不通过');
            //     return resolve({flag: "2"});
            // }
            
            
            http.post('/shjzfkNumberCheck/checkCodeValidate', req).then(res => {
                if("0000"==res.respCode){
                    return resolve({flag: "1", msgCodeVerifyFlag: "1", msgCode: ""});
                }
                else if("7777"==res.respCode){
                    return resolve({flag: "2", respMsg: res.respMsg,validFlag:'4'
                    });
                }
                else{
                    return resolve({flag: "2", respMsg: res.respMsg});
                }
            }).catch((e) => {
                alertError({
                    title: '出错了！',
                    message: e,
                    confirmButtonText: '报告错误'
                });
                return resolve({flag: "2"});
            });
        })
    },
    // todo 商品确定
    viceCardCommoditySubmit({commit, getters,dispatch}, queryParam) {
        return new Promise((resolve, reject) => {
            try {
                dispatch('saveCacheData')
                return resolve({flag: "1"});
            } catch (e) {
                return resolve({flag: "2", respMsg: e});
            }
        })
    },
    // todo 选号确定
    mobileRecommendationSubmit({commit, getters,dispatch}, queryParam) {
        return new Promise((resolve, reject) => {
            try {
                const jzfkOrderData=getters.getJzfkOrderData
                let req={
                    serialNumber: jzfkOrderData.numberChooseData.selectedNumber,
                    selectionTime: '30'
                }
                http.post('/shjzfkGoodsSelect/selectedNum', req).then(res => {
                    errorHandle(res, () => {
                       dispatch('saveCacheData')
                        console.log('选占成功');
                        return resolve({flag: "1"});
                    }, () => {
                        console.log('选占失败');
                        return resolve({flag: "2"});
                    });
                }).catch((e) => {
                    alertError({
                        title: '出错了！',
                        message: e,
                        confirmButtonText: '报告错误'
                    });
                    return resolve({flag: "2"});
                });
                console.log("mobileRecommendationSubmit")
               
                return resolve({flag: "1"});
            } catch (e) {
                return resolve({flag: "2", respMsg: e});
            }
        })
    },
    getBookTimeWord({commit, getters,dispatch}, queryParam) {
        return new Promise((resolve, reject) => {
                dispatch('getHotWords','IptvReSelectDate')
                return resolve({flag: "1"});
        })
    },
    // todo 客户认证确定
    customerAuthenticationSubmit({commit, getters,dispatch}, queryParam) {
        return new Promise((resolve, reject) => {
            try {
                 dispatch('saveCacheData');
                console.log("Promise======")
                return resolve({flag: "1"});
            } catch (e) {
                return resolve({flag: "2", respMsg: e});
            }
        })
    },

    iptvFusionInformationQuery({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        return new Promise((resolve, reject) => {
            let req = {
                serialNumber: queryParam.phoneSerialNumber
            }
            http.post('/mpComm/mpRhQueryByPhoneNum', req).then(res => {
                let mainNumVerifyFlag = 0
                commit('hideLoading');
                errorHandle(res, () => {
                    const iptvOrderData=getters.getIptvOrderData
                    iptvOrderData.timeData.contactPhone=queryParam.phoneSerialNumber
                    iptvOrderData.broadNumberByYw=res.respData.kdNumList
                    commit('setIptvOrderData',iptvOrderData)
                    if(res.respData.kdNumList.length==1){
                        iptvOrderData.selectedNumber=res.respData.kdNumList[0].kdSerialNumber
                        commit('setIptvOrderData',iptvOrderData)
                        return resolve({flag: "2"});
                    }else{
                        return resolve({flag: "1",iptvNumCheckFlag:"1"});
                    }
                }, () => {
                    return resolve({
                        flag: "2",
                        respMsg: res.respMsg 
                    });});
            }).catch((e) => {
                alertError({
                    title: '出错了！',
                    message: e,
                    confirmButtonText: '报告错误'
                });
                return resolve({respMsg: e + "请稍后重试", flag: "2"});
            });
        })
    },

    zwFusionInformationQuery({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        return new Promise((resolve, reject) => {
            let req = {
                serialNumber: queryParam.phoneSerialNumber
            }
            http.post('/mpComm/mpRhQueryByPhoneNum', req).then(res => {
                let mainNumVerifyFlag = 0
                commit('hideLoading');
                errorHandle(res, () => {
                    const zwOrderData=getters.getZwOrderData
                    zwOrderData.broadNumberByYw=res.respData.kdNumList
                    commit('setZwOrderData',zwOrderData);
                    if(res.respData.kdNumList.length==1){
                        zwOrderData.selectedNumber=res.respData.kdNumList[0].kdSerialNumber
                        commit('setZwOrderData',zwOrderData);
                        return resolve({flag: "2"});
                    }else{
                        return resolve({flag: "1",iptvNumCheckFlag:"1"});
                    }
                }, () => {
                    return resolve({
                        flag: "2",
                        respMsg: res.respMsg 
                    });
                });
            }).catch((e) => {
                alertError({
                    title: '出错了！',
                    message: e,
                    confirmButtonText: '报告错误'
                });
                return resolve({respMsg: e + "请稍后重试", flag: "2"});
            });
        })
    },
    qryBroadPhoneByCertCodeZw({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        return new Promise((resolve, reject) => {
            let req = {
                psptId: queryParam.checkCertNumber
            }
            http.post('/mpComm/qryBroadPhoneByAllUsingUser', req).then(res => {
                let mainNumVerifyFlag = 0
                commit('hideLoading');
                errorHandle(res, () => {
                    const zwOrderData=getters.getZwOrderData
                    zwOrderData.broadNumberByYw=res.respData.kdNumList
                    commit('setZwOrderData',zwOrderData);
                    if(res.respData.kdNumList.length==1){
                        zwOrderData.selectedNumber=res.respData.kdNumList[0].kdSerialNumber
                        commit('setZwOrderData',zwOrderData);
                        return resolve({flag: "3"});
                    }else{
                        return resolve({flag: "1",iptvNumCheckFlag:"1"});
                    }
                }, () => {
                    return resolve({
                        flag: "2",
                        respMsg: res.respMsg
                    });
                });
            }).catch((e) => {
                alertError({
                    title: '出错了！',
                    message: e,
                    confirmButtonText: '报告错误'
                });
                return resolve({respMsg: e + "请稍后重试", flag: "2"});
            });
        })
    },
    qryBroadPhoneByCertCodeFuseUp({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        return new Promise((resolve, reject) => {
            let req = {
                psptId: queryParam.checkCertNumber
            }
            http.post('/mpComm/qryBroadPhoneByAllUsingUserFuseUp', req).then(res => {
                let mainNumVerifyFlag = 0
                commit('hideLoading');
                errorHandle(res, () => {
                    const fusionUpFttrOrderData=getters.getFusionUpFttrOrderData
                    fusionUpFttrOrderData.broadNumberByYw=res.respData.kdNumList
                    commit('setFusionUpFttrOrderData',fusionUpFttrOrderData);
                    if(res.respData.kdNumList.length==1){
                        fusionUpFttrOrderData.selectedNumber=res.respData.kdNumList[0].kdSerialNumber
                        commit('setFusionUpFttrOrderData',fusionUpFttrOrderData);
                        return resolve({flag: "3"});
                    }else{
                        return resolve({flag: "1",iptvNumCheckFlag:"1"});
                    }
                }, () => {
                    return resolve({
                        flag: "2",
                        respMsg: res.respMsg
                    });
                });
            }).catch((e) => {
                alertError({
                    title: '出错了！',
                    message: e,
                    confirmButtonText: '报告错误'
                });
                return resolve({respMsg: e + "请稍后重试", flag: "2"});
            });
        })
    },

    qryBroadPhoneByCertCode({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        return new Promise((resolve, reject) => {
            let req = {
                psptId: queryParam.checkCertNumber
            }
            http.post('/mpComm/qryBroadPhoneByAllUsingUser', req).then(res => {
                let mainNumVerifyFlag = 0
                commit('hideLoading');
                errorHandle(res, () => {
                    const iptvOrderData=getters.getIptvOrderData
                    iptvOrderData.timeData.contactPhone=queryParam.phoneSerialNumber
                    iptvOrderData.broadNumberByYw=res.respData.kdNumList
                    commit('setIptvOrderData',iptvOrderData)
                    if(res.respData.kdNumList.length==1){
                        iptvOrderData.selectedNumber=res.respData.kdNumList[0].kdSerialNumber
                        commit('setIptvOrderData',iptvOrderData)
                        return resolve({flag: "3"});
                    }else{
                        return resolve({flag: "1",iptvNumCheckFlag:"1"});
                    }
                }, () => {
                    return resolve({
                        flag: "2",
                        respMsg: res.respMsg
                    });});
            }).catch((e) => {
                alertError({
                    title: '出错了！',
                    message: e,
                    confirmButtonText: '报告错误'
                });
                return resolve({respMsg: e + "请稍后重试", flag: "2"});
            });
        })
    },
    broadUpFusionInformationQuery({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        return new Promise((resolve, reject) => {
            let req = {
                serialNumber: queryParam.qrySerialNumber
            }
            http.post('/mpComm/mpRhQueryByPhoneNum', req).then(res => {
                let mainNumVerifyFlag = 0
                commit('hideLoading');
                errorHandle(res, () => {
                    const broadUpOrderData=getters.getBroadUpOrderData
                    broadUpOrderData.broadNumberByYw=res.respData.kdNumList
                    commit('setBroadUpOrderData',broadUpOrderData);
                    if(res.respData.kdNumList.length==1){
                        broadUpOrderData.selectedNumber=res.respData.kdNumList[0].kdSerialNumber
                        commit('setBroadUpOrderData',broadUpOrderData);
                        return resolve({flag: "3"});
                    }else{
                        return resolve({flag: "1",iptvNumCheckFlag:"1"});
                    }
                }, () => {
                    return resolve({
                        flag: "2",
                        respMsg: res.respMsg
                    });
                });
            }).catch((e) => {
                alertError({
                    title: '出错了！',
                    message: e,
                    confirmButtonText: '报告错误'
                });
                return resolve({respMsg: e + "请稍后重试", flag: "2"});
            });
        })
    },


    orderCancelMsgRecall({commit, getters,dispatch}, queryParam){
        return new Promise((resolve, reject) => {
            if("50088"==getters.getReqDatas.senceNameCode){
                return resolve({flag: "2"});
            }
            else if("40188"==getters.getReqDatas.senceNameCode){
                return resolve({flag: "3"});
            }
            else{
                return resolve({flag: "1", cancelOrderReason:getters.getReqDatas.serviceReason,cancelOrderId:getters.getReqDatas.orderId});

            }
        })
    },
    orderCancelSubmit({commit, getters,dispatch}, queryParam){
        return new Promise((resolve, reject) => {
           let req=getters.getReqDatas;
            http.post('/mpComm/cancelOrder', req).then(res => {
                let mainNumVerifyFlag = 0
                commit('hideLoading');
                errorHandle(res, () => {
                    return resolve({flag: "1"});
                }, () => {
                    return resolve({
                        flag: "2",
                        respMsg: res.respMsg,
                    });
                });
            })
        })
    },

    // iptv宽带号码校验
    iptvNumCheck({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        return new Promise((resolve, reject) => {
            const iptvOrderData=getters.getIptvOrderData
            let serialNumber=queryParam.broadbandNum;
            if(serialNumber == '' || serialNumber == 'undefined' || serialNumber == undefined || serialNumber == null || serialNumber == 'null'){
                serialNumber=iptvOrderData.selectedNumber;
            }
            let req = {
                serialNumber: serialNumber
            }
            http.post('/iptvReceive/checkNumber', req).then(res => {
                let mainNumVerifyFlag = 0
                commit('hideLoading');
                errorHandle(res, () => {
                    iptvOrderData.checkNumberData=res.respData
                    iptvOrderData.checkNumberData.serialNumber=serialNumber
                    iptvOrderData.timeData.contactName=res.respData.showCustName
                    commit('setBlockShow',true)
                    dispatch('getHotWords','IptvNumMsgDetail')
                    commit('setIptvOrderData',iptvOrderData);
                    return resolve({flag: "1",iptvNumCheckFlag:"1"});
                }, () => {
                    return resolve({
                        flag: "2",
                        respMsg: res.respMsg,
                    });
                });
            })
        })
    },
    queryOrderDetails({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        return new Promise((resolve, reject) => {
            let req = {
                orderId: ''
            }
            return resolve({flag:"1"});
            // http.post('/Order/queryOrderDetailsWeb', req).then(res => {
            //     let mainNumVerifyFlag = 0
            //     commit('hideLoading');
            //     errorHandle(res, () => {
            //      
            //         return resolve({flag: "1",iptvNumCheckFlag:"1"});
            //     }, () => {
            //         return resolve({
            //             flag: "2",
            //             respMsg: res.respMsg,
            //         });
            //     });
            // })
        })
    },
    custOrderQuery({commit, getters,dispatch}, queryParam) {
        let req = {};
        let oneMonthAgo=new Date();
        const yearAgo = oneMonthAgo.getFullYear();
        const monthAgo = String(oneMonthAgo.getMonth() + 1).padStart(2, '0');
        const dayAgo = String(oneMonthAgo.getDate()).padStart(2, '0');
        const currentDate = `${yearAgo}-${monthAgo}-${dayAgo}`;
        dispatch('withInMonths',1);
        dispatch('getHotWords','custOrderQuery')
        let  formattedOneMonthAgo=getters.getFormatedDate;
        console.log(formattedOneMonthAgo,"formattedOneMonthAgo")
        const mqBookFlagData=getters.getMqBookFlagData
        console.log(mqBookFlagData.bookAddrTimeFlag)
        console.log(Object.keys(queryParam).length === 0)
        if(Object.keys(queryParam).length === 0){
            
            console.log('456')
            commit('setShowOrderList',false);
            req = {
                startDate:formattedOneMonthAgo,
                endDate:currentDate
            }
            }
        else{
            console.log('123')
           if(queryParam.bssOrderId!=null){
               req = {
                   sceneType: queryParam.sceneType==null?'':queryParam.sceneType,
                   serialNumber:queryParam.serialNumber==null?'':queryParam.serialNumber,
                   orderId:queryParam.bssOrderId==null?'':queryParam.bssOrderId,
                   orderState:queryParam.orderState==null?'':queryParam.orderState,
                   orderStateDetect:queryParam.orderStateDetect==null?'':queryParam.orderStateDetect
               }
           }else{
               req = {
                   sceneType: queryParam.sceneType==null?'':queryParam.sceneType,
                   serialNumber:queryParam.serialNumber==null?'':queryParam.serialNumber,
                   orderId:queryParam.bssOrderId==null?'':queryParam.bssOrderId,
                   startDate:queryParam.startDate==null?formattedOneMonthAgo:queryParam.startDate,
                   endDate:queryParam.endDate==null?currentDate:queryParam.endDate,
                   orderState:queryParam.orderState==null?'':queryParam.orderState,
                   orderStateDetect:queryParam.orderStateDetect==null?'':queryParam.orderStateDetect
               }
           }
        }
        commit('setCacheQryPara',req);
        mqBookFlagData.bookAddrTimeFlag="0";
        commit('setMqBookFlagData',mqBookFlagData);
        commit('showLoading');
        return new Promise((resolve, reject) => {
            http.post('/Order/custOrderQuery', req).then(res => {
                let mainNumVerifyFlag = 0
                commit('hideLoading');
                errorHandle(res, () => {
                    if(Object.keys(queryParam).length === 0){
                        commit('setShowOrderList',false);
                    }else{
                        commit('setShowOrderList',true);
                    }
                    commit('setOrderInfoLists',res.respData)
                    if(res.respData.length==0||res.respData==[]){
                        commit('setShowOrderList',false);
                        return resolve({
                            flag: "2",
                            respMsg: '查询结果为空',
                        });
                    }
                    else{
                        return resolve({
                            flag: "1"
                        });
                    }
                    
                }, () => {
                    commit('setShowOrderList',false);
                    commit('setOrderInfoLists',[])
                    return resolve({
                        flag: "2",
                        respMsg: res.respMsg,
                    });
                });
            })
        })
    },
    showHotWords({commit, getters,dispatch}, queryParam) {
        return new Promise((resolve, reject) => {
            commit('setBlockShow',true);
            const mqBookFlagData=getters.getMqBookFlagData
            mqBookFlagData.bookAddrTimeFlag="1";
            commit('setMqBookFlagData',mqBookFlagData);
            return resolve({flag: "1"});
        })
    },




    // 
    iptvCommInfoSubmit({commit, getters,dispatch}, queryParam) {
        // Toast.loading({
        //     icon:''
        // });
        let iptvCacheList=getters.getIptvCacheList;
        console.log("iptvCacheList.iptvChooseCommLists")
        console.log(iptvCacheList.iptvChooseCommLists)
        commit('showLoading');
        return new Promise((resolve, reject) => {
            if( iptvCacheList.iptvChooseCommLists.length<=0||iptvCacheList.iptvChooseCommLists == undefined || iptvCacheList.iptvChooseCommLists == null|| iptvCacheList.iptvChooseCommLists == []){
                Toast('请至少选择一个超清商品~')
                commit('hideLoading');
                return resolve({ flag: "2"});
            }
            else{
                for(let i=0;i<iptvCacheList.iptvChooseCommLists.length;i++){
                   dispatch('iptvClickFee',iptvCacheList.iptvChooseCommLists[i]);
                }
                commit('hideLoading');
                return resolve({ flag: "1"});
            }
        })
    },
    
    // iptv宽带号码校验
    bookTimeSubmit({commit, getters,dispatch}, queryParam) {
        
        commit('showLoading');
        return new Promise((resolve, reject) => {
            const mqBookFlagData=getters.getMqBookFlagData
            mqBookFlagData.bookAddrTimeFlag="0";
            commit('setMqBookFlagData',mqBookFlagData);
            commit('hideLoading');
            const iptvOrderData=getters.getIptvOrderData
            let contactName=iptvOrderData.timeData.contactName
            let contactPhone=iptvOrderData.timeData.contactPhone
            let stdBookDay=iptvOrderData.timeData.stdBookDay
            let stdBookTime=iptvOrderData.timeData.stdBookTime
            if (contactName == '' || contactName == 'undefined' || contactName == undefined || contactName == null || contactName == 'null') {
                Toast('请填写联系人')
                return resolve({ flag: "2"});
            }
            else  if (contactPhone == '' || contactPhone == 'undefined' || contactPhone == undefined || contactPhone == null || contactPhone == 'null') {
                Toast('请填写联系人电话')
                return resolve({ flag: "2"});
            }
            else if(("1"==iptvOrderData.choiceValue&&(stdBookDay == '' || stdBookDay == 'undefined' || stdBookDay == undefined || stdBookDay == null || stdBookDay == 'null') )||("1"==iptvOrderData.choiceValue && (stdBookTime == '' || stdBookTime == 'undefined' || stdBookTime == undefined || stdBookTime == null || stdBookTime == 'null') )){
                Toast('请选择预约时间段或日期')
                return resolve({ flag: "2"});
            }
            else{
                return resolve({flag: "1"});}
        })
    },
    isItMissing({commit, getters,dispatch}, queryParam){
        return new Promise((resolve, reject) => {
            let ywCodeString={iptvInfoList:[]};
            let ywCode1=[];
            let zwOrderData=getters.getZwOrderData
            let commodityChooseYWUlList=zwOrderData.zwGoodData.commodityChooseYWUlList
            let zwChooseCommLists=zwOrderData.zwGoodData.zwList
            if(commodityChooseYWUlList.length>0){
                for(let i=0;i<commodityChooseYWUlList.length;i++) {
                    let ywCodeInfo={};
                    ywCodeInfo={
                        iptvId: commodityChooseYWUlList[i].ywGoodId,
                        iptvName:commodityChooseYWUlList[i].ywGoodName
                    }
                    ywCode1.push(ywCodeInfo)
                }
            }
            ywCodeString.iptvInfoList=ywCode1
            let req = {
                goodCode:zwChooseCommLists[0].commodityCode,
                ywString:JSON.stringify(ywCodeString)
            }
            http.post('/zhzwZsd/isItMissing', req).then(res => {
                if(res.respCode=='0000'){
                    return resolve({flag: "1"});
                }
                else{
                    return resolve({flag: "2",respMsg:res.respMsg});
                }
            })
        })
    },
    hideHotWord({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        return new Promise((resolve, reject) => {
            commit('setRespTipArrQry',[])
            commit('setBlockShow',false)
            return resolve({flag:"1"});
        })
    },
    // iptv宽带号码校验
    iptvOrderfoSubmit({commit, getters,dispatch}, queryParam) {
        // Toast.loading({
        //     icon:''
        // });
        commit('showLoading');
        return new Promise((resolve, reject) => {
            let iptvInfoList=[];
            let iptvOrderData=getters.getIptvOrderData;
            if(iptvOrderData.iptvGoodData.iptvList.length>0){
                for(let i=0;i<iptvOrderData.iptvGoodData.iptvList.length;i++){
                    let iptv={
                        iptvFlg:iptvOrderData.iptvGoodData.iptvList[i].commodityCode,
                        iptvName:iptvOrderData.iptvGoodData.iptvList[i].iptvName,
                        goodsPrice:iptvOrderData.iptvGoodData.iptvList[i].goodsPrice,
                        goodsJMPrice:iptvOrderData.iptvGoodData.iptvList[i].goodsJMPrice,
                        commType:iptvOrderData.iptvGoodData.iptvList[i].commType
                    }
                    iptvInfoList.push(iptv)
                }
            }
            let ywfGoodInfoList=[];
            if(iptvOrderData.iptvGoodData.commodityChooseYWUlList.length>0){
                if(iptvOrderData.iptvGoodData.commodityChooseYWUlList[0].ywGoodId!='0'){
                    for(let i=0;i<iptvOrderData.iptvGoodData.commodityChooseYWUlList.length;i++){
                        let ywf={
                            ywfFlg:iptvOrderData.iptvGoodData.commodityChooseYWUlList[i].ywGoodId,
                            ywfName:iptvOrderData.iptvGoodData.commodityChooseYWUlList[i].ywGoodName,
                            goodsPrice:0
                        }
                        ywfGoodInfoList.push(ywf);
                    }

                }
                else{
                    let ywf={
                        ywfFlg:0,
                        ywfName:iptvOrderData.iptvGoodData.commodityChooseYWUlList[0].ywGoodName,
                        goodsPrice:0
                    };
                    ywfGoodInfoList.push(ywf);

                }
            }
            let  stdContact=iptvOrderData.timeData.contactName;
            if(stdContact == '' || stdContact == 'undefined' || stdContact == undefined || stdContact == null || stdContact == 'null'){
                stdContact=iptvOrderData.checkNumberData.custName
            }
            if(stdContact==iptvOrderData.checkNumberData.showCustName){
                stdContact=iptvOrderData.checkNumberData.custName
            }
            let communityAddrInfo={
                exchCode:iptvOrderData.checkNumberData.innerExchCode,
                addressName:iptvOrderData.checkNumberData.innerAddr,
                addressCode:iptvOrderData.checkNumberData.innerAddrCode,
                stdContact:stdContact,
                stdContactPhone:iptvOrderData.timeData.contactPhone,
                stdBookDay:iptvOrderData.timeData.stdBookDay,

                stdBookTime:iptvOrderData.timeData.stdBookTime
            }
            let rhRelationMemberMsg=iptvOrderData.checkNumberData;
            if("0"==iptvOrderData.checkNumberData.userTypeFlag){
                rhRelationMemberMsg={
                    userTypeFlag: iptvOrderData.checkNumberData.userTypeFlag
                }
            }
            let req={
                ywfGoodInfoList:ywfGoodInfoList,
                iptvInfoList:iptvInfoList,
                communityAddrInfo:communityAddrInfo,
                serviceNumber:iptvOrderData.checkNumberData.serviceNumber,
                choiceValue:iptvOrderData.choiceValue,
                rhRelationMemberMsg:rhRelationMemberMsg,
                orderPrice:iptvOrderData.orderPrice
            }
            http.post('/iptvReceive/newPreSubmit',req).then((res)=>{
                commit('hideLoading');
                if (res.respCode === '0000') {
                    iptvOrderData.orderId=res.respData.orderId;
                    commit('setIptvOrderData',iptvOrderData);
                    return resolve({flag: "1", respMsg: '订单提交成功!订单号'+res.respData.orderId});
                }
                else{
                    return resolve({flag: "2", respMsg: '订单提交失败'+res.respMsg});
                }
            })
        })
    },
    queryLatestBookDateZw({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        var date = new Date();
        var year = date.getFullYear();
        var month = Number(date.getMonth()) + 1;
        month = month >= 10 ? month : "0" + month;
        var day = date.getDate();
        day = day >= 10 ? day : "0" + day;
        let nowDate= year + "-" + month + "-" + day;
        dispatch('getHotWords','queryLatestBookDate')
        return new Promise((resolve, reject) => {
            let zwOrderData=getters.getZwOrderData
            let req = {
                siteCd:zwOrderData.checkNumberData.showExchCode,
                bookTime:nowDate,
                stdAddrId:zwOrderData.checkNumberData.showAddrCode
            }
            http.post('/iptvReceive/iptvBookingDate',
                req
            ).then(res=>{
                if(res.respCode=='0000'){
                    if(res.respData.length>0){
                        for(let i=0;i<res.respData.length;i++){
                            let c={};
                            if(i==0){
                                if(res.respData[i].flag==0){
                                    zwOrderData.timeData.stdBookDay=nowDate
                                    zwOrderData.timeData.stdBookTime="上午(9:00-12:00)"
                                    commit('setZwOrderData',zwOrderData)
                                    return resolve({flag:"1",bookLatestTime:nowDate+" 上午(9:00-12:00)"});
                                }else {
                                }
                            }
                            if(i==1){
                                if(res.respData[i].flag==0){
                                    zwOrderData.timeData.stdBookDay=nowDate
                                    zwOrderData.timeData.stdBookTime="下午(12:00-18:00)"
                                    commit('setZwOrderData',zwOrderData)

                                    return resolve({flag:"1",bookLatestTime:nowDate+" 下午(12:00-18:00)"});
                                }else {
                                }
                            }
                            if(i==2){
                                if(res.respData[i].flag==0){
                                    zwOrderData.timeData.stdBookDay=nowDate
                                    zwOrderData.timeData.stdBookTime="晚上(18:00-21:00)"
                                    commit('setZwOrderData',zwOrderData)
                                    return resolve({flag:"1",bookLatestTime:nowDate+" 晚上(18:00-21:00)"});
                                }else {
                                    const tomorrow = new Date(date);
                                    tomorrow.setDate(date.getDate() + 1);
                                    let tomorrowDate= tomorrow.toISOString().split('T')[0];
                                    let req1 = {
                                        siteCd:zwOrderData.checkNumberData.showExchCode,
                                        bookTime:tomorrowDate,
                                        stdAddrId:zwOrderData.checkNumberData.showAddrCode
                                    }
                                    http.post('/iptvReceive/iptvBookingDate',
                                        req1
                                    ).then(res=>{
                                        if(res.respCode=='0000'){
                                            for(let i=0;i<res.respData.length;i++){
                                                if(i==0){
                                                    if(res.respData[i].flag==0){
                                                        zwOrderData.timeData.stdBookDay=tomorrowDate
                                                        zwOrderData.timeData.stdBookTime="上午(9:00-12:00)"
                                                        commit('setZwOrderData',zwOrderData)
                                                        return resolve({flag:"1",bookLatestTime:tomorrowDate+" 上午(9:00-12:00)"});
                                                    }else {
                                                    }
                                                }

                                                if(i==1){
                                                    if(res.respData[i].flag==0){
                                                        zwOrderData.timeData.stdBookDay=tomorrowDate
                                                        zwOrderData.timeData.stdBookTime="下午(12:00-18:00)"
                                                        commit('setZwOrderData',zwOrderData)
                                                        return resolve({flag:"1",bookLatestTime:tomorrowDate+" 下午(12:00-18:00)"});
                                                    }else {

                                                    }
                                                }
                                                if(i==2){
                                                    if(res.respData[i].flag==0){
                                                        zwOrderData.timeData.stdBookDay=tomorrowDate
                                                        zwOrderData.timeData.stdBookTime="晚上(18:00-21:00)"
                                                        commit('setZwOrderData',zwOrderData)
                                                        return resolve({flag:"1",bookLatestTime:tomorrowDate+" 晚上(18:00-21:00)"});
                                                    }else{
                                                        return resolve({flag:"2",bookLatestTime:""});
                                                    }
                                                }
                                            }
                                        }})
                                }
                            }
                        }

                    }
                }
            })
        })
    },
    
    defaultTimeTagChangeZw({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        return new Promise((resolve, reject) => {
            const mqBookFlagData=getters.getMqBookFlagData
            mqBookFlagData.bookAddrTimeFlag="1";
            commit('setMqBookFlagData',mqBookFlagData);
            return resolve({flag:"1"});
        })
    },
    ZwReSelectDate({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        dispatch('getHotWords','IptvReSelectDate')
        let zwOrderData=getters.getZwOrderData

        return new Promise((resolve, reject) => {
           if(queryParam.bookDay!=null&&queryParam.bookDay!=''){
               let req = {
                   siteCd:zwOrderData.checkNumberData.showExchCode,
                   bookTime:queryParam.bookDay,
                   stdAddrId:zwOrderData.checkNumberData.showAddrCode
               }
               let bookTimeS='';
               http.post('/iptvReceive/iptvBookingDate',
                   req
               ).then(res=>{
                   commit('hideLoading');
                   if (res.respCode === '0000') {
                       if("0"==res.respData[Number(queryParam.bookTime)-1].flag){
                           if(1==Number(queryParam.bookTime)){
                               bookTimeS='上午(9:00-12:00)'
                           }
                           if(2==Number(queryParam.bookTime)){
                               bookTimeS='下午(12:00-18:00)'
                           }
                           if(3==Number(queryParam.bookTime)){
                               bookTimeS='晚上(18:00-21:00)'
                           }
                           zwOrderData.timeData.stdBookDay=queryParam.bookDay
                           zwOrderData.timeData.stdBookTime=bookTimeS
                           commit('setZwOrderData',zwOrderData)
                           return resolve({flag:"1",bookReSelectTime:queryParam.bookDay+" "+bookTimeS});
                       }else{
                           return resolve({flag: "2", respMsg: "该时间段不可预约，请重新选择"});
                       }
                   }
                   else{
                       return resolve({flag: "2", respMsg: res.respMsg});
                   }
               })
           }else{
               return resolve({flag:"1",bookReSelectTime: zwOrderData.timeData.stdBookDay+" "+zwOrderData.timeData.stdBookTime});
           }
        })
    },
    queryContactMsgZw({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        return new Promise((resolve, reject) => {
            let zwOrderData=getters.getZwOrderData
            let contactPhone=zwOrderData.timeData.contactPhone
            if(contactPhone == '' || contactPhone == 'undefined' || contactPhone == undefined || contactPhone == null || contactPhone == 'null'){
                let req={
                    serialNumber:    zwOrderData.checkNumberData.serialNumber
                }
                http.post('/mpComm/mpRhQueryByBroadNum',req).then((res)=>{
                    commit('hideLoading');
                    if (res.respCode === '0000') {
                        const zwOrderData=getters.getZwOrderData
                       if(res.respData.kdNumList.length==0){
                           commit('setRespTipArrQry',[]);
                           commit('setBlockShow',false)
                           return resolve({flag:"2",respMsg: "未找到联系电话，请输入"});
                       }else{
                           zwOrderData.timeData.contactPhone=res.respData.kdNumList[0].ywSerialNumber
                           commit('setZwOrderData',zwOrderData)
                           dispatch('getHotWords','queryContactMsg')
                           return resolve({flag:"1",contactQueryPhone:res.respData.kdNumList[0].ywSerialNumber});
                       }
                    }
                    else{
                        commit('setRespTipArrQry',[]);
                        commit('setBlockShow',false)
                        return resolve({flag:"2",respMsg: res.respMsg});
                    }
                })
            }else{
                return resolve({flag:"1",contactQueryPhone:zwOrderData.timeData.contactPhone});
            }

        })
    },
    contactMsgSubmitZw({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        commit('setRespTipArrQry',[])
        commit('setBlockShow',false)
        return new Promise((resolve, reject) => {
            let zwOrderData=getters.getZwOrderData
            let contactPhone=queryParam.contactPhone
            let contactName=queryParam.contactName
                if(contactPhone == '' || contactPhone == 'undefined' || contactPhone == undefined || contactPhone == null || contactPhone == 'null'){
                }
                else{
                    zwOrderData.timeData.contactPhone=queryParam.contactPhone
                    commit('setZwOrderData',zwOrderData)
                }
            if(contactName == '' || contactName == 'undefined' || contactName == undefined || contactName == null || contactName == 'null'){
            }
            else{
                zwOrderData.timeData.contactName=queryParam.contactName
                commit('setZwOrderData',zwOrderData)
            }
            return resolve({flag:"1"});
        })
    },
    getProductDataZw({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        return new Promise((resolve, reject) => {
            let zwOrderData=getters.getZwOrderData
            if('ZwGoods'==queryParam.goodsType){
                let req1={
                    isRHUserTag:zwOrderData.checkNumberData.isRHUserTag,
                    onlineMonthsKd:zwOrderData.checkNumberData.onlineMonthsKd,
                    onlineMonthsYw:zwOrderData.checkNumberData.onlineMonths,
                    priceLevel:zwOrderData.checkNumberData.priceLevel,
                    fttrTransFlag:zwOrderData.checkNumberData.fttrTransFlag
                    
                }
                if(queryParam.bmGoodsType!=null){
                    req1={
                        catId:queryParam.bmGoodsType,
					   isRHUserTag:zwOrderData.checkNumberData.isRHUserTag,
                        onlineMonthsKd:zwOrderData.checkNumberData.onlineMonthsKd,
                        onlineMonthsYw:zwOrderData.checkNumberData.onlineMonths,
                        priceLevel:zwOrderData.checkNumberData.priceLevel,
                        fttrTransFlag:zwOrderData.checkNumberData.fttrTransFlag
                }}
                dispatch('getHotWords','ZwSelectProduct')
                http.post('/zhzwZsd/qryProductListZsd', req1).then(res => {
                    commit('hideLoading')
                    if (res.respCode === '0000') {
                        let iptvReProductList=res.respData.zwProList
                        iptvReProductList.sort((a, b) => {
                            const hasPropertyA = 'RENT' in a;
                            const hasPropertyB = 'RENT' in b;
                            // 如果 a 有属性而 b 没有，a 应该在前面
                            if (hasPropertyA && !hasPropertyB) {
                                return -1;
                            }
                            // 如果 b 有属性而 a 没有，b 应该在前面
                            if (!hasPropertyA && hasPropertyB) {
                                return 1;
                            }
                            // 如果两者都有或都没有，保持原有顺序（或根据其他条件排序）
                            return 0;
                    })
                        commit('setZwReProductList',iptvReProductList)
                        return resolve({flag:"1"});}
                    else{
                        return resolve({flag:"2",respMsg: res.respMsg});}
                })}
            
            else{
               if(zwOrderData.zwGoodData.zwList.length<=0){
                   return resolve({flag:"3"});
               }else{
                   let req = {
                       iptvCode:zwOrderData.zwGoodData.zwList[0].commodityCode
                   }
                   http.post('/zhzwZsd/goodQryRelateYw', req).then(res => {
                       commit('hideLoading')
                       let commodityYWUlList=[]
                       if (res.respData.code === '0000') {
                           if(res.respData.ywFreeGoodsListResult.length>0){
                               if( zwOrderData.checkNumberData.isRHUserTag=='1'){
                                   dispatch('getHotWords','ZwYwSelectProduct')
                                   commit('setZwReYwProductList',res.respData.ywFreeGoodsListResult)
                                   return resolve({flag:"1"});}
                               else{
                                   commit('setZwReYwProductList',[])
                                   return resolve({flag:"2"});
                               }
                           }else{
                               return resolve({flag:"2"});
                           }
                       }
                       else{
                           return resolve({flag:"2",respMsg: res.respMsg});
                       }
                   })
               }
            }
        })
    },
    // zw宽带号码校验
    zwBroadbandNumCheck({commit, getters,dispatch}, queryParam) {
        // Toast.loading({
        //     icon:''
        // });
        commit('showLoading');
        const zwOrderData=getters.getZwOrderData
        let serialNumber=queryParam.broadbandNum;
        if(serialNumber == '' || serialNumber == 'undefined' || serialNumber == undefined || serialNumber == null || serialNumber == 'null'){
            serialNumber=zwOrderData.selectedNumber;
        }
        
        return new Promise((resolve, reject) => {
            let req = {
                serialNumber: serialNumber
            }
            http.post('/zhzwZsd/checkNumberZsd', req).then(res => {
                console.log(queryParam.broadbandNum,"queryParam.broadbandNum");

                let mainNumVerifyFlag = 0
                commit('hideLoading');
                errorHandle(res, () => {
                    commit('setBlockShow',true)
                    dispatch('getHotWords','IptvNumMsgDetail')
                    zwOrderData.checkNumberData=res.respData.showJson
                    zwOrderData.checkNumberData.serialNumber=serialNumber
                    zwOrderData.timeData.contactName=res.respData.showJson.showCustName
                    commit('setZwOrderData',zwOrderData);
                    console.log("",zwOrderData.checkNumberData.serialNumber)
                    if(res.respData.showJson.changeFuseUpFttrTag=="1"){
                        return resolve({flag: "2",iptvNumCheckFlag:"2"});
                    }else {
                        return resolve({flag: "1",iptvNumCheckFlag:"1"});
                    }
                }, () => {
                    
                    return resolve({
                        flag: "2",
                        respMsg: res.respMsg,
                    });
                });
            })
        })
    },
    zwSwitchToFusionUpFttr({ commit, getters, dispatch }, queryParam){
        return new Promise((resolve, reject) => {
            return resolve({
                flag: "1",
                serialNumber:getters.getZwOrderData.checkNumberData.serialNumber
            });
        })
    },
    // zw
    zwCommInfoSubmit({commit, getters,dispatch}, queryParam) {
        // Toast.loading({
        //     icon:''
        // });
        commit('showLoading');
        return new Promise((resolve, reject) => {
           
            const zwCacheList=getters.getZwCacheList;
            if( zwCacheList.zwChooseCommLists.length<=0 ||zwCacheList.zwChooseCommLists == undefined ||zwCacheList.zwChooseCommLists == null){
                Toast('请选择一个组网商品~')
                commit('hideLoading');
                return resolve({ flag: "2"});
            }
            
            else{
                if(zwCacheList.zwChooseCommLists.length>0){
                    for(let i=0;i<zwCacheList.zwChooseCommLists.length;i++){
                       dispatch('zwClickFee',zwCacheList.zwChooseCommLists[i])
                    }
                    commit('hideLoading');
                }
                commit('hideLoading');
                return resolve({ flag: "1"});
            }
            
        })
    },
    hideContactNodeWords({commit, getters,dispatch}, queryParam) {
        return new Promise((resolve, reject) => {
               commit('setRespTipArrQry',[]);
               commit('setBlockShow',false)
                return resolve({flag: "1"});
        })
    },
    // zw信息地址
    zwBookTimeSubmit({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        return new Promise((resolve, reject) => {
            const mqBookFlagData=getters.getMqBookFlagData
            mqBookFlagData.bookAddrTimeFlag="0";
            commit('setMqBookFlagData',mqBookFlagData);
            commit('hideLoading');
            const zwOrderData=getters.getZwOrderData
            let contactName=zwOrderData.timeData.contactName
            let contactPhone=zwOrderData.timeData.contactPhone
            let stdBookDay=zwOrderData.timeData.stdBookDay
            let stdBookTime=zwOrderData.timeData.stdBookTime
            if (contactName == '' || contactName == 'undefined' || contactName == undefined || contactName == null || contactName == 'null') {
                Toast('请填写联系人')
                return resolve({flag: "2"});
            }
            else  if (contactPhone == '' || contactPhone == 'undefined' || contactPhone == undefined || contactPhone == null || contactPhone == 'null') {
                Toast('请填写联系人电话')
                return resolve({ flag: "2"});
            }
            else if(("1"==zwOrderData.choiceValue&&(stdBookDay == '' || stdBookDay == 'undefined' || stdBookDay == undefined || stdBookDay == null || stdBookDay == 'null') )||("1"==zwOrderData.choiceValue && (stdBookTime == '' || stdBookTime == 'undefined' || stdBookTime == undefined || stdBookTime == null || stdBookTime == 'null') )){
                Toast('请选择预约时间段或日期')
                return resolve({ flag: "2"});
            }
            else{
                
                return resolve({flag: "1"});
            }
        })
    },

    // 组网
    zwOrderfoSubmit({commit, getters,dispatch}, queryParam) {
        commit('showLoading');

        let zwOrderData=getters.getZwOrderData;
        let zwInfoList={};
        if(zwOrderData.zwGoodData.zwList.length>0){
            let zw={
                commodityCode:zwOrderData.zwGoodData.zwList[0].commodityCode,
                commodityName:zwOrderData.zwGoodData.zwList[0].zwName,
            }
            zwInfoList=zw;
        }
        let ywfGoodInfoList=[];
        if(zwOrderData.zwGoodData.commodityChooseYWUlList.length>0){
            if(zwOrderData.zwGoodData.commodityChooseYWUlList[0].ywGoodId!='0'){
                for(let i=0;i<zwOrderData.zwGoodData.commodityChooseYWUlList.length;i++){
                    let ywf={
                        ywfFlg:zwOrderData.zwGoodData.commodityChooseYWUlList[i].ywGoodId,
                        ywfName:zwOrderData.zwGoodData.commodityChooseYWUlList[i].ywGoodName,
                        goodsPrice:0
                    }
                    ywfGoodInfoList.push(ywf);
                }

            }
            else{
                let ywf={
                    ywfFlg:0,
                    ywfName:zwOrderData.zwGoodData.commodityChooseYWUlList[0].ywGoodName,
                    goodsPrice:0
                };
                ywfGoodInfoList.push(ywf);
            }
        }
        let  stdContact=zwOrderData.timeData.contactName;
        if(stdContact == '' || stdContact == 'undefined' || stdContact == undefined || stdContact == null || stdContact == 'null'){
            stdContact=zwOrderData.checkNumberData.custName
        }
        if(stdContact==zwOrderData.checkNumberData.showCustName){
            stdContact=zwOrderData.checkNumberData.custName
        }
        let communityAddrInfo={
            exchCode:zwOrderData.checkNumberData.showExchCode,
            addressName:zwOrderData.checkNumberData.showInstallAddr,
            addressCode:zwOrderData.checkNumberData.showAddrCode,
            stdContact:stdContact,
            stdContactPhone:zwOrderData.timeData.contactPhone,
            stdBookDay:zwOrderData.timeData.stdBookDay,

            stdBookTime:zwOrderData.timeData.stdBookTime
        }
        let rhRelationMemberMsg={
            userTypeFlag:zwOrderData.checkNumberData.isRHUserTag,
            phoneSerialNumber:zwOrderData.checkNumberData.phoneSerialNumber,
            ywProductName:zwOrderData.checkNumberData.ywProductName,
            ywProductId:zwOrderData.checkNumberData.ywProductId,
            rhSerialNumber:zwOrderData.checkNumberData.rhSerialNumber,
            rhProductId:zwOrderData.checkNumberData.rhProductId ,
            rhProductName:zwOrderData.checkNumberData.rhProductName ,
            mixTypeCode:zwOrderData.checkNumberData.mixTypeCode ,
            kdProductId:zwOrderData.checkNumberData.kdProductId ,
            kdProductName:zwOrderData.checkNumberData.kdProductName
        }
        if("0"==zwOrderData.checkNumberData.isRHUserTag){
            rhRelationMemberMsg={
                userTypeFlag: wOrderData.checkNumberData.isRHUserTag
            }
        }
        let req={
            ywfGoodInfoList:ywfGoodInfoList,
            changeCommodityInfo:zwInfoList,
            communityAddrInfo:communityAddrInfo,
            serviceNumber:zwOrderData.checkNumberData.serialNumber,
            choiceValue:zwOrderData.choiceValue,
            rhRelationMemberMsg:rhRelationMemberMsg,
            orderPrice:zwOrderData.orderPrice,
            remark:"备注:"+zwOrderData.remark+";预约时间:"+zwOrderData.timeData.stdBookDay+" "+zwOrderData.timeData.stdBookTime
        }
        http.post('/zhzwZsd/preSubmitZsd',req).then((res)=>{
            commit('hideLoading');
            if (res.respCode === '0000') {
                zwOrderData.orderId=res.respData.orderId;
                commit('setZwOrderData',zwOrderData);
                return resolve({flag: "1", respMsg: '订单提交成功!订单号'+res.respData.orderId});
            }
            else{
                return resolve({flag: "2", respMsg: '订单提交失败'+res.respMsg});
            }
        })
    },


    queryLatestBookDateBroadUp({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        var date = new Date();
        var year = date.getFullYear();
        var month = Number(date.getMonth()) + 1;
        month = month >= 10 ? month : "0" + month;
        var day = date.getDate();
        day = day >= 10 ? day : "0" + day;
        let nowDate= year + "-" + month + "-" + day;
        dispatch('getHotWords','queryLatestBookDate')
        return new Promise((resolve, reject) => {
            let broadUpOrderData=getters.getBroadUpOrderData
            let req = {
                siteCd:broadUpOrderData.checkNumberData.showExchCode,
                bookTime:nowDate,
                stdAddrId:broadUpOrderData.checkNumberData.showAddrCode
            }
            http.post('/iptvReceive/iptvBookingDate',
                req
            ).then(res=>{
                if(res.respCode=='0000'){
                    if(res.respData.length>0){
                        for(let i=0;i<res.respData.length;i++){
                            let c={};
                            if(i==0){
                                if(res.respData[i].flag==0){
                                    broadUpOrderData.timeData.stdBookDay=nowDate
                                    broadUpOrderData.timeData.stdBookTime="上午(9:00-12:00)"
                                    commit('setBroadUpOrderData',broadUpOrderData)
                                    return resolve({flag:"1",bookLatestTime:nowDate+" 上午(9:00-12:00)"});
                                }else {
                                }
                            }
                            if(i==1){
                                if(res.respData[i].flag==0){
                                    broadUpOrderData.timeData.stdBookDay=nowDate
                                    broadUpOrderData.timeData.stdBookTime="下午(12:00-18:00)"
                                    commit('setBroadUpOrderData',broadUpOrderData)

                                    return resolve({flag:"1",bookLatestTime:nowDate+" 下午(12:00-18:00)"});
                                }else {
                                }
                            }
                            if(i==2){
                                if(res.respData[i].flag==0){
                                    broadUpOrderData.timeData.stdBookDay=nowDate
                                    broadUpOrderData.timeData.stdBookTime="晚上(18:00-21:00)"
                                    commit('setBroadUpOrderData',broadUpOrderData)
                                    return resolve({flag:"1",bookLatestTime:nowDate+" 晚上(18:00-21:00)"});
                                }else {
                                    const tomorrow = new Date(date);
                                    tomorrow.setDate(date.getDate() + 1);
                                    let tomorrowDate= tomorrow.toISOString().split('T')[0];
                                    let req1 = {
                                        siteCd:broadUpOrderData.checkNumberData.showExchCode,
                                        bookTime:tomorrowDate,
                                        stdAddrId:broadUpOrderData.checkNumberData.showAddrCode
                                    }
                                    http.post('/iptvReceive/iptvBookingDate',
                                        req1
                                    ).then(res=>{
                                        if(res.respCode=='0000'){
                                            for(let i=0;i<res.respData.length;i++){
                                                if(i==0){
                                                    if(res.respData[i].flag==0){
                                                        broadUpOrderData.timeData.stdBookDay=tomorrowDate
                                                        broadUpOrderData.timeData.stdBookTime="上午(9:00-12:00)"
                                                        commit('setBroadUpOrderData',broadUpOrderData)
                                                        return resolve({flag:"1",bookLatestTime:tomorrowDate+" 上午(9:00-12:00)"});
                                                    }else {
                                                    }
                                                }

                                                if(i==1){
                                                    if(res.respData[i].flag==0){
                                                        broadUpOrderData.timeData.stdBookDay=tomorrowDate
                                                        broadUpOrderData.timeData.stdBookTime="下午(12:00-18:00)"
                                                        commit('setBroadUpOrderData',broadUpOrderData)
                                                        return resolve({flag:"1",bookLatestTime:tomorrowDate+" 下午(12:00-18:00)"});
                                                    }else {

                                                    }
                                                }
                                                if(i==2){
                                                    if(res.respData[i].flag==0){
                                                        broadUpOrderData.timeData.stdBookDay=tomorrowDate
                                                        broadUpOrderData.timeData.stdBookTime="晚上(18:00-21:00)"
                                                        commit('setBroadUpOrderData',broadUpOrderData)
                                                        return resolve({flag:"1",bookLatestTime:tomorrowDate+" 晚上(18:00-21:00)"});
                                                    }else{
                                                        return resolve({flag:"2",bookLatestTime:""});
                                                    }
                                                }
                                            }
                                        }})
                                }
                            }
                        }

                    }
                }
            })
        })
    },

    defaultTimeTagChangeBroadUp({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        return new Promise((resolve, reject) => {
            const mqBookFlagData=getters.getMqBookFlagData
            mqBookFlagData.bookAddrTimeFlag="1";
            commit('setMqBookFlagData',mqBookFlagData);
            return resolve({flag:"1"});
        })
    },
    broadUpReSelectDate({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        dispatch('getHotWords','IptvReSelectDate')
        let broadUpOrderData=getters.getBroadUpOrderData

        return new Promise((resolve, reject) => {
            if(queryParam.bookDay!=null&&queryParam.bookDay!=''){
                let req = {
                    siteCd:broadUpOrderData.checkNumberData.showExchCode,
                    bookTime:queryParam.bookDay,
                    stdAddrId:broadUpOrderData.checkNumberData.showAddrCode
                }
                let bookTimeS='';
                http.post('/iptvReceive/iptvBookingDate',
                    req
                ).then(res=>{
                    commit('hideLoading');
                    if (res.respCode === '0000') {
                        if("0"==res.respData[Number(queryParam.bookTime)-1].flag){
                            if(1==Number(queryParam.bookTime)){
                                bookTimeS='上午(9:00-12:00)'
                            }
                            if(2==Number(queryParam.bookTime)){
                                bookTimeS='下午(12:00-18:00)'
                            }
                            if(3==Number(queryParam.bookTime)){
                                bookTimeS='晚上(18:00-21:00)'
                            }
                            broadUpOrderData.timeData.stdBookDay=queryParam.bookDay
                            broadUpOrderData.timeData.stdBookTime=bookTimeS
                            commit('setBroadUpOrderData',broadUpOrderData)
                            return resolve({flag:"1",bookReSelectTime:queryParam.bookDay+" "+bookTimeS});
                        }else{
                            return resolve({flag: "2", respMsg: "该时间段不可预约，请重新选择"});
                        }
                    }
                    else{
                        return resolve({flag: "2", respMsg: res.respMsg});
                    }
                })
            }else{
                return resolve({flag:"1",bookReSelectTime: broadUpOrderData.timeData.stdBookDay+" "+broadUpOrderData.timeData.stdBookTime});
            }
        })
    },
    queryContactMsgBroadUp({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        return new Promise((resolve, reject) => {
            let broadUpOrderData=getters.getBroadUpOrderData
            let contactPhone=broadUpOrderData.timeData.contactPhone
            if("1"==broadUpOrderData.checkNumberData.isRHUserTag){
                if(contactPhone == '' || contactPhone == 'undefined' || contactPhone == undefined || contactPhone == null || contactPhone == 'null'){
                    let req={
                        serialNumber:    broadUpOrderData.checkNumberData.serialNumber
                    }
                    http.post('/mpComm/mpRhQueryByBroadNum',req).then((res)=>{
                        commit('hideLoading');
                        if (res.respCode === '0000') {
                            const broadUpOrderData=getters.getBroadUpOrderData
                            if(res.respData.kdNumList.length==0){
                                commit('setRespTipArrQry',[]);
                                commit('setBlockShow',false)
                                return resolve({flag:"2",respMsg: "未找到联系电话，请输入",contactQueryPhone:""});
                            }else{
                                broadUpOrderData.timeData.contactPhone=res.respData.kdNumList[0].ywSerialNumber
                                commit('setBroadUpOrderData',broadUpOrderData)
                                dispatch('getHotWords','queryContactMsg')
                                return resolve({flag:"1",contactQueryPhone:res.respData.kdNumList[0].ywSerialNumber});
                            }
                        }
                        else{
                            commit('setRespTipArrQry',[]);
                            commit('setBlockShow',false)
                            return resolve({flag:"2",respMsg: res.respMsg,contactQueryPhone:""});
                        }
                    })

                }else{
                    return resolve({flag:"1",contactQueryPhone:broadUpOrderData.timeData.contactPhone});
                }
            }else{
                return resolve({flag:"1",contactQueryPhone:broadUpOrderData.timeData.contactPhone});
            }

        })
    },
    wessageMsgSubmitBroadUp({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        return new Promise((resolve, reject) => {
            let broadUpOrderData=getters.getBroadUpOrderData
            broadUpOrderData.timeData.contactPhone=queryParam.contactPhone
            commit('setBroadUpOrderData',broadUpOrderData)
            return resolve({flag:"1"});
        })
    },
    contactMsgSubmitBroadUp({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        commit('setRespTipArrQry',[])
        commit('setBlockShow',false)
        return new Promise((resolve, reject) => {
            let broadUpOrderData=getters.getBroadUpOrderData
            let contactPhone=queryParam.contactPhone
            let contactName=queryParam.contactName
            if(contactPhone == '' || contactPhone == 'undefined' || contactPhone == undefined || contactPhone == null || contactPhone == 'null'){
            }
            else{
                broadUpOrderData.timeData.contactPhone=queryParam.contactPhone
                commit('setBroadUpOrderData',broadUpOrderData)
            }
            if(contactName == '' || contactName == 'undefined' || contactName == undefined || contactName == null || contactName == 'null'){
            }
            else{
                broadUpOrderData.timeData.contactName=queryParam.contactName
                commit('setBroadUpOrderData',broadUpOrderData)
            }
            return resolve({flag:"1"});
        })
    },

    qryBroadPhoneByCertCodeBroadUp({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        return new Promise((resolve, reject) => {
            let req = {
                psptId: queryParam.checkCertNumber
            }
            http.post('/mpComm/qryBroadPhoneByAllUsingUser', req).then(res => {
                let mainNumVerifyFlag = 0
                commit('hideLoading');
                errorHandle(res, () => {
                    const broadUpOrderData=getters.getBroadUpOrderData
                    broadUpOrderData.timeData.contactPhone=queryParam.phoneSerialNumber
                    broadUpOrderData.broadNumberByYw=res.respData.kdNumList
                    commit('setIptvOrderData',broadUpOrderData)
                    if(res.respData.kdNumList.length==1){
                        broadUpOrderData.selectedNumber=res.respData.kdNumList[0].kdSerialNumber
                        commit('setBroadUpOrderData',broadUpOrderData)
                        return resolve({flag: "3"});
                    }else{
                        return resolve({flag: "1",iptvNumCheckFlag:"1"});
                    }
                }, () => {
                    return resolve({
                        flag: "2",
                        respMsg: res.respMsg
                    });});
            }).catch((e) => {
                alertError({
                    title: '出错了！',
                    message: e,
                    confirmButtonText: '报告错误'
                });
                return resolve({respMsg: e + "请稍后重试", flag: "2"});
            });
        })
    },

    getProductDataBroadUp({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        return new Promise((resolve, reject) => {
            let broadUpOrderData=getters.getBroadUpOrderData
                let req1={
                    onlineMonthsKd:broadUpOrderData.checkNumberData.onlineMonthsKd,
                    onlineMonthsYw:broadUpOrderData.checkNumberData.onlineMonthsYw,
                    priceLevel:broadUpOrderData.checkNumberData.priceLevel,
                    kdSpeed:broadUpOrderData.checkNumberData.kdSpeed
                }
                if(queryParam.bmGoodsType!=null){
                    req1={
                        catId:queryParam.bmGoodsType,
                        onlineMonthsKd:broadUpOrderData.checkNumberData.onlineMonthsKd,
                        onlineMonthsYw:broadUpOrderData.checkNumberData.onlineMonthsYw,
                        priceLevel:broadUpOrderData.checkNumberData.priceLevel,
                        kdSpeed:broadUpOrderData.checkNumberData.kdSpeed

                    }}
                http.post('/broadbandUpSpeed/queryProduct', req1).then(res => {
                    commit('hideLoading')
                    if (res.respCode === '0000') {
                        let iptvReProductList=res.respData
                        iptvReProductList.sort((a, b) => {
                            const hasPropertyA = 'RENT' in a;
                            const hasPropertyB = 'RENT' in b;
                            // 如果 a 有属性而 b 没有，a 应该在前面
                            if (hasPropertyA && !hasPropertyB) {
                                return -1;
                            }
                            // 如果 b 有属性而 a 没有，b 应该在前面
                            if (!hasPropertyA && hasPropertyB) {
                                return 1;
                            }
                            // 如果两者都有或都没有，保持原有顺序（或根据其他条件排序）
                            return 0;
                        })
                        commit('setBroadUpReProductList',iptvReProductList)
                        return resolve({flag:"1"});}
                    else{
                        return resolve({flag:"2",respMsg: res.respMsg});}
                })}
        )
    },

    broadUpBroadNumCheck({commit, getters,dispatch}, queryParam) {
        // Toast.loading({
        //     icon:''
        // });
        commit('showLoading');
        const broadUpOrderData=getters.getBroadUpOrderData
        let serialNumber=queryParam.qrySerialNumber;
        return new Promise((resolve, reject) => {
             serialNumber=queryParam.qrySerialNumber;
           let selectedNumber= broadUpOrderData.selectedNumber
            let req = {
                serialNumber: serialNumber,
                phoneType: queryParam.phoneType
            }
            if (selectedNumber == '' || selectedNumber == 'undefined' || selectedNumber == undefined || selectedNumber == null || selectedNumber == 'null') {
            }else{
                req = {
                    serialNumber: selectedNumber,
                    phoneType: "1"
                }
            }

            http.post('/broadbandUpSpeed/checkNumber', req).then(res => {
                console.log(queryParam.broadbandNum,"queryParam.broadbandNum");

                let mainNumVerifyFlag = 0
                commit('hideLoading');
                errorHandle(res, () => {
                    commit('setBlockShow',true)
                    dispatch('getHotWords','IptvNumMsgDetail')
                    broadUpOrderData.checkNumberData=res.respData.showJson
                    broadUpOrderData.checkNumberData.kdSpeed=res.respData.kdSpeed
                    if (res.respData.showJson.phoneSerialNumberBtm == '' || res.respData.showJson.phoneSerialNumberBtm == 'undefined' || res.respData.showJson.phoneSerialNumberBtm == undefined ||res.respData.showJson.phoneSerialNumberBtm  == null || res.respData.showJson.phoneSerialNumberBtm== 'null') {
                    }else{
                        broadUpOrderData.timeData.contactName=res.respData.showJson.showCustName
                    }
                    broadUpOrderData.timeData.contactPhone=decrypted(res.respData.showJson.phoneSerialNumberBtm)
                    if (selectedNumber == '' || selectedNumber == 'undefined' || selectedNumber == undefined || selectedNumber == null || selectedNumber == 'null') {
                        broadUpOrderData.checkNumberData.serialNumber=serialNumber;
                    }else{
                        broadUpOrderData.checkNumberData.serialNumber=selectedNumber;
                    }
                    if("0"==res.respData.showJson.isRHUserTag){
                        broadUpOrderData.checkNumberData.phoneSerialNumberBtm="";
                        broadUpOrderData.checkNumberData.phoneSerialNumber="";
                    }
                    commit('setBroadUpOrderData',broadUpOrderData);
                    console.log("",broadUpOrderData.checkNumberData.serialNumber)
                    return resolve({flag: "1",isRHUserTag:res.respData.showJson.isRHUserTag});
                }, () => {

                    broadUpOrderData.checkNumberData={}
                    commit('setBroadUpOrderData',broadUpOrderData);
                    return resolve({
                        flag: "2",
                        respMsg: res.respMsg,
                        isRHUserTag:"0"
                    });
                });
            })
        })
    },
    broadUpCommInfoSubmit({commit, getters,dispatch}, queryParam) {
        // Toast.loading({
        //     icon:''
        // });
        commit('showLoading');
        return new Promise((resolve, reject) => {
            const broadUpOrderData=getters.getBroadUpOrderData;
            if( broadUpOrderData.broadUpGoodData.broadUpList.length<=0 ||broadUpOrderData.broadUpGoodData.broadUpList == undefined ||broadUpOrderData.broadUpGoodData.broadUpList == null){
                commit('hideLoading');
                return resolve({ flag: "3", numberIsSendFlag:"1"});
            }
            else{
                let kdSpeedNew =broadUpOrderData.broadUpGoodData.broadUpList[0].kdSpeed
                console.log(broadUpOrderData.broadUpGoodData.broadUpList[0],"broadUpOrderData.broadUpGoodData.broadUpList[0].kdSpeed")
                let req={
                    kdSpeedNew:kdSpeedNew,
                    kdSpeed:broadUpOrderData.checkNumberData.kdSpeed,
                    serialNumber:decrypted(getters.getBroadUpOrderData.checkNumberData.kdNumberJm)
                }
                commit('hideLoading');
                http.post('/broadbandUpSpeed/qryBookIsSend', req).then(res => {
                    let mainNumVerifyFlag = 0
                    commit('hideLoading');
                    errorHandle(res, () => {
                            broadUpOrderData.checkNumberData.isBookingTimeFlagNew=res.respData.isBookingTimeFlagNew
                            commit('setBroadUpOrderData',broadUpOrderData);
                            return resolve({flag: "1",numberIsSendFlag:res.respData.isBookingTimeFlagNew});
                    },
                        () => {
                            broadUpOrderData.checkNumberData.isBookingTimeFlagNew="1"
                            commit('setBroadUpOrderData',broadUpOrderData);
                        return resolve({
                            flag: "1",
                           numberIsSendFlag:"1"});
                        });});
            }

        })
    },
    broadUpQryIsHandleFlag({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        return new Promise((resolve, reject) => {
            commit('hideLoading');
            const broadUpOrderData=getters.getBroadUpOrderData;
            http.post('/broadbandUpSpeed/qryIsHandleSwitch', {}).then(res => {
                let mainNumVerifyFlag = 0
                commit('hideLoading');
                errorHandle(res, () => {
                        broadUpOrderData.checkNumberData.isHandleFlag=res.respData;
                        commit('setBroadUpOrderData',broadUpOrderData)
                        return resolve({flag: "1",isHandleFlag:res.respData});
                    },
                    () => {
                        broadUpOrderData.checkNumberData.isHandleFlag="0";
                        commit('setBroadUpOrderData',broadUpOrderData)
                        return resolve({
                            flag: "1",
                            isHandleFlag:"0"});
                    });});
        })
    },
    broadUpQryPhoneNumberIsDk({commit, getters,dispatch}, queryParam) {
        const broadUpOrderData=getters.getBroadUpOrderData;
        commit('showLoading');
        return new Promise((resolve, reject) => {
            commit('hideLoading');
            let serialNumber=decrypted(broadUpOrderData.checkNumberData.kdNumberJm)
            http.post('/broadbandUpSpeed/qryPhoneNumberIsDk', {serialNumber:serialNumber}).then(res => {
                let mainNumVerifyFlag = 0
                commit('hideLoading');
                errorHandle(res, () => {
                        broadUpOrderData.timeData.contactPhone=decrypted(res.respData.phoneSerialNumberBtm)
                        broadUpOrderData.checkNumberData.phoneSerialNumberBtm=res.respData.phoneSerialNumberBtm
                        broadUpOrderData.checkNumberData.phoneSerialNumber=res.respData.phoneSerialNumber
                        commit('setBroadUpOrderData',broadUpOrderData)
                        return resolve({flag: "1"});
                    },
                    () => {
                        return resolve({
                            flag: "1",
                           });
                    });
            });
        })
    },
    broadUpBookTimeSubmit({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        return new Promise((resolve, reject) => {
            const mqBookFlagData=getters.getMqBookFlagData
            mqBookFlagData.bookAddrTimeFlag="0";
            commit('setMqBookFlagData',mqBookFlagData);
            commit('hideLoading');
            const broadUpOrderData=getters.getBroadUpOrderData
            let contactName=broadUpOrderData.timeData.contactName
            let contactPhone=broadUpOrderData.timeData.contactPhone
            let stdBookDay=broadUpOrderData.timeData.stdBookDay
            let stdBookTime=broadUpOrderData.timeData.stdBookTime
            if (contactName == '' || contactName == 'undefined' || contactName == undefined || contactName == null || contactName == 'null') {
                Toast('请填写联系人')
                return resolve({flag: "2"});
            }
            else  if (contactPhone == '' || contactPhone == 'undefined' || contactPhone == undefined || contactPhone == null || contactPhone == 'null') {
                Toast('请填写联系人电话')
                return resolve({ flag: "2"});
            }
            else if(("1"==broadUpOrderData.choiceValue&&(stdBookDay == '' || stdBookDay == 'undefined' || stdBookDay == undefined || stdBookDay == null || stdBookDay == 'null') )||("1"==broadUpOrderData.choiceValue && (stdBookTime == '' || stdBookTime == 'undefined' || stdBookTime == undefined || stdBookTime == null || stdBookTime == 'null') )){
                Toast('请选择预约时间段或日期')
                return resolve({ flag: "2"});
            }
            else{

                return resolve({flag: "1"});
            }
        })
    },
    broadUpOrderfoSubmit({commit, getters,dispatch}, queryParam) {
        commit('showLoading');

        let broadUpOrderData=getters.getBroadUpOrderData;
        if(this.broadUpOrderData.broadUpGoodData.broadUpList.length==0){
            this.$toast('请选择定制商品')
            return;
        }
        let zwInfoList={};
        if(this.broadUpOrderData.broadUpGoodData.broadUpList.length>0){
            let zw={
                commodityCode:broadUpOrderData.broadUpGoodData.broadUpList[0].commodityCode,
                commodityName:broadUpOrderData.broadUpGoodData.broadUpList[0].commodityName,
            }
            zwInfoList=zw;
        }
        let  stdContact=broadUpOrderData.timeData.contactName;
        if(isEmpty(stdContact)){
            stdContact=broadUpOrderData.checkNumberData.custName;
        }
        if(stdContact==broadUpOrderData.checkNumberData.showCustName){
            stdContact=broadUpOrderData.checkNumberData.custName;
        }
        let stdContactPhone=broadUpOrderData.timeData.contactPhone;
        let communityAddrInfo={
            exchCode:broadUpOrderData.checkNumberData.showExchCode,
            addressName:broadUpOrderData.checkNumberData.showInstallAddr,
            addressCode:broadUpOrderData.checkNumberData.showAddrCode,
            stdContact:stdContact,
            stdContactPhone:broadUpOrderData.timeData.contactPhone,
            stdBookDay:broadUpOrderData.timeData.stdBookDay,

            stdBookTime:broadUpOrderData.timeData.stdBookTime
        }
        let rhRelationMemberMsg={
            userTypeFlag: broadUpOrderData.checkNumberData.isRHUserTag,
            kdProductId:broadUpOrderData.checkNumberData.kdProductId ,
            kdProductName:broadUpOrderData.checkNumberData.kdProductName
        }
        if("0"==broadUpOrderData.checkNumberData.isRHUserTag){
            rhRelationMemberMsg={
                userTypeFlag: broadUpOrderData.checkNumberData.isRHUserTag
            }
        }
        let req={
            changeCommodityInfo:zwInfoList,
            communityAddrInfo:communityAddrInfo,
            serviceNumber:decrypted(broadUpOrderData.checkNumberData.kdNumberJm),
            choiceValue:broadUpOrderData.choiceValue,
            rhRelationMemberMsg:rhRelationMemberMsg,
            orderPrice:broadUpOrderData.orderPrice,
            remark:"备注:"+broadUpOrderData.remark+";预约时间:"+broadUpOrderData.timeData.stdBookDay+" "+broadUpOrderData.timeData.stdBookTime
        }
        http.post('/broadbandUpSpeed/preSubmit',req).then((res)=>{
            commit('hideLoading');
            if (res.respCode === '0000') {
                broadUpOrderData.orderId=res.respData.orderId;
                commit('setBroadUpOrderData',broadUpOrderData);
                return resolve({flag: "1", respMsg: '订单提交成功!订单号'+res.respData.orderId});
            }
            else{
                return resolve({flag: "2", respMsg: '订单提交失败'+res.respMsg});
            }
        })
    },
    broadUpSendMsg({commit, getters}, queryParam) {
        commit('showLoading');
        return new Promise((resolve, reject) => {
          let  broadUpOrderData =getters.getBroadUpOrderData;
          let serialNumber="";
          if("1"==broadUpOrderData.checkNumberData.isRHUserTag){
              serialNumber=decrypted(getters.getBroadUpOrderData.checkNumberData.phoneSerialNumberBtm)
          }else{
              if("1"==broadUpOrderData.checkNumberData.isHandleFlag){//单宽用户调用接口查出来的发送短信的号码
                  serialNumber=decrypted(getters.getBroadUpOrderData.checkNumberData.phoneSerialNumberBtm)
              }else{//单宽用户调用接口查出来的发送短信的号码
                  serialNumber=getters.getBroadUpOrderData.timeData.contactPhone
              }
          }
            let req={
                serialNumber:serialNumber,
                commName:getters.getBroadUpOrderData.broadUpGoodData.broadUpList[0].commodityName
            }
            http.post('/broadbandUpSpeed/wgmessage', req).then(res=>{
                commit('hideLoading');
            })
            return resolve({flag: "1",smsContactPhone:serialNumber});

        })
    },
    // 短信验证码校验
    broadUpMsgCodeVerify({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        return new Promise((resolve, reject) => {
            let req={};
                if("1"==getters.getBroadUpOrderData.checkNumberData.isRHUserTag){
             req={
                checkCode:queryParam.msgCode,
                serialNumber:decrypted(getters.getBroadUpOrderData.checkNumberData.phoneSerialNumberBtm)
            }}

                else{
                    if("1"==getters.getBroadUpOrderData.checkNumberData.isHandleFlag){
                        req={
                            checkCode:queryParam.msgCode,
                            serialNumber:decrypted(getters.getBroadUpOrderData.checkNumberData.phoneSerialNumberBtm)
                        }
                    }else{
                        req={
                            checkCode:queryParam.msgCode,
                            serialNumber:getters.getBroadUpOrderData.timeData.contactPhone
                        }
                    }
                }
            commit('hideLoading');
            http.post('/broadbandUpSpeed/checkCodeValidate', req).then(res => {
                if("0000"==res.respCode){
                    return resolve({flag: "1", msgCodeVerifyFlag: "1", msgCode: ""});
                }
                else if("7777"==res.respCode){
                    return resolve({flag: "2", respMsg: res.respMsg,validFlag:'4'
                    });
                }
                else{
                    return resolve({flag: "3", respMsg: res.respMsg});
                }
            }).catch((e) => {
                alertError({
                    title: '出错了！',
                    message: e,
                    confirmButtonText: '报告错误'
                });
                return resolve({flag: "2"});
            });
        })
    },
    //fttr
    fusUpFttrNumCheck({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        return new Promise((resolve, reject) => {
            const fusionUpFttrOrderData=getters.getFusionUpFttrOrderData
            let serialNumber=queryParam.qrySerialNumber;
            if(serialNumber == '' || serialNumber == 'undefined' || serialNumber == undefined || serialNumber == null || serialNumber == 'null'){
                serialNumber=fusionUpFttrOrderData.selectedNumber;
            }
            let req = {
                serialNumber: serialNumber,
                phoneType: queryParam.phoneType
            }
            http.post('/fusionUpFttr/checkNumber', req).then(res => {
                let mainNumVerifyFlag = 0
                commit('hideLoading');
                errorHandle(res, () => {
                    fusionUpFttrOrderData.checkNumberData=res.respData
                    fusionUpFttrOrderData.checkNumberData.serialNumber=serialNumber
                    fusionUpFttrOrderData.timeData.contactName=res.respData.showCustName
                    fusionUpFttrOrderData.timeData.contactPhone=decrypted(res.respData.phoneSerialNumberBtm)
                    commit('setBlockShow',true)
                    commit('setFusionUpFttrOrderData',fusionUpFttrOrderData);
                    return resolve({flag: "1",numberIsSendFlag:res.respData.numberIsSendFlag});
                }, () => {
                    return resolve({
                        flag: "2",
                        respMsg: res.respMsg,
                        numberIsSendFlag:"0"
                    });
                });
            })
        })
    },

    fttrFusionInformationQuery({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        return new Promise((resolve, reject) => {
            let req = {
                serialNumber: queryParam.phoneSerialNumber
            }
            http.post('/mpComm/mpRhQueryByPhoneNumFttr', req).then(res => {
                let mainNumVerifyFlag = 0
                commit('hideLoading');
                errorHandle(res, () => {
                    const fusionUpFttrOrderData=getters.getFusionUpFttrOrderData
                    fusionUpFttrOrderData.timeData.contactPhone=queryParam.phoneSerialNumber
                    fusionUpFttrOrderData.broadNumberByYw=res.respData.kdNumList
                    commit('setFusionUpFttrOrderData',fusionUpFttrOrderData)
                    if(res.respData.kdNumList.length==1){
                        fusionUpFttrOrderData.selectedNumber=res.respData.kdNumList[0].kdSerialNumber
                        commit('setFusionUpFttrOrderData',fusionUpFttrOrderData)
                        return resolve({flag: "2"});
                    }else{
                        return resolve({flag: "1",iptvNumCheckFlag:"1"});
                    }
                }, () => {
                    return resolve({
                        flag: "2",
                        respMsg: res.respMsg
                    });});
            }).catch((e) => {
                alertError({
                    title: '出错了！',
                    message: e,
                    confirmButtonText: '报告错误'
                });
                return resolve({respMsg: e + "请稍后重试", flag: "2"});
            });
        })
    },

    fusUpFttrSendMsg({commit, getters}, queryParam) {
        commit('showLoading');
        return new Promise((resolve, reject) => {
            let req={
                serialNumber:decrypted(getters.getFusionUpFttrOrderData.checkNumberData.phoneSerialNumberBtm)
            }
            http.post('/fusionUpFttr/wgmessage', req).then(res=>{
                commit('hideLoading');
            })
            return resolve({flag: "1"});
        })
    },
    // 短信验证码校验
    fusUpFttrMsgCodeVerify({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        return new Promise((resolve, reject) => {
            let req={
                checkCode:queryParam.msgCode,
                serialNumber:decrypted(getters.getFusionUpFttrOrderData.checkNumberData.phoneSerialNumberBtm)
            }
            commit('hideLoading');
            // 本地测试用的
            // if('123456'==queryParam.msgCode){
            //     console.log('短信验证码通过');
            //     const jzfkOrderData=getters.getFusionUpFttrOrderData
            //     const curTask=getters.getCurTask
            //     jzfkOrderData.curTask.taskId=curTask.taskId
            //     jzfkOrderData.curTask.toolCode=curTask.toolCode
            //     jzfkOrderData.curTask.taskName=curTask.taskName
            //     const chatList=getters.getChatList
            //     jzfkOrderData.chatList=chatList
            //     commit('setJzfkOrderData',jzfkOrderData);
            //
            //     let req1 = {
            //         operatorId: getters.getShbmMsgInfo.operatorId,
            //         jzfkOrderData:JSON.stringify(getters.getFusionUpFttrOrderData)
            //     }
            //     http.post('/contineBreak/saveData',req1).then(res=>{
            //     });
            //     return resolve({flag: "1", msgCodeVerifyFlag: "1", msgCode: ""});
            // }
            // else{
            //     console.log('主号码校验不通过');
            //     return resolve({flag: "2"});
            // }


            http.post('/fusionUpFttr/checkCodeValidate', req).then(res => {
                if("0000"==res.respCode){
                    return resolve({flag: "1", msgCodeVerifyFlag: "1", msgCode: ""});
                }
                else if("7777"==res.respCode){
                    return resolve({flag: "2", respMsg: res.respMsg,validFlag:'4'
                    });
                }
                else{
                    return resolve({flag: "3", respMsg: res.respMsg});
                }
            }).catch((e) => {
                alertError({
                    title: '出错了！',
                    message: e,
                    confirmButtonText: '报告错误'
                });
                return resolve({flag: "2"});
            });
        })
    },
    //融合深套移网附加商品获取------不确定
    getYwProductDataFusUpFttr({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        return new Promise((resolve, reject) => {
            let req1={
            }
            if(queryParam.bmGoodsType!=null){
                req1={
                    catId:queryParam.bmGoodsType,
                }}
            http.post('/fusionUpFttr/getCustGoodForFttr', req1).then(res => {
                commit('hideLoading')
                if (res.respCode === '0000') {
                    let iptvReProductList=res.respData.zwProList
                    iptvReProductList.sort((a, b) => {
                        const hasPropertyA = 'RENT' in a;
                        const hasPropertyB = 'RENT' in b;
                        // 如果 a 有属性而 b 没有，a 应该在前面
                        if (hasPropertyA && !hasPropertyB) {
                            return -1;
                        }
                        // 如果 b 有属性而 a 没有，b 应该在前面
                        if (!hasPropertyA && hasPropertyB) {
                            return 1;
                        }
                        // 如果两者都有或都没有，保持原有顺序（或根据其他条件排序）
                        return 0;
                    })
                    commit('setFusionUpFttrReYwProductList',iptvReProductList)
                    return resolve({flag:"1"});}
                else{
                    return resolve({flag:"2",respMsg: res.respMsg});}
            })
        })
    },
    ywfIsConflictYsk({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        let iptvCode1=[];
        let iptvCodeString={};
        let outCallMonetOrderData = getters.getOutCallMonetOrderData;
        if(outCallMonetOrderData.goodData.commodityChooseYWUlList.length>0){
            for(let i=0;i<outCallMonetOrderData.goodData.commodityChooseYWUlList.length;i++) {
                let  iptvCodeInfo={};
                iptvCodeInfo={
                    ywGoodId: outCallMonetOrderData.goodData.commodityChooseYWUlList[i].ywGoodId,
                }
                iptvCode1.push(iptvCodeInfo)}
            iptvCodeString.ywfList=iptvCode1
            return new Promise((resolve, reject) => {
                let req1={
                    serialNumber:outCallMonetOrderData.checkNumberData.serviceNumber,
                    ywfCode:iptvCodeString
                }
                http.post('/mpComm/ywfIsConflictYsk', req1).then(res => {
                    commit('hideLoading')
                    if (res.respCode === '0000') {
                        return resolve({flag:"1"});}
                    else{
                        outCallMonetOrderData.goodData.commodityChooseYWUlList=[];
                        commit('setOutCallMonetOrderData',outCallMonetOrderData)
                        return resolve({flag:"2",respMsg: res.respMsg});}
                })
            })
        }else{
            return new Promise((resolve, reject) => {
                return resolve({flag:"1"});
            })
        }

    },

    ywfIsConflict({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        let iptvOrderData = getters.getIptvOrderData;
        let commId=iptvOrderData.iptvGoodData.commodityChooseYWUlList[0].ywGoodId
        return new Promise((resolve, reject) => {
            let req1={
                serialNumber:iptvOrderData.checkNumberData.phoneSerialNumber,
                commId:commId
            }
            http.post('/mpComm/ywfIsConflict', req1).then(res => {
                commit('hideLoading')
                if (res.respCode === '0000') {
                    return resolve({flag:"1"});}
                else{
                    return resolve({flag:"2",respMsg: res.respMsg});}
            })
        })
    },

    getProductDataFusUpFttr({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        return new Promise((resolve, reject) => {
                let req1={
                    bigUseFee:getters.getFusionUpFttrOrderData.checkNumberData.bigUseFee,
                    onlineMonths:getters.getFusionUpFttrOrderData.checkNumberData.onlineMonths,
                    fttrTransFlag:getters.getFusionUpFttrOrderData.checkNumberData.fttrTransFlag
                }
                if(queryParam.bmGoodsType!=null){
                    req1={
                        bigUseFee:getters.getFusionUpFttrOrderData.checkNumberData.bigUseFee,
                        catId:queryParam.bmGoodsType,
                        onlineMonths:getters.getFusionUpFttrOrderData.checkNumberData.onlineMonths,
                        fttrTransFlag:getters.getFusionUpFttrOrderData.checkNumberData.fttrTransFlag
                    }}
                http.post('/fusionUpFttr/getCustGoodForFttr', req1).then(res => {
                    commit('hideLoading')
                    if (res.respCode === '0000') {
                        let iptvReProductList=res.respData.zwProList
                        iptvReProductList.sort((a, b) => {
                            const hasPropertyA = 'RENT' in a;
                            const hasPropertyB = 'RENT' in b;
                            // 如果 a 有属性而 b 没有，a 应该在前面
                            if (hasPropertyA && !hasPropertyB) {
                                return -1;
                            }
                            // 如果 b 有属性而 a 没有，b 应该在前面
                            if (!hasPropertyA && hasPropertyB) {
                                return 1;
                            }
                            // 如果两者都有或都没有，保持原有顺序（或根据其他条件排序）
                            return 0;
                        })
                        commit('setFusionUpFttrReProductList',iptvReProductList)
                        return resolve({flag:"1"});}
                    else{
                        return resolve({flag:"2",respMsg: res.respMsg});}
                })
        })
    },
    fusUpFttrOrderInfoSubmit({commit, getters,dispatch}, queryParam) {
        commit('showLoading');

        let fusionUpFttrOrderData=getters.getFusionUpFttrOrderData;
        let zwInfoList={};
        if(fusionUpFttrOrderData.fusionUpFttrGoodData.custList.length>0){
            let zw={
                commodityCode:fusionUpFttrOrderData.fusionUpFttrGoodData.custList[0].commodityCode,
                commodityName:fusionUpFttrOrderData.fusionUpFttrGoodData.custList[0].commodityName,
            }
            zwInfoList=zw;
        }
        let  stdContact=fusionUpFttrOrderData.timeData.contactName;
        if(stdContact == '' || stdContact == 'undefined' || stdContact == undefined || stdContact == null || stdContact == 'null'){
            stdContact=fusionUpFttrOrderData.checkNumberData.custName
        }
        if(stdContact==fusionUpFttrOrderData.checkNumberData.showCustName){
            stdContact=fusionUpFttrOrderData.checkNumberData.custName
        }
        let communityAddrInfo={
            exchCode:fusionUpFttrOrderData.checkNumberData.showExchCode,
            addressName:fusionUpFttrOrderData.checkNumberData.showInstallAddr,
            addressCode:fusionUpFttrOrderData.checkNumberData.showAddrCode,
            stdContact:stdContact,
            stdContactPhone:fusionUpFttrOrderData.timeData.contactPhone,
            stdBookDay:fusionUpFttrOrderData.timeData.stdBookDay,

            stdBookTime:fusionUpFttrOrderData.timeData.stdBookTime
        }
        let rhRelationMemberMsg={
            userTypeFlag:fusionUpFttrOrderData.checkNumberData.isRHUserTag,
            phoneSerialNumber:fusionUpFttrOrderData.checkNumberData.phoneSerialNumber,
            ywProductName:fusionUpFttrOrderData.checkNumberData.ywProductName,
            ywProductId:fusionUpFttrOrderData.checkNumberData.ywProductId,
            rhSerialNumber:fusionUpFttrOrderData.checkNumberData.rhSerialNumber,
            rhProductId:fusionUpFttrOrderData.checkNumberData.rhProductId ,
            rhProductName:fusionUpFttrOrderData.checkNumberData.rhProductName ,
            mixTypeCode:fusionUpFttrOrderData.checkNumberData.mixTypeCode ,
            kdProductId:fusionUpFttrOrderData.checkNumberData.kdProductId ,
            kdProductName:fusionUpFttrOrderData.checkNumberData.kdProductName
        }
        if("0"==fusionUpFttrOrderData.checkNumberData.isRHUserTag){
            rhRelationMemberMsg={
                userTypeFlag: wOrderData.checkNumberData.isRHUserTag
            }
        }
        let req={
            changeCommodityInfo:zwInfoList,
            communityAddrInfo:communityAddrInfo,
            serviceNumber:fusionUpFttrOrderData.checkNumberData.serialNumber,
            choiceValue:fusionUpFttrOrderData.choiceValue,
            rhRelationMemberMsg:rhRelationMemberMsg,
            orderPrice:fusionUpFttrOrderData.orderPrice,
            remark:"备注:"+fusionUpFttrOrderData.remark+";预约时间:"+fusionUpFttrOrderData.timeData.stdBookDay+" "+fusionUpFttrOrderData.timeData.stdBookTime
        }
        http.post('/fusionUpFttr/preSubmit',req).then((res)=>{
            commit('hideLoading');
            if (res.respCode === '0000') {
                fusionUpFttrOrderData.orderId=res.respData.orderId;
                commit('setFusionUpFttrOrderData',fusionUpFttrOrderData);
                return resolve({flag: "1", respMsg: '订单提交成功!订单号'+res.respData.orderId});
            }
            else{
                return resolve({flag: "2", respMsg: '订单提交失败'+res.respMsg});
            }
        })
    },
    bookTimeSubmitFusUpFttr({commit, getters,dispatch}, queryParam) {

        commit('showLoading');
        return new Promise((resolve, reject) => {
            const mqBookFlagData=getters.getMqBookFlagData
            mqBookFlagData.bookAddrTimeFlag="0";
            commit('setMqBookFlagData',mqBookFlagData);
            commit('hideLoading');
            const fusionUpFttrOrderData=getters.getFusionUpFttrOrderData
            let contactName=fusionUpFttrOrderData.timeData.contactName
            let contactPhone=fusionUpFttrOrderData.timeData.contactPhone
            let stdBookDay=fusionUpFttrOrderData.timeData.stdBookDay
            let stdBookTime=fusionUpFttrOrderData.timeData.stdBookTime
            if (contactName == '' || contactName == 'undefined' || contactName == undefined || contactName == null || contactName == 'null') {
                Toast('请填写联系人')
                return resolve({ flag: "2"});
            }
            else  if (contactPhone == '' || contactPhone == 'undefined' || contactPhone == undefined || contactPhone == null || contactPhone == 'null') {
                Toast('请填写联系人电话')
                return resolve({ flag: "2"});
            }
            else if(("1"==fusionUpFttrOrderData.choiceValue&&(stdBookDay == '' || stdBookDay == 'undefined' || stdBookDay == undefined || stdBookDay == null || stdBookDay == 'null') )||("1"==fusionUpFttrOrderData.choiceValue && (stdBookTime == '' || stdBookTime == 'undefined' || stdBookTime == undefined || stdBookTime == null || stdBookTime == 'null') )){
                Toast('请选择预约时间段或日期')
                return resolve({ flag: "2"});
            }
            else{
                return resolve({flag: "1"});}
        })
    },

    queryLatestBookDateFusUpFttr({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        var date = new Date();
        var year = date.getFullYear();
        var month = Number(date.getMonth()) + 1;
        month = month >= 10 ? month : "0" + month;
        var day = date.getDate();
        day = day >= 10 ? day : "0" + day;
        let nowDate= year + "-" + month + "-" + day;
        dispatch('getHotWords','queryLatestBookDate')
        return new Promise((resolve, reject) => {
            let fusionUpFttrOrderData=getters.getFusionUpFttrOrderData
            let req = {
                siteCd:fusionUpFttrOrderData.checkNumberData.innerExchCode,
                bookTime:nowDate,
                stdAddrId:fusionUpFttrOrderData.checkNumberData.innerAddrCode
            }
            http.post('/iptvReceive/iptvBookingDate',
                req
            ).then(res=>{
                if(res.respCode=='0000'){
                    if(res.respData.length>0){
                        for(let i=0;i<res.respData.length;i++){
                            let c={};
                            if(i==0){
                                if(res.respData[i].flag==0){
                                    fusionUpFttrOrderData.timeData.stdBookDay=nowDate
                                    fusionUpFttrOrderData.timeData.stdBookTime="上午(9:00-12:00)"
                                    commit('setFusionUpFttrOrderData',fusionUpFttrOrderData)
                                    return resolve({flag:"1",bookLatestTime:nowDate+" 上午(9:00-12:00)"});
                                }else {
                                }
                            }
                            if(i==1){
                                if(res.respData[i].flag==0){
                                    fusionUpFttrOrderData.timeData.stdBookDay=nowDate
                                    fusionUpFttrOrderData.timeData.stdBookTime="下午(12:00-18:00)"
                                    commit('setFusionUpFttrOrderData',fusionUpFttrOrderData)

                                    return resolve({flag:"1",bookLatestTime:nowDate+" 下午(12:00-18:00)"});
                                }else {
                                }
                            }
                            if(i==2){
                                if(res.respData[i].flag==0){
                                    fusionUpFttrOrderData.timeData.stdBookDay=nowDate
                                    fusionUpFttrOrderData.timeData.stdBookTime="晚上(18:00-21:00)"
                                    commit('setFusionUpFttrOrderData',fusionUpFttrOrderData)
                                    return resolve({flag:"1",bookLatestTime:nowDate+" 晚上(18:00-21:00)"});
                                }else {
                                    const tomorrow = new Date(date);
                                    tomorrow.setDate(date.getDate() + 1);
                                    let tomorrowDate= tomorrow.toISOString().split('T')[0];
                                    let req1 = {
                                        siteCd:fusionUpFttrOrderData.checkNumberData.innerExchCode,
                                        bookTime:tomorrowDate,
                                        stdAddrId:fusionUpFttrOrderData.checkNumberData.innerAddrCode
                                    }
                                    http.post('/iptvReceive/iptvBookingDate',
                                        req1
                                    ).then(res=>{
                                        if(res.respCode=='0000'){
                                            for(let i=0;i<res.respData.length;i++){
                                                if(i==0){
                                                    if(res.respData[i].flag==0){
                                                        fusionUpFttrOrderData.timeData.stdBookDay=tomorrowDate
                                                        fusionUpFttrOrderData.timeData.stdBookTime="上午(9:00-12:00)"
                                                        commit('setFusionUpFttrOrderData',fusionUpFttrOrderData)
                                                        return resolve({flag:"1",bookLatestTime:tomorrowDate+" 上午(9:00-12:00)"});
                                                    }else {
                                                    }
                                                }

                                                if(i==1){
                                                    if(res.respData[i].flag==0){
                                                        fusionUpFttrOrderData.timeData.stdBookDay=tomorrowDate
                                                        fusionUpFttrOrderData.timeData.stdBookTime="下午(12:00-18:00)"
                                                        commit('setFusionUpFttrOrderData',fusionUpFttrOrderData)
                                                        return resolve({flag:"1",bookLatestTime:tomorrowDate+" 下午(12:00-18:00)"});
                                                    }else {

                                                    }
                                                }
                                                if(i==2){
                                                    if(res.respData[i].flag==0){
                                                        fusionUpFttrOrderData.timeData.stdBookDay=tomorrowDate
                                                        fusionUpFttrOrderData.timeData.stdBookTime="晚上(18:00-21:00)"
                                                        commit('setFusionUpFttrOrderData',fusionUpFttrOrderData)
                                                        return resolve({flag:"1",bookLatestTime:tomorrowDate+" 晚上(18:00-21:00)"});
                                                    }else{
                                                        return resolve({flag:"2",bookLatestTime:""});
                                                    }
                                                }
                                            }
                                        }})
                                }
                            }
                        }

                    }
                }
            })
        })
    },

    defaultTimeTagChangeFusUpFttr({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        return new Promise((resolve, reject) => {
            const mqBookFlagData=getters.getMqBookFlagData
            mqBookFlagData.bookAddrTimeFlag="1";
            commit('setMqBookFlagData',mqBookFlagData);
            return resolve({flag:"1"});
        })
    },
    
    fusUpFttrReSelectDate({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        dispatch('getHotWords','IptvReSelectDate')
        let fusionUpFttrOrderData=getters.getFusionUpFttrOrderData

        return new Promise((resolve, reject) => {
            if(queryParam.bookDay!=null&&queryParam.bookDay!=''){
                let req = {
                    siteCd:fusionUpFttrOrderData.checkNumberData.innerExchCode,
                    bookTime:queryParam.bookDay,
                    stdAddrId:fusionUpFttrOrderData.checkNumberData.innerAddrCode
                }
                let bookTimeS='';
                http.post('/iptvReceive/iptvBookingDate',
                    req
                ).then(res=>{
                    commit('hideLoading');
                    if (res.respCode === '0000') {
                        if("0"==res.respData[Number(queryParam.bookTime)-1].flag){
                            if(1==Number(queryParam.bookTime)){
                                bookTimeS='上午(9:00-12:00)'
                            }
                            if(2==Number(queryParam.bookTime)){
                                bookTimeS='下午(12:00-18:00)'
                            }
                            if(3==Number(queryParam.bookTime)){
                                bookTimeS='晚上(18:00-21:00)'
                            }
                            fusionUpFttrOrderData.timeData.stdBookDay=queryParam.bookDay
                            fusionUpFttrOrderData.timeData.stdBookTime=bookTimeS
                            commit('setFusionUpFttrOrderData',fusionUpFttrOrderData)
                            return resolve({flag:"1",bookReSelectTime:queryParam.bookDay+" "+bookTimeS});
                        }else{
                            return resolve({flag: "2", respMsg: "该时间段不可预约，请重新选择"});
                        }
                    }
                    else{
                        return resolve({flag: "2", respMsg: res.respMsg});
                    }
                })
            }else{
                return resolve({flag:"1",bookReSelectTime: fusionUpFttrOrderData.timeData.stdBookDay+" "+fusionUpFttrOrderData.timeData.stdBookTime});
            }
        })
    },
    queryContactMsgFusUpFttr({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        return new Promise((resolve, reject) => {
            let fusionUpFttrOrderData=getters.getFusionUpFttrOrderData
            let contactPhone=fusionUpFttrOrderData.timeData.contactPhone
            if(contactPhone == '' || contactPhone == 'undefined' || contactPhone == undefined || contactPhone == null || contactPhone == 'null'){
                let req={
                    serialNumber:    fusionUpFttrOrderData.checkNumberData.serialNumber
                }
                http.post('/mpComm/mpRhQueryByBroadNum',req).then((res)=>{
                    commit('hideLoading');
                    if (res.respCode === '0000') {
                        const fusionUpFttrOrderData=getters.getFusionUpFttrOrderData
                        if(res.respData.kdNumList.length==0){
                            commit('setRespTipArrQry',[]);
                            commit('setBlockShow',false)
                            return resolve({flag:"2",respMsg: "未找到联系电话，请输入"});
                        }else{
                            fusionUpFttrOrderData.timeData.contactPhone=res.respData.kdNumList[0].ywSerialNumber
                            commit('setFusionUpFttrOrderData',fusionUpFttrOrderData)
                            dispatch('getHotWords','queryContactMsg')
                            return resolve({flag:"1",contactQueryPhone:res.respData.kdNumList[0].ywSerialNumber});
                        }
                    }
                    else{
                        commit('setRespTipArrQry',[]);
                        commit('setBlockShow',false)
                        return resolve({flag:"2",respMsg: res.respMsg});
                    }
                })
            }else{
                return resolve({flag:"1",contactQueryPhone:fusionUpFttrOrderData.timeData.contactPhone});
            }

        })
    },
    contactMsgSubmitFusUpFttr({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        commit('setRespTipArrQry',[])
        commit('setBlockShow',false)
        return new Promise((resolve, reject) => {
            let fusionUpFttrOrderData=getters.getFusionUpFttrOrderData
            let contactPhone=queryParam.contactPhone
            let contactName=queryParam.contactName
            if(contactPhone == '' || contactPhone == 'undefined' || contactPhone == undefined || contactPhone == null || contactPhone == 'null'){
            }
            else{
                fusionUpFttrOrderData.timeData.contactPhone=queryParam.contactPhone
                commit('setFusionUpFttrOrderData',fusionUpFttrOrderData)
            }
            if(contactName == '' || contactName == 'undefined' || contactName == undefined || contactName == null || contactName == 'null'){
            }
            else{
                fusionUpFttrOrderData.timeData.contactName=queryParam.contactName
                commit('setFusionUpFttrOrderData',fusionUpFttrOrderData)
            }
            return resolve({flag:"1"});
        })
    },
    
    
//联系人信息提交
    contactMsgSubmit({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        commit('setRespTipArrQry',[])
        commit('setBlockShow',false)
        return new Promise((resolve, reject) => {
            let iptvOrderData=getters.getIptvOrderData
            let contactPhone=queryParam.contactPhone
            let contactName=queryParam.contactName
            console.log(contactPhone,contactName,"contactName")

            if(contactPhone == '' || contactPhone == 'undefined' || contactPhone == undefined || contactPhone == null || contactPhone == 'null'){
            }
            else{
                iptvOrderData.timeData.contactPhone=queryParam.contactPhone
                commit('setIptvOrderData',iptvOrderData)
            }
            if(contactName == '' || contactName == 'undefined' || contactName == undefined || contactName == null || contactName == 'null'){
            }
            else{
                iptvOrderData.timeData.contactName=queryParam.contactName
                commit('setIptvOrderData',iptvOrderData)
            }
            return resolve({flag:"1"});
        })
    },
    //默认联系人信息查询
    
    queryContactMsg({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        return new Promise((resolve, reject) => {
            dispatch('getHotWords','queryContactMsg')
            let iptvOrderData=getters.getIptvOrderData
            let contactPhone=iptvOrderData.timeData.contactPhone
            if(contactPhone == '' || contactPhone == 'undefined' || contactPhone == undefined || contactPhone == null || contactPhone == 'null'){
                let req={
                    serialNumber:    iptvOrderData.checkNumberData.serialNumber
                }
                http.post('/mpComm/mpRhQueryByBroadNum',req).then((res)=>{
                    commit('hideLoading');
                    if (res.respCode === '0000') {
                        const iptvOrderData=getters.getIptvOrderData
                        if(res.respData.kdNumList.length==0){
                            commit('setBlockShow',false)
                             commit('setRespTipArrQry',[])
                            return resolve({flag:"2",respMsg: "未找到联系电话，请输入"});
                        }else{
                            iptvOrderData.timeData.contactPhone=res.respData.kdNumList[0].ywSerialNumber
                            commit('setIptvOrderData',iptvOrderData)
                            dispatch('getHotWords','queryContactMsg')
                            return resolve({flag:"1",contactQueryPhone:res.respData.kdNumList[0].ywSerialNumber});
                        }
                    }
                    else{
                        commit('setBlockShow',false)
                        commit('setRespTipArrQry',[])
                        return resolve({flag:"2",respMsg: res.respMsg});
                    }
                })
            }else{
                return resolve({flag:"1",contactQueryPhone:iptvOrderData.timeData.contactPhone});
            }
           
        })
    },
    //默认时间
    defaultTimeTagChange({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        return new Promise((resolve, reject) => {
            const mqBookFlagData=getters.getMqBookFlagData
            mqBookFlagData.bookAddrTimeFlag="1";
            commit('setMqBookFlagData',mqBookFlagData);
            return resolve({flag:"1"});
        }
        )
    },
    //预约日期二次确认
    IptvReSelectDate({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        dispatch('getHotWords','IptvReSelectDate')
        let iptvOrderData=getters.getIptvOrderData
        return new Promise((resolve, reject) => {
           if(queryParam.bookDay!=null){
               let req = {
                   siteCd:iptvOrderData.checkNumberData.innerExchCode,
                   bookTime:queryParam.bookDay,
                   stdAddrId:iptvOrderData.checkNumberData.innerAddrCode
               }
               let bookTimeS='';
               http.post('/iptvReceive/iptvBookingDate',
                   req
               ).then(res=>{
                   commit('hideLoading');
                   if (res.respCode === '0000') {
                       if("0"==res.respData[Number(queryParam.bookTime)-1].flag){
                           if(1==Number(queryParam.bookTime)){
                               bookTimeS='上午(9:00-12:00)'
                           }
                           if(2==Number(queryParam.bookTime)){
                               bookTimeS='下午(12:00-18:00)'
                           }
                           if(3==Number(queryParam.bookTime)){
                               bookTimeS='晚上(18:00-21:00)'
                           }
                           iptvOrderData.timeData.stdBookDay=queryParam.bookDay
                           iptvOrderData.timeData.stdBookTime=bookTimeS
                           commit('setIptvOrderData',iptvOrderData)
                           return resolve({flag:"1",bookReSelectTime:queryParam.bookDay+" "+bookTimeS});
                       }else{
                           return resolve({flag: "2", respMsg: "该时间段不可预约，请重新选择"});
                       }
                   }
                   else{
                       return resolve({flag: "2", respMsg: res.respMsg});
                   }
               })
           }
           else{
               return resolve({flag:"1",bookReSelectTime:iptvOrderData.timeData.stdBookDay+" "+iptvOrderData.timeData.stdBookTime});
           }
        })
    },
    //获取最近可预约日期
    queryLatestBookDate({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        var date = new Date();
        var year = date.getFullYear();
        var month = Number(date.getMonth()) + 1;
        month = month >= 10 ? month : "0" + month;
        var day = date.getDate();
        day = day >= 10 ? day : "0" + day;
       let nowDate= year + "-" + month + "-" + day;
       dispatch('getHotWords','queryLatestBookDate')
        return new Promise((resolve, reject) => {
            let iptvOrderData=getters.getIptvOrderData
            let req = {
                siteCd:iptvOrderData.checkNumberData.innerExchCode,
                bookTime:nowDate,
                stdAddrId:iptvOrderData.checkNumberData.innerAddrCode
            }
            http.post('/iptvReceive/iptvBookingDate',
                req
            ).then(res=>{
                if(res.respCode=='0000'){
                    if(res.respData.length>0){
                        for(let i=0;i<res.respData.length;i++){
                            let c={};
                            if(i==0){
                                if(res.respData[i].flag==0){
                                    iptvOrderData.timeData.stdBookDay=nowDate
                                    iptvOrderData.timeData.stdBookTime="上午(9:00-12:00)"
                                    commit('setIptvOrderData',iptvOrderData)
                                    return resolve({flag:"1",bookLatestTime:nowDate+" 上午(9:00-12:00)"});
                                }else {
                                }
                            }
                            if(i==1){
                                if(res.respData[i].flag==0){
                                    iptvOrderData.timeData.stdBookDay=nowDate
                                    iptvOrderData.timeData.stdBookTime="下午(12:00-18:00)"
                                    commit('setIptvOrderData',iptvOrderData)

                                    return resolve({flag:"1",bookLatestTime:nowDate+" 下午(12:00-18:00)"});
                                }else {
                                }
                            }
                            if(i==2){
                                if(res.respData[i].flag==0){
                                    iptvOrderData.timeData.stdBookDay=nowDate
                                    iptvOrderData.timeData.stdBookTime="晚上(18:00-21:00)"
                                    commit('setIptvOrderData',iptvOrderData)
                                    return resolve({flag:"1",bookLatestTime:nowDate+" 晚上(18:00-21:00)"});
                                }else {
                                    const tomorrow = new Date(date);
                                    tomorrow.setDate(date.getDate() + 1);
                                  let tomorrowDate= tomorrow.toISOString().split('T')[0];
                                    let req1 = {
                                        siteCd:iptvOrderData.checkNumberData.innerExchCode,
                                        bookTime:tomorrowDate,
                                        stdAddrId:iptvOrderData.checkNumberData.innerAddrCode
                                    }
                                    http.post('/iptvReceive/iptvBookingDate',
                                        req1
                                    ).then(res=>{
                                        if(res.respCode=='0000'){
                                            for(let i=0;i<res.respData.length;i++){
                                                if(i==0){
                                                    if(res.respData[i].flag==0){
                                                        iptvOrderData.timeData.stdBookDay=tomorrowDate
                                                        iptvOrderData.timeData.stdBookTime="上午(9:00-12:00)"
                                                        commit('setIptvOrderData',iptvOrderData)
                                                        return resolve({flag:"1",bookLatestTime:tomorrowDate+" 上午(9:00-12:00)"});
                                                    }else {
                                                    }
                                                }
                                                
                                                if(i==1){
                                                    if(res.respData[i].flag==0){
                                                        iptvOrderData.timeData.stdBookDay=tomorrowDate
                                                        iptvOrderData.timeData.stdBookTime="下午(12:00-18:00)"
                                                        commit('setIptvOrderData',iptvOrderData)
                                                        return resolve({flag:"1",bookLatestTime:tomorrowDate+" 下午(12:00-18:00)"});
                                                    }else {
                                                        
                                                    }
                                                }
                                                if(i==2){
                                                    if(res.respData[i].flag==0){
                                                        iptvOrderData.timeData.stdBookDay=tomorrowDate
                                                        iptvOrderData.timeData.stdBookTime="晚上(18:00-21:00)"
                                                        commit('setIptvOrderData',iptvOrderData)
                                                        return resolve({flag:"1",bookLatestTime:tomorrowDate+" 晚上(18:00-21:00)"});
                                                    }else{
                                                        return resolve({flag:"2",bookLatestTime:""});
                                                    }
                                                }
                                            }
                                        }})
                                }
                            }
                        }
                        
                    }
                }
          })
        })
    },
    
    //商品查询函数
    getProductData({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        return new Promise((resolve, reject) => {
            let iptvOrderData=getters.getIptvOrderData;
           if('iptvGoods'==queryParam.goodsType){
               let req1={}
               if(queryParam.bmGoodsType!=null){
                   req1={
                       catId:queryParam.bmGoodsType
                   }}
               dispatch('getHotWords','IptvSelectProduct')
               http.post('/iptvReceive/iptvGoodsQry', req1).then(res => {
                   commit('hideLoading')
                   if (res.respCode === '0000') {
                       
                      let iptvReProductList=res.respData
                       commit('setIptvReProductList',iptvReProductList)
                       return resolve({flag:"1"});
                   }
                   else{
                       return resolve({flag:"2",respMsg: res.respMsg});}
               })}
           else{
               let iptvCodeString={
                   iptvInfoList:[]
               }
               let iptvCode1=[]
               console.log(iptvOrderData.iptvGoodData.iptvList,"02106952134")
               if(iptvOrderData.iptvGoodData.iptvList.length<=0){
                   return resolve({flag:"3"});
               }else{
                       for(let i=0;i<iptvOrderData.iptvGoodData.iptvList.length;i++) {
                           let  iptvCodeInfo={};
                           iptvCodeInfo={
                               iptvId: iptvOrderData.iptvGoodData.iptvList[i].commodityCode,
                               iptvName:iptvOrderData.iptvGoodData.iptvList[i].iptvName,
                               commType:iptvOrderData.iptvGoodData.iptvList[i].commType
                           }
                           iptvCode1.push(iptvCodeInfo)}
                   iptvCodeString.iptvInfoList=iptvCode1
                   let req = {
                       iptvCode:JSON.stringify(iptvCodeString),
                       ywProductId:iptvOrderData.checkNumberData.ywProductId==null?"":iptvOrderData.checkNumberData.ywProductId,
                       ywStartDate:iptvOrderData.checkNumberData.ywStartDate==null?"":iptvOrderData.checkNumberData.ywStartDate
                   }
                   http.post('/iptvReceive/getYwfGoodsByIPTV', req).then(res => {
                       commit('hideLoading')
                       let commodityYWUlList=[]
                       if (res.respData.code === '0000') {
                           if(res.respData.ywFreeGoodsListResult.length>0){
                               if( iptvOrderData.checkNumberData.userTypeFlag=='1'){
                                       dispatch('getHotWords','lptvYwSelectProduct')
                                       commit('setIptvReYwProductList',res.respData.ywFreeGoodsListResult)
                                       return resolve({flag:"1"});
                                  }
                               else{
                                   commit('setIptvReYwProductList',[])
                                   return resolve({flag:"2"});
                               }
                           }else{
                               return resolve({flag:"2"});
                           }
                       }
                       else{
                           return resolve({flag:"2",respMsg: res.respMsg});
                       }
                   })
               }
           }
        })
    },
    getHotWordComm({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        return new Promise((resolve, reject) => {
          dispatch('getHotWords',queryParam.hotCodeId)
            return resolve({flag:"1"});
        })
    },
    queryLatestBookDateYsk({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        var date = new Date();
        var year = date.getFullYear();
        var month = Number(date.getMonth()) + 1;
        month = month >= 10 ? month : "0" + month;
        var day = date.getDate();
        day = day >= 10 ? day : "0" + day;
        let nowDate= year + "-" + month + "-" + day;
        // dispatch('getHotWords','queryLatestBookDateYsk')
        return new Promise((resolve, reject) => {
            let outCallMonetOrderData=getters.getOutCallMonetOrderData
            let req = {
                siteCd:outCallMonetOrderData.installAddr.ziyuan.accessList.exchList.exchCode,
                bookTime:nowDate,
                stdAddrId:outCallMonetOrderData.installAddr.SEGM_ID
            }
            http.post('/iptvReceive/iptvBookingDate',
                req
            ).then(res=>{
                if(res.respCode=='0000'){
                    if(res.respData.length>0){
                        for(let i=0;i<res.respData.length;i++){
                            let c={};
                            if(i==0){
                                if(res.respData[i].flag==0){
                                    outCallMonetOrderData.timeData.stdBookDay=nowDate
                                    outCallMonetOrderData.timeData.stdBookTime="上午(9:00-12:00)"
                                    commit('setOutCallMonetOrderData',outCallMonetOrderData)
                                    return resolve({flag:"1",bookLatestTime:nowDate+" 上午(9:00-12:00)"});
                                }else {
                                }
                            }
                            if(i==1){
                                if(res.respData[i].flag==0){
                                    outCallMonetOrderData.timeData.stdBookDay=nowDate
                                    outCallMonetOrderData.timeData.stdBookTime="下午(12:00-18:00)"
                                    commit('setOutCallMonetOrderData',outCallMonetOrderData)

                                    return resolve({flag:"1",bookLatestTime:nowDate+" 下午(12:00-18:00)"});
                                }else {
                                }
                            }
                            if(i==2){
                                if(res.respData[i].flag==0){
                                    outCallMonetOrderData.timeData.stdBookDay=nowDate
                                    outCallMonetOrderData.timeData.stdBookTime="晚上(18:00-21:00)"
                                    commit('setOutCallMonetOrderData',outCallMonetOrderData)
                                    return resolve({flag:"1",bookLatestTime:nowDate+" 晚上(18:00-21:00)"});
                                }else {
                                    const tomorrow = new Date(date);
                                    tomorrow.setDate(date.getDate() + 1);
                                    let tomorrowDate= tomorrow.toISOString().split('T')[0];
                                    let req1 = {
                                        siteCd:outCallMonetOrderData.installAddr.ziyuan.accessList.exchList.exchCode,
                                        bookTime:tomorrowDate,
                                        stdAddrId:outCallMonetOrderData.installAddr.SEGM_ID
                                    }
                                    http.post('/iptvReceive/iptvBookingDate',
                                        req1
                                    ).then(res=>{
                                        if(res.respCode=='0000'){
                                            for(let i=0;i<res.respData.length;i++){
                                                if(i==0){
                                                    if(res.respData[i].flag==0){
                                                        outCallMonetOrderData.timeData.stdBookDay=tomorrowDate
                                                        outCallMonetOrderData.timeData.stdBookTime="上午(9:00-12:00)"
                                                        commit('setOutCallMonetOrderData',outCallMonetOrderData)
                                                        return resolve({flag:"1",bookLatestTime:tomorrowDate+" 上午(9:00-12:00)"});
                                                    }else {
                                                    }
                                                }

                                                if(i==1){
                                                    if(res.respData[i].flag==0){
                                                        outCallMonetOrderData.timeData.stdBookDay=tomorrowDate
                                                        outCallMonetOrderData.timeData.stdBookTime="下午(12:00-18:00)"
                                                        commit('setOutCallMonetOrderData',outCallMonetOrderData)
                                                        return resolve({flag:"1",bookLatestTime:tomorrowDate+" 下午(12:00-18:00)"});
                                                    }else {

                                                    }
                                                }
                                                if(i==2){
                                                    if(res.respData[i].flag==0){
                                                        outCallMonetOrderData.timeData.stdBookDay=tomorrowDate
                                                        outCallMonetOrderData.timeData.stdBookTime="晚上(18:00-21:00)"
                                                        commit('setOutCallMonetOrderData',outCallMonetOrderData)
                                                        return resolve({flag:"1",bookLatestTime:tomorrowDate+" 晚上(18:00-21:00)"});
                                                    }else{
                                                        return resolve({flag:"2",bookLatestTime:""});
                                                    }
                                                }
                                            }
                                        }})
                                }
                            }
                        }

                    }
                }
            })
        })
    },

    defaultTimeTagChangeYsk({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        return new Promise((resolve, reject) => {
            const mqBookFlagData=getters.getMqBookFlagData
            mqBookFlagData.bookAddrTimeFlag="1";
            commit('setMqBookFlagData',mqBookFlagData);
            return resolve({flag:"1"});
        })
    },
    yskReSelectDate({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        // dispatch('getHotWords','yskReSelectDate')
        let outCallMonetOrderData=getters.getOutCallMonetOrderData

        return new Promise((resolve, reject) => {
            if(queryParam.bookDay!=null&&queryParam.bookDay!=''){
                let req = {
                    siteCd:outCallMonetOrderData.installAddr.ziyuan.accessList.exchList.exchCode,
                    bookTime:queryParam.bookDay,
                    stdAddrId:outCallMonetOrderData.installAddr.SEGM_ID
                }
                let bookTimeS='';
                http.post('/iptvReceive/iptvBookingDate',
                    req
                ).then(res=>{
                    commit('hideLoading');
                    if (res.respCode === '0000') {
                        if("0"==res.respData[Number(queryParam.bookTime)-1].flag){
                            if(1==Number(queryParam.bookTime)){
                                bookTimeS='上午(9:00-12:00)'
                            }
                            if(2==Number(queryParam.bookTime)){
                                bookTimeS='下午(12:00-18:00)'
                            }
                            if(3==Number(queryParam.bookTime)){
                                bookTimeS='晚上(18:00-21:00)'
                            }
                            outCallMonetOrderData.timeData.stdBookDay=queryParam.bookDay
                            outCallMonetOrderData.timeData.stdBookTime=bookTimeS
                            commit('setOutCallMonetOrderData',outCallMonetOrderData)
                            return resolve({flag:"1",bookReSelectTime:queryParam.bookDay+" "+bookTimeS});
                        }else{
                            return resolve({flag: "2", respMsg: "该时间段不可预约，请重新选择"});
                        }
                    }
                    else{
                        return resolve({flag: "2", respMsg: res.respMsg});
                    }
                })
            }else{
                return resolve({flag:"1",bookReSelectTime: outCallMonetOrderData.timeData.stdBookDay+" "+outCallMonetOrderData.timeData.stdBookTime});}
        })
    },
    queryContactMsgYsk({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        // dispatch('getHotWords','queryContactMsgYsk')
        return new Promise((resolve, reject) => {
            let outCallMonetOrderData=getters.getOutCallMonetOrderData
            let contactPhone=queryParam.contactPhone
            if(contactPhone == '' || contactPhone == 'undefined' || contactPhone == undefined || contactPhone == null || contactPhone == 'null'){
                return resolve({flag:"1",contactQueryPhone:outCallMonetOrderData.timeData.contactPhone});
            }else{
                return resolve({flag:"1",contactQueryPhone:contactPhone});}
        })
    },
    selectAddressCommit({commit, getters,dispatch}, queryParam){
        return new Promise((resolve, reject) => {
            let outCallMonetOrderData=getters.getOutCallMonetOrderData
                if(Object.keys(outCallMonetOrderData.installAddr).length === 0){
                return resolve({flag:"2"});
            }else{
                return resolve({flag:"1"});
            }

        })
    },
    contactMsgSubmitYsk({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        commit('setRespTipArrQry',[])
        commit('setBlockShow',false)
        return new Promise((resolve, reject) => {
            let outCallMonetOrderData=getters.getOutCallMonetOrderData
            let contactPhone=queryParam.contactPhone
            let contactName=queryParam.contactName
            if(contactPhone == '' || contactPhone == 'undefined' || contactPhone == undefined || contactPhone == null || contactPhone == 'null'){
            }
            else{
                outCallMonetOrderData.timeData.contactPhone=queryParam.contactPhone
                commit('setOutCallMonetOrderData',outCallMonetOrderData)
            }
            if(contactName == '' || contactName == 'undefined' || contactName == undefined || contactName == null || contactName == 'null'){
            }
            else{
                outCallMonetOrderData.timeData.contactName=queryParam.contactName
                commit('setOutCallMonetOrderData',outCallMonetOrderData)
            }
            return resolve({flag:"1"});
        })
    },
    getKdProductDataYsk({commit, getters,dispatch}, queryParam) {
        commit('showLoading');
        return new Promise((resolve, reject) => {
            let outCallMonetOrderData=getters.getOutCallMonetOrderData
            let wslProductLimitListCache=outCallMonetOrderData.checkNumberData.wslProductLimitList;
                if (queryParam.kdGoodsType == '' || queryParam.kdGoodsType == 'undefined' || queryParam.kdGoodsType == undefined || queryParam.kdGoodsType == null || queryParam.kdGoodsType == 'null') {
                }
           else{
                wslProductLimitListCache=wslProductLimitListCache.filter(i=>i.ancestors==(queryParam.kdGoodsType))
            }
                if (queryParam.kdJmTag == '' || queryParam.kdJmTag == 'undefined' || queryParam.kdJmTag == undefined || queryParam.kdJmTag == null || queryParam.kdJmTag == 'null') {
                    
                }
           else{
                wslProductLimitListCache=wslProductLimitListCache.filter(i=>i.limitTag==queryParam.kdJmTag)
            }
            if(wslProductLimitListCache.length==0){
                wslProductLimitListCache=outCallMonetOrderData.checkNumberData.wslProductLimitList
            }
            commit('setWslProductLimitList',wslProductLimitListCache)
            console.log(wslProductLimitListCache)
            return resolve({flag:"1"});
        })
    },
    // Ysk号码校验
    yskNumCheck({commit, getters,dispatch}, queryParam) {
        // Toast.loading({
        //     icon:''
        // });
        commit('showLoading');
        const outCallMonetOrderData=getters.getOutCallMonetOrderData
        let serialNumber=queryParam.phoneSerialNumber;
        return new Promise((resolve, reject) => {
            let req = {
                serviceNumber: serialNumber,
                serviceClassCode:"0000",
                switchToOutCallFlag:outCallMonetOrderData.singleSwitchParams.switchToOutCallFlag,
                commId:outCallMonetOrderData.singleSwitchParams.commId
            }
            http.post('/outCallMonetGBroad/checkNumber', req).then(res => {
                console.log(queryParam.phoneSerialNumber,"queryParam.broadbandNum");

                let mainNumVerifyFlag = 0
                commit('hideLoading');
                errorHandle(res, () => {
                    commit('setBlockShow',true)
                    // dispatch('getHotWords','yskNumCheck')
                    outCallMonetOrderData.checkNumberData=res.respData
                    outCallMonetOrderData.checkNumberData.serialNumber=serialNumber
                    outCallMonetOrderData.timeData.contactPhone=serialNumber
                    outCallMonetOrderData.timeData.contactName=res.respData.showJson.showCustName
                    commit('setOutCallMonetOrderData',outCallMonetOrderData);
                    console.log("",outCallMonetOrderData.checkNumberData.serialNumber)
                    return resolve({flag: "1",switchToOutCallFlag:outCallMonetOrderData.singleSwitchParams.switchToOutCallFlag});
                }, () => {
                    return resolve({
                        flag: "2",
                        respMsg: res.respMsg,
                        switchToOutCallFlag:"0"
                    });
                });
            })
        })
    },
    // 移送宽
    yskOrderInfoSubmit({commit, getters,dispatch}, queryParam) {
        return new Promise((resolve, reject) => {
            let outCallMonetOrderData=getters.getOutCallMonetOrderData;
            let zwInfoList={};
            if(outCallMonetOrderData.goodData.zwList.length>0){
                let zw={
                    commodityCode:outCallMonetOrderData.goodData.zwList[0].commodityCode,
                    commodityName:outCallMonetOrderData.goodData.zwList[0].commodityName,
                }
                zwInfoList=zw;
            }

            let custInfoList={};
            if(outCallMonetOrderData.goodData.custList.length>0){
                let zw={
                    commodityCode:outCallMonetOrderData.goodData.custList[0].commodityCode,
                    commodityName:outCallMonetOrderData.goodData.custList[0].commodityName,
                }
                custInfoList=zw;
            }


            let ywfGoodInfoList=[];
            if(outCallMonetOrderData.goodData.commodityChooseYWUlList.length==0){
                let ywf={
                    ywfFlg:0,
                    ywfName:'不需要',
                    goodsPrice:0
                };
                ywfGoodInfoList.push(ywf);
            }
            if(outCallMonetOrderData.goodData.commodityChooseYWUlList.length>0){
                if(outCallMonetOrderData.goodData.commodityChooseYWUlList[0].ywGoodId!='0'){
                    for(let i=0;i<outCallMonetOrderData.goodData.commodityChooseYWUlList.length;i++){
                        let ywf={
                            ywfFlg:outCallMonetOrderData.goodData.commodityChooseYWUlList[i].ywGoodId,
                            ywfName:outCallMonetOrderData.goodData.commodityChooseYWUlList[i].ywGoodName,
                            goodsPrice:0
                        }
                        ywfGoodInfoList.push(ywf);
                    }
                }
                else{
                    let ywf={
                        ywfFlg:0,
                        ywfName:outCallMonetOrderData.goodData.commodityChooseYWUlList[0].ywGoodName,
                        goodsPrice:0
                    };
                    ywfGoodInfoList.push(ywf);

                }
            }
            let iptvInfoList=[];
            if(outCallMonetOrderData.goodData.iptvList.length>0) {
                for (let i = 0; i < outCallMonetOrderData.goodData.iptvList.length; i++) {
                    let iptv = {
                        iptvFlg: outCallMonetOrderData.goodData.iptvList[i].commodityCode,
                        iptvName: outCallMonetOrderData.goodData.iptvList[i].iptvName,
                        goodsPrice: outCallMonetOrderData.goodData.iptvList[i].goodsPrice,
                        goodsJMPrice: outCallMonetOrderData.goodData.iptvList[i].goodsJMPrice,
                        commType: outCallMonetOrderData.goodData.iptvList[i].commType
                    }
                    iptvInfoList.push(iptv)
                }
            }

            let kdfGoodInfoList=[];
            if(outCallMonetOrderData.goodData.kdfList.length==0){
                let ywf={
                    commId:0,
                    commName:'不需要',
                    goodsPrice:0
                };
                kdfGoodInfoList.push(ywf);
            }
            if(outCallMonetOrderData.goodData.kdfList.length>0){
                if(outCallMonetOrderData.goodData.kdfList[0].commId!='0'){
                    for(let i=0;i<outCallMonetOrderData.goodData.kdfList.length;i++){
                        let ywf={
                            kdfjFlg:outCallMonetOrderData.goodData.kdfList[i].commId,
                            kdfjName:outCallMonetOrderData.goodData.kdfList[i].commName,
                            goodsPrice:0
                        }
                        kdfGoodInfoList.push(ywf);
                    }
                }
                else{
                    let ywf={
                        kdfjFlg:0,
                        kdfjName:outCallMonetOrderData.goodData.kdfList[0].commName,
                        goodsPrice:0
                    };
                    kdfGoodInfoList.push(ywf);

                }
            }
            let  stdContact=outCallMonetOrderData.timeData.contactName;
            if(isEmpty(stdContact)){
                stdContact=outCallMonetOrderData.checkNumberData.showJson.custName;
            }
            if(stdContact==outCallMonetOrderData.checkNumberData.showJson.showCustName){
                stdContact=outCallMonetOrderData.checkNumberData.showJson.custName;
            }
            let communityAddrInfo={
                exchCode:outCallMonetOrderData.installAddr.ziyuan.accessList.exchList.exchCode,
                addressName:outCallMonetOrderData.installAddr.fullAddr,
                addressCode:outCallMonetOrderData.installAddr.SEGM_ID,
                stdContact:stdContact,
                stdContactPhone:outCallMonetOrderData.timeData.contactPhone,
                stdBookDay:outCallMonetOrderData.timeData.stdBookDay,
                stdBookTime:outCallMonetOrderData.timeData.stdBookTime
            }
            let req={
                kdfjGoodInfoList:kdfGoodInfoList,
                ywfGoodInfoList:ywfGoodInfoList,
                iptvInfoList:iptvInfoList,
                zwCommodityInfo:zwInfoList,
                kdCommodityInfo:custInfoList,
                orderType:"03",
                communityAddrInfo:communityAddrInfo,
                serviceNumber:outCallMonetOrderData.checkNumberData.serialNumber,
                choiceValue:"1",
                orderPrice:outCallMonetOrderData.orderPrice,
                remark:"备注:",
                singleSwitchParams:outCallMonetOrderData.singleSwitchParams
            }
            http.post('/outCallMonetGBroad/preSubmitYsk',req).then((res)=>{
                if (res.respCode === '0000') {
                    outCallMonetOrderData.orderId=res.respData.orderId;
                    commit('setOutCallMonetOrderData',outCallMonetOrderData);
                    return resolve({flag: "1", respMsg: '订单提交成功!订单号'+res.respData.orderId});

                } else{
                    return resolve({flag: "2", respMsg: '订单提交失败'+res.respMsg});
                }
            })
        })
    },
    // 移送宽
    getIptvProductDataYsk({commit, getters,dispatch}, queryParam) {
        return new Promise((resolve, reject) => {
            let req1={}
            if(queryParam.bmGoodsType!=null){
                req1={
                    catId:queryParam.bmGoodsType
                }}
            // dispatch('getHotWords','getIptvProductDataYsk')
            http.post('/outCallMonetGBroad/newQueryIptvInfo', req1).then(res => {
                commit('hideLoading')
                if (res.respCode === '0000') {
                    let iptvReProductList=res.respData
                    commit('setYskIptvCacheList',iptvReProductList)
                    return resolve({flag:"1"});
                }
                else{
                    return resolve({flag:"2",respMsg: res.respMsg});}
            })
        })
       
    },
// 移送宽
    getZwProductDataYsk({commit, getters,dispatch}, queryParam) {
        let req1={}
        if(queryParam.bmGoodsType!=null){
            req1={
                catId:queryParam.bmGoodsType
            }}
        return new Promise((resolve, reject) => {
            // dispatch('getHotWords','getZwProductDataYsk')
            http.post('/outCallMonetGBroad/zwProCoCenterCommSearch', req1).then(res => {
                commit('hideLoading')
                if (res.respCode === '0000') {
                    let zuReProductList=res.respData.zwGoodList
                    commit('setYskZwCacheList',zuReProductList)
                    return resolve({flag:"1"});
                }
                else{
                    return resolve({flag:"2",respMsg: res.respMsg});}
            })
        })
       
    },
    selectRrightIptvYsk({commit, getters,dispatch}, queryParam){
        return new Promise((resolve, reject) => {
            let outCallMonetOrderData=getters.getOutCallMonetOrderData
            if(outCallMonetOrderData.goodData.iptvList.length<=0){
                return resolve({flag:"2",errorTip:"请选择超清商品"});
            }else{
                let c=[];
                for(let i=0;i<outCallMonetOrderData.goodData.iptvList.length;i++){
                    c.push(outCallMonetOrderData.goodData.iptvList[i].ancestors)
                }
                if(c.includes("4031062,1001")&&(!c.includes("4031061,1001"))){
                    return resolve({flag:"2",errorTip:"请重新选择，需要从第一路IPTV开始选择。"});
                }
               else if(c.includes("4031063,1001")&&((!c.includes("4031061,1001")||!c.includes("4031062,1001")))){
                    return resolve({flag:"2",errorTip:"请重新选择，需要从第一路IPTV开始选择。"});
                }
               else{
                    return resolve({flag:"1",errorTip:""});  
                }
            }
        })
    },
    // 移送宽
    getYwfjProductDataYsk ({commit, getters,dispatch}, queryParam) {
        // dispatch('getHotWords','getYwfjProductDataYsk')
        let outCallMonetOrderData=getters.getOutCallMonetOrderData
        let iptvCodeString={
            iptvInfoList:[]
        }
        let iptvCode1=[]
        return new Promise((resolve, reject) => {
            console.log(outCallMonetOrderData.goodData.iptvList,"02106952134")
            if(outCallMonetOrderData.goodData.iptvList.length<=0){
                if(outCallMonetOrderData.b2iCommTag=="0"){
                    if(outCallMonetOrderData.zuWithYwList.length>0){
                        return resolve({flag:"1"});
                    }
                    else{
                        return resolve({flag:"3"});
                    }
                }
               else{
                    return resolve({flag:"3"});
                }
            }
            else{
                for(let i=0;i<outCallMonetOrderData.goodData.iptvList.length;i++) {
                    let  iptvCodeInfo={};
                    iptvCodeInfo={
                        iptvId: outCallMonetOrderData.goodData.iptvList[i].commodityCode,
                        iptvName:outCallMonetOrderData.goodData.iptvList[i].iptvName,
                        commType:outCallMonetOrderData.goodData.iptvList[i].commType
                    }
                    iptvCode1.push(iptvCodeInfo)}
                iptvCodeString.iptvInfoList=iptvCode1
                let req = {
                    iptvCode:JSON.stringify(iptvCodeString),
                    ywProductId:outCallMonetOrderData.checkNumberData.showJson.ywProductId==null?"":outCallMonetOrderData.checkNumberData.showJson.ywProductId
                }
                http.post('/outCallMonetGBroad/getYwfGoodsByIPTV', req).then(res => {
                    commit('hideLoading')
                    let commodityYWUlList=[]
                    if (res.respData.code === '0000') {
                        if(res.respData.ywFreeGoodsListResult.length>0){
                            if(outCallMonetOrderData.b2iCommTag=="0"){
                                    commit('setYskYwfCacheList',res.respData.ywFreeGoodsListResult)
                                    return resolve({flag:"1"});
                            }
                            else{
                                return resolve({flag:"2"});
                            }
                              
                        }else{
                            if(outCallMonetOrderData.b2iCommTag=="0"){
                                commit('setYskYwfCacheList',[])
                                if(outCallMonetOrderData.zuWithYwList.length>0){
                                    return resolve({flag:"1"});
                                }
                                else{
                                    return resolve({flag:"2"});
                                }
                            }
                            else{
                                return resolve({flag:"2"});
                            }
                        }
                    }
                    else{
                        return resolve({flag:"2",respMsg: res.respMsg});
                    }
                })
            }
        })
    },
    getKdfjProductDataYsk({commit, getters,dispatch}, queryParam) {
        return new Promise((resolve, reject) => {
            let outCallMonetOrderData=getters.getOutCallMonetOrderData

            // dispatch('getHotWords','getKdfjProductDataYsk')
            http.post('/outCallMonetGBroad/qryKuanDaiFuJia', {}).then(res => {
                commit('hideLoading')
                if (res.respCode === '0000') {
                    let kdfReProductList=res.respData
                    outCallMonetOrderData.goodData.kdfList=kdfReProductList
                    commit('setYskKdfCacheList',kdfReProductList)
                    commit('setOutCallMonetOrderData',outCallMonetOrderData)
                    return resolve({flag:"1"});
                }
                else{
                    outCallMonetOrderData.goodData.kdfList=[]
                    commit('setOutCallMonetOrderData',outCallMonetOrderData)
                    return resolve({flag:"1"});}
            })
        })
    },
    saveCacheData({commit, getters}, queryParam) {
        return new Promise((resolve, reject) => {
                const jzfkOrderData=getters.getJzfkOrderData
                const curTask=getters.getCurTask
                jzfkOrderData.curTask.taskId=curTask.taskId
                jzfkOrderData.curTask.toolCode=curTask.toolCode
                jzfkOrderData.curTask.taskName=curTask.taskName

                const chatList=getters.getChatList
                jzfkOrderData.chatList=chatList
                commit('setJzfkOrderData',jzfkOrderData);

                let req1 = {
                    operatorId: getters.getShbmMsgInfo.operatorId,
                    jzfkOrderData:JSON.stringify(getters.getJzfkOrderData)
                }
                http.post('/contineBreak/saveData',req1).then(res=>{
                });
        })
    },
    iptvClickFee({commit, getters},iptv){
        let data = {
            "iptvQryCommId": iptv.commodityCode,
            "iptvJmTag": "0"
        }

        http.post('/iptvReceive/getIptvGoodsFeeByInterface', data).then(res => {
            if (res.respCode != "0000") {
            }
            // 接收返回参数
           else{
                iptv.goodsJMPrice=res.respData.goodsJmPrice;
                iptv.goodsPrice=res.respData.goodsPrice;
                const iptvOrderData=getters.getIptvOrderData;
                iptvOrderData.iptvGoodData.iptvList.push(iptv);
                let orderPrice=iptvOrderData.orderPrice
                orderPrice+=(parseInt(res.respData.goodsJmPrice)+parseInt(res.respData.goodsPrice));
                iptvOrderData.orderPrice=orderPrice;
                commit('setIptvOrderData',iptvOrderData);
            }
        }).catch(e => {
            console.log(e)
        })
    },
    zwClickFee({commit, getters},zw){
        let data = {
            "commId": zw.commodityCode
        }
        http.post('/zhzwZsd/qryProduct', data).then(res => {
            if (res.respCode != "0000") {
            }
          else{
                // 接收返回参数
              let  zwOrderData=getters.getZwOrderData;
              let orderPrice=zwOrderData.orderPrice;
                 orderPrice+=parseInt(res.respData.goodsPrice);
                zwOrderData.orderPrice=orderPrice;
                commit('setZwOrderData',zwOrderData);
            }
        }).catch(e => {
           console.log(e)
        })
    },

 
    withInMonths({commit, getters},n){
            // 获取当前日期
        if (n <= 0 || n > 11) {
            throw new Error("n should be a positive integer and less than or equal to 11");
        }
        const currentDate = new Date();
        let year = currentDate.getFullYear();
        let month = currentDate.getMonth();
        // Subtract n months from the current month
        month -= n;

        // Handle the case where month goes negative (e.g., January - 1 month should be December of the previous year)
        if (month < 0) {
            year -= Math.ceil(Math.abs(month) / 12); // Adjust year
            month += 12 * Math.ceil(Math.abs(month) / 12); // Normalize month to be within 0-11
        }

        // Create the new date object with the adjusted year and month
        const previousDate = new Date(year, month, currentDate.getDate());

        // Format the date as yyyy-mm-dd
        const formattedDate = previousDate.toISOString().split('T')[0];
            console.log(formattedDate,"formattedDate")
            commit('setFormatedDate',formattedDate)
},
    getHotWords({commit, getters},qry){
        commit('setBlockShow',true);
        let data=
        {hotWordId:qry}
        http.post('/mpComm/getHotWordsByCodeId', data).then(res => {
            let tips=[]
            if (res.respCode == "0000") {
                if(res.respData.length>0){
                    tips = res.respData
                    commit('setRespTipArrQry',tips)
                }else{
                    commit('setRespTipArrQry',tips)
                }
            }
            
            else{
                commit('setRespTipArrQry',tips)
            }
        }).catch(e => {
            console.log(e)
        })
    },
    updateChatList({commit, state}, param) {
        //对话记录
        const talkParam = { ...param };
        //去除语音base64数据
        if (talkParam.moduleName === 'AudioInput'){
            talkParam.params.audio = ''
        }
        //查找上一个对话记录
        const previous = state.chatList[state.chatList.length - 1];
        const record = {
            instanceId: '',
            previousInstanceId: previous ? previous.params.instanceId : '',
            agentSessionId: state.agentSessionId,
            nodeCode:  state.curTask?.taskName || '',
            type: talkParam.type,
            submit: '0',
            content: JSON.stringify(talkParam),
            failureReply: talkParam.failureReply ? talkParam.failureReply : '0',
            sender: talkParam.sender==='1'?'Chatbot':'',
            sendTime: getCurrentFormattedTime()
        }
        commit('updateChatList', param);
        //获取instanceId
        const generateInstanceId = http.post('/smartAccept/generateInstanceId', {}).then(res => {
            if (res.respCode === '0000') {
                record.instanceId = res.respData.split("|")[2]
                if (param.params){
                    param.params.instanceId = res.respData.split("|")[2]
                }else {
                    param.params = {
                        instanceId: res.respData.split("|")[2]
                    }
                }
            }else {
                record.instanceId = ''
            }
        }).catch(err => {
            console.log("获取instanceId失败：",err)
            record.instanceId = ''
        })
        Promise.all([generateInstanceId]).then(()=>{
            //对话记录
            http.post('/smartAccept/conversation', record)
                .catch(err => {
                    console.log("保存对话记录失败：", err)
                });
        })
    },
    custOrderQueryForThree({commit, getters,dispatch}, queryParam) {
        let req = {};
        let oneMonthAgo=new Date();
        const yearAgo = oneMonthAgo.getFullYear();
        const monthAgo = String(oneMonthAgo.getMonth() + 1).padStart(2, '0');
        const dayAgo = String(oneMonthAgo.getDate()).padStart(2, '0');
        const currentDate = `${yearAgo}-${monthAgo}-${dayAgo}`;
        dispatch('withInMonths',1);
        dispatch('getHotWords','custOrderQuery')
        let  formattedOneMonthAgo=getters.getFormatedDate;
        console.log(formattedOneMonthAgo,"formattedOneMonthAgo")
        const mqBookFlagData=getters.getMqBookFlagData
        console.log(mqBookFlagData.bookAddrTimeFlag)
        console.log(Object.keys(queryParam).length === 0)
        if(Object.keys(queryParam).length === 0){

            console.log('456')
            commit('setShowOrderList',false);
            req = {
                startDate:formattedOneMonthAgo,
                endDate:currentDate
            }
        }
        else{
            console.log('123')
            if(queryParam.bssOrderId!=null){
                req = {
                    sceneType: queryParam.sceneType==null?'':queryParam.sceneType,
                    serialNumber:queryParam.serialNumber==null?'':queryParam.serialNumber,
                    orderId:queryParam.bssOrderId==null?'':queryParam.bssOrderId,
                    orderState:queryParam.orderState==null?'':queryParam.orderState,
                    orderStateDetect:queryParam.orderStateDetect==null?'':queryParam.orderStateDetect
                }
            }else{
                req = {
                    sceneType: queryParam.sceneType==null?'':queryParam.sceneType,
                    serialNumber:queryParam.serialNumber==null?'':queryParam.serialNumber,
                    orderId:queryParam.bssOrderId==null?'':queryParam.bssOrderId,
                    startDate:queryParam.startDate==null?formattedOneMonthAgo:queryParam.startDate,
                    endDate:queryParam.endDate==null?currentDate:queryParam.endDate,
                    orderState:queryParam.orderState==null?'':queryParam.orderState,
                    orderStateDetect:queryParam.orderStateDetect==null?'':queryParam.orderStateDetect
                }
            }
        }
        commit('setCacheQryPara',req);
        mqBookFlagData.bookAddrTimeFlag="0";
        commit('setMqBookFlagData',mqBookFlagData);
        commit('showLoading');
        return new Promise((resolve, reject) => {
            http.post('/Order/custOrderQueryForThree', req).then(res => {
                let mainNumVerifyFlag = 0
                commit('hideLoading');
                errorHandle(res, () => {
                    if(Object.keys(queryParam).length === 0){
                        commit('setShowOrderList',false);
                    }else{
                        commit('setShowOrderList',true);
                    }
                    commit('setOrderInfoLists',res.respData)
                    if(res.respData.length==0||res.respData==[]){
                        commit('setShowOrderList',false);
                        return resolve({
                            flag: "2",
                            respMsg: '查询结果为空',
                        });
                    }
                    else{
                        return resolve({
                            flag: "1"
                        });
                    }

                }, () => {
                    commit('setShowOrderList',false);
                    commit('setOrderInfoLists',[])
                    return resolve({
                        flag: "2",
                        respMsg: res.respMsg,
                    });
                });
            })
        })
    },
    /**
     * 单移网
     */
    // 号码校验
    singleModeNetworkNumCheck({ commit, getters, dispatch }, queryParam) {
        console.log(getters, commit, "getters-----------");
        commit("showLoading");
        return new Promise((resolve, reject) => {
            const singleModeNetworkOrderData = getters.getSingleModeNetworkOrderData;
            let serialNumber = queryParam.qrySerialNumber;
            if (
                serialNumber == "" ||
                serialNumber == "undefined" ||
                serialNumber == undefined ||
                serialNumber == null ||
                serialNumber == "null"
            ) {
                serialNumber = singleModeNetworkOrderData.selectedNumber;
            }
            let req = {
                serialNumber: serialNumber,
                phoneType: queryParam.phoneType,
            };
            http.post("/singleMobile/checkNumber", req).then((res) => {
                let mainNumVerifyFlag = 0;
                commit("hideLoading");
                errorHandle(
                    res,
                    () => {
                        singleModeNetworkOrderData.checkNumberData = res.respData;
                        singleModeNetworkOrderData.checkNumberData.serialNumber = serialNumber;
                        commit("setBlockShow", true);
                        commit("setSingleModeNetworkOrderData", singleModeNetworkOrderData);
                        return resolve({
                            flag: "1",
                            numberIsSendFlag: res.respData.numberIsSendFlag,
                            userTypeFlag: res.respData.userTypeFlag
                        });
                    },
                    () => {
                        return resolve({
                            flag: "2",
                            respMsg: res.respMsg,
                            numberIsSendFlag: "0",
                            userTypeFlag:"0"
                        });
                    }
                );
            });
        });
    },
    // 发短信
    singleModeNetworkSendMsg({ commit, getters }, queryParam) {
        commit("showLoading");
        return new Promise((resolve, reject) => {
            let req = {
                serialNumber: getters.getSingleModeNetworkOrderData.checkNumberData.serialNumber,
            };
            http.post("/singleMobile/wgmessage", req).then((res) => {
                commit("hideLoading");
            });
            return resolve({ flag: "1" });
        });
    },
    // 短信验证码校验
    singleModeNetworkCodeVerify({ commit, getters, dispatch }, queryParam) {
        commit("showLoading");
        return new Promise((resolve, reject) => {
            let req = {
                serialNumber:
                getters.getSingleModeNetworkOrderData.checkNumberData.serialNumber,
                checkCode: queryParam.msgCode,
            };
            commit("hideLoading");
            http
                .post("/singleMobile/checkCodeValidate", req)
                .then((res) => {
                    if ("0000" == res.respCode) {
                        return resolve({ flag: "1", msgCodeVerifyFlag: "1", msgCode: "" });
                    } else if ("7777" == res.respCode) {
                        return resolve({ flag: "2", respMsg: res.respMsg, validFlag: "4" });
                    } else {
                        return resolve({ flag: "3", respMsg: res.respMsg });
                    }
                })
                .catch((e) => {
                    alertError({
                        title: "出错了！",
                        message: e,
                        confirmButtonText: "报告错误",
                    });
                    return resolve({ flag: "2" });
                });
        });
    },
    // 合约商品
    getSingleModeNetworkList({ commit, getters, dispatch }, queryParam) {
        commit("showLoading");
        return new Promise((resolve, reject) => {
             let custList = getters.getSingleModeNetworkOrderData.fusionUpFttrGoodData.custList;
             let commId="";
             if(custList.length>0){
                 commId=custList[0].commodityCode
             }
            let req1 = {
                singleCommId: commId
            };
            if (queryParam.bmGoodsType != null) {
                req1 = {
                    catId: queryParam.bmGoodsType,
                    singleCommId: commId
                };
            }
            http.post("/singleMobile/getCustGoodForContanct", req1).then((res) => {
                commit("hideLoading");
                if (res.respCode === "0000") {
                    let iptvReProductList = res.respData.zwProList;
                    iptvReProductList.sort((a, b) => {
                        const hasPropertyA = "RENT" in a;
                        const hasPropertyB = "RENT" in b;
                        // 如果 a 有属性而 b 没有，a 应该在前面
                        if (hasPropertyA && !hasPropertyB) {
                            return -1;
                        }
                        // 如果 b 有属性而 a 没有，b 应该在前面
                        if (!hasPropertyA && hasPropertyB) {
                            return 1;
                        }
                        // 如果两者都有或都没有，保持原有顺序（或根据其他条件排序）
                        return 0;
                    });
                    commit("setSingleModeNetworkList", iptvReProductList);
                    return resolve({ flag: "1" });
                } else {
                    return resolve({ flag: "2", respMsg: res.respMsg });
                }
            });
        });
    },
    // 移网商品
    getYwSingleModeNetworkList({ commit, getters, dispatch }, queryParam) {
        commit("showLoading");
        return new Promise((resolve, reject) => {
            console.log(getters, "getters");
            const getData = getters.getSingleModeNetworkOrderData.checkNumberData;
            let req1 = {
                bigUseFee: getData.bigUseFee,
                onlineMonths: getData.onlineMonths,
            };
            if (queryParam.bmGoodsType != null) {
                req1 = {
                    bigUseFee: getData.bigUseFee,
                    onlineMonths: getData.onlineMonths,
                    catId: queryParam.bmGoodsType,
                };
            }
            http.post("/singleMobile/getCustGoodForSingle", req1).then((res) => {
                commit("hideLoading");
                if (res.respCode === "0000") {
                    let iptvReProductList = res.respData.zwProList;
                    iptvReProductList.sort((a, b) => {
                        const hasPropertyA = "RENT" in a;
                        const hasPropertyB = "RENT" in b;
                        // 如果 a 有属性而 b 没有，a 应该在前面
                        if (hasPropertyA && !hasPropertyB) {
                            return -1;
                        }
                        // 如果 b 有属性而 a 没有，b 应该在前面
                        if (!hasPropertyA && hasPropertyB) {
                            return 1;
                        }
                        // 如果两者都有或都没有，保持原有顺序（或根据其他条件排序）
                        return 0;
                    });
                    commit("setYwSingleModeNetworkList", iptvReProductList);
                    return resolve({ flag: "1" });
                } else {
                    return resolve({ flag: "2", respMsg: res.respMsg });
                }
            });
        });
    },
    // 订单提交
    SingleModeNetworkpreSubmit({ commit, getters, dispatch }, queryParam) {
        this.$emit("startLoading", "");
        const _data = getters.getSingleModeNetworkOrderData;
        let contactCommodityInfoList=[];
        const custList = _data.fusionUpFttrGoodData.custList;
        const   contactList= _data.fusionUpFttrGoodData.commodityChooseYWUlList;
        if(contactList.length>0){
            for(let i=0;i<contactList.length;i++){
                let c={
                    commodityCode:contactList[i].commodityCode,
                    commodityName:contactList[i].commodityName
                }
                contactCommodityInfoList.push(c)
            }
        }
        let req = {
            //单商品
            singleCommodityInfo: {
                commodityCode: custList[0].commodityCode,
                commodityName: custList[0].commodityName,
            },
            //合约商品
            contactCommodityInfoList: contactCommodityInfoList,
            serviceNumber: _data.checkNumberData.serialNumber,
            orderPrice: _data.orderPrice,
            remark: _data.remark,
            custName: _data.checkNumberData.custName,
        };
        this.$http.post("/singleMobile/preSubmit", req).then((res) => {
            this.$emit("endLoading", "");
            if (res.respCode === "0000") {
                fusionUpFttrOrderData.orderId = res.respData.orderId;
                commit("setFusionUpFttrOrderData", fusionUpFttrOrderData);
                return resolve({
                    flag: "1",
                    respMsg: "订单提交成功!订单号" + res.respData.orderId,
                });
            } else {
                return resolve({ flag: "2", respMsg: "订单提交失败" + res.respMsg });
            }
        });
    },
    //通过证件号去号码
    qryBroadPhoneByCertCodeSingleMobile({commit, getters,dispatch}, queryParam){
        commit('showLoading');
        return new Promise((resolve, reject) => {
            let req = {
                psptId: queryParam.checkCertNumber
            }
            http.post('/mpComm/qryYwPhoneByCertSingleMobile', req).then(res => {
                let mainNumVerifyFlag = 0
                commit('hideLoading');
                errorHandle(res, () => {
                    const singleModeNetworkOrderData=getters.getSingleModeNetworkOrderData
                    singleModeNetworkOrderData.broadNumberByYw=res.respData.ywNumList
                    commit('setSingleModeNetworkOrderData',singleModeNetworkOrderData);
                    if(res.respData.ywNumList.length==1){
                        singleModeNetworkOrderData.selectedNumber=res.respData.ywNumList[0].ywSerialNumber
                        commit('setSingleModeNetworkOrderData',singleModeNetworkOrderData);
                        return resolve({flag: "3"});
                    }else{
                        return resolve({flag: "1",iptvNumCheckFlag:"1"});
                    }
                }, () => {
                    return resolve({
                        flag: "2",
                        respMsg: res.respMsg
                    });
                });
            }).catch((e) => {
                alertError({
                    title: '出错了！',
                    message: e,
                    confirmButtonText: '报告错误'
                });
                return resolve({respMsg: e + "请稍后重试", flag: "2"});
            });
        })
    },


    SingleMobileSwitchToFuseFttr({ commit, getters, dispatch }, queryParam){
        return new Promise((resolve, reject) => {
            return resolve({
                flag: "1",
                serialNumber:getters.getSingleModeNetworkOrderData.checkNumberData.serialNumber
            });
        })

    },

    SingleMobileSwitchToYsk({ commit, getters, dispatch }, queryParam){
        return new Promise((resolve, reject) => {
            return resolve({
                flag: "1",
                serialNumber:getters.getSingleModeNetworkOrderData.checkNumberData.serialNumber
            });
        })
    }
};