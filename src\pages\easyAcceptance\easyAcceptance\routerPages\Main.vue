<template>
    <div class="container" style="height: 100%;" ref="containertotal">
        <Header :showBack="false" title="智能受理" @hideChatContainer="showChatContainer = false" @startNewChat="startNewChat" @checkOrderDetail="checkOrderDetail"  @setUpEnd="setUpEnd"  @userInput="userInput"/>
<!--        <div class="main-content">
            <div class="w-part part-1" v-show="commonMenus.length > 0">
                <van-row gutter="12">
                    <van-col span="6" v-for="item in commonMenus" :key="item.MENU_ID" @click="clickMenu(item)">
                        <div class="part-item">
                            <img :src="require(`@/assets/images/icon_commonMenu_${item.MENU_NAME}.png`)" />
                            <div>{{ item.MENU_NAME }}</div>
                        </div>
                    </van-col>
                </van-row>
            </div>
            <div class="w-part part-2" v-show="toolMenus.length > 0">
                <div class="part-title">工具及服务</div>
                <div class="part-content">
                    <van-row gutter="12">
                        <van-col span="12" v-for="item in toolMenus" :key="item.MENU_ID" @click="clickMenu(item)">
                            <div class="part-item">
                                <span>{{ item.MENU_NAME }}</span>
                                <img :src="require(`@/assets/images/icon_toolMenu_${item.MENU_NAME}.png`)" />
                            </div>
                        </van-col>
                        &lt;!&ndash; <van-col span="12" v-for="item in mockMenus" :key="item.name" @click="clickMockMenu(item)">
                            <div class="part-item">
                                <span>{{ item.name }}</span>
                                <img :src="require(`@/assets/images/client_portrait.png`)" />
                            </div>
                        </van-col> &ndash;&gt;
                    </van-row>
                </div>
            </div>
            <div class="w-part part-3" v-show="quesList.length > 0">
                <div class="part-title" v-show="userNum !== '' && userNum !== undefined">{{ userNum }}位用户正在浏览办理</div>
                <div class="triangle"></div>
                <div class="part-content">
                    <img src="@/assets/images/robot.png" class="robot-img" />
                    <div class="part-item hot" v-for="item in quesList.slice(0, 1)" :key="item.CONTENT" @click="clickQues(item.CONTENT)">
                        <img v-if="item.TYPE === 'process'" src="@/assets/images/icon_03.png" />
                        <img v-if="item.TYPE === 'menu'" src="@/assets/images/icon_20.png" />
                        <span>{{ item.CONTENT }}</span>
                        <img src="@/assets/images/fire_blue.png" />
                    </div>
                    <div>
                        <div class="part-item" :class="{'hot': index < 2}" v-for="(item, index) in quesList.slice(1, quesList.length)" :key="item.CONTENT" @click="clickQues(item.CONTENT)">
                            <img v-if="item.TYPE === 'process'" src="@/assets/images/icon_03.png" />
                            <img v-if="item.TYPE === 'menu'" src="@/assets/images/icon_20.png" />
                            <span>{{ item.CONTENT }}</span>
                            <img v-if="index < 2" src="@/assets/images/fire_blue.png" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-part part-4" v-show="toolList.length > 0">
                <div class="part-title">试试这样问我</div>
                <div class="part-4-content">
                    <div style="color: #666666;">作为您的业务助理，我可以帮您快速地解决一些问题：</div>
                    <div class="ques-example" v-for="(item, index) in toolList" :key="index" @click="clickQues(item.task_name)">
                        <span>{{ item.task_name }}</span>
                    </div>
                </div>
            </div>
        </div>-->
        <ChatContainer ref="chatContainer" v-show="showChatContainer" @clickMenu="clickMenu" />
        <responseTip @userInputForLabel="userInputForLabel"  @userInput="userInput" v-show="blockShow"/>
        <Footer ref="footer" @userInput="userInput" @showChatBox="showChatBox" />
        <OrderDetail :showOrderDetail.sync="showOrderDetail" @callNewChatApi="callNewChatApi" />
    </div>
  
</template>

<script>
import {mapActions, mapMutations, mapState} from 'vuex';
import { Dialog } from 'vant';
import Header from '../components/Header'
import Footer from '../components/Footer'
import ChatContainer from './components/ChatContainer'
import OrderDetail from './components/chatBox/OrderDetail.vue'
import responseTip from '../components/responseTip'
import { Mobile, Wade } from 'rk-web-utils';
import WadeMobile from 'rk-native-plugin';
import { alertError } from '@/assets/bizComponents/funcComponent.js';
import { getSentenceTime } from '../assets/js/func.js';
import watermark from '@/assets/js/watermark.js';
export default {
    name: 'Main',
    components: {
        Header,
        Footer,
        ChatContainer,
        OrderDetail,
        responseTip
    },
    data() {
        return {
            quesList: [],
            quesHotNum: 0,
            showChatContainer: true,
            showOrderDetail: false,
            commonMenus: [],
            toolMenus: [],
            userNum: '',
            toolList: [],
            recoverFlag:false,
          isBlank:''
        }
    },
    computed: {
        ...mapState([
            'chatList',
            'staffId',
            'sessionId',
            'loginPhoneNumber',
            'agentSessionId',
            'shbmMsgInfo',
            'jzfkOrderData',
            'curTask',
            'blockShow'
        ])
    },
    created() {
        this.$toast.loading({
            message: '正在初始化页面，请稍等...',
            forbidClick: true,
            duration: 0
        });
      this.$http.post('/staff/getStaff',{id:''}).then(res=>{
        // this.shbmMsgInfo=res.respData;
        this.setShbmMsgInfo(res.respData);
        console.log(this.shbmMsgInfo);
        console.log("_jzfk_agentHisSessionId"+this.isEmpty(localStorage.getItem(res.respData.operatorId+"_jzfk_agentHisSessionId")))
        if(this.isEmpty(localStorage.getItem(res.respData.operatorId+"_jzfk_agentHisSessionId"))){
          this.$refs.chatContainer.startLoading()
          this.updateChatList({
            sender: '1',
            type: 'module',
            moduleName: 'startChat',
            moduleLevel: 1,
            show: true
          })
          this.$refs.chatContainer.endLoading()
        }
          watermark.init(this.shbmMsgInfo.operatorId)
      });
        Promise.all([]).then(async () => {
            // await this.queryTaskList()
          await this.getAgentSessionId()
          this.$toast.clear();
        }).catch(() => {
            this.$toast.clear();
        }).finally(() => {
            WadeMobile.getAuthorization('microPhone').then(data => {
                console.log(data);
            })
        })
    },
    mounted() {
    const winHeight = window.innerHeight;
    window.addEventListener('resize', (event) => {
      const thisHeight = window.innerHeight;
      if (winHeight - thisHeight > 50) {
        // 键盘弹起
        event.stopPropagation()
        this.$nextTick(() => {
          this.$refs.containertotal.scrollTo({
            top: this.$refs.containertotal.scrollHeight,
            behavior: 'smooth'
          });
        })
      }
    });
  },
  methods: {
        ...mapMutations([
            'setStaffDataCodes',
            'setStaffId',
            'setLoginPhoneNumber',
            'backToAnyStep',
            'setLoginInfo',
            'setAgentSessionId',
            'setCurTask',
            'setRunTimeTaskList',
            'setShbmMsgInfo',
            'setJzfkOrderData',
            'setCurTask'
        ]),
    ...mapActions([
      'updateChatList'
    ]),
      setUpEnd(){
        Dialog.alert({
          title: '提示',
          confirmButtonColor:'#0081ff',
          message: '功能正在开发中，敬请期待!',
        }).then(() => {
          // on close
        });
      },
      isEmpty(value) {
        let flag = false
        if (value == '' || value == 'undefined' || value == undefined || value == null || value == 'null') {
          flag = true
        }
        return flag
      },
      checkOrderDetail(){
        this.showOrderDetail = true
      },
        // 获取agentSessionId
        getAgentSessionId() {
            return new Promise((resolve, reject) => {
                const params = {
                    account: this.shbmMsgInfo.operatorId, // 工号
                    accountType:   '02' , // 账号类型：01-手机号；02-工号
                    otherParam: '' // 其他身份数据（非必填）
                }
                var qs = require('qs');
                this.$http.post('/smartFlowChat/generateAgentSessionId', params).then(res => {
                    if (res.respCode === '0000') {
                        console.log(this.shbmMsgInfo.operatorId+"**************this.shbmMsgInfo.operatorId")
                      console.log(localStorage.getItem(this.shbmMsgInfo.operatorId+"_jzfk_agentHisSessionId")+"**************localStorage.getItem(this.shbmMsgInfo.operatorId+\"_jzfk_agentHisSessionId\")")
                      if(this.isEmpty(this.agentSessionId)){
                        this.setAgentSessionId(res.respData.agentSessionId);
                      }
                      if(this.isEmpty(localStorage.getItem(this.shbmMsgInfo.operatorId+"_jzfk_agentHisSessionId"))){
                        this.setAgentSessionId(res.respData.agentSessionId);
                        localStorage.setItem(this.shbmMsgInfo.operatorId+"_jzfk_agentHisSessionId",res.respData.agentSessionId)
                      }
                      else{
                        console.log(localStorage.getItem(this.shbmMsgInfo.operatorId+"_jzfk_agentHisSessionId")+"*********")
                        this.$http.post('/contineBreak/getData',{operatorId:this.shbmMsgInfo.operatorId}).then(res=>{
                          console.log("res.respData.isBlank+++++++"+("0"==res.respData.isBlank))
                          this.isBlank=res.respData.isBlank;
                          if("0"==res.respData.isBlank){
                            console.log('/contineBreak/getData')
                            this.updateChatList({
                              sender: '1',
                              type: 'module',
                              moduleName: 'ConfirmBox',
                              moduleLevel: 1,
                              params: {
                                msg: `系统检测到您之前已完成部分操作，是否恢复之前的数据?`,
                                confirmText: '确认',
                                cancelText: '取消',
                                confirm: ()=> {
                                  if(!this.isEmpty(res.respData.jzfkOrderData.curTask.taskId)){
                                    this.curTask.taskId=res.respData.jzfkOrderData.curTask.taskId;
                                    this.curTask.toolCode=res.respData.jzfkOrderData.curTask.toolCode;
                                    this.curTask.taskName=res.respData.jzfkOrderData.curTask.taskName;
                                    this.setCurTask(this.curTask)
                                  }
                                  this.setJzfkOrderData(res.respData.jzfkOrderData);
                                  this.setAgentSessionId(localStorage.getItem(this.shbmMsgInfo.operatorId+"_jzfk_agentHisSessionId"));
                                  this.updateChatList( res.respData.jzfkOrderData.chatList)
                                  const chat = res.respData.jzfkOrderData.chatList.slice().reverse().find(v => v.moduleName != 'ConfirmBox' && v.moduleName != 'ErrorInfo' && v.taskId == this.curTask.taskId)
                                  console.log(chat)
                                  if (chat) {
                                    this.updateChatList({
                                      ...chat,
                                      disabled: false,
                                    })
                                  } else {
                                    this.updateChatList({
                                      sender: '1',
                                      type: 'module',
                                      moduleName: 'TextResponse',
                                      moduleLevel: 1,
                                      params: {
                                        text: '请重新输入信息'
                                      },
                                      show: true
                                    })
                                  }
                                },
                                cancel: ()=> {
                                  localStorage.removeItem(this.shbmMsgInfo.operatorId+"_jzfk_agentHisSessionId")
                                  this.startNewChat()
                                },
                              },
                              show: true
                            })
                          }
                          else{
                            localStorage.removeItem(this.shbmMsgInfo.operatorId+"_jzfk_agentHisSessionId")
                            this.startNewChat()
                          }

                        });

                      }

                     
                    } else {
                        alertError({
                            title: '初始化失败',
                            message: res.detail,
                            confirmButtonText: '关闭子应用'
                        }).then(() => {
                            WadeMobile.closeAllSubApp(); // 关闭页面,回到主应用
                        });
                    }
                }).catch(e => {
                    alertError({
                        title: '初始化失败',
                        message: e,
                        confirmButtonText: '关闭子应用'
                    }).then(() => {
                        WadeMobile.closeAllSubApp(); // 关闭页面,回到主应用
                    });
                }).finally(() => {
                    resolve()
                });
            })
        },
        // 获取小工具等列表
        // queryTaskList() {
        //     return new Promise((resolve, reject) => {
        //         const params = {
        //             agentSessionId: this.agentSessionId
        //         }
        //         var qs = require('qs');
        //         this.$http.post('/smartFlowChat/queryTaskList', params).then(res => {
        //             if (res.respCode === '0000') {
        //                 this.toolList = res.respData.task_list
        //             } else {
        //                 this.toolList = []
        //             }
        //             resolve()
        //         }).catch(e => {
        //             alertError({
        //                 title: '出错了！',
        //                 message: e,
        //                 confirmButtonText: '报告错误'
        //             });
        //             reject()
        //         })
        //     })
        // },
        // 点击试试这样问我
        clickQues(content) {
            this.$refs.footer.keyword = content
        },
        // 用户输入（语音或文字）处理
        userInput(data) {
            this.showChatContainer = true
            this.$refs.chatContainer.userInputProcess(data)
        },
      // 用户点击快捷提示词处理
      userInputForLabel(data) {
          this.showChatContainer = true
          this.$refs.chatContainer.userInputProcessForLabel(data)
      },
        showChatBox(showWelcome) {
            this.showChatContainer = true
            if (showWelcome) {
                this.$nextTick(async () => {
                    if (this.chatList.length === 0) {
                        // 获取开场白
                        const prologue = await this.queryAgentPrologue();
                        
                        // 渲染开场白
                        this.updateChatList({
                            sender: '1',
                            type: 'module',
                            moduleName: 'startChat',
                            moduleLevel: 1,
                            params: {
                                text: prologue
                            },
                            show: true
                        })
                      // await this.updateChatList({
                      //   sender: '1',
                      //   type: 'module',
                      //   moduleName: 'selectAddress',
                      //   moduleLevel: 1,
                      //   show: true
                      // })
                      // this.updateChatList({
                      //   sender: '1',
                      //   type: 'module',
                      //   moduleName: 'OrderConfirmBox',
                      //   moduleLevel: 1,
                      //   params: {
                      //   },
                      //   show: true
                      // })
                        // setTimeout(() => {
                        //     this.$refs.chatContainer.newChatApi({
                        //         textInput: "热销",
                        //         inputType: "0",
                        //         type: "1",
                        //     }, "正在查询热销商品", "抱歉，查询热销商品失败！请输入您想要办理的业务~")
                        // }, getSentenceTime(prologue))
                    }
                })
            }
        },
        // 获取开场白
        queryAgentPrologue() {
            return new Promise((resolve, reject) => {
                const params = {
                    agentSessionId: this.agentSessionId
                }
                var qs = require('qs');
                this.$refs.chatContainer.startLoading()
                this.$http.post('/smartFlowChat/queryAgentPrologue', params).then(res => {
                    this.$refs.chatContainer.endLoading()
                    if (res.respCode === '0000') {
                        resolve(res.respData.output)
                    }
                }).catch(e => {
                    alertError({
                        title: '出错了！',
                        message: e,
                        confirmButtonText: '报告错误'
                    });
                    reject()
                });
            })
        },
        // 新建会话
        startNewChat() {
            if (this.chatList.length === 0) {
                this.showChatBox(true)
                return;
            }
            Dialog.confirm({
                title: '提示',
                message: '确定结束当前对话，并开始新的对话吗？',
                confirmButtonText: '是',
                confirmButtonColor: '#0081ff',
                cancelButtonText: '否'
            }).then(() => {// on confirm
                this.setCurTask({});
                this.setRunTimeTaskList([])
                const stepInfo = {
                    name: 'chatStart',
                    stepIndex: this.mixChangeStepList.indexOf('chatStart'),
                    stepList: this.mixChangeStepList,
                  jzfkOrderData: {
                    mainNumberCheckData:{mainNumber:'',brandCode:'',productName:'',custInfo: {}},
                    smsCodeCheckData:{},
                    goodsSelectData:[],
                    numberChooseData:{params:{},numberList:[]},
                    custCheckData:{custInfo:{}},
                    preSubmitShowData:{locationInfo:{}},
                    blockShow:true,
                    cacheQryPara:{},
                    iptvReProductList:[],
                    iptvReYwProductList:[],
                    curTask:{taskId:'',toolCode:''}
                  },
                  iptvCacheList:{
                    iptvChooseCommLists:[]
                  },
                  respTipArrQry:[],
                  iptvOrderData:{
                    checkNumberData:{},
                    iptvGoodData:{iptvList:[],commodityChooseYWUlList:[]},
                    timeData:[],
                    orderPrice:0,
                    broadNumberByYw:[],
                    selectedNumber:''
                  },
                  zwCacheList:{
                    zwChooseCommLists:[]
                  },
                  reqDatas:{},

                  zwOrderData:{
                    autoDeduct:'0',
                    checkNumberData:{},
                    zwGoodData:{zwList:[],commodityChooseYWUlList:[]},
                    timeData:[],
                    orderPrice:0,
                    broadNumberByYw:[],
                    selectedNumber:''
                  },
                  zwOrderPrice:0,
                  iptvOrderPrice:0,
                  kdOrderPrice:0,
                  mqBookFlagData:{bookAddrTimeFlag:"0"},
                  yskIptvCacheList:[],
                  yskCustCacheList:[],
                  yskYwfCacheList:[],
                  yskKdfCacheList:[],
                  yskZwCacheList:[],
                  wslProductLimitList:[],
                  outCallMonetOrderData:{
                    singleSwitchParams:{
                      commId:'',
                      commName:'',
                      switchToOutCallFlag:"0",
                    },
                    checkNumberData:{},
                    installAddr:{},
                    custWithYwList:[],
                    zuWithYwList:[],
                    goodData:{zwList:[],commodityChooseYWUlList:[],iptvList:[],custList:[],kdfList:[]},
                    timeData:[],
                    orderPrice:0,
                  },
                  broadUpReProductList:[],
                  broadUpOrderData:{
                    checkNumberData:{},
                    broadUpGoodData:{broadUpList:[],commodityChooseYWUlList:[]},
                    timeData:[],
                    orderPrice:0,
                    broadNumberByYw:[],
                    selectedNumber:'',
                    remark:'',
                    isHandleFlag:"0"
                  },
                  fusionUpFttrReYwProductList:[],
                  fusionUpFttrReProductList:[],
                  fusionUpFttrOrderData:{
                    autoDeduct:'0',
                    checkNumberData:{},
                    fusionUpFttrGoodData:{custList:[],commodityChooseYWUlList:[],commList:[]},
                    timeData:[],
                    orderPrice:0,
                    broadNumberByYw:[],
                    selectedNumber:'',
                    remark:''
                  },
                  isFour:4,
                    clearChat: true
                }
                
              this.$http.post('/staff/getStaff',{id:''}).then(res=>{
                this.setShbmMsgInfo(res.respData);
                localStorage.removeItem(res.respData.operatorId+"_jzfk_agentHisSessionId")
                Promise.all([this.backToAnyStep(stepInfo),this.deleteCacheData(), this.getAgentSessionId()]).then(() => {
                  this.showChatBox(true)
                });
                console.log(this.shbmMsgInfo);
              });
            }).catch(() => {});
        },
      deleteCacheData(){
        this.$http.post('/contineBreak/deleteData',{operatorId:this.shbmMsgInfo.operatorId}).then(res=>{
              return new Promise((resolve, reject) => {
                return resolve();
              })
              })
      },
        // 点击菜单
        clickMenu(menu) {
            if (menu.MENU_TYPE === 'H') { // 菜单为h5页面
                let url = menu.APP_REQUEST_HOST + menu.APP_REQUEST_PATH
                if (menu.MENU_APP_ID == 590211) { // 中台应用
                    url += menu.MENU_URL
                } else {
                    url += menu.MENU_PAGE_ACTION
                }
                Mobile.openH5(url, null);
            } else if (menu.MENU_TYPE === 'I') { // 菜单为IPU子应用
                WadeMobile.openIpuApp(new Wade.DataMap({
                    APP_ID: menu.MENU_APP_ID,
                    MENU_PAGE_ACTION: menu.MENU_PAGE_ACTION,
                    MENU_WELCOME_PAGE: menu.MENU_WELCOME_PAGE,
                    EXT_PARAM: menu.EXTRA_PARAMS,
                }));
                // let param = {
                //     APP_ID: '' + menu.MENU_APP_ID,
                //     MENU_PAGE_ACTION: menu.MENU_PAGE_ACTION,
                //     MENU_WELCOME_PAGE: menu.MENU_WELCOME_PAGE,
                //     EXT_PARAM: menu.EXTRA_PARAMS
                // };
                // Mobile.openIpuApp(param);
            } else if (menu.MENU_TYPE == 'U') { // URL
                let url = menu.APP_REQUEST_HOST + menu.APP_REQUEST_PATH + menu.MENU_URL
                WadeMobile.openUrl(url, undefined, menu.MENU_NAME);
            }
        },
        callNewChatApi(params) {
            this.$refs.chatContainer.newChatApi(params)
        },
    }
};
</script>

<style lang="scss" scoped>
.container {
  overflow: scroll;
  background-image: url('@/assets/images/backgroundMain.png');
  background-size: cover;
  background-repeat: no-repeat;
}

.w-part {
    padding: 8px 0;
}

.part-1 {
    .part-item {
        width: 100%;
        background-color: #ffffff;
        border-radius: 7px;
        text-align: center;
        padding: 8px 0 6px 0;
        font-family: PingFangSC-Medium, PingFang SC;

        img {
            height: 26px;
            margin-bottom: 3px;
        }
    }
}

.part-2 {
    font-family: PingFangSC-Medium, PingFang SC;

    .part-title {
        font-size: 14px;
    }

    .part-content {
        background-color: #ffffff;
        border-radius: 7px;
        padding: 12px 10px;
        margin-top: 10px;

        .part-item {
            height: 40px;
            background: #F5F7FD;
            border-radius: 3px;
            color: #205BE2;
            font-size: 14px;
            display: flex;
            align-items: center;
            padding: 0 10px;

            span {
                flex-grow: 1;
            }

            img {
                height: 30px;
            }
        }
    }
}

.part-3 {
    width: 100%;

    .part-title {
        color: #ffffff;
        font-size: 18px;
        background: url(@/assets/images/fire.png) no-repeat left center;
        background-size: 20px 20px;
        padding-left: 25px;
        font-family: PingFangSC-Medium, PingFang SC;
    }

    .triangle {
        width: 0;
        height: 0;
        border-left: 12px solid transparent;
        border-right: 12px solid transparent;
        border-bottom: 12px solid #ffffff;
        margin: 8px 0 0 50px;
    }

    .part-content {
        position: relative;
        width: 100%;
        border-radius: 7px;
        background-color: #ffffff;
        margin-top: -1px;
        padding: 12px 8px 2px 8px;
        box-sizing: border-box;

        .robot-img {
            width: 96px;
            height: 91px;
            position: absolute;
            right: 18px;
            top: -52px;
        }

        .part-item {
            height: 30px;
            background: #F4F5F7;
            border-radius: 15px;
            line-height: 30px;
            margin-bottom: 10px;
            display: inline-block;
            margin-right: 8px;
            padding: 0 6px;
            color: #36383C;
            font-weight: 400;

            img {
                width: 18px;
                height: 18px;
                display: inline-block;
                position: relative;
                top: 4px;
            }

            &.hot {
                padding-right: 4px;
                color: #3489FC;
                font-size: 13px;
                font-family: PingFangSC-Semibold, PingFang SC;
                font-weight: 600;
            }
        }
    }
}

/deep/.iframe-close.van-icon-clear {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 28px;
    color: #67666685;
}

.part-4 {
    font-family: PingFangSC-Medium, PingFang SC;

    .part-4-content {
        background-color: #ffffff;
        border-radius: 7px;
        padding: 12px 10px;
        margin-top: 10px;

        .part-4-item {
            height: 40px;
            background: #F5F7FD;
            border-radius: 3px;
            color: #205BE2;
            font-size: 14px;
            display: flex;
            align-items: center;
            padding: 0 10px;

            span {
                flex-grow: 1;
            }

            img {
                height: 30px;
            }
        }
    }

    .ques-example {
        background: #0081ff12;
        color: #0081FF;
        margin: 8px 8px 0 0;
        font-size: 12px;
        border-radius: 3px;
        text-align: center;
        display: inline-block;
        height: 24px;
        line-height: 24px;
        padding: 0 8px;
    }
}
</style>