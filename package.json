{"name": "super-app-h5", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"create": "node ./templateFile/create.js", "dev": "webpack-dev-server --config webpack.dev.js", "devmock": "webpack-dev-server --config webpack.devMock.js", "test": "webpack --config webpack.test.js", "build": "webpack --config webpack.prod.js", "pre": "webpack --config webpack.pre.js", "server": "node proxy.js", "lint": "npx eslint --cache --ext .js,.vue ./ && npx stylelint **/*.{css,scss,sass,less,vue} --cache --aei", "lint:fix": "npx eslint --cache --fix --ext .js,.vue ./ && npx stylelint **/*.{css,scss,sass,less,vue} --cache --fix --aei", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "changelog:all": "conventional-changelog -p angular -i CHANGELOG.md -s -r 0", "lint-staged": "npx lint-staged -q"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@babel/polyfill": "7.10.4", "axios": "0.20.0", "babel-plugin-import": "^1.13.6", "crypto-js": "^4.1.1", "exif-js": "2.3.0", "janus-manager": "^1.3.9", "jquery": "^3.6.0", "js-audio-recorder": "^1.0.7", "js-md5": "^0.7.3", "jsencrypt": "^3.3.2", "lodash": "4.17.15", "moment": "2.29.1", "pako": "^2.1.0", "pinyin-match": "^1.2.2", "rk-native-plugin": "1.0.68", "rk-web-utils": "1.0.58", "rux-ui-v": "0.2.26", "vant": "2.12.6", "video.js": "^7.19.2", "vue": "2.6.12", "vue-clipboard2": "^0.3.3", "vue-pdf": "4.2.0", "vue-qr": "2.3.0", "vue-router": "3.4.3", "vuex": "3.5.1", "watermark-dom": "^2.3.0", "zepto": "1.2.0"}, "devDependencies": {"@babel/core": "^7.11.4", "@babel/preset-env": "^7.11.0", "@commitlint/cli": "^13.1.0", "@commitlint/config-conventional": "^13.1.0", "babel-eslint": "^10.1.0", "babel-loader": "^8.1.0", "clean-webpack-plugin": "^3.0.0", "colors": "^1.4.0", "conventional-changelog-cli": "^2.1.1", "copy-webpack-plugin": "^6.0.3", "css-loader": "^4.2.1", "eslint": "^7.32.0", "eslint-loader": "^4.0.2", "eslint-plugin-vue": "^7.16.0", "exports-loader": "^0.7.0", "express": "^4.17.1", "file-loader": "^6.0.0", "git-cz": "^4.8.0", "glob": "^7.1.6", "html-webpack-plugin": "^3.2.0", "http-proxy-middleware": "^1.0.5", "husky": "^7.0.0", "less": "^4.0.0", "less-loader": "^7.2.0", "lint-staged": "^10.5.4", "mini-css-extract-plugin": "^0.10.0", "moment-locales-webpack-plugin": "^1.2.0", "postcss-loader": "^3.0.0", "postcss-pxtorem": "^5.1.1", "sass": "^1.89.2", "sass-loader": "^8.0.2", "script-loader": "^0.7.2", "style-loader": "^1.2.1", "stylelint": "^13.13.1", "stylelint-config-standard": "^22.0.0", "stylelint-scss": "^3.21.0", "url-loader": "^4.1.0", "vue-loader": "^15.9.3", "vue-template-compiler": "2.6.12", "webpack": "^4.41.6", "webpack-cli": "^3.3.12", "webpack-dev-server": "^3.11.0", "webpack-merge": "^4.2.2", "webpackbar": "^4.0.0"}, "config": {"commitizen": {"path": "./node_modules/git-cz"}}}