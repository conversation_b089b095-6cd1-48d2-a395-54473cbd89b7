<template>
  <div class="orderTrack">
    <div class="order-msg3">
      <div class="order-msg3-tit">处理中<van-icon name="clock-o" color="#fff" style="position: absolute;bottom: 0px; margin-left: 5px;" /></div>
      <div>订单已受理，正在处理中。</div>
    </div>
    <div class="order-msg1">
      <h1 class="tip tip2">订单信息</h1>
      <div class="tip" style="font-size: 12px;"><span>业务号码：</span><span>02106952134</span></div>
      <div class="tip" style="font-size: 12px;"><span>装机地址：</span><span>上海市浦东新区********</span></div>
      <div class="tip" style="font-size: 12px;"><span>下单时间：</span><span>2025-01-23 14:11:24</span></div>
    </div>
    <div class="order-msg2">
      <h1 class="tip tip2" style="position: relative">订单跟踪 <van-icon color="#263A5F" name="info-o" style="position: absolute;bottom: 6px;margin-left: 5px;" /></h1>
<!--      <div class="stepbox orders-detail1">-->
<!--        <van-steps :active="stepActive"-->
<!--                   active-icon="clock"-->
<!--                   finish-icon="passed"-->
<!--                   active-color="#3388ff"-->
<!--                   style="width: 100%;background: #fff;">-->
<!--          <van-step v-for='(name, index) in steps' :key="index" :style="`width: ${ 90.7 / ( steps.length - 1 ) }%;`">-->
<!--            <div>{{ name.substring(0, 2) }}</div>-->
<!--            <div>{{ name.substring(2, 4) }}</div>-->
<!--          </van-step>-->
<!--        </van-steps>-->
<!--      </div>-->
      <y-steps :stepList="stepList"></y-steps>
    </div>
  </div>
</template>

<script>
import YSteps from "@/pages/easyAcceptance/easyAcceptance/components/YSteps.vue"
export default {
  name: "orderTrack",
  components:{YSteps},
  data(){
    return {
      steps: ['销售下单', '订单生产', '业务开通', '订单归档'],
      stepActive: 1,
      stepList: [
        {
          "title": "订单领用",
          "isFinished": true,
          "isActive": false,
          "showSlot":
              "<h3>处理人：沪审核</h3>" +
              "<h3>处理时间：2025-01-17 15:32:50</h3>"
        },
        {
          "title": "自动领单",
          "isFinished": true,
          "isActive": false,
          "showSlot":
              "<h3>处理人：自动</h3>" +
              "<h3>处理时间：2025-01-17 15:32:51</h3>"
        },
        {
          "title": "退单申请",
          "isFinished": true,
          "isActive": false,
          "showSlot":
              "<h3>处理人：张将辰</h3>" +
              "<h3>处理时间：2025-01-17 15:32:51</h3>"
        },
        {
          "title": "退单审核通过",
          "isFinished": true,
          "isActive": false,
          "showSlot":
              "<h3>处理人：张将辰</h3>"+
              "<h3>处理时间：2025-01-17 15:39:33</h3>"
        },
        {
          "title": "订单归档",
          "isFinished": true,
          "isActive": false,
          "showSlot":
              "<h3>处理人：自动</h3>" +
              "<h3>处理时间：2025-01-17 15:39:33</h3>"
        }
      ]
    }
  },
  methods:{

  }
}
</script>

<style lang="scss" scoped>
.orderTrack .orders-detail1 /deep/{
  .van-step--horizontal:not(:last-child) .van-step__title {
    margin-top: 45px!important;
    display: flex;
    flex-direction: column;
    margin-left: 0 !important;
    width: 100%;
    transform: none !important;
    text-align: left;
  }

  .van-step--horizontal:nth-child(6) .van-step__title {
    margin-left: -1.3rem;
  }

  .van-steps--horizontal .van-steps__items {
    display: block !important;
  }

  .van-step:last-child {
    width: auto !important;
  }
}

/deep/.van-step--finish .van-step__title {
  color: #07c160 !important;
}
/deep/.van-step--finish .van-step__line {
  background: #07c160 !important;
}

/deep/.van-step__icon--finish {
  color: #07c160 !important;
}

/deep/.van-step--horizontal .van-step__icon {
  font-size: .7rem;
}

/deep/.van-step__circle {
  width: 0.4rem;
  height: 0.4rem;
  background-color: #bbb;
}

/deep/.van-step__line {
  background-color: #bbb;
}

/deep/.van-step__circle-container {
  width: 0.7rem;
  height: 0.7rem;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #fff;
}

/deep/.van-steps--horizontal {
  padding: 0px 0.42667rem !important;
}

.orders-detail1{
  .van-step--horizontal .van-step__title{
    margin-top: 45px!important;
  }
}

.stepbox {
  width: 100%;
  overflow: hidden;
  padding: 0;
  text-align: center;
  display: flex;
  justify-content: space-between;

  span {
    overflow: hidden;
    display: inline-block;
  }

}

.orderTrack {
  //background: #ffffff;
  //height: 100%;
  .order-msg1 {
    background: #fff;
    width: 100%;
    border-radius: 10px;
    padding: 10px 0;
  }
  .order-msg2 {
    background: #fff;
    width: 100%;
    border-radius: 10px;
    margin-top: 10px;
    padding-top: 10px;
  }
  .order-msg3{
    width: 100%;
    height: 80px;
    justify-content: center;
    align-items: center;
    color: #fff;
    div{
      text-align: center;
    }
    .order-msg3-tit{
      position: relative;
      margin: 10px 0;
      font-size: 18px;
    }
  }
  .tip{
    color: #263A5F;
    margin: 5px 0 5px 10px;
  }
  .tip2{
    margin-left: 0;
    padding: 0 0 5px 10px;
    border-bottom: 1px solid #9cafd3;
  }
  .line {
    height: 10px;
    background: #F4F4F4;
  }

  .content {
    margin: 0 20px;

    p {
      margin: 5px 0;
      display: flex;
      justify-content: space-between;

      span {
        font-size: 14px;

        &:nth-child(1) {
          text-align: left;
          color: #999;
        }

        &:nth-child(2) {
          text-align: right;
          color: #333;
        }
      }
    }
  }
}
</style>