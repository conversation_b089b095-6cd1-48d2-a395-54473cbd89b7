<template>
  <div style="background-color: white" >
    <h1 class="bh_title">组网信息</h1>
    <VanFieldCheckSingleBox :columns="columnsData" @checkBookedNotChooseNew="ywfNoNeed"  @confirm="handleData"></VanFieldCheckSingleBox>
    <div class="submit-container" v-show="zwChooseCommLists!==null&& Object.keys(zwChooseCommLists).length>=1">
      <span>已选择的组网商品:</span>
      <div class="list-item"  v-for="(item, index) in zwChooseCommLists"
          >
              <span style="float: left">{{ item.zwName }} 
              </span>
      </div>
    </div>
    <h1 class="bh_title" id="ywFreeGoods">移网附加优惠</h1>
    <div class="one-key-open-account">
      <div class="form-wrap">
        <div class="card-style" >
          <div class="contract-wrap" >
            <div class="made-list">
              <div  class="list-item"   :class="isYwfDiscnt==-1 ? 'active':''" v-show="ywfuInfoNoNeedIsShow" @click="ywfNoNeed()">
                <span href="javascript: void(0)" >不需要</span>
              </div>
              
              <div class="list-item"  v-for="(item, index) in commodityYWUlList"
                   :key="index" @click="commodityYWUlClick(item,$event)"   v-if="commodityYWUlList!==null&& Object.keys(commodityYWUlList).length>=1" >
              <span style="float: left" :id="'YWF'+item.ywGoodId">{{ item.ywGoodName }} 
              </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="submit-container">
        <van-button class="sub" @click="submit()" block>确定</van-button>
      </div>
    </div>
    
  </div>
</template>

<script>

import errorTips from '@/assets/bizComponents/errorTips/errorTips.vue';
import {mapMutations, mapState} from "vuex";
import VanFieldCheckSingleBox from "@/assets/bizComponents/select/VanFieldCheckSingleBox.vue"
import VanFieldCheckBox from "@/assets/bizComponents/select/VanFieldCheckbox.vue"


export default {
  name: "ZwGoodChoose",
  data() {
    return {
      orderPriceNum:0,
      columnsData:[],
      wslBoradBusinessList: [],
      ywfGoodInfoList:[],
      ywfGoodInfo:{},
      isYwfDiscnt:-1,
      commodityYWUlList:[],
      ywfuInfoNoNeedIsShow:true,
      pageYwfuPrice:0,
      choosedZW:"",
      zwInvalid:[],
      zwInfoCheckList:[],
      pageZwPrice:0,
      ziFeiPrice:0,
      commodityChooseYWUlList:[],
      zwChooseCommLists:[],
      zwInfoList:[],
      zwInfo:{}
    }
  },
  
  computed: {
    ...mapState([
      'jzfkOrderData',
        'shbmMsgInfo',
        'zwOrderData',
        'zwCacheList'
    ])
  },
  components: {
    errorTips,
    VanFieldCheckSingleBox,
    VanFieldCheckBox
  },
  mounted() {
    this.ywfNoNeed();
    this.$emit('startLoading', '')
    this.$http.post('/zhzwZsd/qryProductListZsd', {isRHUserTag:this.zwOrderData.checkNumberData.isRHUserTag}).then(res => {
      this.$emit('endLoading', '')
      if("0000"==res.respCode){
       this.columnsData=[];
       if(res.respData.zwProList.length>0){
         for(let i=0;i<res.respData.zwProList.length;i++){
           let c={
             value:res.respData.zwProList[i].commodityCode,
             label:res.respData.zwProList[i].iptvName,
             commType:res.respData.zwProList[i].commType
           }
           
           this.columnsData.push(c)
         }
       }
      }
      console.log(this.columnsData)
    })
  },
  methods: {
    ...mapMutations(['setZwOrderData','setZwCacheList']),
    submit(){
      if (this.zwChooseCommLists.length<=0) {
        this.$toast('请选择组网商品~');
        return;
      }
      this.ywCodeString={};
      var ywCode1=[];
      // console.log(this.iptvChooseCommLists.length+"pokokp")
      if(this.commodityChooseYWUlList.length>0){
        for(var i in this.commodityChooseYWUlList) {
          this.ywCodeInfo={};
          this.ywCodeInfo={
            iptvId: this.commodityChooseYWUlList[i].ywGoodId,
            iptvName: this.commodityChooseYWUlList[i].ywGoodName
          }
          ywCode1.push(this.ywCodeInfo)
        }
      }
      this.ywCodeString.iptvInfoList=ywCode1
      let req = {
        goodCode:this.zwChooseCommLists[0].commodityCode,
        ywString:JSON.stringify(this.ywCodeString)
      }
      
      this.$http.post('/zhzwZsd/isItMissing', req).then(res => {
        if(res.respCode!='0000'){
          this.ywCodeString={};
          this.ywCodeInfo={};
          this.$toast(res.respMsg) ;
          return;
        }
        
        // if(this.zwCacheList.zwChooseCommLists.length>0){
        //   for(let i=0;i<this.zwCacheList.zwChooseCommLists.length;i++){
        //     this.zwClickFee(this.zwCacheList.zwChooseCommLists[i],'+')
        //   }
        // }
        
        let data = {
          inputType: "1",
          type: '1',
          textInput: "zwCommInfoSubmit",
          notifyFlag: '',
          taskName:'智能组网甩单'
        }
        this.$emit('newChatApi', data);
      })
    
    },
    handleData(val,selectedDataList){
      this.zwChooseCommLists=[];
      
      if(selectedDataList.length>0){
     for(let i=0;i<selectedDataList.length;i++){
       let zw={
         commodityCode:selectedDataList[i].value,
         zwName:selectedDataList[i].label,
         commType:selectedDataList[i].commType
       }
       // this.zwClickFee(zw,'+');最后点击确定调用
       this.zwChooseCommLists.push(zw);
       this.zwCacheList.zwChooseCommLists=this.zwChooseCommLists;
       this.setZwCacheList(this.zwCacheList)
       this.zwOrderData.zwGoodData.zwList.push(zw);
       this.setZwOrderData(this.zwOrderData);
       
     }
     
     this.zwClickYwfGoods();
      }
    },
    zwClickYwfGoods(){
      this.zwCodeString={};
      var zwCode1="";
      // console.log(this.zwChooseCommLists.length+"pokokp")
      if(this.zwChooseCommLists.length>0){
        zwCode1=this.zwChooseCommLists[0].commodityCode
      }
      let req = {
        iptvCode:zwCode1,
        userCode:this.shbmMsgInfo.operatorId
      }
      
      this.$emit('startLoading', '')
      this.$http.post('/zhzwZsd/goodQryRelateYw', req).then(res => {
        this.$emit('endLoading', '')
        if (res.respData.code != "0000") {
          this.dialogMsg = res.respMsg
          this.dialogVisible = true
          return;
        }
        this.commodityYWUlList=[];
        if( res.respData.ywFreeGoodsListResult.length>0){
          for(var i=0; i<res.respData.ywFreeGoodsListResult.length; i++) {
            var ywGood = res.respData.ywFreeGoodsListResult[i];
            var ywGoodName = ywGood.commName;
            var ywGoodId = ywGood.commId;
            var ywGoodType=ywGood.commType;
            this.commodityYWUlList.push({
              ywGoodId:ywGoodId,
              ywGoodName:ywGoodName,
              ywGoodType:ywGoodType
            })
          }
        }
        console.log(this.commodityYWUlList.length+"--------")
      })
    },
    
    selectItemMade(item,e){
      const $this = $(e.target);
      if($this.parent().hasClass("active")){
        $this.parent().removeClass('active');
        }
      else{
        this.isYwfDiscnt=0;
        $this.parent().addClass("active");
      }
    },
    goodsClick(item,e) {
      const $this = $(e.target);
      var flag = item.commodityCode;//如果不需要，则没有调测费用  0：不需要zw  其他：需要
      if(flag!=0){
        if($this.parent().hasClass("cur1")){
          $this.parent().removeClass('cur1');
          $this.parent().removeClass('cur');
          this.zwChooseCommLists = this.zwChooseCommLists.filter(i=>i!==item);
          if(this.zwChooseCommLists.length>0){
            this.ywfNoNeed();
            this.zwClickFee(item,1,"-");
            this.zwClickYwfGoods();
          }
          else{
            this.zwClickFee(item,1,"-");
            this.ywfNoNeed();
            this.commodityYWUlList=[];
          }
          this.orderPrice=0;
          this.orderPrice = this.pageZwPrice+this.ziFeiPrice+this.pageYwfuPrice;
        }
        else{
          $this.parent().addClass("cur1");
          $this.parent().removeClass('cur');
          this.checkBookedNotChoose(item);

          this.orderPrice=0;
          this.orderPrice = this.pageZwPrice+this.ziFeiPrice+this.pageYwfuPrice;

          // $this.parent().addClass("cur1");
          // $this.parent().removeClass('cur');
          // this.zwChooseCommLists.push(item);
          // this.zwClickFee(item,"+");
        }
        // this.zwClickFee(this.zwChooseCommLists)
      }
    },
    commodityYWUlClick(item,e) {
      // console.log(this.pageZwPrice)
      const $this = $(e.target);
      if(this.zwChooseCommLists.length==0){
        this.$toast("请先选择组网商品");
        return;
      }
      // console.log(item)
      if($this.parent().hasClass("active")){
        $this.parent().removeClass('active');
        this.commodityChooseYWUlList = this.commodityChooseYWUlList.filter(i=>i!==item);
        this.zwOrderData.zwGoodData.commodityChooseYWUlList=this.commodityChooseYWUlList;
        this.setZwOrderData(this.zwOrderData);
        console.log("this.zwOrderData.zwGoodData.commodityChooseYWUlList")
        console.log(this.zwOrderData.zwGoodData.commodityChooseYWUlList)

        if(this.commodityChooseYWUlList.length>0){
          this.isYwfDiscnt=0;
        }
        else{
          this.isYwfDiscnt=-1;
          this.ywfNoNeed();
          return;
        }
        this.orderPrice=0;
        this.orderPrice = this.pageZwPrice+this.ziFeiPrice+this.pageYwfuPrice;
      }else{
        this.isYwfDiscnt=0;
        $this.parent().addClass("active");
        this.commodityChooseYWUlList.push(item);
        this.zwOrderData.zwGoodData.commodityChooseYWUlList=this.commodityChooseYWUlList;
        this.setZwOrderData(this.zwOrderData)
        console.log("this.zwOrderData.zwGoodData.commodityChooseYWUlList")
        console.log(this.zwOrderData.zwGoodData.commodityChooseYWUlList)
        this.orderPrice=0;
        this.orderPrice = this.pageZwPrice+this.ziFeiPrice+this.pageYwfuPrice;
      }
      this.feeDesc = this.orderPrice > 0 ? "施工中收费" : " 无须支付";
    },
    commodityFeeClick(item,sub) {
      // var ywFlag = $this.attr("ywFlag");// 移网优惠资费无需处理
      // var ywGoodId = $this.attr("ywGoodId");//移网优惠资费无需处理
      var ywFlag = item.ywGoodId;
      var ywGoodId = item.ywGoodId;
      // var zwFlag = this.commodityZWUls.zwCommodityId;
      //调用定制商品查询
      let data = {
          "ywQryCommId": ywGoodId,
          "zwQryCommId": "0",
          "staffId":this.shbmMsgInfo.operatorId
      }
      this.$http.post('/outCallMonet/getYwfGoodsFeeByInterface', data).then((res) => {
        if (res.respCode != "0000") {
          this.dialogMsg = res.respMsg
          this.dialogVisible = true
          return;
        }
        // 接收返回参数
        item.ywfPrice=res.respData.ywfFee;
        if(sub=='+'){
          this.pageYwfuPrice=this.pageYwfuPrice+parseInt(res.respData.ywfFee);
        }
        else{
          this.pageYwfuPrice=this.pageYwfuPrice-parseInt(res.respData.ywfFee);
        }
        this.orderPrice=0;
        this.orderPrice =this.pageZwPrice+this.ziFeiPrice+this.pageYwfuPrice;
        this.feeDesc = this.orderPrice > 0 ? "施工中收费" : " 无须支付";
      }).catch(e => {
        this.dialogMsg = e
        this.dialogVisible = true
      })

    },

    cancelSingleYWU(e,item,index){
      const $this = $(e.target);
      let id=this.commodityChooseYWUlList[index].ywGoodId
      $('#'+'YWF'+id).parent().removeClass('cur1')
      this.commodityChooseYWUlList = this.commodityChooseYWUlList.filter(i=>i!==item);
      if(this.commodityChooseYWUlList.length>0){
        this.commodityFeeClick(item,"-");

      }
      if(this.commodityChooseYWUlList.length==0){
        this.isYwfDiscnt=-1;
        this.commodityFeeClick(item,"-");
        this.ywfNoNeed();
      }
      else{
      }
      this.feeDesc = this.orderPrice > 0 ? "施工中收费" : " 无须支付";
    },
    ywfNoNeed(){
      if(this.commodityChooseYWUlList.length>0){
        for(let i in this.commodityChooseYWUlList){
          let id=this.commodityChooseYWUlList[i].ywGoodId
          $('#'+'YWF'+id).parent().removeClass('active')
          this.pageYwfuPrice=0;
          this.orderPrice=0;
          this.orderPrice = this.pageZwPrice+this.ziFeiPrice+this.pageYwfuPrice;
        }
      }
      let c=[{ywGoodId:'0',ywGoodName:'不需要'}];
      this.zwOrderData.zwGoodData.commodityChooseYWUlList=c;
      this.setZwOrderData(this.zwOrderData);
      this.commodityChooseYWUlList=[];
      this.isYwfDiscnt=-1;
    },

    cancelSingle(e,item,index){
      const $this = $(e.target);
      let id=this.zwChooseCommLists[index].commodityCode
      $('#'+'ZW'+id).parent().removeClass('cur1')
      this.zwChooseCommLists = this.zwChooseCommLists.filter(i=>i!==item);
      this.zwClickFee(item,"-");
    },
    checkBookedNotChooseNew(zw){
      let data = {
        "reqData": {
          "zwQryCommId": zw,
          "zwInvalidList": JSON.stringify(this.zwOrderData.zwInvalid),
          "zwInfoCheckList":JSON.stringify(this.zwOrderData.zwInfoCheckList)
        },
      }
      console.log("checkBookedNotChooseNew")
      this.$http.post('/zwReceive/checkBookedNotChoose', data).then(res => {
        if (res.respCode != "0000") {
          return false;
        }
        this.ywfNoNeed();
        return true;
      }).catch(e => {
        return false;
      })
    },
    checkBookedNotChoose(zw){
      let data = {
        "reqData": {
          "zwQryCommId": zw.commodityCode,
          "userCode": this.shbmMsgInfo.operatorId,
          "zwInvalidList": JSON.stringify(this.zwInvalid),
          "zwInfoCheckList":JSON.stringify(this.zwInfoCheckList)
        },
      }

      this.$http.post('/zw/checkBookedNotChoose', data).then(res => {
        if (res.respCode != "0000") {
          $('#'+'ZW'+zw.commodityCode).parent().removeClass('cur1');
          this.dialogMsg = res.respMsg
          this.dialogVisible = true
          return;
        }
        this.zwChooseCommLists.push(zw);
        this.zwClickFee(zw,"+");
        this.ywfNoNeed();
        this.zwClickYwfGoods();
      }).catch(e => {
        this.dialogMsg = e
        this.dialogVisible = true
      })
    },
    
    zwClickFee(zw,sub){
      let data = {
          "commId": zw.commodityCode
      }
      this.$http.post('/zhzwZsd/qryProduct', data).then(res => {
        if (res.respCode != "0000") {
         this.$toast(res.resMsg);
          return;
        }
        // 接收返回参数
        if(sub=='+'){
          this.ziFeiPrice=0;
          this.pageZwPrice=this.pageZwPrice+parseInt(res.respData.goodsPrice);
          this.orderPriceNum=this.ziFeiPrice+this.pageZwPrice;
        }
        else{
          this.ziFeiPrice=0;
          this.pageZwPrice=this.pageZwPrice-parseInt(res.respData.goodsPrice);
          this.orderPriceNum=this.ziFeiPrice+this.pageZwPrice;

        }
        this.zwOrderData.orderPrice=this.orderPriceNum;
        this.setZwOrderData(this.zwOrderData)
      }).catch(e => {
        this.dialogMsg = e
        this.dialogVisible = true
      })
    },
  }
}
</script>
<style lang="scss">
.submit-container{
  .sub{
    border: 2px solid rgba(73,124,246,1);
    border-radius: 5px;
    color: rgba(73,124,246,1);
    margin-top: 20px;
  }
}

.one-key-open-account{
  overflow: hidden;
  .content {
    min-height: 50px;
  }

  .demo {
    width: 300px;
    height: 100px;
    background-color: #646566;
  }
  .card-style{
    margin: 0;
    //padding-bottom: 15px;
    width: 100%;
    height: auto;
    background: #FFFFFF;
    border-radius: 8px;
    border: 1px solid #FFFFFF;
  }
  .form-wrap {
    padding: 0;
    background: #F5F5F5;
    .contract-wrap {
      margin: 0;
      /*border-bottom: 1px solid #F7F7F7;*/
      p {
        padding: 10px 0;
        height: 34px;
        line-height: 34px;
        font-size: 14px;
        color: #333;
      }
      .desc{
        color: #ee0a24;
        font-size: 12px;

      }
      .made-list {
        display: flex;
        flex-direction: column;
        .list-item {
          padding: 0 10px;
          height:fit-content;
          line-height: 30px;
          font-size: 14px;
          margin: 5px 10px;
          background: #F5F8FA;
          border: 1.5px dashed rgba(203, 203, 203, 1);
          border-radius: 8px;
        }
        .active {
          background: #EDF7FF;
          border: 1px solid rgba(80,148,245,1);
        }
      }
      .contract-list {
        display: flex;
        flex-direction: column;
        .list-item {
          padding: 0 10px;
          width: fit-content;
          height: fit-content;
          line-height: 30px;
          font-size: 14px;
          color: #999;
          margin-bottom: 10px;
          background: #F7F7F7;
          border-radius: 4px;
          border: 1.5px solid #F7F7F7;
        }
        .active {
          color: #0081FF;
          background: rgba(0,129,255,0.10);
          border: 1px solid #0081FF;
        }
      }
    }
    .border {
      background: #FFFFFF;
      border-radius: 4px;
      border: 1px solid #CACACA;
    }
    .blueBorder {
      background: rgba(0, 129, 255, 0.05);
      border-radius: 12px;
      border: 1px solid #0081FF;
      width: 300px;
      margin-left: 20px;
    }
    .notBlueBorder {
      border-radius: 12px;
      width: 300px;
      margin-left: 20px;
    }
    .made-name{
      color: #323233;
      font-size: 0.37333rem;
      line-height: 0.64rem;
    }
    .made-van {
      /*padding: 5px 0px;*/
      /*background: #c8e3fe;*/
      /*border-radius: 12px;*/
      /*font-size: 14px;*/
      align-content: center;
      width: 300px;
      text-align: center;
    }

  }
}


</style>

<style lang="scss" scoped>
.card-verification {
  background-color: rgb(57,159,254);
  .desc {
    color: black;
    font-size: 13px;
    line-height: 20px;
  }
  .margin-two {
    bottom: 10px;
    left: 15px;
    right: 15px;
    z-index: 2;
    position: absolute;
  }
  .van-buttons {
    border-radius: 4px;
    height: 44px;
    font-size: 13px;
    color: #FFFFFF;
    background-color: #0081FF;
  }
  .font {
    color:#333333;
  }

}

</style>