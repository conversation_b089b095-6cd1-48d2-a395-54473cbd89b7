import WadeMobile from 'rk-native-plugin';

let clock;
//滚动动画 Param currentY:当前距离 targetY目标距离
const scrollAnimation = (currentY, targetY) => {
    // 计算需要移动的距离
    let needScrollTop = targetY - currentY;
    let _currentY = currentY;
    clock = setTimeout(() => {
        // 一次调用滑动帧数，每次调用会不一样
        const dist = Math.ceil(needScrollTop / 2);
        _currentY += dist;
        window.scroll(0, _currentY);
        // 如果移动幅度小于十个像素，直接移动，否则递归调用，实现动画效果
        if (needScrollTop > 10 || needScrollTop < -10) {
            scrollAnimation(_currentY, targetY);
        } else {
            window.scroll(0, targetY);
            clearTimeout(clock);
        }
    }, 1);
}
// 获取当前元素距离顶部的距离
// elem:当前元素
const getElementTop = (elem) => {
    //获得elem元素距相对定位的父元素的top
    let elemTop = elem.offsetTop;
    //将elem换成起相对定位的父元素
    elem = elem.offsetParent;
    //只要还有相对定位的父元素
    while (elem != null) {
        //获得父元素 距它父元素的top值,累加到结果中
        elemTop += elem.offsetTop;
        //再次将elem换成它相对定位的父元素上;
        elem = elem.offsetParent;
    }
    return elemTop;
}
// IOS键盘遮挡输入框处理方法
// element: 当前元素
const softKeyboard = (element) => {
    if (!WadeMobile.isIOS()) {
        return;
    }
    // 获取当前元素距离页面顶部的距离
    let currentElementTop = getElementTop(element.currentTarget || element.target);
    // 获取当前元素距离页面底部的距离
    let currentElementBottom = window.innerHeight - currentElementTop;
    // 当前元素高度
    let currentElementHeight = element.target.offsetHeight;
    // 键盘高度 todo 获取键盘高度方法待定
    let keyboardHeight = window.innerHeight * 0.65;
    // 当前元素比键盘高10以上，不需要滚动
    if (currentElementBottom > keyboardHeight + currentElementHeight + 10) {
        return;
    }
    // 需要移动的距离，保持输入框在键盘以上10
    let needScrollHeight = keyboardHeight - currentElementBottom + currentElementHeight * 2;
    // 滚动
    scrollAnimation(0, needScrollHeight);
}

// 设置所有input和textarea监听，可配合vue的updated生命周期
const softKeyboardAll = () => {
    let inputList = document.getElementsByTagName('input');
    for (let item of inputList) {
        item.onfocus = function(e) {
            adapters.softKeyboard(e);
        };
    }
    let textareaList = document.getElementsByTagName('textarea');
    for (let item of textareaList) {
        item.onfocus = function(e) {
            adapters.softKeyboard(e);
        };
    }
}

//导出待使用
export const adapters = {
    softKeyboard,
    softKeyboardAll
}
