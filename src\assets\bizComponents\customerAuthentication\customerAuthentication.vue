<template>
  <div class="customer">
    <p class="label-title">填写认证信息</p>
    <div v-if="codeValue == '02' || codeValue == '35'" class="flexRadio" style="margin-top:20px;align-items: center;">
      <span class="font-size">读取方式</span>
      <van-radio-group v-model="radio" class="rightRadio" direction="horizontal">
        <van-radio name="blueTooth" checked-color="#0081FF" class="font-size">蓝牙</van-radio>
        <van-radio v-if="isEid == '1' && codeValue == '02'" name="eidTooth" checked-color="#0081FF" class="font-size">电子身份证</van-radio>
      </van-radio-group>
    </div>
    <div class="flexRadio" @click="isShowPicker">
      <span class="font-size">证件类型</span>
      <div v-if="codeDesc">
        <span class="rightRadio font-size margin-r12">{{codeDesc}}</span>
      </div>
      <div class="other-group" v-else>
        <span class="rightRadio font-size" style="right:17px;">请点击选择证件类型</span>
        <van-icon class="rightRadio" name="arrow" color="#888" size="17" />
      </div>
    </div>
    <div v-if="codeValue == '02' || codeValue == '35'">
      <div v-if="radio == 'blueTooth'" class="flexRadio" @click="getDeviceList()">
        <span class="font-size">设备名称</span>
        <div v-if="btName">
          <span class="rightRadio font-size">{{fmtBtName}}</span>
        </div>
        <div class="other-group" v-else>
          <span class="rightRadio font-size" style="right:17px;">请点击选择设备名称</span>
          <van-icon class="rightRadio" name="arrow" color="#888" size="17" />
        </div>
      </div>
      <div v-if="radio == 'eidTooth'">
        <div class="flexRadio" style="align-items: center;">
          <span class="font-size">条形码</span>
          <div class="rightRadio">
            <van-field label-class="ID-container" v-model="eid" placeholder="请输入条形码" input-align="right" />
          </div>
        </div>
        <div class="my-button">
          <van-button type="default" class="van-button" @click="scanQrCode"  block>扫描</van-button>
          <van-button type="default" class="van-button" @click="checkCode"  block>校验</van-button>
        </div>
      </div>
      <div class="ID-container">
        <!-- 边角线 -->
        <div class="row-line" style="right:0;"></div>
        <div class="col-line" style="right:0;"></div>
        <div class="row-line" style="left:0;"></div>
        <div class="col-line" style="left:0;"></div>
        <div class="row-line" style="right:0;bottom:0;"></div>
        <div class="col-line" style="right:0;bottom:0;"></div>
        <div class="row-line" style="left:0;bottom:0;"></div>
        <div class="col-line" style="left:0;bottom:0;"></div>
        <!--显示身份证信息,由于用了canvas绘图,container里都用style写法保持样式一致-->
        <div class="margin-two">
          <van-swipe class="my-swipe" :autoplay="3000" indicator-color="white" style="width: 300px;height: 191px;">
            <van-swipe-item>
              <img :id="`frontID_`+this.idSuffix" src="../../images/frontID.png" style="display:none;">
              <canvas :id="`frontCanvasNoSecret_`+this.idSuffix" width="300" height="191" style="display: none"></canvas>
              <canvas :id="`frontCanvas_`+this.idSuffix" width="300" height="191"></canvas>
            </van-swipe-item>
            <van-swipe-item>
              <img :id="`backID_`+this.idSuffix" src="../../images/backID.png" style="display:none;">
              <canvas :id="`backCanvas_`+this.idSuffix" width="300" height="191"></canvas>
              <canvas :id="`backCanvasNoSecret_`+this.idSuffix" width="300" height="191"></canvas>
            </van-swipe-item>
          </van-swipe>
        </div>
      </div>
      <div class="margin-two" style="text-align: center">
        <span style="font-size: 14px;color: red;">{{noticeContentText=='0'?"":noticeContentText}}</span>
        <van-button v-if="radio == 'blueTooth'" type="default" class="van-button" @click="readIDCard"  block>读取身份证</van-button>
      </div>
    </div>
    <div v-else>
      <div class="flexRadio" style="align-items: center;">
        <span class="font-size">证件号码</span>
        <div class="rightRadio">
          <van-field label-class="ID-container" v-model="certNum" placeholder="请输入证件号码" input-align="right" />
        </div>
      </div>
      <div class="flexRadio" style="align-items: center;">
        <span class="font-size">证件地址</span>
        <div class="rightRadio">
          <van-field label-class="ID-container" v-model="address"  @blur="addressCheck()"  placeholder="请输入证件地址" input-align="right" />
        </div>
      </div>
      <div class="flexRadio flexcheck" style="align-items: center;">
        <span class="font-size" >客户名称</span>
        <div class="rightRadio fontCheck">
          <van-field label-class="ID-container"  v-model="certName" placeholder="请输入客户名称" input-align="right" >
            <template #button>
              <van-button class="checkButton" @click="saveIdCardInfo"><span class="checkFont">校验</span></van-button>
            </template>
          </van-field>
        </div>
      </div>
    </div>
    <div >
      <!-- 认证方式-->
      <div v-show="codeValue == '08' || codeValue == '02'">
        <div class="flexRadio" style="margin-top:20px;align-items: center;">
          <span class="font-size">认证方式</span>
          <van-radio-group v-model="app_face" class="rightRadio" direction="horizontal">
            <van-radio  name="app_face_living_gzt" checked-color="#0081FF" class="font-size" >活体+人脸识别</van-radio>
          </van-radio-group>
        </div>

      </div>
      <!--弹出框确认-->
      <van-overlay :show="isShowDailog">
        <div class="wrapper" @click.stop>
          <div class="block">
            <p class="context">读取身份证成功，是否确认上传</p>
            <div class="bottom-button">
              <p style="width:50%;" @click="isShowDailog = false">取消</p>
              <p class="right-button" @click="saveIdCardInfo">确定</p>
            </div>
          </div>
        </div>
      </van-overlay>
      <!--拍照-->
      <div class="img-grounp">
        <div v-show="facePicFlag" class="img-single" @click="faceRecognition()">
          <div v-if="faceImgResult" @click.stop="preview(faceImgResult)">
            <van-badge color="#0081FF">
              <img :src="faceImgResult" class="img-style"/>
              <template #content>
                <van-icon name="cross" class="badge-icon" @click.stop="deletePhoto(faceImgResult)"/>
              </template>
            </van-badge>
          </div>
          <div v-else>
            <img src="../../images/face.png" class="img-style">
          </div>
          <p class="text">活体检测</p>
        </div>
        <div class="img-single" @click="takePhotos('face')">
          <div v-if="customerImg" @click.stop="preview(customerImg)">
            <van-badge color="#0081FF">
              <img :src="customerImg" class="img-style"/>
              <template #content>
                <van-icon name="cross" class="badge-icon" @click.stop="deletePhoto(customerImg)"/>
              </template>
            </van-badge>
          </div>
          <div v-else>
            <img src="../../images/auth.png" class="img-style" />
          </div>
          <p class="text">客户照片</p>
        </div>
        <div  class="img-single" @click="takePhotos('idCardFront')" v-show="!(codeValue == '02' && (staffPhotoTag=='1' || IDCardPhotoType=='1'))">
          <div v-if="frontImg" @click.stop="preview(frontImg)">
            <van-badge color="#0081FF">
              <img :src="frontImg" class="img-style"/>
              <template #content>
                <van-icon name="cross" class="badge-icon" @click.stop="deletePhoto(frontImg)"/>
              </template>
            </van-badge>
          </div>
          <div v-else>
            <img src="../../images/auth.png" class="img-style" />
          </div>
          <p class="text">身份证正面</p>
        </div>
        <div class="img-single" @click="takePhotos('idCardBack')" v-show="!(codeValue == '02' && (staffPhotoTag=='1' || IDCardPhotoType=='1'))">
          <div v-if="backImg" @click.stop="preview(backImg)">
            <van-badge color="#0081FF">
              <img :src="backImg" class="img-style"/>
              <template #content>
                <van-icon name="cross" class="badge-icon" @click.stop="deletePhoto(backImg)"/>
              </template>
            </van-badge>
          </div>
          <div v-else>
            <img src="../../images/auth.png" class="img-style" />
          </div>
          <p class="text">身份证背面</p>
        </div>
      </div>
      <!--      <van-cell-group>-->
      <!--        <van-field v-model="faceBase64" label="base64有水印未裁剪"  />-->
      <!--        <van-field v-model="faceBase641" label="base64活体无水印"  />-->
      <!--        <van-field v-model="faceBase642" label="base64活体有水印"  />-->
      <!--        <van-field v-model="faceBaseSize" label="base64长度"  />-->
      <!--      </van-cell-group>-->
      <!--人脸比对方式 -->
      <div v-show="codeValue == '02'">
        <div class="flexRadio" style="margin-top:20px;align-items: center;">
          <span class="font-size">人脸比对方式</span>
          <van-radio-group v-model="app_face_check" class="rightRadio" direction="horizontal">
            <van-radio name="app_face_chipcheck" checked-color="#0081FF" class="font-size" @click="app_face_check = 'app_face_chipcheck'">芯片照</van-radio>
           </van-radio-group>
        </div>
      </div>
      <div class="flexRadio" style="align-items: center;">
        <span class="font-size">联系电话<span style="color: red">*</span> </span>
        <div class="rightRadio">
          <van-field label-class="ID-container" type="digit"  maxlength="11" v-model="contactPhone"  @blur="numCheck()" placeholder="建议输入联通号码" input-align="right" :value = this.contactPhone   :readonly = "this.contactCheck"/>
        </div>
      </div>
    </div>
    <div class="submit-container">
      <van-button @click="pushCustomerData" block>确定</van-button>
    </div>
    <!-- 证件类型弹窗-->
    <div class="customer-poup">
      <van-popup v-model="showPicker" position="bottom">
        <van-picker :columns="columns" show-toolbar value-key="codeDesc" title="选择证件类型"  @confirm="onConfirm" @change="onChange" />
      </van-popup>
    </div>
    <errorTips
            v-if="errorInfo"
            :errorTip="errorInfo.errorTip||''"
            :suggestion="errorInfo.suggestion||''"
            :orgErrorInfo="errorInfo.orgErrorInfo||''"
            :isShow="isShow"
            @changeShow="showUpdate"

    />
  </div>
</template>

<script>
import {mapActions, mapMutations, mapState} from 'vuex';
  import WadeMobile from 'rk-native-plugin';
  import { Mobile } from 'rk-web-utils';
  import {Dialog,ImagePreview} from "vant";
  import  {encryptBy,decryptBy} from "@/assets/js/encryption";
  import errorTips from '@/assets/bizComponents/errorTips/errorTips.vue';
  import simheiFont from '@/assets/font/simhei.ttf';

  export default {
    name: "customerAuthentication",
    data() {
      return {
        idSuffix:'',
        tradeTypeCode:"66666",
        isShow:false,
        radio: 'blueTooth',//默认勾选蓝牙
        certName:'',
        certNum:'',
        contactPhone: "",//客户联系电话
        //readerIP: {},//读卡器IP地址
        app_face_check: 'app_face_chipcheck',//人脸识别校验方式
        app_face:'app_face_living_gzt',//  认证方式
        btName: '',//蓝牙设备名称
        codeDesc:'18位身份证',//证件名称
        codeValue:'02',//证件编码取值(证件类型编码)
        showPicker: false,//证件类型弹窗
        columns: [],
        address: '',//蓝牙设备地址
        customerSubmit:{},//客户资料提交数据
        staffId: '',
        IDCardInfo: {},
        readCard: false,//身份认证是否校验通过
        facePicFlag: true,//人脸识别权限开关
        faceFlag: false,
        gztFlag: false,
        contactCheck: false,
        SESSION_ID: '',
        cityName: '',
        livingTypeList:[],
        faceTypeList:[],
        custInfo: {},//客户资料校验过后回填出去的数据
        identityInfo: {},//读取证件信息
        photoInfoList:[],//客户资料提交数据
        isShowDailog: false,//是否展示弹出确认框
        faceImgResult: "",//返回的最终的人脸照片
        realFaceImg: '',//实体照片
        customerImg: '',//客户照片
        frontImg: '',//身份证正面照
        backImg: '',
        isNewCust:'',//新客户标识（0老客户1新客户）
        custType:'',//客户类型（0个人  1集客标识）
        eid:'',
        isEid:'0',
        serialNumber:'',//读证流水号
        readTime:'',//电子二代证读取时间
        sydToken:"db2df8a1aaef7556a4dfc4ec293bff2a9c60a75c8462a967",
        srToken:"3096426b8d6cfa2ddea8aa3b53d0c9a8bf21fc4f3840becd",
        stToken:"81914f904a720c03af35f0f6018836d11efef67cf9404f83",
        ktToken:"3cbe8f3c7907dec229058a9a3571e7ca287f1dd56ff1ee04",
        dcToken:"3cbe8f3c7907dec229058a9a3571e7ca287f1dd56ff1ee04",
        sydpToken:"f8330751a1645e1d90c0b98574f817dd6f2dcfe25bf87e4b9770064dd4acbcd5b903bf0e69bcc2084eab7b8e1898e909af1eef4628d36499",
        kaerToken:"7c686d1ce03506f974fd074774420fd0",
        readerIP: {
          "SYD": "",
          "SR":"",
          "ST":"",
          "KT":"",
          "DC":"",
          "SR_SE":"1",
          "SYDP":"",
          "SYD_SE":"1",
          "KAER_ACCOUNT":"",
          "KAER_PASSWORD":""
        },
        orderId:'',
        noticeContentText:'0',
        IDCardPhotoType:"1",
        staffPhotoTag:"1",
        readIDcardTag:false,
        errorInfo:{},//转义错误信息
        isRealName:"1"
        // faceBase64:'',
        // faceBase641:'',
        // faceBase642:'',
        // faceBaseSize:'',
      }
    },
    components: {
      errorTips
    },
    
    computed: {
      ...mapState(['shbmMsgInfo','jzfkOrderData','chatList']),
      fmtBtName() {
        if (this.btName.substring(0, 3).indexOf("SR") != -1) {
          return "[" + this.btName + "]森锐"
        } else if (this.btName.substring(0, 3).indexOf("HOD") != -1 || this.btName.substring(0, 3).indexOf("SYD") != -1) {
          return "[" + this.btName + "]三元达"
        } else if (this.btName.substring(0, 3).indexOf("KT") != -1) {
          return "[" + this.btName + "]卡尔"
        } else if (this.btName.substring(0, 3).indexOf("DC") != -1) {
          return "[" + this.btName + "]正宏"
        } else if (this.btName.substring(0, 3).indexOf("SS") != -1) {
          return "[" + this.btName + "]神思"
        } else if (this.btName.substring(0, 3).indexOf("ST") != -1) {
          return "[" + this.btName + "]信通"
        } else {
          return "[" + this.btName + "]其它"
        }
      }
    },
    watch: {
      radio: {
        // handler(newPay,oldPay) {
        //   this.eid='';
        //   this.clearCustomerInfos();
        // },
        // immediate: true
      }
    },
    created(){
      this.idSuffix=''+this.chatList.length
    },
    mounted() {
      
      this.getMemoryCache()
      this.initCanvas();
      // this.initAuthentication();
      this.app_face_check ="app_face_chipcheck";
      this.IDCardPhotoType =  "1";
      this.staffPhotoTag =   "1";
      this.app_face ='app_face_living_gzt';
      this.facePicFlag = true;
      this.isEid = "0";
      this.orderId=this.shbmMsgInfo.orderId;
      this.contactPhone=this.jzfkOrderData.mainNumberCheckData.mainNumber
    },
    methods: {
      ...mapMutations([
        'setFlowStep',
        'setRobotWorking',
        'setNum',
        'setJzfkOrderData'
      ]),
      ...mapActions([
        'updateChatList'
      ]),
      showUpdate(data){
        if(data === 'false'){
          this.isShow = false
        }
      },
      isShowPicker() {

        this.showPicker = true

      },
      //照片预览
      preview(item){
        ImagePreview([item]);
      },
      deletePhoto(other) {
        if(this.faceImgResult==other){
          this.faceImgResult='';
        }else if(this.customerImg==other){
          this.customerImg='';
        }else if(this.frontImg==other){
          this.frontImg='';
        }else if(this.backImg==other){
          this.backImg='';
        }
      },
      //认证方式
      appFace(param,type) {
        this.facePicFlag=type;
        this.app_face = param;
      },
      //页面初始化接口
      initAuthentication() {
        this.$toast.loading({
          message: '正在进行页面初始化，请稍等...',
          duration: 0,
          forbidClick: true,
          overlay: true
        })
        if (this.isRealName != 'isRealName') {
          this.$http.post('/customerpicinfoverify/photoTypeGet').then((res)=>{
            //活体校验方式
            this.livingTypeList = res.respData.livingTypeList;
            let livingTypeList = this.livingTypeList;
            //人脸识别校验方式
            this.faceTypeList = res.respData.faceTypeList;
            let faceTypeList = this.faceTypeList;
            for (var i = 0; i < faceTypeList.length; i++) {
              //人脸识别校验方式
              if (faceTypeList[i] == 'app_face_nocheck' || faceTypeList[i] == 'app_face_chipcheck') {



              }
            }
            for (var i = 0; i < livingTypeList.length; i++) {
              if (livingTypeList[i] == 'app_face_noliving') {
                this.app_face = livingTypeList[i];
                this.facePicFlag = false;
              }
              if(livingTypeList[i] == 'app_face_living_gzt') {
                this.app_face ='app_face_living_gzt';
                this.facePicFlag = true;
              }
            }
          }).catch((err)=>{
            this.$toast.clear()
            this.$dialog.alert({
              title:"糟糕，接口出错了，请您重试！",
              message:err
            })
            //this.$toast("糟糕，接口出错了，请您重试！"+err);
          })
        }
        //初始化获取证件类型

        this.$http.post('/customerAuthentication/customerTypeGet',{tradeTypeCode:this.tradeTypeCode}).then((res) => {
          if (res.respCode == '0000') {
            this.$toast.clear()
            if (this.isRealName != 'isRealName') {
              this.columns =[  {
                "codeDesc": "18位身份证",
                "codeId": "WSL_OPEN_CERT_TYPE",
                "codeValue": "02",
                "dispord": 1,
                "remark": "xiebin:开户证件类型"
              }];
            }

          } else {
            this.$toast("糟糕，获取证件类型接口出错了，")
          }
        }).catch((e) => {
          this.$toast.clear()
          this.$dialog.alert({
            title:"请您重试！获取证件类型接口出错了，出错信息为：",
            message:e
          })
          //this.$toast("请您重试！获取证件类型接口出错了，出错信息为：" + e)
        })

      },
      getMemoryCache() {

        Mobile.getMemoryCache(["SESSION_ID"]).then((res) => {
          this.SESSION_ID= res.SESSION_ID
        })
      },
      getLocalCache() { //取蓝牙设备离线缓存
        Mobile.getOfflineCache(["btName", "address"]).then((result) => {
          this.btName = result.btName
          this.address = result.address
        })
      },
      isEmpty(value) {
        let flag = false
        if (value == '' || value == 'undefined' || value == undefined || value == null || value == 'null') {
          flag = true
        }
        return flag
      },
      getCityName() {// WadeMobile.location()插件安卓和ios返回的地址信息字段名上有区别，需要判断设备类型再取值
        WadeMobile.location().then((info) => {
          if (WadeMobile.isAndroid()) {
            this.cityName = info.CITY
          } else if (WadeMobile.isIOS()) {
            this.cityName = info.City
          }
        }).catch(e => {
          console.log(e)
        })
      },
      //获取蓝牙设备
      getDeviceList() {
        WadeMobile.deviceList([]).then((result) => {
          this.btName = result.btName.replace("-", ":")
          this.address = result.address
        }).catch((e) => {
          this.$toast("调用设备异常，请重新选取：" + e)
        })
      },
      //读取身份证信息
      scanQrCode(){
        WadeMobile.scanQrCode().then((info) => {
          this.eid=info;
          this.readIDCard();
        });
      },
      //输入条形码校验
      checkCode(){
        this.readIDCard();
      },
      //读取身份证信息
      readIDCard() {
        if (!this.btName && this.radio=='blueTooth') {
          this.$toast("请先选取设备")
        } else {
          this.clearCustomerInfos();
          if (WadeMobile.isIOS() || this.radio=='eidTooth') {
            this.$toast.loading({//这里的话插件对安卓做了弹窗展示，所以我们这里针对IOS做提示就够了
              message: '正在读卡，请稍等...',
              duration: 0,
              forbidClick: true,
            })
          }
          let date = new Date();
          let year = date.getFullYear(); //获取年
          let month = (date.getMonth()+1)<10?"0"+(date.getMonth()+1):date.getMonth()+1;//获取月
          let day = date.getDate() < 10 ? "0"+date.getDate():date.getDate(); //获取当日
          let dd = year+""+month+day;
          if(this.radio=='eidTooth'){
            if(!this.eid){
              Dialog.alert({
                message: "请先输入电子身份证条形码"
              });
              return;
            }
            this.$http.post('/customerAuthentication/queryElecID',{qrCode:this.eid}).then((result) => {
              if(result.respCode=='0000'){
                let respData = result.respData;
                this.IDCardInfo = {
                  authority: respData.ISSUE_DEPART,//签发机关
                  address: respData.ADDRESS,
                  birth: respData.BIRTHDAY,
                  cardNo: respData.CERT_NUM,
                  ethnicity: respData.NATION,//民族
                  name: respData.CERT_NAME,
                  period: respData.VALIDITY_DATE.replace(/\./g,''),//有效期限
                  sex: respData.SEX,
                  avatar: "data:image/png;base64," + respData.FACEIMG,
                  avatarToSub:respData.FACEIMG,
                  effectDate: respData.VALIDITY_DATE.split("-")[0].replace(/\./g,''),
                  expireDate: respData.VALIDITY_DATE.split("-")[1].replace(/\./g,''),
                  btName: 'eidcard_tag',
                  orderSource:  WadeMobile.isIOS() ? 'zwtios':'zwtand'
                }
                this.frontImg = 'data:image/png;base64,' + respData.FACEIMG;
                this.backImg = 'data:image/png;base64,' + respData.BASEIMG;
                this.photoInfoList = this.photoInfoList.filter(item => item.photoNumber !=='cust02' && item.photoNumber !=='cust03');
                this.photoInfoList.push(
                        {
                          photoNumber: 'cust02',
                          photoValue: respData.FACEIMG
                        },
                        {
                          photoNumber: 'cust03',
                          photoValue: respData.BASEIMG
                        })
              }else{
                this.$toast({
                  message: result.respMsg,
                  duration: 5000,
                  forbidClick: true,
                  overlay: true
                })
                return;
              }
              this.drawCanvas()
              this.saveIdCardInfo();
            }).catch((e) => {
              this.readCard = false
              this.$toast({
                message: '糟糕，接口出错了，请您重试！'+e,
                duration: 5000,
                forbidClick: true,
                overlay: true
              })
              return;
            })
          }else{
            this.readerIP= {
              "SYD": decryptBy(this.sydToken),
              "SR":decryptBy(this.srToken),
              "ST":decryptBy(this.stToken),
              "KT":decryptBy(this.ktToken),
              "DC":decryptBy(this.dcToken),
              "SR_SE":"1",
              "SYDP":decryptBy(this.sydpToken),
              "SYD_SE":"1",
              "KAER_ACCOUNT":decryptBy(this.kaerToken),
              "KAER_PASSWORD":decryptBy(this.kaerToken)
            };
            WadeMobile.readIDCard([this.btName, this.address, '1', this.readerIP]).then((result) => {
              if (WadeMobile.isAndroid()) {
                this.IDCardInfo = {
                  authority: result.authority,//签发机关
                  address: result.address,
                  birth: result.birth,
                  cardNo: result.cardNo,
                  ethnicity: result.ethnicity,//民族
                  name: result.name,
                  period: result.effectDate + '-' + result.expireDate,//有效期限
                  sex: result.sex,
                  avatar: "data:image/png;base64," + result.avatar,
                  avatarToSub:result.avatar,
                  effectDate: result.effectDate,
                  expireDate: result.expireDate,
                  btName: this.btName,
                  orderSource: 'zwtand'
                }
              } else if (WadeMobile.isIOS()) {
                this.IDCardInfo = {
                  authority: result.IssuedAt,
                  address: result.Address,
                  birth: result.Born,
                  cardNo: result.CardNo,
                  ethnicity: result.Nation,
                  name: result.Name,
                  period: result.EffectedDate + '-' + result.ExpiredDate,
                  sex: result.Sex,
                  avatar: "data:image/png;base64," + result.avatar,
                  avatarToSub:result.avatar,
                  effectDate: result.EffectedDate,
                  expireDate: result.ExpiredDate,
                  orderSource: 'zwtios'
                }
              }
              this.photoInfoList = this.photoInfoList.filter(item => item.photoNumber !=='cust15');
              this.photoInfoList.push({
                photoNumber: 'cust15',
                photoValue: result.avatar
              })
              if (this.IDCardInfo.expireDate < dd) {
                this.$toast("证件已失效!");
                return;
              }
              if (this.isEmpty(this.IDCardInfo.cardNo)) {
                this.$toast("身份证未识别,请重新读卡!");
                return;
              }
              if (!this.ageBiggerThen16(this.IDCardInfo.cardNo)) {
                this.$toast("您未满16周岁，请到营业厅受理开户业务!");
                return;
              }
              this.drawCanvas();
              if (this.IDCardInfo.name) {
                Mobile.setOfflineCache("btName", this.btName)
                Mobile.setOfflineCache("address", this.address)
              }

            }).catch((e) => {
              this.$toast.clear()
              this.$toast("糟糕，读取身份证失败"+e)
              return;
            })
          }
        }
      },
      clearCustomerInfos() {
        if(this.codeValue=='02'){
          this.drawCanvasForSapce();
        }
        this.customerImg = ""
        this.faceImgResult = ""
        this.frontImg = ""
        this.backImg = ""
        // this.certNum = ''
        // this.certName = ''
        // this.address = ''
        this.identityInfo = {}
        this.IDCardInfo = {}
      },
      initCanvas() { //先初始化canvas
        let c = document.getElementById("frontCanvas_"+this.idSuffix);
        let ctx = c.getContext("2d");
        ctx.clearRect(0, 0, 300, 191);
        let frontImg = document.getElementById("frontID_"+this.idSuffix);
        frontImg.addEventListener('load', () => {
          ctx.drawImage(frontImg, 0, 0, 300, 191);
        });
        
        
        // frontImg.onload = (() => {
        //       ctx.drawImage(frontImg, 0, 0, 300, 191);
        //   })
        //身份证反面
        let ct = document.getElementById("backCanvas_"+this.idSuffix);
        let cxt = ct.getContext("2d");
        cxt.clearRect(0, 0, 300, 191);
        let backImg = document.getElementById("backID_"+this.idSuffix);
        backImg.addEventListener('load', () => {
          cxt.drawImage(backImg, 0, 0, 300, 191);
        });
        console.log("initCAN")
        // backImg.onload = (() => {
        //     cxt.drawImage(backImg, 0, 0, 300, 191);
        // })
      },
      drawCanvasForSapce() {
        //身份证正面
        let c = document.getElementById("frontCanvas_"+this.idSuffix)
        let ctx = c.getContext("2d");
        let frontImg = document.getElementById("frontID_"+this.idSuffix)
        ctx.drawImage(frontImg, 0, 0, 300, 191)
        ctx.font = "8px"
        ctx.textBaseline = 'middle'
        ctx.fillStyle = "#000"
        //大头贴
        let avaterImage = document.getElementById('frontCanvas_'+this.idSuffix)
        avaterImage = new Image()
        avaterImage.src = this.IDCardInfo.avatar;
        avaterImage.onload = (() => {
          ctx.fillText(" ", 46, 33)
        })
        //身份证反面
        let ct = document.getElementById("backCanvas_"+this.idSuffix)
        let cxt = ct.getContext("2d");
        let backImg = document.getElementById("backID_"+this.idSuffix)
        cxt.drawImage(backImg, 0, 0, 300, 191)
        cxt.font = "10px"
        cxt.textBaseline = 'middle'
        cxt.fillStyle = "#000"
        cxt.fillText(" ", 115, 144)
      },
      drawCanvas() {
        this.$toast.loading({
          message: '正在进行客户资料认证,请稍等...',
          duration: 10000,
          forbidClick: true,
          overlay: true
        })
        //身份证正面
        const myFont = new FontFace('myFont', 'url('+simheiFont+')');
        myFont.load().then(font => {
          document.fonts.add(font)
        }).then(() => {
          let c = document.getElementById("frontCanvas_"+this.idSuffix)
          let ctx = c.getContext("2d");
          let frontImg = document.getElementById("frontID_"+this.idSuffix)
          ctx.drawImage(frontImg, 0, 0, 300, 191)
          ctx.font = "10px myFont"
          ctx.textBaseline = 'middle'
          ctx.fillStyle = "#000"
          //大头贴
          let avaterImage = document.getElementById('frontCanvas_'+this.idSuffix)
          avaterImage = new Image()
          avaterImage.src = this.IDCardInfo.avatar;
          avaterImage.onload = (() => {
            ctx.drawImage(avaterImage, 190, 30, 85, 110)
            ctx.fillText(this.formatName(this.IDCardInfo.name), 46, 33)
            ctx.fillText(this.IDCardInfo.sex, 47, 57)
            ctx.fillText(this.IDCardInfo.ethnicity, 115, 57)
            ctx.fillText(parseInt(this.IDCardInfo.birth.substring(0, 4)), 60, 80)//年
            ctx.fillText(parseInt(this.IDCardInfo.birth.substring(4, 6)), 105, 80)//月
            ctx.fillText(parseInt(this.IDCardInfo.birth.substring(6, 8)), 130, 80)//日
            ctx.fillText(this.formatID(this.IDCardInfo.cardNo), 90, 165)
            //ctx.fillText(this.IDCardInfo.address, 46, 105)
            //身份证地址换行处理
            let lineWidth = 0
            let canvasWidth = c.width
            let lastSubStrIndex = 0
            let initX = 46
            let initY = 105
            let lineHeight = 13
            const str = this.IDCardInfo.address
            for (let i = 0;i < str.length;i++) {
              lineWidth += ctx.measureText(str[i]).width
              if (lineWidth > canvasWidth / 9 * 4) {
                ctx.fillText(str.substring(lastSubStrIndex, i), initX, initY)
                initY += lineHeight
                lineWidth = 0
                lastSubStrIndex = i
              }
              if (i == str.length - 1) {
                ctx.fillText(str.substring(lastSubStrIndex, i + 1), initX, initY)
              }
            }
          })
          //身份证反面
          let ct = document.getElementById("backCanvas_"+this.idSuffix);
          let ctNoSec = document.getElementById("backCanvasNoSecret_"+this.idSuffix);
          let cxt = ct.getContext("2d");
          let cxtNoSec = ctNoSec.getContext("2d");
          let backImg = document.getElementById("backID_"+this.idSuffix)
          cxt.drawImage(backImg, 0, 0, 300, 191)
          cxt.font = "10px myFont"
          cxt.textBaseline = 'middle'
          cxt.fillStyle = "#000"
          cxt.fillText(this.IDCardInfo.authority, 115, 144)
          cxt.fillText(this.formatPeriod(this.IDCardInfo.period), 115, 168);
          cxtNoSec.drawImage(backImg, 0, 0, 300, 191)
          cxtNoSec.font = "10px myFont"
          cxtNoSec.textBaseline = 'middle'
          cxtNoSec.fillStyle = "#000"
          cxtNoSec.fillText(this.IDCardInfo.authority, 115, 144)
          cxtNoSec.fillText(this.formatPeriod(this.IDCardInfo.period), 115, 168);
          if(this.isRealName != 'isRealName'&&(this.IDCardPhotoType=="1"||this.staffPhotoTag=="1")&&this.codeValue=='02'){//开关打开并且有合成身份证权限


            let noSec = document.getElementById("frontCanvasNoSecret_"+this.idSuffix);
            let ctx = noSec.getContext("2d");
            let frontImg = document.getElementById("frontID_"+this.idSuffix);
            ctx.drawImage(frontImg, 0, 0, 300, 191);
            ctx.font = "10px myFont";
            ctx.textBaseline = 'middle';
            ctx.fillStyle = "#000";
            //大头贴
            let avaterImage = document.getElementById('frontCanvasNoSecret_'+this.idSuffix);
            avaterImage = new Image();
            avaterImage.src = this.IDCardInfo.avatar;
            avaterImage.onload = (() => {
              ctx.drawImage(avaterImage, 190, 30, 85, 110)
              ctx.fillText(this.IDCardInfo.name, 46, 33)
              ctx.fillText(this.IDCardInfo.sex, 47, 57)
              ctx.fillText(this.IDCardInfo.ethnicity, 115, 57)
              ctx.fillText(parseInt(this.IDCardInfo.birth.substring(0, 4)), 60, 80)//年
              ctx.fillText(parseInt(this.IDCardInfo.birth.substring(4, 6)), 105, 80)//月
              ctx.fillText(parseInt(this.IDCardInfo.birth.substring(6, 8)), 130, 80)//日
              ctx.fillText(this.IDCardInfo.cardNo, 90, 165)
              //身份证地址换行处理
              let lineWidth = 0
              let canvasWidth = c.width
              let lastSubStrIndex = 0
              let initX = 46
              let initY = 105
              let lineHeight = 13
              const str = this.IDCardInfo.address
              for (let i = 0;i < str.length;i++) {
                lineWidth += ctx.measureText(str[i]).width
                if (lineWidth > canvasWidth / 9 * 4) {
                  ctx.fillText(str.substring(lastSubStrIndex, i), initX, initY)
                  initY += lineHeight
                  lineWidth = 0
                  lastSubStrIndex = i
                }
                if (i == str.length - 1) {
                  ctx.fillText(str.substring(lastSubStrIndex, i + 1), initX, initY)
                }
              }
              ctx.fillStyle = 'rgba(204, 40, 51, 1)';
              ctx.translate((avaterImage.width + 70) / 2, (avaterImage.height + 70) / 2);
              // ctx.rotate((Math.PI / 180) * -30);
              ctx.translate(-(avaterImage.width + 70) / 2, -(avaterImage.height + 70) / 2);
              // 单独绘制水印
              ctx.fillText('仅限办理中国联通业务时使用', avaterImage.width * 21 / 100, avaterImage.height *  10.3  / 10);
              var _this = this;
              let yyyy = new Date().getFullYear();
              let mm = new Date().getMonth() + 1;
              let dd = new Date().getDate();
              let hh = new Date().getHours();
              let mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes();
              let ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds();
              let yy = yyyy.toString().substring(2);
              _this.gettime = yy + '-' + mm + '-' + dd + ' ' + hh + ':' + mf + ':' + ss;
              ctx.fillText(this.shbmMsgInfo.channelId+","+ this.shbmMsgInfo.operatorId, avaterImage.width * 21 / 100, avaterImage.height * 11.15 / 10);
              ctx.fillText(_this.gettime, avaterImage.width * 21 / 100, avaterImage.height * 11.9 / 10);
              var makeFrontImg=noSec.toDataURL('image/jpeg');//身份证正面合成照片base64流
              this.frontImg = makeFrontImg;
              let strFront = 'data:image/jpeg;base64,';
              let frontImgtoSub = makeFrontImg.substring(strFront.length,makeFrontImg.length);
              this.photoInfoList = this.photoInfoList.filter(item => item.photoNumber !=='cust02');
              this.photoInfoList.push({
                photoNumber: 'cust02',
                photoValue: frontImgtoSub
              })

            });

            cxtNoSec.fillStyle = 'rgba(204, 40, 51, 1)';
            cxtNoSec.translate((ctNoSec.width + 70) / 2, (ctNoSec.height + 70) / 2);
            cxtNoSec.translate(-(ctNoSec.width + 70) / 2, -(ctNoSec.height + 70) / 2);
            // 单独绘制水印
            cxtNoSec.fillText('仅限办理中国联通业务时使用', ctNoSec.width * 24.5 / 100, ctNoSec.height * 5.3 / 10);
            cxtNoSec.fillText( this.shbmMsgInfo.channelId+","+ this.shbmMsgInfo.operatorId, ctNoSec.width * 24.5 / 100, ctNoSec.height * 6 / 10);
             var _this = this;
            let yyyy = new Date().getFullYear();
            let mm = new Date().getMonth() + 1;
            let dd = new Date().getDate();
            let hh = new Date().getHours();
            let mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes();
            let ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds();
            let yy = yyyy.toString().substring(2);
            _this.gettime = yy + '-' + mm + '-' + dd + ' ' + hh + ':' + mf + ':' + ss;
            cxtNoSec.fillText(_this.gettime, ctNoSec.width * 24.5 / 100, ctNoSec.height * 6.7 / 10);
            var makeBackImg=ctNoSec.toDataURL('image/jpeg');//身份证背面合成照片base64流
            this.backImg = makeBackImg;
            let strBack = 'data:image/jpeg;base64,';
            let backImgtoSub = makeBackImg.substring(strBack.length, makeBackImg.length);
            this.photoInfoList = this.photoInfoList.filter(item => item.photoNumber !=='cust03');
            this.photoInfoList.push({
              photoNumber: 'cust03',
              photoValue: backImgtoSub
            })

            // this.waterMarkForMake( makeBackImg,"身份证背面照","1").then(base64 =>{
            //   this.backImg = base64;
            //   let str = 'data:image/jpeg;base64,';
            //   let backImgtoSub = base64.substring(str.length, base64.length);
            //   this.photoInfoList = this.photoInfoList.filter(item => item.photoNumber !=='cust03');
            //   this.photoInfoList.push({
            //     photoNumber: 'cust03',
            //     photoValue: backImgtoSub
            //   })
            // });
          }
          this.saveIdCardInfo();

        });

      },
      //给照片增加水印
      waterMarkForMake (img,photoType,imageType) {
        return new Promise((resolve) => {
          if(imageType=="1"){
            this.getImgUrlByMake({
              url:img,
              cb: (base64) => {
                resolve(base64);
              }
            });
          }else {
            this.getImgUrlForFrontByMake({
              url:img,
              cb: (base64) => {
                resolve(base64);
              }
            });
          }

        });
      },
      getImgUrlByMake ({
                         url = '',
                         textAlign = 'center',
                         textBaseline = 'center',
                         fillStyle = 'rgba(204, 40, 51, 1)',
                         content = '仅限办理中国联通业务时使用',
                         font = '10px myFont',
                         cb = null,
                         textX = 70,
                         textY = 70
                       }) {
        Mobile.getMemoryCache(["STAFF_ID","staffInfo"]).then((result) => {
          const img = new Image();
          img.src = url;
          img.crossOrigin = 'anonymous';
          img.onload = function () {
            const myFont = new FontFace('myFont', 'url('+simheiFont+')');
            myFont.load().then(font => {
              document.fonts.add(font)
            }).then(() => {
              const canvas = document.createElement('canvas');
              canvas.width = img.width;
              canvas.height = img.height;
              const ctx = canvas.getContext('2d');
              ctx.drawImage(img, 0, 0);
              ctx.textAlign = textAlign;
              ctx.textBaseline = textBaseline;
              ctx.font = font;
              ctx.fillStyle = fillStyle;
              ctx.translate((img.width + textX) / 2, (img.height + textY) / 2);
              // ctx.rotate((Math.PI / 180) * -30);
              ctx.translate(-(img.width + textX) / 2, -(img.height + textY) / 2);
              // 单独绘制水印
              ctx.fillText(content, img.width * 53 / 100, img.height * 5.3 / 10);
              var _this = this;
              let yyyy = new Date().getFullYear();
              let mm = new Date().getMonth() + 1;
              let dd = new Date().getDate();
              let hh = new Date().getHours();
              let mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes();
              let ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds();
              let yy = yyyy.toString().substring(2);
              _this.gettime = yy + '-' + mm + '-' + dd + ' ' + hh + ':' + mf + ':' + ss;
              ctx.fillText(JSON.parse(result.staffInfo).DEPART_CODE + "," + JSON.parse(result.staffInfo).STAFF_ID, img.width * 46.5 / 100, img.height * 6 / 10);
              ctx.fillText(_this.gettime, img.width * 45 / 100, img.height * 6.7 / 10);
              // 将绘制完成的canvas转换为base64的地址
              const base64Url = canvas.toDataURL('image/jpeg');
              // return base64Url
              cb && cb(base64Url);
            });

          };
        });
      },
      getImgUrlForFrontByMake ({
                                 url = '',
                                 textAlign = 'center',
                                 textBaseline = 'center',
                                 fillStyle = 'rgba(204, 40, 51, 1)',
                                 content = '仅限办理中国联通业务时使用',
                                 font = '10px myFont',
                                 cb = null,
                                 textX = 70,
                                 textY = 70
                               }) {
        Mobile.getMemoryCache(["STAFF_ID","staffInfo"]).then((result) => {
          const img = new Image();
          img.src = url;
          img.crossOrigin = 'anonymous';
          img.onload = function () {
            const myFont = new FontFace('myFont', 'url('+simheiFont+')');
            myFont.load().then(font => {
              document.fonts.add(font)
            }).then(() => {
              const canvas = document.createElement('canvas');
              canvas.width = img.width;
              canvas.height = img.height;
              const ctx = canvas.getContext('2d');
              ctx.drawImage(img, 0, 0);
              ctx.textAlign = textAlign;
              ctx.textBaseline = textBaseline;
              ctx.font = font;
              ctx.fillStyle = fillStyle;
              ctx.translate((img.width + textX) / 2, (img.height + textY) / 2);
              // ctx.rotate((Math.PI / 180) * -30);
              ctx.translate(-(img.width + textX) / 2, -(img.height + textY) / 2);
              // 单独绘制水印
              ctx.fillText(content, img.width * 28 / 100, img.height * 6.85 / 10);
              var _this = this;
              let yyyy = new Date().getFullYear();
              let mm = new Date().getMonth() + 1;
              let dd = new Date().getDate();
              let hh = new Date().getHours();
              let mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes();
              let ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds();
              let yy = yyyy.toString().substring(2);
              _this.gettime = yy + '-' + mm + '-' + dd + ' ' + hh + ':' + mf + ':' + ss;
              ctx.fillText(JSON.parse(result.staffInfo).DEPART_CODE+","+JSON.parse(result.staffInfo).STAFF_ID, img.width * 21.5 / 100, img.height * 7.45 / 10);
              ctx.fillText(_this.gettime, img.width * 20 / 100, img.height * 8.1 / 10);
              // 将绘制完成的canvas转换为base64的地址
              const base64Url = canvas.toDataURL('image/jpeg');
              // return base64Url
              cb && cb(base64Url);
            });

          };
        });
      },
      saveIdCardInfo() {
        let reqData = {};
        if (this.codeValue == '02' || this.codeValue == '35') {
          reqData = {
            certType:this.codeValue,//this.IDCardInfo//证件类型
            tradeTypeCode: this.tradeTypeCode,//业务类型
            period: this.IDCardInfo.period,//证件有效期
            certNum: this.IDCardInfo.cardNo,//证件号码
            certName:this.IDCardInfo.name,

            sex: this.IDCardInfo.sex,
            nation: this.IDCardInfo.ethnicity,
            certAddr: this.IDCardInfo.address,
            certValidityBegin: this.IDCardInfo.effectDate,
            certValidityEnd: this.IDCardInfo.expireDate,
            issuing: this.IDCardInfo.authority,
            btName:this.btName,
            certImage:this.IDCardInfo.avatarToSub,
            sessionId: this.SESSION_ID,
            orderSource: this.IDCardInfo.orderSource,
            orderId: this.orderId,
            eid:this.eid,
            faceImg: this.frontImg,
            baseImg: this.backImg
          }
        } else {
          reqData = {
            certType:this.codeValue,//this.IDCardInfo//证件类型
            tradeTypeCode: this.tradeTypeCode,//业务类型
            certNum: this.certNum,//证件号码
            certName:this.certName,
            orderId: this.orderId
          }
        }


        this.$http.post('/customerAuthentication/customerAuthentication',reqData).then((res) => {
          if (res.respCode == '0000') {
            this.readCard = true;
            //custInfo客户信息
            this.custInfo = res.respData.custInfo

            //客户类型（0个人  1集客标识）
            this.custType = res.respData.custType
            //新客户标识（0老客户1新客户）
            this.isNewCust = res.respData.isNewCust
            this.customerSubmit.isNewCust = res.respData.isNewCust
            this.serialNumber=res.respData.serialNumber //读证流水号
            this.readTime=res.respData.readTime //电子二代证读取时间
            this.noticeContentText=res.respData.noticeContentText;
            if(  this.noticeContentText!='0'){
              this.$dialog.alert({
                title:"提示:",
                message: this.noticeContentText+"，点击确定可以继续开户"
              })
            }
            this.$emit("changeName");
            this.$toast.clear();
            this.$toast("客户认证校验成功！");
          } else {
            this.clearCustomerInfos();
            this.$toast.clear();
            this.readCard =false;
            document.documentElement.scrollTop = 0;
            this.$dialog.alert({
              title:"提示:",
              message: '客户资料校验出错！'+res.respMsg
            })

            // this.$toast({
            //     message: '糟糕，客户认证校验接口出错了'+res.respMsg,
            //     duration: 5000,
            //     forbidClick: true,
            //     overlay: true
            // })
          }
        }).catch((e) => {
          this.readCard = false
          this.$toast({
            message: '糟糕，接口出错了，请您重试！'+e,
            duration: 5000,
            forbidClick: true,
            overlay: true
          })
        })
      },
      //格式化姓名处理
      formatName(e) {
        let reg = /^(.).+(.)$/g;
        let str = e
        if (str.length > 2) {
          return str.replace(reg, '$1***$2')
        } else {
          return str.substr(0, 1) + '*'
        }
      },
      //格式化处理身份证号
      formatID(e) {
        let str = e
        let head = str.substr(0, 6)
        let tail = str.substr(16, 18)
        let all = head + '***********' + tail
        return all
      },
      //格式化有限期
      formatPeriod(e) {
        let str = e
        let startYear = str.substr(0, 4)
        let startMonth = str.substr(4, 2)
        let startDay = str.substr(6, 2)
        let endYear = str.substr(8, 5)
        let endMonth = str.substr(13, 2)
        let endDay = str.substr(15, 2)
        let result = startYear + '.' + startMonth + '.' + startDay + endYear + '.' + endMonth + '.' + endDay
        return result
      },
      //判断身份证是否大于16岁
      ageBiggerThen16(sn) {
        // this.$toast('证件号码是：'+sn);
        if (!sn) {
          return false;
        }
        //一天的毫秒值
        let dayTime = 86400000;
        let bstr = sn.substring(6, 14);
        let _now = new Date();
        let _bir = new Date(parseInt(bstr.substring(0, 4)) + 16, parseInt(bstr.substring(4, 6)) - 1, bstr.substring(6, 8))
        //_bir的构造函数的返回的时间是0点 对于同一天的比较必然是_now>_bir 所以+1天进行比较
        return _now.getTime() > (_bir.getTime() + dayTime)
      },
      //活体检测
      faceRecognition() {
        WadeMobile.faceAuthentication([0, 0, 1]).then((result) => {
          if (result.retCode === '0') {
            let faceImgHead="data:image/jpeg;base64,";
            let faceImg = "data:image/jpeg;base64,"+result.img;
            let img = result.img;
            // this.faceBaseSize=img.length;
            // let img_best = result.img_best;
            // this.faceImgResult = faceImg; //拍照第一张进行展示
            this.waterMark(faceImg,"客户活体照","0").then(base64 =>{
              // faceImg=this.faceImgResult;
              this.faceImgResult=base64;
              let faceImgResultSub = base64.substring(faceImgHead.length,base64.length);
              if (this.isEmpty(result.img_best)) {//活体第二张照片必须要有，不能为空
                this.$toast("活体认证照片采集失败，请重新尝试人脸识别")
                this.faceImgResult = ""
                return
              }
              //this.realFaceImg = result.img_best
              this.photoInfoList = this.photoInfoList.filter(item => item.photoNumber !=='cust13'&&item.photoNumber !=='cust14');
              this.photoInfoList.push({
                photoNumber: 'cust13',
                photoValue: faceImgResultSub,
                photoValueNoWater: img
              },{
                photoNumber: 'cust14',
                photoValue: result.img_best
              })
            })
          } else {
            this.faceImgResult = ""
            this.$toast("活体认证照片采集失败，请重新尝试人脸识别")
          }
        }).catch((e) => {
          this.faceImgResult = ""
          this.$toast("调用活体认证方法出错，错误信息为:" + e)
        })
      },
      getNowFormatDate() {
        let date = new Date();
        let seperator1 = "-";
        let seperator2 = ":";
        let month = date.getMonth() + 1;
        let strDate = date.getDate();
        if (month >= 1 && month <= 9) {
          month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
          strDate = "0" + strDate;
        }
        let hours = date.getHours();
        let minutes = date.getMinutes();
        let seconds = date.getSeconds();
        if (hours >= 0 && hours <= 9) {
          hours = "0" + hours;
        }
        if (minutes >= 0 && minutes <= 9) {
          minutes = "0" + minutes;
        }
        if (seconds >= 0 && seconds <= 9) {
          seconds = "0" + seconds;
        }
        let currentdate = date.getFullYear() + seperator1 + month + seperator1 + strDate + " " + hours + seperator2 + minutes + seperator2 + seconds;
        return currentdate;
      },
      takePhotos(photoType) {
        if (photoType == "face") {
          WadeMobile.getIdentifyPhoto(photoType, "base64").then((img) => {
            // this.faceBaseSize=img.length;
            if (img.length / 1024 * 3 / 4 < 50) {
              this.$toast("图片质量过低，请重新拍摄")
              return
            }
            this.waterMark(img,"客户人脸照","1").then(base64 =>{
              this.customerImg=base64;
              let str = 'data:image/jpeg;base64,';
              let faceImgtoSub = base64.substring(str.length,base64.length);
              let strJpg = 'data:image/png;base64,';
              let faceImgtoSubNoWater = img.substring(strJpg.length,img.length);
              this.photoInfoList = this.photoInfoList.filter(item => item.photoNumber !=='cust01');
              this.photoInfoList.push({
                photoNumber: 'cust01',
                photoValue: faceImgtoSub,
                photoValueNoWater: faceImgtoSubNoWater
              })
            });//添加水印
          }).catch((e) => {
            this.$toast("抱歉！调用失败，请您重新拍照！错误信息为:" + e)
          })
        } else if (photoType == "idCardFront") {
          if (!this.customerImg) {
            this.$toast("请先拍摄客户照片")
          } else {
            WadeMobile.getIdentifyPhoto(photoType, "base64").then((img) => {
              // this.faceBase64=img;
              // this.faceBaseSize=img.length;
              if (img.length / 1024 * 3 / 4 < 50) {
                this.$toast("图片质量过低，请重新拍摄")
                return
              }
              this.waterMark(img,"身份证正面照","2").then(base64 =>{
                this.frontImg=base64;
                let str = 'data:image/jpeg;base64,';
                let frontImgtoSub = base64.substring(str.length,base64.length);
                this.photoInfoList = this.photoInfoList.filter(item => item.photoNumber !=='cust02');
                this.photoInfoList.push({
                  photoNumber: 'cust02',
                  photoValue: frontImgtoSub
                })
              })
            }).catch((e) => {
              this.$toast("抱歉！调用失败，请您重新拍照！错误信息为:" + e)
            })
          }
        } else {
          if (!this.frontImg) {
            this.$toast("请先拍摄身份证正面照片")
          } else {
            WadeMobile.getIdentifyPhoto(photoType, "base64").then((img) => {
              if (img.length / 1024 * 3 / 4 < 50) {
                this.$toast("图片质量过低，请重新拍摄")
                return
              }
              let str = 'data:image/jpeg;base64,';
              this.waterMark(img,"身份证背面照","3").then(base64 =>{
                this.backImg = base64;
                // img=base64;
                let backImgtoSub = base64.substring(str.length, base64.length);
                this.photoInfoList = this.photoInfoList.filter(item => item.photoNumber !=='cust03');
                this.photoInfoList.push({
                  photoNumber: 'cust03',
                  photoValue: backImgtoSub
                })
              })
            }).catch((e) => {
              this.$toast("抱歉！调用失败，请您重新拍照！错误信息为:" + e)
            })

          }
        }
      },
      //完成校验
      pushCustomerData() {
        if(this.isRealName != 'isRealName' &&( this.isEmpty(this.frontImg)||this.isEmpty(this.backImg))){
          this.$dialog.alert({
            title:"提示:",
            message:  "身份证读取异常!请重新读取"
          })
          return;
        }
        if (!this.readCard) {
          this.$toast("请先校验客户信息！")
          return
        }
        // if (this.isRealName == "isRealName") {
        //   this.$emit('customerInfo', {"certNum":this.IDCardInfo.cardNo,"certName":this.IDCardInfo.name,"certType":this.codeValue});
        //   return;
        // }
        if (!this.isEmpty(this.contactPhone)) {
          if (!(/^1[3456789]\d{9}$/.test(this.contactPhone))) {
            this.$toast("请填写正确的手机号码!");
            return;
          } else {
            this.customerSubmit.contactPhone = this.contactPhone
          }
        } else {
          // this.$toast("联系电话用于接收校园宽带密码短信，不能为空，建议使用联通手机号码!");
          this.$dialog.alert({
            title:"温馨提示!",
            message:"联系电话用于接收短信消息，不能为空，建议使用联通手机号码!"
          })
          return;
        }
        this.$dialog.confirm({
          title: '提醒',
          message: '请确定你所认证的是副卡所属客户，如果无误，请点击确定，谢谢!',
        }).then(() => {
          this.pushCustomerDataCommit();
        }).catch(() => {
          // on cancel
        });

      },
      //完成校验提交数据
      pushCustomerDataCommit() {
        this.$dialog.close();

        //读取的证件信息
        this.customerSubmit.faceType = this.app_face_check
        if (this.codeValue == '02' || this.codeValue=='35') {
          this.identityInfo.sex = this.IDCardInfo.cardNo
          this.identityInfo.address = this.IDCardInfo.address
          this.identityInfo.name = this.IDCardInfo.name
          this.identityInfo.cardNo = this.IDCardInfo.cardNo
          this.identityInfo.baseimg = this.IDCardInfo.avatarToSub
          this.identityInfo.period = this.IDCardInfo.period
        } else {
          this.identityInfo.address = this.address
          this.identityInfo.name = this.certName
          this.identityInfo.cardNo = this.certNum
          this.identityInfo.sex = 'M'
          this.identityInfo.period = '20200109-20290109'
          this.customerSubmit.faceType = 'app_face_nocheck'
        }
        // if (!this.identityInfo.address) {
        //   this.$toast("请输入证件地址！")
        //   return;
        // }
        this.$toast.loading({
          message: '正在进行客户资料提交,请稍等...',
          duration: 0,
          forbidClick: true,
          overlay: true
        })

        this.customerSubmit.identityInfo = this.identityInfo
        this.customerSubmit.photoInfoList = this.photoInfoList
        this.customerSubmit.certType = this.codeValue
        this.customerSubmit.orderId = this.orderId;
        this.$http.post('/customerAuthentication/customerSubmit', 
         this.customerSubmit
        ).then((res) => {
          this.$toast.clear()
          if (res.respCode == '0000') {
            this.$toast("调用成功！")
            var custId = res.respData.custId //客户标识
            this.identityInfo.baseimg="";
            let customer = {
              custInfo: this.custInfo,
              custId: this.custInfo.CUST_ID || custId, //客户ID
              photoCollectNo: res.respData.photoCollectNo,  //照片流水
              extranetUrl: res.respData.extranetUrl,//外网地址
              intranetUrl: res.respData.intranetUrl, //内网地址
              identityInfo: this.identityInfo, //客户认证信息
              contactPhone: this.contactPhone, // 联系电话
              custType: this.custType, // 客户类型
              certType: this.codeValue, // 证件类型
              similarity: res.respData.similarity, // 比对分数
              facePicFlag: this.facePicFlag, // 活体认证 （true / false）
              //faceCheck: this.customerSubmit.faceType, // 人脸比对
              faceCheck : this.customerSubmit.faceType === 'app_face_nocheck'?'1':'2',
              appFace : this.app_face === 'app_face_living_gzt'?'2':'1', //校验方式
              isEid : this.radio === 'eidTooth'?'1':'0',
              serialNumber:this.serialNumber,
              readTime:this.readTime,
              authority:this.IDCardInfo.authority,
              btName:this.btName.replace(/\r|\n|\s/g,'')
            }
            this.jzfkOrderData.custCheckData=customer
            this.setJzfkOrderData(this.jzfkOrderData)

            let data = {
              inputType: "1",
              type: '1',
              textInput: "customerAuthenticationSubmit",
              notifyFlag: ''
            }
            this.$emit('newChatApi', data);
          } else {
            this.$toast.clear()
            if(res.respMsg.indexOf("客户已存在")!=-1){
              this.isShow = true
              this.errorInfo= {
                orgErrorInfo:"老客户资料校验失败，输入信息与CBSS客户资料不一致！",
                suggestion:"护照：请至CBSS查询老客户资料后正确填写。 " +
                        "身份证：请至CBSS修改客户资料信息，",
                errorTip:res.respMsg
              }
            }else {
              this.$dialog.alert({
                title:"提示:",
                message:res.respMsg
              })
            }

            //this.$toast("糟糕，接口初始化出错了，请您重试！"+res.respMsg)
          }
        }).catch((err) => {
          this.$toast("糟糕，接口初始化出错了，请您重试！"+err);
        })
      },

      numCheck(){
        if(!/^((1[0-9]{2})+\d{8})$/.test(this.contactPhone)){
          this.$dialog.alert({
            title:"提示",
            message:"请输入正确的号码!"
          });
          this.contactPhone="";
        }
      },
      addressCheck(){
        const msg=/[\u4e00-\u9fa5]{8,}/;
        if(!msg.test(this.address)){
          this.$dialog.alert({
            title:"提示",
            message:"证件地址必须大于七个汉字!"
          });
          this.address="";
        }
      },
      //证件类型弹窗
      onChange(picker, value) {
        let that = this;
        that.codeDesc = value.codeDesc;
        that.codeValue = value.codeValue;
      },
      onConfirm(value) {
        let that = this;
        that.codeDesc = value.codeDesc;
        that.codeValue = value.codeValue;
        this.showPicker = false;
      },
      //给照片增加水印
      waterMark (img,photoType,imageType) {
        var base64Str="";
        return new Promise((resolve) => {
          if(imageType=="0") {
            this.getImgUrlLive({
              url:img,
              cb: (base64) => {
                resolve(base64);
              }
            })
          }else {
            this.getImgUrl({
              url:img,
              cb: (base64) => {
                resolve(base64);
              }
            })
          }
        });
      },
      getImgUrl ({
                   url = '',
                   textAlign = 'center',
                   textBaseline = 'center',
                   fillStyle = 'rgba(204, 40, 51, 1)',
                   content = '仅限办理中国联通业务时使用',
                   font = '35px microsoft yahei',
                   cb = null,
                   textX = 70,
                   textY = 70
                 }) {
        Mobile.getMemoryCache(["STAFF_ID","staffInfo"]).then((result) => {
          const img = new Image();
          img.src = url;
          img.crossOrigin = 'anonymous';
          img.onload = function () {
            const canvas = document.createElement('canvas');
            canvas.width = img.width;
            canvas.height = img.height;
            const ctx = canvas.getContext('2d');
            ctx.drawImage(img, 0, 0);
            ctx.textAlign = textAlign;
            ctx.textBaseline = textBaseline;
            ctx.font = font;
            ctx.fillStyle = fillStyle;
            ctx.translate((img.width + textX) / 2, (img.height + textY) / 2);
            // ctx.rotate((Math.PI / 180) * -30);
            ctx.translate(-(img.width + textX) / 2, -(img.height + textY) / 2);
            // 单独绘制水印
            ctx.fillText(content, img.width / 2, img.height / 10);
            var _this = this;
            let yyyy = new Date().getFullYear();
            let mm = new Date().getMonth()+1;
            let dd = new Date().getDate();
            let hh = new Date().getHours();
            let mf = new Date().getMinutes()<10 ? '0'+new Date().getMinutes() : new Date().getMinutes();
            let ss = new Date().getSeconds()<10 ? '0'+new Date().getSeconds() : new Date().getSeconds();
            let yy=yyyy.toString().substring(2);
            _this.gettime = yy+'-'+mm+'-'+dd+' '+hh+':'+mf+':'+ss;
            ctx.fillText(JSON.parse(result.staffInfo).DEPART_CODE+","+JSON.parse(result.staffInfo).STAFF_ID, img.width / 2, img.height * 9 / 10);
            ctx.fillText(_this.gettime, img.width / 2, img.height * 9.5 / 10);
            // 将绘制完成的canvas转换为base64的地址
            const base64Url = canvas.toDataURL('image/jpeg');
            // return base64Url
            cb && cb(base64Url);
          };
        });

      },
      getImgUrlLive ({
                       url = '',
                       textAlign = 'center',
                       textBaseline = 'center',
                       fillStyle = 'rgba(204, 40, 51, 1)',
                       content = '仅限办理中国联通业务时使用',
                       font = '23px microsoft yahei',
                       cb = null,
                       textX = 70,
                       textY = 70
                     }) {
        Mobile.getMemoryCache(["STAFF_ID","staffInfo"]).then((result) => {
          const img = new Image();
          img.src = url;
          img.crossOrigin = 'anonymous';
          img.onload = function () {
            const canvas = document.createElement('canvas');
            canvas.width = img.width;
            canvas.height = img.height;
            const ctx = canvas.getContext('2d');
            ctx.drawImage(img, 0, 0);
            ctx.textAlign = textAlign;
            ctx.textBaseline = textBaseline;
            ctx.font = font;
            ctx.fillStyle = fillStyle;
            ctx.translate((img.width + textX) / 2, (img.height + textY) / 2);
            // ctx.rotate((Math.PI / 180) * -30);
            ctx.translate(-(img.width + textX) / 2, -(img.height + textY) / 2);
            // 单独绘制水印
            ctx.fillText(content, img.width / 2, img.height / 10);
            var _this = this;
            let yyyy = new Date().getFullYear();
            let mm = new Date().getMonth()+1;
            let dd = new Date().getDate();
            let hh = new Date().getHours();
            let mf = new Date().getMinutes()<10 ? '0'+new Date().getMinutes() : new Date().getMinutes();
            let ss = new Date().getSeconds()<10 ? '0'+new Date().getSeconds() : new Date().getSeconds();
            let yy=yyyy.toString().substring(2);
            _this.gettime = yy+'-'+mm+'-'+dd+' '+hh+':'+mf+':'+ss;
            ctx.fillText(JSON.parse(result.staffInfo).DEPART_CODE+","+JSON.parse(result.staffInfo).STAFF_ID, img.width / 2, img.height * 9 / 10);
            ctx.fillText(_this.gettime, img.width / 2, img.height * 9.5 / 10);
            // 将绘制完成的canvas转换为base64的地址
            const base64Url = canvas.toDataURL('image/jpeg');
            // return base64Url
            cb && cb(base64Url);
          };
        });

      },
    }
  }
</script>

<style lang="scss">
  .flexcheck {
    margin-top: 3px;
    .van-cell {
      padding-bottom: 6px;
      padding-top: 6px;
    }
    .fontCheck {
      width: 75%;
    }
  }
  .customer-poup {
    .van-picker__cancel {
      display: none;
    }
    .van-cell {
      margin-left: -16px;
    }
    .van-picker__title {
      margin-left: 20px;
      font-size: 18px;
      color: #333333;
      line-height: 18px;
      font-weight: 600;
    }
    .van-picker__confirm {
      font-size: 18px;
      color: #0081FF;
      font-weight: 600;
    }
  }
</style>
<style lang="scss" scoped>
  .customer {
    margin-left: 15px;
    margin-right: 15px;
  }
  .checkButton {
    width: 70px;
    height: 30px;
    background: #0081FF;
    border-radius: 4px;
  }
  .checkFont {
    font-size: 14px;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 14px;
  }

  .label-title {
    margin-top: 30px;
    font-size: 18px;
    font-weight: 600;
    color: #333333;
  }
  .flexRadio {
    display: flex;
    flex-direction: row;
    position: relative;
    line-height: 50px;
    border-bottom: 1px solid #e4e4e4;
    color: #333333;
    .rightRadio {
      position: absolute;
      right: 0;
    }
    .other-group {
      color: #aaa;
      display: flex;
      align-items: center;
    }
    .font-size {
      font-size: 14px;
    }
    .margin-r12 {
      margin-right: 12px;
    }
  }
  .my-button{
    display: flex;
  }
  .warn-show {
    margin: 15px 0 7px 0;
    padding: 7px;
    color: #aaa;
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: rgb(226, 244, 255);
    border: 1px solid rgb(132, 210, 255);
    .p-text {
      font-size: 12px;
      margin-left: 5px;
    }
  }

  .ID-container {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    .row-line {
      position: absolute;
      width: 15px;
      height: 2px;
      background-color: #aaa;
    }
    .col-line {
      position: absolute;
      width: 2px;
      height: 15px;
      background-color: #aaa;
    }
    .my-swipe {
      .van-swipe-item {
        color: #fff;
        width: 300px;
        height: 191px;
        text-align: center;
        border-radius: 10px;
      }
    }
  }

  .margin-two {
    margin: 20px 0;
    .p-text {
      font-size: 12px;
      text-align: center;
      color: #aaa;
      line-height: 20px;
    }
  }

  .van-button {
    border-radius: 7px;
    width: 100%;
    font-size: 14px;
    color: #fff;
    background-color: #0081ff;
  }

  .active {
    display: none;
  }
  .wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  .block {
    width: 300px;
    height: 150px;
    text-align: center;
    border-radius: 7px;
    position: relative;
    background-color: #fff;
    .context {
      color: #333333;
      line-height: 100px;
      font-size: 15px;
      font-weight: 600;
    }
    .bottom-button {
      width: 100%;
      height: 50px;
      background-color: #fff;
      color: #333333;
      font-size: 14px;
      font-weight: 600;
      display: flex;
      flex-direction: row;
      position: absolute;
      bottom: 0;
      border-bottom-left-radius: 7px;
      border-bottom-right-radius: 7px;
      align-items: center;
      justify-content: center;
      border-top: 1px solid #ccc;
      .right-button {
        width: 50%;
        background-color: #0081ff;
        line-height: 51px;
        color: #fff;
        border-bottom-right-radius: 7px;
      }
    }
  }

  .img-grounp {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    .img-single {
      width: 31%;
      margin-top: 10px;
      padding: 4px;
      flex-direction: row;
      .img-style {
        width: 100%;
        height: 110px;
      }
      .badge-icon {
        display: block;
        font-size: 10px;
        line-height: 20px;
      }
      .text {
        margin-top: 5px;
        text-align: center;
        font-size: 14px;
        font-weight: bold;
        color: rgb(116, 115, 115);
      }
    }
  }

  .submit-container {
    padding: 15px 0;
  }

  .reamrk {
    margin-top: 20px;
    border: 1px solid #e4e4e4;
  }

  .phone-con {
    line-height: 30px;
    color: #333333;
  }

  .line-bottom {
    border-bottom: 1px solid #e4e4e4;
    padding-left: 0;
    background-color: rgba(255, 255, 255, 0);
  }

  .active {
    display: none;
  }
</style>