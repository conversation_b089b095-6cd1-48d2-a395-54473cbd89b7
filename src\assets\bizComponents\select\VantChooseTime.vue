<template>
  <tr>
    <td width="10%" class="label">是否预约：</td>
    <td >
      <el-select v-model="ifPreferTime" placeholder="请选择" width="10%" @change="getChoice($event)">
        <el-option
            v-for="item in options"
            :label="item.label"
            :key="item.value"
            :value="item.value"
            size="medium">
        </el-option>
      </el-select>
    </td>

  </tr>
  <tr  v-show="isShowTime">
    <td width="10%" class="label">预约日期：</td>
    <td width="20%">
      <el-date-picker
          style="width: 145px;"
          v-model="stdPreBookDay"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
          :picker-options="pickerOptions"
          @change="chooseBookDay($event)">
      </el-date-picker>
    </td>
    <td width="10%" class="label">预约时段：</td>
    <td width="60%" >
      <ul id="stdBookTime" class="clearfix bookTime" :data="timeList" >
        <div  style="font-size: 12px" v-if="timeList==null||timeList==[]">

        </div>
        <div style="font-size: 12px" v-else>
          <li  v-if="timeList[0].flag=='0'" style="margin-left: 0;" ><a href="javascript:void(0)" @click="changeStdBookTime('上午(9:00-12:00)',$event)" >{{timeList[0].flag=='0'?'上午9:00-12:00':'上午9:00-12:00 已约满'}}</a>
          </li>
          <li  v-else style="margin-left: 0;opacity: 0.8;cursor:no-drop" ><a href="javascript:void(0)" style="pointer-events:none;" @click="changeStdBookTime('上午(9:00-12:00)',$event)" >{{timeList[0].flag=='0'?'上午9:00-12:00':'上午9:00-12:00 已约满'}}</a>
          </li>

          <li  v-if="timeList[1].flag=='0'"  ><a href="javascript:void(0)"   @click="changeStdBookTime('下午(12:00-18:00)',$event)">{{timeList[1].flag=='0'?'下午12:00-18:00':'下午12:00-18:00 已约满'}}</a>
          </li>
          <li  v-else style="opacity: 0.8;cursor:no-drop" ><a href="javascript:void(0)" style="pointer-events:none;" @click="changeStdBookTime('下午(12:00-18:00)',$event)">{{timeList[1].flag=='0'?'下午12:00-18:00':'下午12:00-18:00 已约满'}}</a>
          </li>

          <li  v-if="timeList[2].flag=='0'" ><a href="javascript:void(0)" @click="changeStdBookTime('晚上(18:00-20:00)',$event)">{{timeList[2].flag=='0'?'晚上 18:00-20:00':'晚上 18:00-20:00 已约满'}}</a>
          </li>
          <li  v-else style="opacity: 0.8;cursor:no-drop" ><a href="javascript:void(0)" style="pointer-events:none;" @click="changeStdBookTime('晚上(18:00-20:00)',$event)">{{timeList[2].flag=='0'?'晚上 18:00-20:00':'晚上 18:00-20:00 已约满'}}</a>
          </li>
        </div>
      </ul>
    </td>
  </tr>

</template>

<script>
export default {
  name: 'VantChooseTime',
  data () {
    return {
      isShowBook:false,
      timeList: [{
        flag:'',
        key:'',

      },
        {
          flag:'',
          key:'' ,

        }
        ,{
          flag:'',
          key:'',

        }],
      cTime:'',
      isShowTime:'',
      choiceValue:'',
      ifPreferTime: '1',
      options:[
        {
          value: '1',
          label: '是'
        },
        {
          value: '0',
          label: '否'
        }],
      queryBookDayEntity:{
        siteCd:'',
        stdAddrId:'',
        bookTime:''
      },
      stdPreBookDay:"",
      stdPreBookTime:"",
      pickerOptions: {
        disabledDate(time) {

          //此条为设置禁止用户选择今天之前的日期，不包含今天。
          return time.getTime() < (Date.now()-(24 * 60 * 60 * 1000));
        },},
    }
  },
  methods: {
    changeStdBookTime(Time,  e) {
      const $this = $(e.target);
      $this.parent().siblings().removeClass("cur");
      $this.parent().addClass("cur")
      this.stdPreBookTime=Time
    },
    getChoice(val){
      if(val==1){
        this.isShowTime=true;
        this.stdPreBookDay=this.cTime;
        this.chooseBookDay(this.stdPreBookDay);
      }
      else{
        this.isShowTime=false
        this.stdPreBookDay=''
      }
      this.choiceValue=val
    },

    //选择日期，发送请求
    chooseBookDay(val){
      if(val==null||val==""){
        this.dialogMsg = "请先进行选择日期!";
        this.dialogVisible = true;
        return;
      }
      this.queryBookDayEntity.bookTime=val;
      let req = {
        queryBookDayEntity: JSON.stringify(this.queryBookDayEntity)
      }
      this.$http.post('/Single/queryBookCapacityInfo',
          {reqData: req }
      ).then(res=>{

        if(res.respCode=='0000'){
          this.timeList=res.respData.data
        }
        else {
          this.timeList= [{
            flag:'0',
            key:'',
          },
            {
              flag:'0',
              key:'' ,
            }
            ,{
              flag:'0',
              key:'',
            }]
        }
      })
    },
  }
  
}
</script>

<style lang="less" scoped>

</style>

