<template>
  <div class="card-verification" style="background-color: white">
    <div class="subpreview-box" v-for="(item,index1) in showList" :key="index1" @click="item.showAll=!item.showAll">
      <div :style="{background: item.showAll ? '#E5F0FF':'#EDF7FF'}" class="total-title">
        <span class="custom-label-name">{{ item.name }}</span>
        <van-icon :name="item.showAll ? 'arrow-up':'arrow'" />
      </div>
      <div v-for="(item1,index) in item.itemList" :key="index" style="padding: 0 10px;">
        <div v-show="item.showAll" style="display:flex">
          <span class="custom-title flex-shink">{{ item1.text }}</span>
          <span class="custom-title">{{item1.value}}</span>
        </div>
      </div>
    </div>
    <div class="submit-container">
      <van-button class="sub" @click="preSubmit()" block>提交</van-button>
    </div>
  </div>
</template>

<script>
import WadeMobile from "rk-native-plugin";
import errorTips from '@/assets/bizComponents/errorTips/errorTips.vue';
import {copyText} from "../../../assets/js/func";
import {mapActions, mapMutations, mapState} from "vuex";
export default {
  name: "IptvOrderInfo",
  data() {
    return {
      imageList: [
        require('../../../images/arrow.png'),
        require('../../../images/add.png')
      ],
      showList: [
      
      ],
   
    }
  },
  components: {
    errorTips
  },
  computed: {
    ...mapState([
      'chatList',
      'sessionId',
      'staffId',
      'flowStep',
      'num',
      'shbmMsgInfo',
      'jzfkOrderData',
      'activeModuleIndex',
      'loginPhoneNumber',
      'instanceId',
        'iptvOrderData'
    ])
  },
  mounted(){
    this.setRespTipArrQry([])
    this.setBlockShow(false);
    this.checkOrderDetail()
    console.log('tgybyhbhbh')
    console.log(this.iptvOrderData.iptvGoodData.commodityChooseYWUlList,"this.iptvOrderData.iptvGoodData.commodityChooseYWUlList")
    console.log(this.iptvOrderData.iptvGoodData.iptvList,"this.iptvOrderData.iptvGoodData.iptvList")
    let iptvString='';
    let ywfString='无';
    if(this.iptvOrderData.iptvGoodData.iptvList.length>0){
      for(let i=0;i<this.iptvOrderData.iptvGoodData.iptvList.length-1;i++){
        iptvString+=this.iptvOrderData.iptvGoodData.iptvList[i].iptvName+",";
      }
      iptvString+=this.iptvOrderData.iptvGoodData.iptvList[this.iptvOrderData.iptvGoodData.iptvList.length-1].iptvName;
    }
    if(this.iptvOrderData.iptvGoodData.commodityChooseYWUlList.length==0){
      this.iptvOrderData.iptvGoodData.commodityChooseYWUlList.push({
        ywGoodId:'0',
        ywGoodName:'不需要'
      })
      this.setIptvOrderData(this.iptvOrderData);
    }
    if(this.iptvOrderData.iptvGoodData.commodityChooseYWUlList.length>0){
      if(this.iptvOrderData.iptvGoodData.commodityChooseYWUlList[0].ywGoodId!='0'){
        ywfString='';
        for(let i=0;i<this.iptvOrderData.iptvGoodData.commodityChooseYWUlList.length-1;i++){
          ywfString+=this.iptvOrderData.iptvGoodData.commodityChooseYWUlList[i].ywGoodName+",";
        }
        ywfString+=this.iptvOrderData.iptvGoodData.commodityChooseYWUlList[this.iptvOrderData.iptvGoodData.commodityChooseYWUlList.length-1].ywGoodName;
      }
    }
    this.showList=[
      {
        name: '客户信息',
        showAll: true,
        itemList:[
          {text:'宽带号码：',value:this.iptvOrderData.checkNumberData.serviceNumber},
          {text:'客户名称：',value:this.iptvOrderData.checkNumberData.showCustName}
        ]
      },{
        name: '商品信息',
        showAll: true,
        itemList:[
          {text:'IPTV：',value:iptvString},
          {text:'移网附加优惠商品：',value:ywfString}
        ]
      },{
        name: '装机信息',
        showAll: true,
        itemList:[
          {text:'装机地址：',value:this.iptvOrderData.checkNumberData.showInstallAddr}
        ]
      },
    ]
  },

  methods: {
    ...mapMutations([
      'setFlowStep',
      'setRobotWorking',
      'setIptvOrderData',
        'setRespTipArrQry',
        'setBlockShow'
    ]),
    ...mapActions([
      'updateChatList'
    ]),

    isEmpty(value) {
      let flag = false
      if (value == '' || value == 'undefined' || value == undefined || value == null || value == 'null') {
        flag = true
      }
      return flag
    },

    uniqueByProperty(arr, prop) {
      const uniqueMap = new Map();
      return arr.reduce((acc, current) => {
        const key = current[prop];
        if (!uniqueMap.has(key)) {
          uniqueMap.set(key, true);
          acc.push(current);
        }
        return acc;
      }, []);
    },
    checkOrderDetail(){
      WadeMobile.getSysInfo("PLATFORM").then((info) => {
        this.iptvOrderData.platfForm=info;
        this.setIptvOrderData(this.iptvOrderData);
        console.log("PLATFORM：：" + info);
      }, (err) => {
        console.log("PLATFORM"+"失败：" + err);
      });
    },
    preSubmit(){
      this.$emit('startLoading', '')
      let iptvInfoList=[];
      if(this.iptvOrderData.iptvGoodData.iptvList.length>0){
        for(let i=0;i<this.iptvOrderData.iptvGoodData.iptvList.length;i++){
        let iptv={
          iptvFlg:this.iptvOrderData.iptvGoodData.iptvList[i].commodityCode,
          iptvName:this.iptvOrderData.iptvGoodData.iptvList[i].iptvName,
          goodsPrice:this.iptvOrderData.iptvGoodData.iptvList[i].goodsPrice,
         goodsJMPrice:this.iptvOrderData.iptvGoodData.iptvList[i].goodsJMPrice,
             commType:this.iptvOrderData.iptvGoodData.iptvList[i].commType
        }
        iptvInfoList.push(iptv)
        }
      }
      let ywfGoodInfoList=[];
      if(this.iptvOrderData.iptvGoodData.commodityChooseYWUlList.length==0){
        let ywf={
          ywfFlg:0,
          ywfName:'不需要',
          goodsPrice:0
        };
        ywfGoodInfoList.push(ywf);
      }
      if(this.iptvOrderData.iptvGoodData.commodityChooseYWUlList.length>0){
        if(this.iptvOrderData.iptvGoodData.commodityChooseYWUlList[0].ywGoodId!='0'){
          for(let i=0;i<this.iptvOrderData.iptvGoodData.commodityChooseYWUlList.length;i++){
          let ywf={
            ywfFlg:this.iptvOrderData.iptvGoodData.commodityChooseYWUlList[i].ywGoodId,
            ywfName:this.iptvOrderData.iptvGoodData.commodityChooseYWUlList[i].ywGoodName,
            goodsPrice:0
          }
            ywfGoodInfoList.push(ywf);
          }
          
        }
        else{
            let ywf={
              ywfFlg:0,
              ywfName:this.iptvOrderData.iptvGoodData.commodityChooseYWUlList[0].ywGoodName,
              goodsPrice:0
                };
                ywfGoodInfoList.push(ywf);
          
        }
      }
      let  stdContact=this.iptvOrderData.timeData.contactName;
      if(this.isEmpty(stdContact)){
        stdContact=this.iptvOrderData.checkNumberData.custName
      }
      if(stdContact==this.iptvOrderData.checkNumberData.showCustName){
        stdContact=this.iptvOrderData.checkNumberData.custName
      }
      let communityAddrInfo={
        exchCode:this.iptvOrderData.checkNumberData.innerExchCode,
        addressName:this.iptvOrderData.checkNumberData.innerAddr,
      addressCode:this.iptvOrderData.checkNumberData.innerAddrCode,
      stdContact:stdContact,
       stdContactPhone:this.iptvOrderData.timeData.contactPhone,
        stdBookDay:this.iptvOrderData.timeData.stdBookDay,
        
        stdBookTime:this.iptvOrderData.timeData.stdBookTime
      }
      let rhRelationMemberMsg=this.iptvOrderData.checkNumberData;
      if("0"==this.iptvOrderData.checkNumberData.userTypeFlag){
        rhRelationMemberMsg={
          userTypeFlag: this.iptvOrderData.checkNumberData.userTypeFlag
        }
      }
      let req={
        ywfGoodInfoList:ywfGoodInfoList,
        iptvInfoList:iptvInfoList,
        communityAddrInfo:communityAddrInfo,
        serviceNumber:this.iptvOrderData.checkNumberData.serviceNumber,
        choiceValue:this.iptvOrderData.choiceValue,
        rhRelationMemberMsg:rhRelationMemberMsg,
        orderPrice:this.iptvOrderData.orderPrice
      }
      this.$http.post('/iptvReceive/newPreSubmit',req).then((res)=>{
        this.$emit('endLoading', '')
        if (res.respCode === '0000') {
          this.iptvOrderData.orderId=res.respData.orderId;
          this.updateChatList({
            sender: '1',
            type: 'module',
            moduleName: 'TextResponse',
            moduleLevel: 1,
            params: {
              text: '订单提交成功!订单号'+res.respData.orderId
            },
            show: true
          })
          this.setIptvOrderData(this.iptvOrderData);
          let data = {
            inputType: "1",
            type: '1',
            textInput: "iptvOrderfoSubmit",
            notifyFlag: '',
            taskName:'智能超清甩单'
          }
          this.$emit('newChatApi', data);
        }
        else{
          this.updateChatList({
            sender: '1',
            type: 'module',
            moduleName: 'TextResponse',
            moduleLevel: 1,
            params: {
              text: res.respMsg
            },
            show: true
          })
        }

      })
    }
  }
}
</script>
<style lang="scss">
.card-verification {
  width:100%;
  margin: auto;
  .card-group {
    background-color: white;
    margin: 0px 10px;
    padding:0px 5px;
    .tip {
      .van-cell__value {
        color: black;
        text-align: right;
      }}

    .card-list {
      .van-cell__title {
        font-size: 13px;
        color: #666666;
      }
      .van-cell__value {
        color: black;
        text-align: right;
        font-size: 13px;
      }
      .van-cell {
        color: black !important;
        padding: 0px 16px;
      }
      .van-cell::after {
        color: black !important;
        border-bottom: 0;

      }
    }
    .van-hairline--top-bottom::after, .van-hairline-unset--top-bottom::after {
      border-width: 0 0;
    }
  }
  .card {
    .van-cell__title {
      font-size: 13px;
      color: #000000;
    }
    .checkButton {
      width: 70px;
      height: 30px;
      background: #0081FF;
      border-radius: 4px;
    }
    .checkFont {
      font-size: 13px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 14px;
    }
  }
  .check{
    .van-cell__title {
      font-size: 13px;
      color: #333333;
    }
  }
}

</style>

<style lang="scss" scoped>
.card-verification {
  background-color: rgb(57,159,254);
  .subpreview-box{
    border-radius: 10px;
    margin-bottom: 10px;
    box-shadow: 0 2px 1px 0 #eee;
    .total-title{
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 5px 10px;
      width: 93%;
      border-radius: 10px 10px 0 0;
      margin-bottom: 10px;
      .custom-label-name{
        font-size: 15px;
        line-height: 30px;
        color: #263A5F;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
      }
    }
    .custom-title{
      font-family: PingFangSC-Regular;
      font-size: 14px;
      line-height: 26px;
      color: #263A5F;
    }
    .flex-shink{
      flex-shrink: 0;
    }
  }
  .desc {
    color: black;
    font-size: 13px;
    line-height: 20px;
  }
  .margin-two {
    bottom: 10px;
    left: 15px;
    right: 15px;
    z-index: 2;
    position: absolute;
  }
  .van-buttons {
    border-radius: 4px;
    height: 44px;
    font-size: 13px;
    color: #FFFFFF;
    background-color: #0081FF;
  }
  .font {
    color:#333333;
  }
  .submit-container{
    .sub{
      border: 2px solid rgba(73,124,246,1);
      border-radius: 5px;
      color: rgba(73,124,246,1);
      margin-top: 20px;
    }
  }
}

</style>