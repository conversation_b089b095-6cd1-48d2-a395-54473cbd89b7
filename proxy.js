/* eslint-disable no-console */
const express = require('express');
const os = require('os');
const { createProxyMiddleware } = require('http-proxy-middleware');

//需要转发到的后端服务器地址
const target = process.argv[3] || 'http://localhost:3000';
//本地静态资源服务器端口号
const serverPort = process.argv[2] || 8081;


const app = express();

app.use('/api', createProxyMiddleware({
    target: target,
    pathRewrite: {
        '^/api/': '/'
    },
    changeOrigin: true,
    logLevel: 'warn'
}));


app.use(express.static('dist', {
    maxAge: '1y'
}));

app.listen(serverPort);


function getIpAddress() {
    let result = [];
    const interfaces = os.networkInterfaces();
    for (let devName in interfaces) {
        interfaces[devName].forEach(item => {
            if (item.family === 'IPv4' && item.address !== '127.0.0.1' && !item.internal) {
                result.push(item.address);
            }
        });
    }
    return result;
}
getIpAddress().forEach(ipAddress => {
    console.log(`本地静态资源部署在http://${ipAddress}:${serverPort}`);
});
console.log(`ajax请求已转发至${target}`);
