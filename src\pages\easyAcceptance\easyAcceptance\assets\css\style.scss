.container {
    width: 100%;
    box-sizing: border-box;
    overflow-y: hidden;
    height: 100%;
}

.main-content {
    position: absolute;
    padding: 10px 12px;
    width: 100%;
    box-sizing: border-box;
    margin-top: -143px;
    height: calc(100% - 104px);
    overflow-y: auto;
    font-size: 13px;
}

.dialog-box {
    position: relative;
    margin-top: 10px;

    .client-dialog {
        display: flex;
        justify-content: flex-end;

        .dialog-bubble {
            background-image: linear-gradient(to right, #4F54F4, #5097F5);
            margin-right: 8px;
            word-break: break-all;
            color: #fff;
        }

        &.audio-dialog {
            position: relative;
            padding-bottom: 35px;

            .module-bubble {
                // background-image: none;
                width: unset;
                // margin-right: 2px;
                // padding: 0;
            }
        }
    }

    .robot-dialog {
        display: flex;
        color: #263A5F;

        .dialog-bubble {
            background: #FFFFFF;
            margin-left: 8px;
        }
    }

    .dialog-bubble {
        padding: 10px 9px;
        border-radius: 7px;
        box-sizing: border-box;
        min-width: 30px;
        max-width: 310px;
        font-size: 15px;
        line-height: 22px;

        &.module-bubble {
            width: 100%;

            &.text-module-bubble {
                width: auto;
                max-width: 100%;
            }
        }

        .dialog-text {
            line-height: 22px;
            font-size: 15px;
        }
    }

    .startchat-module-bubble {
        margin: 0 auto;
        margin-left: 0 !important;
        // background: linear-gradient(to bottom right, #D3E8FF, #D7E1FF) !important;
        width: 100% !important;
        min-width: 96% !important;
        max-width: 100% !important;
        background: url("@/assets/images/home/<USER>") !important;
        background-repeat: no-repeat !important;
        background-size: 100% 100% !important;
    }

    .selectproduct-module-bubble {
        background: none !important;
    }

    .orderConfirmBox-module-bubble {
        background-image: linear-gradient(180deg, #BBDBFF 0%, #D9EAFF 100%) !important;
        max-width: 345px;
    }

    .no-padding {
        padding: 0;
    }

    .tip-bubble {
        border: 1px solid rgba(182, 191, 222, 1);
        background-color: rgba(0, 0, 0, 0) !important;
    }

    .client-image {
        background: url(@/assets/images/client.png) no-repeat;
        background-size: contain;
        width: 26px;
        height: 26px;
        display: inline-block;
        flex-shrink: 0;
        flex-grow: 0;
    }

    .robot-image {
        background: url(@/assets/images/robot.png) no-repeat;
        background-size: contain;
        width: 26px;
        height: 26px;
        display: inline-block;
        flex-shrink: 0;
        flex-grow: 0;
    }

    .w-mask {
        width: 100%;
        height: 100%;
        background: #eeeeee;
        position: absolute;
        top: 0;
        left: 0;
        border-radius: 7px;
        opacity: .4;
        z-index: 1111;
    }
}

.part {
    margin-bottom: 10px;

    &:last-child {
        margin-bottom: 5px;
    }

    .part-title {
        display: flex;
        justify-content: space-between;

        .title-left {
            color: #141C23;
            font-weight: bold;
            font-size: 14px;
        }
    }

    .title-more {
        color: #B5B5B5;
        font-size: 12px;
        flex-grow: 0;
        flex-shrink: 0;
    }

    .title-right {
        color: #B5B5B5;
        font-size: 12px;
        padding-right: 16px;
        height: 14px;
        line-height: 14px;
        margin-top: 4px;

        &.choose-another {
            display: inline-block;
            float: right;
            width: 36px;
            padding-left: 15px;
            flex-grow: 0;
            flex-shrink: 0;
            margin-bottom: 4px;
            background: url(@/assets/images/change.png) no-repeat right center;
            background-size: 12px;
        }
    }

    &.blue-part {
        background: #E6F4FF;
        border-radius: 6px;
        font-size: 14px;

        &.flex {
            display: flex;
        }

        &.blue-part-padding {
            padding: 10px 12px;
        }

        .part-title {
            width: 64px;
            background-image: linear-gradient(to bottom, #3775EB, #A0B9FE);
            padding: 8px 4px;
            box-sizing: border-box;
            border-radius: 6px 0 0 6px;
            position: relative;

            .title-top {
                font-size: 16px;
                color: #ffffff;
                padding: 0 10px;
                line-height: 24px;
            }

            .title-bottom {
                background: #ffffff;
                border-radius: 10px;
                opacity: 0.58;
                position: absolute;
                bottom: 8px;
                width: 56px;
                padding: 2px 2px 2px 4px;
                box-sizing: border-box;

                div {
                    width: 100%;
                    background: url(@/assets/images/change_blue.png) no-repeat right center;
                    background-size: 12px;
                    color: #3489FC;
                    font-size: 12px;
                    height: 14px;
                    line-height: 14px;
                }
            }
        }

        .part-content {
            flex-grow: 1;
            padding: 8px 10px;

            .ques-item {
                background: #E6F4FF;
                padding: 5px 0;

                .van-icon {
                    line-height: 32px;
                    font-size: 18px;
                }

                .ques-number {
                    padding-left: 20px;

                    &.one {
                        background: url(@/assets/images/one.png) no-repeat left center;
                        background-size: 14px 14px;
                    }

                    &.two {
                        background: url(@/assets/images/two.png) no-repeat left center;
                        background-size: 14px 14px;
                    }

                    &.three {
                        background: url(@/assets/images/three.png) no-repeat left center;
                        background-size: 14px 14px;
                    }

                    &.four {
                        background: url(@/assets/images/four.png) no-repeat left center;
                        background-size: 14px 14px;
                    }
                }
            }

            .van-cell:after {
                border-bottom: none;
            }
        }
    }
}

.van-radio__label {
    margin-left: 4px;
}

.white-block {
    background-color: #ffffff;
    padding: 20px 15px 0 15px;
}

.w-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;

    .title-left {
        .card-index {
            font-size: 16px;
            font-weight: bold;
            display: inline-block;
        }

        .new-icon {
            display: inline-block;
            padding: 0px 5px 1px 4px;
            border-radius: 7px 0;
            background-image: linear-gradient(to right, #FF7F1A, #FF4750);
            color: #ffffff;
            font-size: 14px;
            margin-left: 3px;
            position: relative;
            top: -1px;
        }
    }

    .title-right {
        .title-right-text {
            background: url(@/assets/images/sim_card.png) no-repeat left center;
            background-size: 17px 17px;
            color: #cccccc;
            padding-left: 24px;
            display: inline-block;
            line-height: 22px;
        }

        .van-icon {
            display: inline-block;
            position: relative;
            top: 4px;
            margin-left: 6px;
        }
    }

    .order-detail-btn {
        position: absolute;
        top: -30px;
        right: -15px;
        background-image: linear-gradient(to bottom, #FF7D08, #FFD48D);
        border-radius: 5px 0 0 5px;
        padding: 0 10px 0 5px;
        height: 24px;
        line-height: 24px;

        div {
            color: #ffffff;
            background: url(@/assets/images/order_detail.png) no-repeat left 3.5px;
            background-size: 16px 16px;
            padding-left: 19px;
        }
    }
}

.complete-percent {
    display: inline-block;
    color: var(--blue);
    border: 1px solid var(--blue);
    border-radius: 14px;
    padding: 0 8px;
    height: 22px;
    line-height: 22px;
}

.w-check-area {
    font-size: 14px;

    .label {
        display: inline-block;
    }

    .check-list {
        display: inline-block;

        .w-check {
            display: inline-block;
            height: 32px;
            line-height: 32px;
            border: 1px solid #C5CAD5;
            border-radius: 2px;
            padding: 0 15px;
            margin-right: 10px;

            &.checked {
                border-color: #25AFFF;
                color: #25AFFF;
                background: #F5FAFD url(@/assets/images/checked_top.png) no-repeat right top;
                background-size: 12px 12px;
            }
        }
    }
}

.blue-block {
    background-color: #F2F9FF;
    border: 1px solid #86C3FF;
    border-radius: 6px;
    padding: 12px;

    .blue-block-title {
        font-size: 14px;
    }

    .blue-block-content {
        margin-top: 6px;

        .blue-block-item {
            color: #999999;
            display: inline-block;
            margin-right: 30px;
            line-height: 24px;
        }
    }
}

.main-card {
    .van-cell {
        padding-left: 10px;
        padding-right: 10px;
    }
}

.w-options {
    .w-option {
        display: inline-block;
        height: 28px;
        line-height: 28px;
        padding: 0 12px;
        background: #F4F5F7;
        border-radius: 4px;
        margin: 10px 8px 0 0;
        align-items: center;
        border: 1px solid #F4F5F7;
        min-width: 60px;
        text-align: center;

        img {
            width: 18px;
            margin-right: 5px;
        }

        &.checked {
            border: 1px solid #0088FF;
            color: #0088FF;
            background: #E6F4FF url(@/assets/images/checked.png) no-repeat right bottom;
            background-size: 18px 18px;
        }

        &.has-image {
            display: inline-flex;
            padding-left: 8px;
        }
    }
}

.common-content {
    padding: 0;
}

.van-submit-bar {
    &.price-footer {
        .van-button {
            display: none;
        }

        .price-info {
            width: 100%;
            font-size: 14px;
        }
    }
}

.w-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;

    .title-right {
        width: 115px;
        text-align: right;

        .title-right-text {
            background: url(@/assets/images/qr_code.png) no-repeat left center;
            background-size: 17px 17px;
            color: #cccccc;
            padding-left: 20px;
            display: inline-block;
            line-height: 22px;
        }

        .van-icon {
            display: inline-block;
            position: relative;
            top: 4px;
            margin-left: 6px;
        }
    }
}

.w-tabs {
    display: flex;
    flex-grow: 1;
    margin-left: 5px;

    .w-tab {
        font-size: 15px;
        padding: 5px 10px;
        position: relative;

        &.active {
            color: var(--blue);
            font-weight: bold;

            &:after {
                content: " ";
                position: absolute;
                width: 20px;
                border-bottom: 3px solid var(--blue);
                left: 20px;
                bottom: -3px;
            }
        }
    }
}

.card-detail {
    .card-name {
        font-weight: bold;
        padding: 12px 15px;
    }

    .card-block {
        background-color: #ffffff;
        padding: 12px 15px;
        border-radius: 8px;
        box-shadow: 0px 5px 7px #eaeaea;

        .card-title {
            font-weight: bold;
        }

        .w-tags {
            margin: 7px 0 10px 0;

            .w-tag {
                font-size: 12px;
                padding: 0 3px;
                display: inline-block;
                margin-right: 8px;
                border-radius: 3px;
                border-width: 1px;
                border-style: solid;

                &.tag-0 {
                    color: var(--blue);
                    border-color: var(--blue);
                    background-color: #EDF5FF;
                }

                &.tag-1 {
                    color: var(--green);
                    border-color: var(--green);
                    background-color: #F6FFF6;
                }

                &.tag-2 {
                    color: var(--orange);
                    border-color: var(--orange);
                    background-color: #FFEEE8;
                }

                &.tag-3 {
                    color: var(--yellow);
                    border-color: var(--yellow);
                    background-color: #FFF8E8;
                }
            }
        }

        .info-item {
            margin-bottom: 5px;

            &:last-child {
                margin-bottom: 0;
            }
        }

        .my-swipe {
            margin-top: 10px;

            .van-swipe-item {
                text-align: center;

                img {
                    width: 90%;
                }

                div {
                    margin: 5px 0 30px 0;
                }
            }

            /deep/.van-swipe__indicators {
                bottom: 0;
            }
        }
    }
}

.van-divider {
    margin: 0;
    padding-top: 15px;
    background-color: #ffffff;
    border-color: #999999;
}

.id-card-area {
    background: url('@/assets/images/id_border.png') no-repeat;
    background-size: contain;
    padding: 10px 25px;
    width: calc(100% - 50px);
    height: 163px;

    img {
        width: 100%;
    }
}

.image-upload {
    .image-block {
        width: 100%;
        border: 1px dashed #cccccc;
        border-radius: 2px;
        height: 0;
        padding-top: 100%;
        position: relative;

        img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
        }

        .add-icon {
            width: 30px;
            left: calc(50% - 15px);
            top: calc(50% - 15px);
        }
    }

    .image-name {
        line-height: 36px;
        text-align: center;
    }
}

.blue-card {
    background: #E6F4FF;
    box-shadow: 1px 1px 6px #E6F4FF;
    border-radius: 6px;
    padding: 10px;
}

.pro-card {
    background-image: linear-gradient(to bottom, #D2E2FF, #FFFFFF);
    border-radius: 6px;
    margin-bottom: 10px;
    box-shadow: 3px 3px 6px #eeeeee;
    padding: 10px;
    display: flex;
    border: 1px solid transparent;

    &.blue-bg {
        background: #E6F4FF;
        box-shadow: 1px 1px 6px #E6F4FF;
    }

    &.checked {
        border: 1px solid var(--blue);
    }

    img {
        width: 50px;
        height: 50px;
    }

    .pro-info {
        margin-left: 10px;

        .pro-name {
            font-size: 14px;
            font-weight: 550;
        }

        .pro-labels {
            font-size: 12px;

            .pro-label {
                display: inline-block;
                border-radius: 3px;
                padding: 0 2px;
                height: 16px;
                line-height: 16px;
                border-width: 1px;
                border-style: solid;
                margin-bottom: 5px;

                &.label-blue {
                    border-color: var(--blue);
                    background-color: #EDF5FF;
                    color: var(--blue);
                }

                &.label-green {
                    border-color: var(--green);
                    background-color: #F6FFF6;
                    color: var(--green);
                }

                &.label-orange {
                    border-color: var(--orange);
                    background-color: #FFEEE8;
                    color: var(--orange);
                }

                &.label-yellow {
                    border-color: var(--yellow);
                    background-color: #fff4de;
                    color: var(--yellow);
                }
            }
        }

        .pro-detail-title {
            display: flex;
            align-items: baseline;
            justify-content: space-between;

            .pro-detail-btn {
                color: var(--blue);
                font-size: 13px;
                // padding-left: 18px;

                &.open {
                    background: url('@/assets/images/open.png') no-repeat left center;
                    background-size: 15px 15px;
                }

                &.close {
                    background: url('@/assets/images/close.png') no-repeat left center;
                    background-size: 15px 15px;
                }
            }

            .pro-sale {
                color: #B5B5B5;
            }
        }

        .pro-detail {
            color: #666666;
        }
    }
}

.num-card {
    font-size: 15px;
    border: 1px solid #cecece;
    border-radius: 4px;
    height: 40px;
    line-height: 40px;
    margin: 4px 0;
    text-align: center;

    &.checked {
        border: 1px solid var(--blue);
        background: url(@/assets/images/checked.png) no-repeat right bottom;
        background-size: 18px 18px;
    }

    .nice-num {
        font-size: 13px;
        border-radius: 1px;
        padding: 0 3px;
        text-align: center;
        background-color: var(--blue);

        &:after {
            content: '靓';
            color: #ffffff;
        }
    }
}

.face-recognition {
    width: 100%;
    text-align: center;

    img {
        width: 70%;
    }
}

.info-list {
    width: 100%;

    .info-item {
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        line-height: 28px;

        .info-label {
            color: #999999;
        }
    }
}

.van-nav-bar {
    z-index: 0;
}

.van-dialog {
    .van-dialog__confirm {
        background: var(--blue);
    }

    .van-dialog__header {
        padding-top: 15px;
        padding-bottom: 12px;
        border-bottom: 1px solid #eeeeee;
    }

    .van-dialog__content {
        padding: 12px 15px;
    }
}

.cursor-pointer {
    cursor: pointer;
}


// .chat-loading {
//     width: 100%;
//     text-align: center;

//     img {
//         width: 80px;
//     }
// }
.van-popup__close-icon {
    top: 20px;
    right: 15px;
    color: #000000;
    font-size: 22px;
}

.icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}