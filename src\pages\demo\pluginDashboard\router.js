import Vue from 'vue';
import Router from 'vue-router';
import { fixRouter } from '@/assets/js/utils';
import Main from './routerPages/Main';
import Welcome from './routerPages/Welcome';

Vue.use(Router);

let router = new Router({
    routes: [
        {
            path: '/',
            name: 'Main',
            component: Main,
            meta: {
                topBar: {
                    title: '首页',
                    share: false,
                    copy: false,
                    topBarBg: '#dcfffc',
                    textColor: '#080c34',
                    titleAlign: 'center',
                    back: false,
                    exit: true,
                    refresh: true
                }
            }
        },
        {
            path: '/Welcome',
            name: 'Welcome',
            component: Welcome,
            meta: {
                topBar: {
                    title: '欢迎页',
                    share: true,
                    copy: true,
                    topBarBg: '#ffec38',
                    textColor: '#ff2328',
                    titleAlign: 'left',
                    back: true,
                    exit: false,
                    refresh: false
                }
            }
        }
    ]
});
fixRouter(router);

export default router;
