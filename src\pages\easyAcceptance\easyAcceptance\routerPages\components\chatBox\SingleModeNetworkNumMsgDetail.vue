<!-- 单移网信息 -->
<template>
  <div class="card-verification" style="background-color: white">
    <div class="card-group">
      <div style="padding-left: 5px">您的信息如下</div>
      <div>
        <div style="margin-top: 2px" class="msg-blue-card">
          <div class="msg-blue-main-cell">
            <span style="font-weight: bold">移网信息:</span>
          </div>
          <div class="msg-blue-cell">
            <span>姓名:</span
            ><span class="val-blue-cell">{{ resp.showCustName }}</span>
          </div>
          <div class="msg-blue-cell">
            <span>证件号码:</span
            ><span class="val-blue-cell-new">{{ resp.showCertCode }}</span>
          </div>
          <div class="msg-blue-cell">
            <span>套餐名称:</span
            ><span class="val-blue-cell">{{ resp.showMainProduct }}</span>
          </div>
          <div class="msg-blue-cell">
            <span>档位:</span
            ><span class="val-blue-cell">{{ resp.priceLevel }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import errorTips from "@/assets/bizComponents/errorTips/errorTips.vue";
import { copyText } from "../../../assets/js/func";
import { mapState, mapGetters } from "vuex";
export default {
  name: "SingleModeNetworkNumMsgDetail",
  data() {
    return {
      productName: "",
      imageList: [
        require("../../../images/arrow.png"),
        require("../../../images/add.png"),
      ],
      showUserType: "",
      zwInvalid: [],
      choosedZW: "",
      resp: {
        zwName: "ZW1",
        productName: "上海宽带1000M基本套餐(专属)",
        custName_tm: "徐**",
      },
    };
  },
  computed: {
    ...mapState(["singleModeNetworkOrderData"]),
    ...mapGetters(["getSingleModeNetworkOrderData"]),
  },
  components: {
    errorTips,
  },
  mounted() {
    this.resp = this.getSingleModeNetworkOrderData.checkNumberData;
    this.showUserType = this.resp.isRHUserTag === "1" ? "是" : "否";
    if ("1" == this.resp.isRHUserTag) {
      this.productName = this.resp.kdProductName;
    } else {
      this.productName = this.resp.showProductName;
    }
  },

  methods: {
    maskPhoneNumber(phoneNumber) {
      return phoneNumber.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
    },

    maskPspt(pspt) {
      return pspt.replace(/^(.{4})(.*)(.{4})$/, "$1*********$3");
    },
    maskName(name) {
      return name.substring(0, 1) + "*".repeat(name.length - 1);
    },
  },
};
</script>
<style lang="scss">
.card-verification {
  width: 100%;
  margin: auto;
  .card-group {
    background-color: white;
    margin: 0;
    padding: 0;
    .msg-blue-card {
      background: #e9f4fe;
      border-radius: 10px;
      padding: 10px;
      margin-bottom: 10px;
      .msg-blue-main-cell {
        display: flex;
        justify-content: space-between;
        margin: 5px 0;
      }
      .msg-blue-cell {
        display: flex;
        justify-content: space-between;
        padding-left: 10px;
        margin: 8px 0;
      }
      .val-blue-cell-new {
        width: 60%;
        text-align: right;
        word-wrap: break-word;
      }
      .val-blue-cell {
        width: 54%;
        text-align: right;
        word-wrap: break-word;
      }
    }
    .tip {
      .van-cell__value {
        color: black;
        text-align: right;
      }
    }
    .van-hairline--top-bottom::after,
    .van-hairline-unset--top-bottom::after {
      border-width: 0 0;
    }
  }
}
</style>

<style lang="scss" scoped>
.card-verification {
  background-color: rgb(57, 159, 254);
  .desc {
    color: black;
    font-size: 13px;
    line-height: 20px;
  }
  .margin-two {
    bottom: 10px;
    left: 15px;
    right: 15px;
    z-index: 2;
    position: absolute;
  }
  .van-buttons {
    border-radius: 4px;
    height: 44px;
    font-size: 13px;
    color: #ffffff;
    background-color: #0081ff;
  }
  .font {
    color: #333333;
  }
}
</style>
