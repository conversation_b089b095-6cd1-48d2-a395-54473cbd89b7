<template>
    <div>
        <van-button
            @click="share"
            type="primary"
        >
            分享
        </van-button>
    </div>
</template>

<script>
import WadeMobile from 'rk-native-plugin';
const imgUrl = 'https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1598438824414&di=7516cae37bc7ba7a4485d70e090c5005&imgtype=0&src=http%3A%2F%2Fp1.itc.cn%2Fq_70%2Cc_lfill%2Cw_300%2Ch_200%2Cg_faces%2Fimages01%2F20200721%2F5066c67936e54e15a06c96fa06c13fdb.jpeg';

export default {
    name: 'Share',
    methods: {
        share() {
            WadeMobile.jShare([imgUrl, 1, location.href, document.title, '中国联通软件研究院统一APP框架子应用模板工程']);
        }
    }
};
</script>

<style scoped>

</style>
