<template>
    <div class="d_newCustData">
        <p class="label-title">
            创建客户资料
        </p>
        <div class="flexRadio" style="align-items: center;">
            <span class="font-size">客户名称</span>
            <div class="rightRadio">
                <van-field
                    class="van-field"
                    :clearable="true"
                    maxlength="50"
                    v-model="psptName"
                    placeholder="请输入客户名称"
                    input-align="right"
                />
            </div>
        </div>
        <div class="flexRadio" style="align-items: center;">
            <span class="font-size">证件号码</span>
            <div class="rightRadio">
                <van-field
                    class="van-field"
                    :clearable="true"
                    v-model="psptId"
                    placeholder="请输入证件号码"
                    input-align="right"
                />
            </div>
        </div>
        <div class="flexRadio" style="align-items: center;">
            <span class="font-size">证件有效期</span>
            <div class="rightRadio" @click="show = true">
                {{ period }}
            </div>
        </div>
        <van-popup v-model="show" position="bottom">
            <van-datetime-picker
                type="date"
                title="选择年月日"
                :min-date="minDate"
                :max-date="maxDate"
                @confirm="selectOrderTime"
                @cancel="show=false"
            />
        </van-popup>
        <div class="flexRadio" style="align-items: center;">
            <span class="font-size">性别</span>
            <div class="rightRadio">
                <van-radio-group v-model="sex" direction="horizontal" style="width: 3rem; margin: 0.5rem auto;">
                    <van-radio name="1">
                        男
                    </van-radio>
                    <van-radio name="0">
                        女
                    </van-radio>
                </van-radio-group>
            </div>
        </div>
        <div class="flexRadio" style="align-items: center;">
            <span class="font-size">证件地址</span>
            <div class="rightRadio">
                <van-field
                    class="van-field"
                    :clearable="true"
                    v-model="custCardAddr"
                    placeholder="请输入证件地址"
                    input-align="right"
                />
            </div>
        </div>
        <div class="flexRadio" style="align-items: center;">
            <span class="font-size">联系人姓名</span>
            <div class="rightRadio">
                <van-field
                    class="van-field"
                    :clearable="true"
                    v-model="custContactName"
                    placeholder="请输入联系人姓名"
                    input-align="right"
                />
            </div>
        </div>
        <div class="flexRadio" style="align-items: center;">
            <span class="font-size">联系人电话</span>
            <div class="rightRadio">
                <van-field
                    class="van-field"
                    :clearable="true"
                    maxlength="11"
                    v-model="custContactPhoneNumber"
                    placeholder="请输入联系人电话"
                    input-align="right"
                />
            </div>
        </div>

        <div class="submit-container">
            <van-button @click="okBtn1" block>
                确定
            </van-button>
        </div>
    </div>
</template>
<script>
import { alertError } from '../funcComponent';
import util from '@/assets/bizComponents/customerAuthentication/CustomerComponents/customerUtil.js';
import Bus from '@/assets/bizComponents/PaperlessSign/bus';
export default {
    name: 'NewCustData',
    data() {
        return {
            psptName: '',
            psptId: '',
            period: '请选择失效时间',
            sex: '',
            currentDate: '',
            show: false,
            minDate: new Date(2021, 12, 31), //初始化时间
            maxDate: new Date(2050, 12, 31),
            custCardAddr: '',
            custContactName: '',
            custContactPhoneNumber: ''
        };
    },
    methods: {
        selectOrderTime(date) {

            var tempDate = {
                year: date.getFullYear(),
                month: date.getMonth() + 1,
                day: date.getDate()
            };
            this.period = tempDate.year +  (tempDate.month >= 10 ? '' + tempDate.month : '0' + tempDate.month) +  (tempDate.day >= 10 ? '' + tempDate.day : '0' + tempDate.day);
            this.show = false;
        },
        okBtn1() {
            if (util.isEmpty(this.psptName)) {
                alertError({
                    message: '客户姓名不能为空'
                });
                return;
            }
            if (util.isEmpty(this.psptId)) {
                alertError({
                    message: '证件号码不能为空'
                });
                return;
            }
            if (util.isEmpty(this.period)) {
                alertError({
                    message: '证件有效期不能为空'
                });
                return;
            }
            if (util.isEmpty(this.custCardAddr)) {
                alertError({
                    message: '证件地址不能为空'
                });
                return;
            }
            if (util.isEmpty(this.custContactName)) {
                alertError({
                    message: '联系人不能为空'
                });
                return;
            }
            if (util.isEmpty(this.custContactPhoneNumber)) {
                alertError({
                    message: '联系人电话不能为空'
                });
                return;
            }
            if (!(/^1[3456789]\d{9}$/.test(this.custContactPhoneNumber))) {
                alertError({
                    message: '请填写正确的联系人手机号码'
                });
                return;
            }
            let passPortNewCustomer = {
                psptName: this.psptName,
                psptId: this.psptId,
                sex: this.sex,
                period: this.period,
                custCardAddr: this.custCardAddr,
                custContactName: this.custContactName,
                custContactPhoneNumber: this.custContactPhoneNumber
            };
            Bus.$emit('passPortNewCustomer', passPortNewCustomer);
            this.$router.back();
        }
    }
};
</script>
<style lang="scss" scoped>
.d_newCustData {
    margin: 0.26667rem;

    .textbutton {
        width: 15%;
        padding: 5px 0;
        position: absolute;
        right: 5%;
        color: #ebebeb;
        font-size: 12px;
        border-radius: 0.1rem;
        border: none;
        background-color: #0081ff;
        margin-top: 10px;
    }

    .submitbutton {
        position: fixed;
        bottom: 0;
        text-align: center;
        width: 95%;
    }

    .inputtext {
        height: 50px;
        width: 100%;
        border: none;
        font-size: 14px;
        border-radius: 7px;
        border-bottom: 1px solid #e4e4e4;
    }

    .inputtext::-webkit-input-placeholder {
        color: #f6f6f6;
    }

    .label-title {
        margin-top: 25px;
        font-size: 18px;
        font-weight: bold;
        color: #333333;
    }

    .margin-ht {
        margin-top: 10px;
        align-items: center;
    }

    .flexRadio {
        display: flex;
        flex-direction: row;
        position: relative;
        line-height: 50px;
        border-bottom: 1px solid #e4e4e4;
        color: #333333;

        .rightRadio {
            position: absolute;
            right: 0;
        }

        .size-radio {
            top: 5px;
            font-size: 20px;
        }

        .other-group {
            color: #aaaaaa;
            display: flex;
            align-items: center;
        }

        .font-size {
            font-size: 14px;
        }

        .font-color {
            color: #cbcbce;
            right: 17px;
        }

        .icon-top {
            padding-top: 5px;
        }

        .van-field {
            background-color: rgba(255, 255, 255, 0);
            padding-right: 0;
        }

        .warn-tip {
            position: absolute;
            right: 0;
            top: 40px;
            font-size: 12px;
            color: red;
        }
    }

    .tip-height {
        height: 20px;
        width: 100%;
    }

    .warn-show {
        margin: 15px 0 7px 0;
        padding: 7px;
        color: #aaaaaa;
        display: flex;
        flex-direction: row;
        align-items: center;
        background-color: rgb(226, 244, 255);
        border: 1px solid rgb(132, 210, 255);

        submit-container .warning-o {
            width: 15px;
            height: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #84d2ff;
            border-radius: 50%;
            text-align: center;

            .warning-i {
                color: #84d2ff;
                font-size: 12px;
                font-weight: bold;
            }
        }

        .p-text {
            font-size: 12px;
            color: #666666;
            margin-left: 5px;
        }
    }

    .ID-container {
        margin-top: 20px;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;

        .row-line {
            position: absolute;
            width: 15px;
            height: 2px;
            background-color: #aaaaaa;
        }

        .col-line {
            position: absolute;
            width: 2px;
            height: 15px;
            background-color: #aaaaaa;
        }

        .my-swipe {
            .van-swipe-item {
                color: #ffffff;
                width: 300px;
                height: 191px;
                text-align: center;
                border-radius: 10px;
            }
        }
    }

    .margin-two {
        margin: 20px 0;

        .p-text {
            font-size: 12px;
            text-align: center;
            color: #666666;
            line-height: 20px;
        }

        .p-red-text {
            font-size: 12px;
            text-align: center;
            color: #fb4940;
            line-height: 20px;
        }
    }

    .van-button {
        border-radius: 7px;
        width: 100%;
        font-size: 18px;
        color: #ffffff;
        background-color: #0081ff;
    }

    van-button-b {
        margin-top: 7px;
    }

    .active {
        display: none;
    }

    .wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
    }

    .block-container {
        width: 300px;
        height: 150px;
        text-align: center;
        border-radius: 7px;
        position: relative;
        background-color: #ffffff;

        .context {
            color: #333333;
            line-height: 100px;
            font-size: 15px;
            font-weight: 600;
        }

        .bottom-button {
            width: 100%;
            height: 50px;
            background-color: #ffffff;
            color: #333333;
            font-size: 14px;
            font-weight: 600;
            display: flex;
            flex-direction: row;
            position: absolute;
            bottom: 0;
            border-bottom-left-radius: 7px;
            border-bottom-right-radius: 7px;
            align-items: center;
            justify-content: center;
            border-top: 1px solid #cccccc;

            .right-button {
                width: 50%;
                background-color: #0081ff;
                line-height: 51px;
                color: #ffffff;
                border-bottom-right-radius: 7px;
            }
        }
    }

    .submit-container {
        padding: 30px 0;
    }

    .reamrk {
        margin-top: 20px;
        border: 1px solid #e4e4e4;
    }

    .phone-con {
        line-height: 30px;
        color: #333333;
    }

    .line-bottom {
        border-bottom: 1px solid #e4e4e4;
        padding-left: 0;
        background-color: rgba(255, 255, 255, 0);
    }

    .remark-con {
        border: 1px solid #e4e4e4;
        margin: 10px 0;
    }

    .flex-grow-full {
        flex-grow: 1;
    }

    .read-type-item {
        margin-left: 10px;
    }

    .flex-box {
        display: flex;
        align-items: center;
    }

    .serial-num-btn {
        width: 30%;
        font-size: 0.35rem;
        margin-left: 10px;
    }

    .simple-modify-block {
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: white;
        overflow: auto;
    }

    .no-border::after {
        border-bottom: 0;
    }

    .icon-xiala3 {
        display: inline-block;
        transform: rotate(180deg);
    }

    .zhankaisvg {
        background: url(../../images/zhedie.svg) 50% 50% no-repeat;
        border: none;
    }

    .zhediesvg {
        background: url(../../images/zhankai.svg) 50% 50% no-repeat;
        border: none;
    }
}
</style>
