const path = require('path');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const VueLoaderPlugin = require('vue-loader/lib/plugin');
const MomentLocalesPlugin = require('moment-locales-webpack-plugin');
const utils = require('./utils');
const WebpackBar = require('webpackbar');

const vantVarPath = path.resolve(__dirname, 'src/assets/css/var.less');

module.exports = {
    entry: utils.entries,
    output: {
        filename: '[name]-[contenthash:5].js',
        path: path.resolve(__dirname, 'dist')
    },
    stats: 'errors-only',
    resolve: {
        alias: {
            '@': path.resolve(__dirname, 'src')
        },
        extensions: ['.js', '.vue', '.json']
    },
    plugins: [
        new WebpackBar(),
        new CleanWebpackPlugin(),
        new MiniCssExtractPlugin({
            filename: 'style/[name]-[contenthash:5].css',
            ignoreOrder: true
        }),
        new VueLoaderPlugin(),
        new MomentLocalesPlugin({
            localesToKeep: ['zh-cn']
        })
    ].concat(utils.htmlPlugins()),
    module: {
        rules: [
            {
                test: /\.vue$/,
                use: [
                    'vue-loader'
                ]
            },
            {
                test: /\.(png|jpg|gif|JPG)$/,
                use: [{
                    loader: 'url-loader',
                    options: {
                        limit: 1,
                        outputPath: 'img/',
                        esModule: false,
                        name: '[name]-[hash:5].[ext]'
                    }
                }]
            },
            {
                test: /\.(woff2?|eot|ttf|otf|svg)(\?.*)?$/,
                use: [{
                    loader: 'url-loader',
                    options: {
                        limit: 1,
                        outputPath: 'fonts/',
                        name: '[name]-[hash:5].[ext]'
                    }
                }]
            },
            {
                test: /\.js$/,
                use: [
                    {
                        loader: 'babel-loader',
                        options: {
                            presets: [
                                '@babel/preset-env'
                            ]
                        }
                    }
                ],
                include: path.resolve(__dirname, 'src'),
                exclude: /node_modules/
            },
            {
                test: /\.css$/,
                use: [
                    {
                        loader: MiniCssExtractPlugin.loader,
                        options: {
                            publicPath: '../'
                        }
                    },
                    'css-loader', 'postcss-loader'
                ]
            },
            {
                test: /\.s[ac]ss$/i,
                use: [
                    {
                        loader: MiniCssExtractPlugin.loader,
                        options: {
                            publicPath: '../'
                        }
                    },
                    'css-loader', 'postcss-loader', 'sass-loader'
                ]
            },
            {
                test: /\.less$/,
                use: [
                    {
                        loader: MiniCssExtractPlugin.loader,
                        options: {
                            sourceMap: true
                        }
                    }, {
                        loader: 'css-loader',
                        options: {
                            sourceMap: true
                        }
                    }, {
                        loader: 'postcss-loader',
                        options: {
                            sourceMap: 'inline'
                        }
                    }, {
                        loader: 'less-loader',
                        options: {
                            sourceMap: true,
                            lessOptions: {
                                modifyVars: {
                                    hack: `true; @import "${vantVarPath}";`
                                }
                            }
                        }
                    }
                ]
            },
            {
                test: require.resolve('zepto'),
                loader: 'exports-loader?window.Zepto!script-loader'
            }
        ]
    },
    optimization: {
        // namedModules: true,
        // namedChunks: true,
        moduleIds: 'named',
        chunkIds: 'named',
        runtimeChunk: 'single',
        splitChunks: {
            cacheGroups: {
                common: {
                    // 公共的模块，例如调用客户端的插件、公共业务组件等
                    priority: -20, 
                    chunks: 'all',
                    minSize: 0, //最小大小0,不管模块多大，只要重复引用就抽离
                    minChunks: 4, //引用次数2次以上就抽离
                    name: 'common',
                    reuseExistingChunk: true,
                    test: function(module) {
                        if (module.resource && module.resource.includes('jquery')) {
                            return false;
                        }
                        return true;
                    },
                    enforce: true
                },
                assets: { 
                    priority: -10,  
                    test: function(module) {
                        if (module.resource && module.resource.includes('bizComponents')) {
                            return true;
                        }
                        return false;
                    },
                    minSize: 0,
                    chunks: 'all',
                    minChunks: 4,
                    name: 'chunk-assets',
                    reuseExistingChunk: true,
                    enforce: true
                },
                vendor: {  //此处为了抽离第三方的公共模块，例如Vue、vue-router、rux-ui等
                    priority: 1,  
                    test: function(module) {
                        if ((!(/[\\/]node_modules[\\/]/).test(module.resource)) || (module.resource && module.resource.includes('jquery'))) {
                            return false;
                        }
                        return true;
                    },
                    minSize: 0,
                    chunks: 'all',
                    minChunks: 4,
                    name: 'vendor',
                    reuseExistingChunk: true
                },
                rux: {
                    priority: 10,  
                    test: /[\\/]node_modules[\\/]rux-ui-v[\\/]/,
                    minSize: 0,
                    chunks: 'all',
                    minChunks: 4,
                    name: 'chunk-rux',
                    reuseExistingChunk: true
                },
                pdf: { 
                    priority: 20,  
                    test: /[\\/]node_modules[\\/]pdfjs-/,
                    minSize: 0,
                    chunks: 'all',
                    minChunks: 4,
                    name: 'chunk-pdfjs',
                    reuseExistingChunk: true
                }
            }
        }
    }
};
