<template>
  <div class="text-box">
    <div :class="{ 'over-hidden': !unfold }" ref="textBox">
      <span ref="spanBox">{{ text }}</span>
    </div>
    <div class="btn" v-if="ifOver" @click="unfold = !unfold">{{ unfold ? '收起' : '展开' }}</div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      ifOver: false, // 文本是否超出三行，默认否
      unfold: false, // 文本是否是展开状态 默认为收起
    }
  },
  props: {
    text: {
      type: String,
      default: '',
    },
  },
  mounted() {
    // 判断是否显示展开收起按钮
    this.$nextTick(() => {
      this.ifOver = this.$refs.spanBox.offsetHeight > this.$refs.textBox.offsetHeight
    })
  },
  watch: {
    // 判断是否显示展开收起按钮
    text: function () {
      this.$nextTick(() => {
        this.ifOver = this.$refs.spanBox.offsetHeight > this.$refs.textBox.offsetHeight
      })
    },
  },
}
</script>
<style scoped>
.over-hidden {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
  overflow: hidden;
}
.btn {
  color: #3489FC;
  float: right;
}
</style>
