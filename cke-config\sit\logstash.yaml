kind: Deployment
apiVersion: apps/v1
metadata:
  name: logstash
  namespace: mc
  labels:
    k8s-app: logstash
spec:
  replicas: 1
  selector:
    matchLabels:
      k8s-app: logstash
  template:
    metadata:
      name: logstash
      labels:
        k8s-app: logstash
    spec:
      containers:
        - name: logstash
          image: harbor.dcos.guangzhou.unicom.local/billing_idsp_prod/logstash-cke:2020
          env:
          - name: ETCD
            value: "http://*************:39701"
          - name: CONFIGPATH
            value: "/user/shanghaimc/config/"
          - name: PIPLINESPATH
            value: "/user/shanghaimc/piplines1/"
          volumeMounts:
          - name: cfs-log
            mountPath: /log
      volumes:
      - name: cfs-log
        persistentVolumeClaim:
          claimName: mccfs
          readOnly: false