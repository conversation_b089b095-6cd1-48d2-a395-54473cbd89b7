import { addMockUrlAuto } from './utils';

/*
* 1. apiPost 同一个项目下所有接口的 mock 地址前缀一样，所以独立出来
* 2. apiPost mock 地址一般为: 固定前缀 + 实际地址，mockBasePath 为固定前缀
* */
const mockBasePath = 'https://mock.apipost.cn/app/mock/project/501ea4d5-156e-403b-8e58-dfc518b2272f/';

/*
* 1. isMock: 是否使用 mock 替换该 url
* 2. if (useBasePath && mockUrlAuto) 使用 mockUrlAuto, 否则使用 mockUrl
* 3. url: 匹配的url
* 4. mockUrl: 手动填写的mockUrl，可选
* 5. useBasePath: true:使用mockUrlAuto, false:使用mockUrl
* 6. mockUrlAuto: 自动生成，无需填写。mockBasePath + url
* */
const mockDemo = [
    {
        url: 'touch_sub_back/shoppingCartController/shoppingCartPaymentQuery',
        mockUrl: 'https://mock.apipost.cn/app/mock/project/501ea4d5-156e-403b-8e58-dfc518b2272f/touch_sub_back/shoppingCartController/shoppingCartPaymentQuery',
        useBasePath: true,
        mockUrlAuto: '',
        isMock: true
    }

];
addMockUrlAuto(mockDemo, mockBasePath);

export default mockDemo;
