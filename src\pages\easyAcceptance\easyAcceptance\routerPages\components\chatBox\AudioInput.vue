<template>
    <div>
        <div class="client-dialog-bubble" v-show="params.text">
            <div class="client-dialog-icon"></div>
            <div class="client-dialog-text">{{ params.text }}</div>
        </div>
        <audio :src="params.audio" controls ref="audio" style="display: none;"></audio>
        <van-icon name="play-circle-o" @click="play" v-show="params.audio" />
    </div>
</template>

<script>
export default {
    name: 'AudioInput',
    props: {
        params: {
            audio: '',
            text: ''
        }
    },
    data() {
        return {}
    },
    created() {
        // console.log(this.params);
    },
    methods: {
        play() {
            this.$refs.audio.play()
        }
    }
};
</script>

<style lang="scss" scoped>
.van-icon-play-circle-o {
    position: absolute;
    display: block;
    font-size: 26px;
    color: #4997ff;
    bottom: 0;
    right: 35px;
}
</style>
