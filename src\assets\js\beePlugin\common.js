import <PERSON> from './jcl';
import Mobile from './mobile';
import ClientTool from './client-toolkit';
import wadeMobile from './wadeMobile';
import {Toast} from "vant";

var Common = new function(){
    /**
     * 请求后端接口
     * @method callServer
     * @param {string} [action] 接口名
     * @param {Object} [param] 接口入参
     * @param {number} [timeout=30] 接口超时时间（单位：s）
     * @return
     */
    // this.callServer= function (action, param, timeout=30) {
    //     let count = 5;
    //     let outReject;
    //     let reqTimeout = false;
    //     let reqFinish = false;
    //     let promise = new Promise((resolve, reject) => {
    //         outReject = reject;
    //         Mobile.getMemoryCache([Constant.SESSION_ID,Constant.STAFF_ID]).then((cacheValue) => {
    //             return Object.assign({}, param ? param : {}, cacheValue ? cacheValue : {});
    //         }).then((combineParam) => {
    //             !function dataRequest(count) {
    //                 Mobile.dataRequest(action, combineParam).then((resultData) => {
    //                     if (!reqTimeout) {
    //                         reqFinish = true;
    //                         const resultCode = resultData[Constant.X_RESULTCODE];
    //                         if(resultCode != 0){
    //                             if (resultCode == -100) {
    //                                 //暂时取消自动跳转至登录页的逻辑，后续登陆开发完成后再加上
    //                                 // Mobile.openPage("login");
    //                                 // reject(resultData);
    //                                 console.log("Timeout action>>>>>", action);
    //                                 import("../loginUtil").then(loginUtil =>{
    //                                     Toast.loading({
    //                                         message: "正在尝试重新登录...",
    //                                         duration: 0
    //                                     });
    //                                     loginUtil.default.reLogin().then(result=>{
    //                                         Toast.clear();
    //                                         Mobile.openPage("Index");
    //                                     }).catch(err=>{
    //                                         Toast.clear();
    //                                         Mobile.openPage("login");
    //                                     })
    //                                 }).catch(err=>{
    //                                     Mobile.openPage("login");
    //                                 });
    //                             }
    //                             console.log(`调用接口${action}失败：`, resultData);
    //                             reject(resultData);
    //                         }else{
    //                             console.log(`调用接口${action}成功：`, resultData);
    //                             resolve(resultData);
    //                         }
    //                     }
    //
    //                 }, (err) => {
    //                     if (!reqTimeout) {
    //                         if (count <= 1) {
    //                             reqFinish = true;
    //                             console.log(`调用接口${action}失败：`, err);
    //                             reject(err);
    //                         }
    //                         setTimeout(() => {
    //                             dataRequest(--count);
    //                         }, 5000);
    //                     }
    //                 });
    //             }(count);
    //         })
    //     });
    //     setTimeout(() => {
    //         if (!reqFinish) {
    //             reqTimeout = true;
    //             console.log(`调用接口${action}超时`);
    //             outReject("请求超时");
    //         }
    //     }, timeout*1000);
    //     return promise;
    //
    // }
    // this.callSvc = function(action,param,callback,error){
    //     var _this = this;
    //     param = param ? param : new Wade.DataMap();
    //     error = error ? error : function(x_info, x_code, traceId) {
    //         Mobile.loadingStop();
    //         Common.loadingStop();
    //         if(x_code==-100){
    //             Mobile.closeIpuApp("SESSION_TIMEOUT");
    //             return;
    //         }
    //         if (x_code != null && x_code > '4000') {
    //             var tip = [];
    //             x_code != null && x_code != '8888' && x_code != '-1' ? tip.push("错误编码:[" + x_code + "]") : "";
    //             x_info ? tip.push("错误信息:[" + x_info + "]") : "";
    //             traceId ? tip.push("错误流水:[" + traceId + "]") : "";
    //             Mobile.alert(tip.join("<br/>"), null, "出错了", true);
    //         } else {
    //             Mobile.tip(x_info || "未获取到异常信息，请稍候重试~");
    //         }
    //     };
    //     var loadingControl = function (input) {
    //         try {
    //             var ret = {
    //                 tip: param.get('_tip'),
    //                 autoClose: param.get('_autoClose')
    //             }
    //             null == ret.autoClose && (ret.autoClose = (null != ret.tip));
    //             param.remove('_tip');
    //             param.remove('_autoClose');
    //             return ret;
    //         } catch (e) {
    //             return {};
    //         }
    //
    //     }(param);
    //     if (loadingControl.tip) {
    //         Common.loadingStart(loadingControl.tip);
    //     }
    //
    //     Common.get([Constant.SESSION_ID,Constant.STAFF_ID]).then(function(data) {
    //         if (typeof data == "string") {
    //             data = new Wade.DataMap(data);
    //         }
    //         if (data.get(Constant.SESSION_ID)) {
    //             param.put(Constant.SESSION_ID, data.get(Constant.SESSION_ID));
    //         }
    //         if (data.get(Constant.STAFF_ID)) {
    //             param.put(Constant.STAFF_ID, data.get(Constant.STAFF_ID));
    //         }
    //         callSvc(action, param, callback, error);
    //     });
    //
    //     function callSvc(_action,_param,_callback,_error){
    //         var reconnectionCountLimit = 5;
    //         var reconnectCount = 0;
    //         var timeSecond = 10;
    //         var isReconnected = false;
    //         dataRequest();
    //         function dataRequest() {
    //             if(isReconnected){
    //                 Common.loadingStart("重连中，请稍等，正在进行第"+reconnectCount+"次尝试中.......");
    //             }
    //             Mobile.dataRequest(_action,_param).then(function(resultData){
    //                 if(isReconnected){
    //                     Common.loadingStop();
    //                 }
    //                 resultData = new Wade.DataMap(resultData);
    //                 var x_resultcode = resultData.get(Constant.X_RESULTCODE);
    //                 var x_resultinfo = resultData.get(Constant.X_RESULTINFO);
    //                 var traceId = resultData.get(Constant.TRACE_ID);
    //                 if(x_resultcode != 0){
    //                     if (x_resultcode == -100) {
    //                         Mobile.closeIpuApp("SESSION_TIMEOUT");
    //                         return;
    //                     }
    //                     _error(_this.resetErrorMsg(x_resultinfo), x_resultcode, traceId, resultData.get(Constant.RESP_DATA));//接口异常则回调报错函数
    //                 }else{
    //                     _callback(resultData);
    //                     if (loadingControl.autoClose) {
    //                         Common.loadingStop();
    //                     }
    //                 }
    //             },function (errData) {
    //                 if(reconnectionCountLimit>0){
    //                     isReconnected=true;
    //                     reconnectCount++;
    //                     Common.loadingStart("连接失败，"+timeSecond+"s后将进行第"+reconnectCount+"次尝试......");
    //                     setTimeout(function () {
    //                         reconnectionCountLimit--;
    //                         Common.loadingStop();
    //                         dataRequest();
    //                     },timeSecond*1000);
    //                 }else{
    //                     Common.loadingStop();
    //                     _error("客户端抛出--"+errData);
    //                 }
    //             });
    //         }
    //     }
    // };
    // var ERROR_MAPPING = {"failed to connect to": "网络异常，请重试~"}
    // // reset error msg throw by ipu framework.
    // this.resetErrorMsg = function (resultInfo) {
    //     if (null == resultInfo || resultInfo == "") {return "未获取到异常信息，请稍候重试~"}
    //
    //     for (var key in ERROR_MAPPING) {
    //         if (ERROR_MAPPING.hasOwnProperty(key) && resultInfo.indexOf(key) > -1) {
    //             return ERROR_MAPPING[key];
    //         }
    //     }
    //     return resultInfo;
    // };
    // this.limitFun = function(fun, limitTime) {
    //     limitTime = limitTime || 1000;
    //     return function (e) {
    //         var lastTime = $(this).data('lastTime');
    //         var nowTime = Date.now() || new Date().getTime();
    //
    //         if (!lastTime || nowTime - lastTime > limitTime) {
    //             fun.call(this, e);
    //             $(this).data('lastTime', nowTime);
    //         }
    //     }
    // };
    // this.downLoadImg = function (imgUrl,version,download) {
    //     return Mobile.downloadImg([imgUrl, download],version)
    // };
    this.openUrlWithPlug = function (url) {
        return Mobile.openUrlWithPlug(url);
    };
    this.openPage = function(action, param, error) {
        param = param ? new Wade.DataMap(param) : new Wade.DataMap();
        error = error ? error : function(x_info, x_code) {
            Mobile.loadingStop();
            if(x_code==-100){
                Mobile.closeIpuApp("SESSION_TIMEOUT");
                return;
            }
            if(x_code){
                Mobile.alert("错误编码:[" + x_code + "]\n错误信息:" + x_info);
            }else{
                Mobile.alert("错误信息:" + x_info);
            }
        };
        Common.get([Constant.SESSION_ID,Constant.STAFF_ID]).then((data) => {
            if (typeof data == "string") {
                data = new Wade.DataMap(data);
            }
            if (data.get(Constant.SESSION_ID)) {
                param.put(Constant.SESSION_ID, data.get(Constant.SESSION_ID));
            }
            if (data.get(Constant.STAFF_ID)) {
                param.put(Constant.STAFF_ID, data.get(Constant.STAFF_ID));
            }
            openPage(action, param, error);
        })

        function openPage(_action, _param, _error) {
            Mobile.openPage(_action, _param, function(errMsg) {
                if (typeof (errMsg) == "string") {
                    errMsg = new Wade.DataMap(errMsg);
                }
                var x_resultcode = errMsg.get("X_RESULTCODE");
                var x_resultinfo = errMsg.get("X_RESULTINFO");
                _error(x_resultinfo, x_resultcode);
            });
        }
    };

    this.openWindow = function(action, param) {
        return Mobile.openWindow(action, param);
    }
    this.getPostParam = function () {
        return Mobile.getPostParam();
    }
    this.getPage = function(action, param) {
        param = param ? new Wade.DataMap(param) : new Wade.DataMap();
        return new Promise((resolve, reject) => {
            Common.get([Constant.SESSION_ID,Constant.STAFF_ID]).then((data) => {
                if (typeof data == "string") {
                    data = new Wade.DataMap(data);
                }
                if (data.get(Constant.SESSION_ID)) {
                    param.put(Constant.SESSION_ID, data.get(Constant.SESSION_ID));
                }
                if (data.get(Constant.STAFF_ID)) {
                    param.put(Constant.STAFF_ID, data.get(Constant.STAFF_ID));
                }
                getPage(action, param, (data) => {
                    resolve(data);
                }, (err) => {
                    reject(err);
                });
            })
        })
        function getPage(_action, _param, _callback, _error) {
            Mobile.getPage(_action, _param).then(_callback, (errMsg) => {
                if (typeof (errMsg) == "string") {
                    errMsg = new Wade.DataMap(errMsg);
                }
                _error(JSON.parse(errMsg.toString()));
            })
        }
    };

    /** 打开主子应用页面方法，即老的子应用 */
    this.openMainSubAppPage = function (action, param) {
        this.openAppPage("200001", action, param);//老的子应用
    };

    this.openCoreAppPage = function (action, param) {
        this.openAppPage("300038", action, param);
    }

    this.openLocalH5 = function (path, cookies, callback, err) {
        const index = location.href.lastIndexOf("/", location.href.indexOf(".html"));
        const prePath = location.href.substring(0, index + 1);
        const url = prePath + path;
        Mobile.openH5(url, cookies, callback, err);
    }

    /** 打开指定IPU应用页面方法 */
    this.openAppPage = function (appId, action, param) {
        const paramStr = param ? Wade.DataMap(param).toString() : "";
        return new Promise(((resolve, reject) => {
            Mobile.savePostParam(paramStr);
            wadeMobile.openIpuApp(new Wade.DataMap({
                EXT_PARAM: paramStr,
                APP_ID: appId,
                // MENU_TYPE: "I",
                MENU_PAGE_ACTION: action
            }), data => {
                resolve(data);
            }, err => {
                reject(err);
            });
        }));
    };

    /** 获取位置信息 */
    this.getLocationInfo = function (datas) {
        wadeMobile.location().then((info) => {
            wadeMobile.loadingStop();
            var data=new Wade.DataMap(info);
            datas.put("GPRS_Y",data.get('Longitude'));
            datas.put("GPRS_X",data.get('Latitude'));
            if(wadeMobile.isIOS()) {
                datas.put("GPRS_ADDRESS", data.get('LocationDesc'));
            }else if(wadeMobile.isAndroid()){
                datas.put("GPRS_ADDRESS", data.get('ADDRESS'));
            }
        }, (error) => {
            wadeMobile.loadingStop();
            wadeMobile.tip(error);
        })
    };

    this.closeApp = function(){
        if(confirm("确定要退出应用程序吗?")){
            Mobile.closeApp();
        }
    };

    this.logoutAccount = function(){
        if(confirm("确定要注销该工号吗?")){
            Common.remove(Constant.SESSION_ID);
            WadeMobile.clearBackStack();
            Mobile.openTemplate("Home");
        }
    };

    this.put = function(key, value) {
        if(!checkMapKey(key)){
            return;
        }
        Mobile.setMemoryCache(key, value);
    };
    this.get = function(key, value) {
        if(!checkArrayKey(key)){
            return Promise.reject("参数类型异常");
        }
        return Mobile.getMemoryCache(key, value);
    };
    this.remove = function(key) {
        if(!checkArrayKey(key)){
            return;
        }
        Mobile.removeMemoryCache(key);
    };
    this.clear = function() {
        Mobile.clearMemoryCache();
    };
    this.putLocal = function(key, value) {
        if(!checkMapKey(key)){
            return;
        }
        Mobile.setOfflineCache(key, value);
    };
    this.getLocal = function(key, value) {
        if(!checkArrayKey(key)){
            return Promise.reject("参数类型异常");
        }
        return Mobile.getOfflineCache(key,value);
    };
    this.removeLocal = function(key) {
        if(!checkArrayKey(key)){
            return;
        }
        Mobile.removeOfflineCache(key);
    };
    this.clearLocal = function() {
        Mobile.clearOfflineCache();
    };
    this.loadingStart = function(tip) {
        if(!tip) {
            tip = "正在加载...";
        }
        this.loadingStop();
        $('body').append('<div class="mask" id="loading-mask"><div class="mask-loading-content">' + tip + '</div></div>');
    };
    this.loadingStop =  function() {
        try {
            $('#loading-mask').remove();
        } catch (e){}
    };
    /*数据库操作*/
    var dbName = "display";
    this.execSQL = function(sql,bindArgs){
        return Mobile.execSQL(dbName,sql,bindArgs);
    };

    function checkMapKey(key){
        if (!key || (typeof (key) != "string" && !ClientTool.tool.isDataMap(key))) {
            Mobile.alert(key+"参数类型异常");
            return false;
        } else {
            return true;
        }
    }

    function checkArrayKey(key){
        if (!key || (typeof (key) != "string" && !ClientTool.tool.isArray(key))) {
            Mobile.alert(key+"参数类型异常");
            return false;
        } else {
            return true;
        }
    }
}

window.Constant = {
    OPEN_PAGE_KEY : "OPEN_PAGE_KEY",
    STAFF_ID : "STAFF_ID",
    SESSION_ID : "SESSION_ID",
    X_RECORDNUM : "X_RECORDNUM",
    X_RESULTCODE : "X_RESULTCODE",
    X_RESULTINFO : "X_RESULTINFO",
    X_RESULTCAUSE : "X_RESULTCAUSE",
    TRACE_ID: "traceId",
    RESP_DATA: "respData"
};

export default Common;
