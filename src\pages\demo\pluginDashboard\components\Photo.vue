<template>
    <div>
        <van-field
            readonly
            clickable
            label="类型"
            :value="value"
            placeholder="选择拍照类型"
            @click="showPicker = true"
        />
        <van-button
            icon="photograph"
            @click="takePhoto"
            type="primary"
        >
            拍照
        </van-button>
        <img
            :src="image"
            width="300"
            height="300"
            alt=""
        >
        <van-popup
            v-model="showPicker"
            round
            position="bottom"
        >
            <van-picker
                show-toolbar
                :columns="columns"
                @cancel="showPicker = false"
                @confirm="onConfirm"
            />
        </van-popup>
    </div>
</template>

<script>
import WadeMobile from 'rk-native-plugin';

export default {
    name: 'Photo',
    data() {
        return {
            value: '',
            activePhotoType: '',
            image: '',
            showPicker: false,
            columns: [
                {
                    text: '身份证正面',
                    photoType: 'idCardFront'
                },
                {
                    text: '身份证反面',
                    photoType: 'idCardBack'
                },
                {
                    text: '人脸拍照',
                    photoType: 'face'
                },
                {
                    text: '无框拍照',
                    photoType: 'noBorder'
                }
            ],
        };
    },
    methods: {
        onConfirm(value) {
            this.value = value.text;
            this.activePhotoType = value.photoType;
            this.showPicker = false;
        },
        takePhoto() {
            if (!this.activePhotoType) {
                this.$toast('请选择拍照类型');
                return;
            } else {
                WadeMobile.getIdentifyPhoto(this.activePhotoType, 'base64').then((img) => {
                    this.image = img;
                }).catch((e) => {
                    // eslint-disable-next-line no-console
                    console.log(e);
                });
            }
        }
    },
};
</script>

<style scoped>

</style>
