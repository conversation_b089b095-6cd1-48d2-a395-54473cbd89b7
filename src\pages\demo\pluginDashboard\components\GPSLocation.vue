<template>
    <div>
        <van-button
            icon="location-o"
            @click="getLocationInfo"
            type="primary"
        >
            获取定位信息
        </van-button>
        <van-button
            icon="location-o"
            @click="closeH5"
            type="primary"
        >
            关闭
        </van-button>
        <van-button
            icon="rux-xiaochengxu-gengduo"
            type="primary"
        />
        <van-icon name="rux-jiaofuweixuanzhong" />
        <van-cell
            title="省份"
            :value="locationInfo.province"
            size="large"
        />
        <van-cell
            title="地市"
            :value="locationInfo.city"
            size="large"
        />
        <van-cell
            title="地址"
            :value="locationInfo.address"
            size="large"
        />
        <van-cell
            title="经度"
            :value="locationInfo.longitude"
            size="large"
        />
        <van-cell
            title="纬度"
            :value="locationInfo.latitude"
            size="large"
        />
    </div>
</template>

<script>
import { Mobile } from 'rk-web-utils';
import WadeMobile from 'rk-native-plugin';

export default {
    name: 'GPSLocation',
    data() {
        return {
            locationInfo: {
                province: '',
                city: '',
                address: '',
                longitude: '',
                latitude: ''
            }
        };
    },
    methods: {
        getLocationInfo() {
            WadeMobile.location().then((info) => {
                // WadeMobile.location()插件安卓和ios返回的地址信息字段名上有区别，需要判断设备类型再取值
                if (WadeMobile.isAndroid()) {
                    this.locationInfo = {
                        province: info.PROVINCE,
                        city: info.CITY,
                        address: info.ADDRESS,
                        longitude: info.Longitude,
                        latitude: info.Latitude
                    };
                } else if (WadeMobile.isIOS()) {
                    this.locationInfo = {
                        province: info.Province,
                        city: info.City,
                        address: info.LocationDesc,
                        longitude: info.Longitude,
                        latitude: info.Latitude
                    };
                }
            }).catch(e => {
                // eslint-disable-next-line no-console
                console.log(e);
            });
        },
        closeH5() {
            Mobile.closeH5('111');
        }
    },
    mounted() {

    }
};
</script>

<style scoped>

</style>
