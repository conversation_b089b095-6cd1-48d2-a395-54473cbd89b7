<template>
    <div>
        <van-button
            icon="scan"
            @click="scan"
            type="primary"
        >
            扫码
        </van-button>
        <van-cell
            title="扫码结果"
            :value="scanResult"
            size="large"
        />
    </div>
</template>

<script>
import WadeMobile from 'rk-native-plugin';

export default {
    name: 'ScanQrCode',
    data() {
        return {
            scanResult: ''
        };
    },
    methods: {
        scan() {
            WadeMobile.scanQrCode().then((info) => {
                // eslint-disable-next-line no-console
                console.log('二维码或条形码的信息为：' + info);
                this.scanResult = info;
            }).catch((e) => {
                // eslint-disable-next-line no-console
                console.log(e);
            });
        }
    }
};
</script>

<style scoped>

</style>
